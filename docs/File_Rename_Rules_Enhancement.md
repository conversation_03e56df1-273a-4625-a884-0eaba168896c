# 开基确认单文件重命名规则完善

## 概述

在开基确认单系统中，文件重命名功能在两个关键环节发挥作用：
1. **上传识别时**：OCR识别后根据解析结果重命名文件
2. **确认时**：用户确认信息后根据最终确认的数据重命名文件

## 重命名规则

### 文件命名格式
```
{产品名称}_交易_{交易渠道}_{格式化日期}.pdf
```

### 字段说明
- **产品名称**：根据产品ID从账户信息表获取的产品全名
- **交易渠道**：邮件发送方的管理机构
- **格式化日期**：数据日期，格式为中文日期格式（如：2024年01月01日）

## 实现逻辑

### 1. 核心方法：`transFileName()`

```java
private String transFileName(ParseParam parseParam, OpenFundConfirmationStatement lastStatement) {
    Map<String, String> productIdName = parseParam.getProductIdName();
    if (lastStatement == null) {
        lastStatement = getLastStatement();
    }
    List<String> productIds = lastStatement.getProductIds();
    productIds = productIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
    List<String> nameList = new ArrayList<>(productIds.size());
    for (int i = 0; i < productIds.size(); i++) {
        String productId = productIds.get(i);
        String productName = productIdName.get(productId);
        nameList.add(i, productName);
    }
    String productName = StringUtil.getProductName(nameList);
    String transactionChannel = lastStatement.getTransactionChannel();
    String dataDate = lastStatement.getDataDate();
    String formatDate = "";
    if (dataDate != null && !dataDate.isEmpty()) {
        formatDate = DateUtil.format(DateUtil.parse(dataDate, BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN), BaseConstant.CHINESE_DATE_FORMAT_PATTERN);
    }
    return productName + "_交易_" + transactionChannel +"_" + formatDate + ".pdf";
}
```

### 2. 上传时重命名（第319-326行）

```java
// OCR识别和数据处理后
List<OpenFundConfirmationStatement> allStatement = getAllStatement();
String newFileName = transFileName(parseParam, null);
File rename = FileUtil.rename(new File(filePath), newFileName, true);
allStatement.forEach(openFundConfirmationStatement -> {
    openFundConfirmationStatement.setFilePath(rename.getAbsolutePath());
    openFundConfirmationStatement.setAttachmentName(rename.getName());
    long hash = LongHashFunction.xx().hashBytes(bytes);
    openFundConfirmationStatement.setFileId(HexUtil.toHex(hash));
});
```

### 3. 确认时重命名（完善后）

```java
@Transactional(rollbackFor = Exception.class)
public void confirm(@NonNull OpenFundConfirmationStatement openFundConfirmationStatement) {
    String id = openFundConfirmationStatement.getId();
    OpenFundConfirmationStatement inDb = this.getById(id);
    if (inDb == null) {
        throw new IllegalStateException("OpenFundConfirmationStatement不存在");
    }
    
    // 使用确认时的最新数据生成文件名，而不是数据库中的旧数据
    // 将用户确认的产品ID和业务类型设置到inDb对象中，以便生成正确的文件名
    inDb.setProductIds(openFundConfirmationStatement.getProductIds());
    inDb.setBusinessTypes(openFundConfirmationStatement.getBusinessTypes());
    
    // 按照上传时的重命名规则生成新文件名
    String newFileName = transFileName(initMap(), inDb);
    File rename = FileUtil.rename(new File(inDb.getFilePath()), newFileName, true);
    
    // 更新文件路径和附件名称
    openFundConfirmationStatement.setFilePath(rename.getAbsolutePath());
    openFundConfirmationStatement.setAttachmentName(rename.getName());
    openFundConfirmationStatement.setOcrConfirmationStatus(OcrConfirmationStatus.CONFIRMED);
    this.updateById(openFundConfirmationStatement);
    
    // ... 后续处理逻辑
}
```

## 完善内容

### 问题分析
确认时的重命名逻辑存在以下问题：
1. **数据不一致**：使用数据库中的旧数据而不是用户确认的最新数据
2. **信息缺失**：没有将用户修改的产品ID和业务类型同步到用于生成文件名的对象中

### 解决方案
1. **数据同步**：
   ```java
   // 将用户确认的最新数据设置到inDb对象中
   inDb.setProductIds(openFundConfirmationStatement.getProductIds());
   inDb.setBusinessTypes(openFundConfirmationStatement.getBusinessTypes());
   ```

2. **规则一致性**：
   - 确认时使用与上传时相同的 `transFileName()` 方法
   - 确保文件名生成逻辑完全一致

3. **注释完善**：
   - 添加详细注释说明重命名的目的和逻辑
   - 明确数据来源和处理流程

## 重命名时机

### 上传时重命名
- **时机**：OCR识别完成，数据解析和处理完成后
- **数据来源**：OCR识别结果 + 字段映射规则
- **目的**：根据识别结果生成标准化文件名

### 确认时重命名  
- **时机**：用户确认信息，点击确认按钮后
- **数据来源**：用户确认的最终数据（可能包含手动修正）
- **目的**：根据最终确认的准确信息生成最终文件名

## 数据流程

```
上传文件 → OCR识别 → 数据解析 → 第一次重命名（基于识别结果）
    ↓
用户查看和修正 → 确认信息 → 第二次重命名（基于确认数据）
    ↓
最终文件名（准确反映确认后的信息）
```

## 注意事项

1. **文件路径更新**：重命名后必须更新数据库中的文件路径和附件名称
2. **事务处理**：重命名操作在事务中进行，确保数据一致性
3. **异常处理**：文件重命名失败时需要适当的异常处理
4. **日志记录**：重命名操作应该有相应的日志记录

## 验证要点

1. **命名准确性**：文件名是否正确反映了确认后的产品信息
2. **路径一致性**：数据库中的文件路径是否与实际文件路径一致
3. **重复处理**：多次确认是否会导致重复重命名问题
4. **特殊字符处理**：文件名中的特殊字符是否被正确处理
