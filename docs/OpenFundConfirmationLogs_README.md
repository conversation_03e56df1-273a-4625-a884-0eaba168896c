# 开基确认单操作日志系统

## 概述

本系统为开基确认单操作提供了完整的日志记录功能，包括上传、下载、邮件发送、OCR确认和删除操作的日志记录。

## 功能特性

### 1. 日志记录类型

- **上传日志**: 记录文件上传操作
- **下载日志**: 记录文件下载操作  
- **邮件发送日志**: 记录邮件发送操作
- **OCR确认日志**: 记录OCR确认操作
- **删除记录日志**: 记录删除操作

### 2. 数据库表结构

#### 上传日志表 (log_open_fund_confirmation_upload)
- `id`: 主键ID
- `file_name`: 文件名称
- `file_path`: 文件路径
- `file_id`: 文件唯一标识
- `upload_time`: 上传时间
- `operator`: 操作人
- `operation_status`: 操作状态

#### 下载日志表 (log_open_fund_confirmation_download)
- `id`: 主键ID
- `file_name`: 文件名称
- `file_path`: 文件路径
- `file_id`: 文件唯一标识
- `download_time`: 下载时间
- `operator`: 操作人
- `operation_status`: 操作状态

#### 邮件发送日志表 (log_open_fund_confirmation_mail)
- `id`: 主键ID
- `task_name`: 邮件发送任务名称
- `send_method`: 发送方式(AUTO/MANUAL)
- `mail_status`: 邮件发送状态
- `send_time`: 邮件发送时间
- `operator`: 操作人
- `mail_log_id`: 邮件日志ID

#### OCR确认日志表 (log_open_fund_confirmation_ocr)
- `id`: 主键ID
- `confirmation_id`: 确认单ID
- `file_name`: 文件名称
- `account_set_name`: 账套名称
- `confirm_time`: 确认时间
- `operator`: 操作人
- `confirm_status`: 确认状态

#### 删除记录日志表 (log_open_fund_confirmation_delete)
- `id`: 主键ID
- `confirmation_id`: 确认单ID
- `file_name`: 文件名称
- `account_set_name`: 账套名称
- `delete_time`: 删除时间
- `operator`: 操作人
- `delete_status`: 删除状态

## API接口

### 1. 获取所有日志
```
GET /open-confirmation-logs/all?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 2. 获取上传日志
```
GET /open-confirmation-logs/upload?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 3. 获取下载日志
```
GET /open-confirmation-logs/download?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 4. 获取邮件发送日志
```
GET /open-confirmation-logs/mail?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 5. 获取OCR确认日志
```
GET /open-confirmation-logs/ocr?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 6. 获取删除记录日志
```
GET /open-confirmation-logs/delete?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 7. 文件下载
```
GET /open-confirmation-logs/download-file/{fileId}
```

#### 参数说明
- `current`: 当前页码，默认1
- `size`: 每页大小，默认10
- `startDate`: 开始日期，格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss（可选）
- `endDate`: 结束日期，格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss（可选）

## 前端展示格式

### 上传确认单
| 文件名称 | 上传时间 | 操作人 | 操作状态 |
|---------|---------|-------|---------|
| [文件点击可下载] | XXXXXX | 用户名 | 成功 |

### 下载确认单
| 时间 | 操作人 | 操作状态 | 文件 |
|-----|-------|---------|------|
| XXXXXX | 用户名 | 成功 | [文件点击可下载] |

### 发送邮件
| 邮件发送任务 | 发送方式 | 状态 | 邮件发送时间 | 操作人 | 查看 |
|------------|---------|-----|------------|-------|------|
| 开基确认单邮件发送 | 自动/手工 | 成功/失败 | XXXXXX | 用户名 | 查看 |

### OCR确认
| 确认时间 | 操作人 | 文件名称 | 账套名称 | 状态 |
|---------|-------|---------|---------|------|
| XXXXXX | 用户名 | 文件名 | 账套名 | 已确认 |

### 删除记录
| 时间 | 操作人 | 文件名称 | 账套名称 | 操作 |
|-----|-------|---------|---------|------|
| XXXXXX | 用户名 | 文件名 | 账套名 | 删除记录 |

## 部署说明

### 1. 执行数据库脚本
```sql
-- 执行 sql/open_fund_confirmation_logs.sql 中的建表语句
```

### 2. 重启应用
重启Spring Boot应用以加载新的Bean和配置。

### 3. 验证功能
- 测试文件上传，检查上传日志
- 测试文件下载，检查下载日志
- 测试邮件发送，检查邮件日志
- 测试OCR确认，检查确认日志
- 测试删除操作，检查删除日志

## 特殊处理说明

### 1. 邮件发送状态处理
由于邮件发送是异步的，邮件发送状态的记录分为两个阶段：
- **发送启动阶段**: 调用sendMail方法时记录"发送中"状态
- **发送完成阶段**: 在OpenFundMailSendJob中，根据SendMailInfo的实际status字段记录最终发送结果
  - 手动发送：通过Controller调用sendMail方法触发，任务名称为"开基确认单邮件发送"
  - 自动发送：通过定时任务触发，任务名称使用Cron.jobName
  - 邮件日志ID：使用SendMailInfo.logId字段，这是邮件的唯一标识

### 2. 日期范围查询
所有日志查询接口都支持日期范围参数：
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- 支持格式: `yyyy-MM-dd` 或 `yyyy-MM-dd HH:mm:ss`
- 如果不提供日期参数，则查询所有数据

## 注意事项

1. 所有日志记录都是异步的，不会影响主业务流程
2. 日志记录失败不会影响主业务操作
3. 文件下载通过fileId进行，确保安全性
4. 操作人信息从当前登录用户获取
5. 日志数据支持分页查询，避免大数据量问题
6. 邮件发送状态使用SendMailInfo的实际状态，确保状态准确性
7. 日期查询支持精确到秒的时间范围过滤
