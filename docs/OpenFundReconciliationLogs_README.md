# 开基对账单日志功能

## 概述

参照开基确认单的日志功能，为开基对账单模块添加了完整的日志记录功能，包括上传、下载、邮件发送、OCR确认和删除操作的日志记录。

## 功能特性

### 日志类型

1. **上传日志** - 记录对账单文件上传操作
2. **下载日志** - 记录对账单文件下载操作
3. **邮件发送日志** - 记录邮件发送操作
4. **OCR确认日志** - 记录OCR确认操作
5. **删除记录日志** - 记录删除操作

### 主要功能

- 自动记录所有关键操作的日志
- 支持按时间范围查询日志
- 支持分页查询
- 提供统一的日志查看接口
- 支持通过日志ID下载文件

## 数据库表结构

### 上传日志表 (log_open_fund_reconciliation_upload)
- `id`: 主键ID
- `file_name`: 文件名称
- `file_path`: 文件路径
- `file_id`: 文件唯一标识
- `reconciliation_id`: 对账单ID
- `upload_time`: 上传时间
- `operator`: 操作人
- `operation_status`: 操作状态

### 下载日志表 (log_open_fund_reconciliation_download)
- `id`: 主键ID
- `file_name`: 文件名称
- `file_path`: 文件路径
- `file_id`: 文件唯一标识
- `reconciliation_id`: 对账单ID
- `download_time`: 下载时间
- `operator`: 操作人
- `operation_status`: 操作状态

### 邮件发送日志表 (log_open_fund_reconciliation_mail)
- `id`: 主键ID
- `reconciliation_id`: 对账单ID
- `task_name`: 邮件发送任务名称
- `send_method`: 发送方式(AUTO/MANUAL)
- `mail_status`: 邮件发送状态
- `send_time`: 邮件发送时间
- `operator`: 操作人
- `mail_log_id`: 邮件日志ID

### OCR确认日志表 (log_open_fund_reconciliation_ocr)
- `id`: 主键ID
- `reconciliation_id`: 对账单ID
- `file_name`: 文件名称
- `account_set_name`: 账套名称
- `confirm_time`: 确认时间
- `operator`: 操作人
- `confirm_status`: 确认状态

### 删除记录日志表 (log_open_fund_reconciliation_delete)
- `id`: 主键ID
- `reconciliation_id`: 对账单ID
- `file_name`: 文件名称
- `account_set_name`: 账套名称
- `delete_time`: 删除时间
- `operator`: 操作人
- `delete_status`: 删除状态

## API接口

### 1. 获取所有日志
```
GET /open-reconciliation-logs/all?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 2. 获取上传日志
```
GET /open-reconciliation-logs/upload?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 3. 获取下载日志
```
GET /open-reconciliation-logs/download?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 4. 获取邮件发送日志
```
GET /open-reconciliation-logs/mail?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 5. 获取OCR确认日志
```
GET /open-reconciliation-logs/ocr?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 6. 获取删除记录日志
```
GET /open-reconciliation-logs/delete?current=1&size=10&startDate=2024-01-01&endDate=2024-12-31
```

### 7. 通过对账单ID下载文件
```
GET /open-reconciliation-logs/download-by-id?id={reconciliationId}
```

## 实现细节

### 自动日志记录

日志记录已集成到以下业务方法中，完全参照开基确认单的实现方式：

1. **OpenFundReconciliationStatementService.upload()** - 在finally块中记录上传日志，支持成功/失败状态
2. **OpenFundReconciliationStatementService.download()** - 支持logDownload参数控制是否记录日志，记录下载日志
3. **OpenFundReconciliationStatementService.confirm()** - 在方法最后记录OCR确认日志
4. **OpenFundReconciliationStatementService.sendMail()** - 通过Job参数传递操作人和发送方式，由邮件发送Job记录日志
5. **OpenFundReconciliationStatementService.removeAllInfo()** - 先删除相关日志，再记录删除操作日志

### 实现特点

- **参照确认单实现**：完全按照OpenFundConfirmationStatementService的日志嵌入方式实现
- **upload方法**：使用dealAndReturnIds获取创建的对账单ID，在finally块中记录日志
- **download方法**：提供logDownload参数控制是否记录日志，支持单文件和批量下载
- **confirm方法**：在业务逻辑完成后记录OCR确认日志
- **sendMail方法**：通过Job参数传递操作人和发送方式，由邮件发送Job负责记录日志
- **removeAllInfo方法**：先删除相关日志记录，再记录删除操作日志

### 错误处理

- 所有日志记录操作都包含异常处理，确保业务操作不会因日志记录失败而中断
- 操作状态会根据业务操作的成功/失败状态进行记录
- upload方法在异常情况下也会记录失败状态的日志

### 数据清理

- 当删除对账单时，会先自动清理相关的所有日志记录
- 然后记录删除操作日志
- 支持通过`LogOpenFundReconciliationService.deleteLogsByReconciliationId()`方法批量删除日志

## 使用说明

### 1. 数据库初始化
执行 `sql/open_fund_reconciliation_logs.sql` 脚本创建日志表。

### 2. 配置
确保Spring Boot应用能够扫描到新增的组件：
- Controller: `OpenFundReconciliationLogController`
- Service: `LogOpenFundReconciliationService`
- Mapper: 各种日志Mapper接口

### 3. 权限配置
根据需要为日志查看接口配置相应的权限控制。

## 注意事项

1. 日志记录是异步进行的，不会影响主业务流程的性能
2. 所有时间字段都使用系统当前时间
3. 操作人信息通过`SecureUtil.currentUserName()`获取
4. 文件ID使用哈希算法生成，确保唯一性
5. 日志数据建议定期归档，避免表数据过大影响查询性能
