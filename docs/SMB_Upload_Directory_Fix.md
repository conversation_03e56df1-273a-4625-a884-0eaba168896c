# SMB文件上传目录自动创建功能改进

## 问题描述

虽然 `SMBManager.uploadFile` 方法已经包含了自动创建目录的功能，但在实际使用中可能遇到"文件夹不存在"的错误。

## 可能的原因

1. **路径分隔符问题**: 原代码只处理 `\\` 分隔符，不支持 `/` 分隔符
2. **错误处理不足**: 目录创建失败时没有详细的错误信息
3. **权限问题**: SMB用户可能没有创建目录的权限
4. **连接问题**: SMB连接不稳定导致操作失败
5. **路径验证不足**: 没有验证目录是否真的创建成功

## 改进方案

### 1. 增强的 `createDir` 方法

```java
private void createDir(String path) {
    if (path == null || path.trim().isEmpty()) {
        log.warn("创建目录失败: 路径为空");
        return;
    }
    
    try {
        smbClientInfo.reconnect();
        DiskShare share = smbClientInfo.getShare();
        
        // 处理路径分隔符，支持 / 和 \ 两种格式
        String normalizedPath = path.replace("/", "\\");
        List<String> dirs = Arrays.stream(normalizedPath.split("\\\\"))
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
        
        if (dirs.isEmpty()) {
            log.warn("创建目录失败: 解析后的路径为空, 原始路径: {}", path);
            return;
        }
        
        dirs = buildPaths(dirs);
        log.info("准备创建目录层级: {}", dirs);
        
        for (String thisDir : dirs) {
            try {
                if (!share.folderExists(thisDir)) {
                    log.info("创建目录: {}", thisDir);
                    share.mkdir(thisDir);
                    log.info("目录创建成功: {}", thisDir);
                } else {
                    log.debug("目录已存在: {}", thisDir);
                }
            } catch (Exception e) {
                log.error("创建目录失败: {}, 错误信息: {}", thisDir, e.getMessage(), e);
                throw new SMBException("创建目录失败: " + thisDir, e);
            }
        }
        
        log.info("目录创建完成: {}", path);
        
    } catch (Exception e) {
        log.error("创建目录过程中发生异常: {}, 错误信息: {}", path, e.getMessage(), e);
        throw new SMBException("创建目录失败: " + path, e);
    }
}
```

### 2. 增强的 `uploadFile` 方法

```java
public void uploadFile(String path, String fileName, byte[] bytes) {
    if (fileName == null || fileName.trim().isEmpty()) {
        throw new SMBException("文件名不能为空");
    }
    if (bytes == null || bytes.length == 0) {
        throw new SMBException("文件内容不能为空");
    }
    
    try {
        smbClientInfo.reconnect();
        SMBConfig smbConfig = smbClientInfo.getSmbConfig();
        String upload = smbConfig.getUpload();
        DiskShare share = smbClientInfo.getShare();
        String subPath = concatSeparator(upload, path);
        
        log.info("开始上传文件: fileName={}, path={}, subPath={}", fileName, path, subPath);
        
        // 确保目录存在
        createDir(subPath);
        
        String fullPath = concatSeparator(subPath, fileName);
        log.info("文件完整路径: {}", fullPath);
        
        // 验证目录是否真的存在
        if (!share.folderExists(subPath)) {
            throw new SMBException("目录创建失败或不存在: " + subPath);
        }
        
        File file = share.openFile(fullPath, EnumSet.of(AccessMask.GENERIC_WRITE), null, SMB2ShareAccess.ALL, SMB2CreateDisposition.FILE_OVERWRITE_IF, null);
        OutputStream outputStream = file.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
        outputStream.close();
        file.close();
        
        log.info("文件上传成功: {}", fullPath);
        
    } catch (SMBException e) {
        log.error("SMB异常 - 上传文件失败: fileName={}, path={}, 错误信息: {}", fileName, path, e.getMessage(), e);
        throw e;
    } catch (Exception e) {
        log.error("上传文件失败: fileName={}, path={}, 错误信息: {}", fileName, path, e.getMessage(), e);
        throw new SMBException("上传文件失败: " + fileName, e);
    } finally {
        smbClientInfo.closeAll();
    }
}
```

## 改进特性

### 1. 路径兼容性
- **支持多种分隔符**: 自动处理 `/` 和 `\` 两种路径分隔符
- **路径标准化**: 统一转换为Windows风格的路径分隔符

### 2. 详细日志记录
- **操作跟踪**: 记录每个目录的创建过程
- **错误定位**: 精确定位失败的目录层级
- **调试信息**: 提供详细的路径信息用于问题排查

### 3. 增强错误处理
- **参数验证**: 检查文件名和内容是否有效
- **目录验证**: 验证目录是否真的创建成功
- **异常分类**: 区分SMB异常和其他异常
- **资源清理**: 确保文件流正确关闭

### 4. 逐级目录创建
- **层级处理**: 逐级创建目录，确保父目录存在
- **存在检查**: 避免重复创建已存在的目录
- **失败处理**: 单个目录创建失败时提供详细错误信息

## 使用示例

```java
// 支持多种路径格式
smbService.upload("/third_szt/20250115", "文件名.xlsx", fileBytes);
smbService.upload("\\third_szt\\20250115", "文件名.xlsx", fileBytes);
smbService.upload("third_szt/20250115", "文件名.xlsx", fileBytes);
```

## 错误排查

### 1. 查看日志
改进后的代码会输出详细的日志信息：
```
INFO  - 开始上传文件: fileName=投资人A_净值对接_20250115.xlsx, path=/third_szt/20250115, subPath=upload\third_szt\20250115
INFO  - 准备创建目录层级: [third_szt, third_szt\20250115]
INFO  - 创建目录: third_szt
INFO  - 目录创建成功: third_szt
INFO  - 创建目录: third_szt\20250115
INFO  - 目录创建成功: third_szt\20250115
INFO  - 目录创建完成: upload\third_szt\20250115
INFO  - 文件完整路径: upload\third_szt\20250115\投资人A_净值对接_20250115.xlsx
INFO  - 文件上传成功: upload\third_szt\20250115\投资人A_净值对接_20250115.xlsx
```

### 2. 常见错误及解决方案

#### 权限不足
```
ERROR - 创建目录失败: third_szt, 错误信息: Access denied
```
**解决方案**: 检查SMB用户是否有创建目录的权限

#### 连接问题
```
ERROR - 创建目录过程中发生异常: /third_szt/20250115, 错误信息: Connection reset
```
**解决方案**: 检查网络连接和SMB服务器状态

#### 路径问题
```
WARN  - 创建目录失败: 解析后的路径为空, 原始路径: ///
```
**解决方案**: 检查传入的路径格式是否正确

## 测试建议

1. **基本功能测试**: 测试正常的文件上传
2. **目录创建测试**: 测试多级目录的自动创建
3. **路径格式测试**: 测试不同的路径分隔符
4. **错误场景测试**: 测试权限不足、连接断开等异常情况
5. **并发测试**: 测试多个线程同时上传文件的情况
