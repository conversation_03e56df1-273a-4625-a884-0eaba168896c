# SendThirdSZTJob 实现说明

## 概述

`SendThirdSZTJob` 是一个用于处理第三方深证通发送的定时任务，主要功能包括：

1. 使用 `ThirdFileUtil` 生成第三方净值对接文件
2. 将生成的文件上传到远程文件夹
3. 调用RPA处理，将上传完成的路径和配置的SZTPath传给RPA

## 核心组件

### 1. SendThirdSZTJob
- **位置**: `src/main/java/cn/sdata/om/al/job/SendThirdSZTJob.java`
- **功能**: 主要的Job执行类，负责协调整个第三方深证通发送流程

### 2. ThirdSZTHandler
- **位置**: `src/main/java/cn/sdata/om/al/rpa/ThirdSZTHandler.java`
- **功能**: RPA执行结果处理器，负责处理RPA执行完成后的状态更新

## 主要流程

### 1. 任务启动
```java
// 在NetValueDisclosureService中调用
public void sendToSZTThird(@NonNull List<String> productIds, @NonNull String dataDate) {
    // 获取第三方深证通联系人信息
    Map<String, Set<InvestorContacts>> thirdSZTContacts = getThirdSZTContacts(productIds);
    
    // 获取深证通路径配置
    Map<String, String> sztProductPath = getThirdSZTProductPath(thirdSZTContacts);
    
    // 获取第三方净值披露映射
    Map<String, List<String>> thirdNetValueDisclosureMap = getThirdNetValueDisclosure(thirdSZTContacts);
    
    // 启动SendThirdSZTJob
    List<String> jobIds = cronService.getJobIdByClass(SendThirdSZTJob.class);
    JobDataMap jobDataMap = new JobDataMap();
    jobDataMap.put(DATA_DATE, dataDate);
    jobDataMap.put(NET_VALUE_DISCLOSURE_THIRD_MAP, needDeal);
    jobDataMap.put(SZT_PATH, sztProductPath);
    cronService.startJobNow(jobIds, jobDataMap);
}
```

### 2. 文件生成和上传
```java
// 1. 转换数据格式
List<ValuationTableData> valuationTableDataList = convertToValuationTableData(netValueDisclosures);

// 2. 生成Excel文件
byte[] fileBytes = thirdFileUtil.getAttachment(valuationTableDataList, thirdPartyName);

// 3. 上传到远程文件夹
String fileName = generateFileName(thirdPartyName, dataDate);
String remotePath = getRemotePath(dataDate);
smbService.upload(remotePath, fileName, fileBytes);
```

### 3. RPA调用
```java
// 构建RPA参数
Map<String, Object> rpaParams = new HashMap<>();
rpaParams.put("数据日期", dataDate);
rpaParams.put("第三方机构", thirdPartyName);
rpaParams.put("文件路径", uploadPath);
rpaParams.put("深证通路径", sztPath);

// 执行RPA
RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, rpaParams, logId);

// 启动监控
rpaExecuteService.startTimer(rpaExecLog, flow, callbackParams, logId);
```

## 配置要求

### 1. 数据库配置
需要在 `investor_contacts` 表中配置第三方深证通相关信息：
- `method`: 'SZT'
- `handler`: 'THIRD_NET_VALUE'
- `szt_path`: 深证通路径配置

### 2. 模板文件
需要在 `classpath:third/` 目录下放置对应的Excel模板文件：
- 文件命名格式: `{第三方机构名称}净值对接--模板.xlsx`

### 3. RPA流程配置
需要在系统中配置对应的RPA流程，用于处理第三方深证通数据传输。

## 使用示例

### 1. 手动触发
```java
@Autowired
private NetValueDisclosureService netValueDisclosureService;

// 发送第三方深证通
List<String> productIds = Arrays.asList("PROD001", "PROD002");
String dataDate = "2025-01-15";
netValueDisclosureService.sendToSZTThird(productIds, dataDate);
```

### 2. 定时任务配置
在定时任务管理界面配置 `SendThirdSZTJob`，设置执行时间和参数。

## 文件路径规则

### 1. 生成的文件名格式
```
{第三方机构名称}_净值对接_{YYYYMMDD}.xlsx
```

### 2. 远程上传路径格式
```
/third_szt/{YYYYMMDD}/
```

## 状态管理

### 1. 净值披露状态
- `szt_status`: 深证通发送状态
  - `RUNNING`: 执行中
  - `COMPLETED`: 执行完成
  - `FAILED`: 执行失败

### 2. RPA执行日志
系统会自动记录RPA执行日志，包括：
- 执行ID
- 执行状态
- 执行结果
- 错误信息（如有）

## 错误处理

### 1. 参数校验
- 检查第三方净值披露数据是否为空
- 检查深证通路径配置是否为空
- 检查数据日期格式是否正确

### 2. 异常处理
- 文件生成失败时的处理
- 文件上传失败时的处理
- RPA调用失败时的处理

### 3. 状态回滚
当任务执行失败时，会自动更新相关记录的状态为失败状态。

## 监控和日志

### 1. 日志记录
- 任务开始和结束日志
- 每个第三方机构的处理日志
- 文件生成和上传日志
- RPA调用日志

### 2. 状态监控
- 可通过净值披露管理界面查看处理状态
- 可通过RPA执行日志查看详细执行情况

## 注意事项

1. **模板文件**: 确保第三方机构对应的Excel模板文件存在
2. **路径配置**: 确保深证通路径配置正确
3. **权限配置**: 确保有远程文件夹的上传权限
4. **RPA流程**: 确保RPA流程配置正确且可用
5. **数据完整性**: 确保净值数据完整且准确

## 扩展说明

如需添加新的第三方机构支持：

1. 在 `investor_contacts` 表中添加相应配置
2. 在 `classpath:third/` 目录下添加对应的模板文件
3. 根据需要调整文件生成逻辑
4. 配置对应的RPA流程
