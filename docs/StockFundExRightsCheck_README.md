# 股票和开基除权价格检查功能开发文档

## 功能概述

股票和开基除权价格检查功能用于校验估值系统中的除权流水、红股流水、红利发放流水是否与聚源数据中的除权信息一致。

## 业务逻辑

### 核心校验规则

对于除权流水、红股流水、红利发放流水，均采用以下统一的校验逻辑：

1. **正常状态**：
   - 有流水且今日是目标日期（除权日/红股发放日/红利发放日）
   - 无流水且不是目标日期

2. **流水日期异常**：
   - 有流水但当日不是目标日期

3. **业务流水缺失**：
   - 没有流水但是目标日期

### 数据来源

- **估值系统**：证券持仓数据（账套名称、产品代码、证券类型、证券名称、证券代码）
- **聚源数据**：除权信息（登记日、除权日、红股发放日、红利发放日、红股分红比例、现金分红比例）

## 技术架构

### 数据库设计

#### 主表：audit_stock_fund_ex_rights_check
- 存储检查结果和相关信息
- 使用枚举值存储检查状态（0-正常，1-流水日期异常，2-业务流水缺失）

#### 聚源数据表：juyuan_ex_rights_data
- 存储从聚源获取的除权数据
- 支持按日期同步和查询

### 核心组件

#### 实体类
- `StockFundExRightsCheck`：主检查结果实体
- `JuyuanExRightsData`：聚源除权数据实体
- `FlowCheckStatus`：检查状态枚举

#### 服务层
- `StockFundExRightsCheckService`：主业务服务
- `JuyuanExRightsDataService`：聚源数据服务
- `JuyuanDataSyncService`：聚源数据同步服务

#### 工具类
- `ExRightsCheckRuleUtil`：业务规则校验工具类

#### 控制器
- `StockFundExRightsCheckController`：REST API接口

#### 定时任务
- `StockFundExRightsCheckJob`：自动执行检查的定时任务

## API接口

### 主要接口

1. **分页查询**
   ```
   POST /audit/stock-fund-ex-rights-check/page
   ```

2. **手动执行检查**
   ```
   POST /audit/stock-fund-ex-rights-check/execute?dataDate=2025-08-09
   ```

3. **同步聚源数据**
   ```
   POST /audit/stock-fund-ex-rights-check/sync-juyuan-data?dataDate=2025-08-09
   ```

4. **获取统计数据**
   ```
   GET /audit/stock-fund-ex-rights-check/summary-statistics?dataDate=2025-08-09
   GET /audit/stock-fund-ex-rights-check/abnormal-statistics?dataDate=2025-08-09
   ```

5. **重新检查**
   ```
   POST /audit/stock-fund-ex-rights-check/recheck/{id}
   POST /audit/stock-fund-ex-rights-check/batch-recheck?dataDate=2025-08-09
   ```

## 部署说明

### 数据库初始化

1. 执行SQL脚本：`sql/stock_and_fund_ex_rights_check.sql`
2. 创建相关表结构

### 配置说明

无需额外配置，使用现有的数据源配置：
- `valuation`：估值系统数据源
- `master`：主数据库

### 聚源数据集成

目前聚源数据集成部分提供了接口框架，需要根据实际的聚源API或数据文件格式进行具体实现：

1. 在 `JuyuanDataSyncServiceImpl.fetchExRightsDataFromApi()` 中实现API调用
2. 在 `JuyuanDataSyncServiceImpl.fetchExRightsDataFromFile()` 中实现文件解析
3. 在 `JuyuanDataSyncServiceImpl.convertRawData()` 中实现数据转换

## 使用说明

### 手动执行

1. 通过API接口手动触发检查
2. 指定数据日期进行检查

### 自动执行

1. 配置定时任务 `StockFundExRightsCheckJob`
2. 设置执行时间和频率

### 结果查看

1. 通过分页查询接口查看检查结果
2. 通过统计接口查看汇总信息
3. 筛选异常数据进行处理

## 扩展说明

### 添加新的检查规则

1. 在 `ExRightsCheckRuleUtil` 中添加新的规则方法
2. 在 `StockFundExRightsCheckServiceImpl` 中调用新规则
3. 更新数据库表结构（如需要）

### 集成其他数据源

1. 创建新的数据同步服务
2. 实现数据获取和转换逻辑
3. 在主服务中调用新的数据源

## 注意事项

1. 聚源数据集成需要根据实际情况实现
2. 估值系统表名和字段名需要根据实际情况调整
3. 定时任务执行时间需要根据业务需求配置
4. 异常数据需要及时处理和跟进

## 开发完成情况

✅ 数据库设计和建表  
✅ 实体类和DTO设计  
✅ Mapper层开发  
✅ Service层业务逻辑开发  
✅ Controller层接口开发  
✅ 定时任务开发  
✅ 聚源数据集成框架  
✅ 估值系统流水查询  
✅ 业务规则校验实现  

所有核心功能已完成开发，可以进行测试和部署。
