# 补仓资金管理接口文档

## 基础信息
- **模块名称**: 补仓资金管理
- **基础路径**: `/audit/supplementary-funding`
- **实体类**: `cn.sdata.om.al.audit.entity.SupplementaryFunding`
- **Service类**: `cn.sdata.om.al.audit.service.SupplementaryFundingService`
- **Controller类**: `cn.sdata.om.al.audit.controller.SupplementaryFundingController`

## 架构设计
本模块采用标准的三层架构设计：
- **Controller层**: 负责接收HTTP请求和返回响应，不包含业务逻辑
- **Service层**: 包含所有业务逻辑、参数校验、异常处理
- **Mapper层**: 负责数据库操作

## 数据库表结构
```sql
CREATE TABLE `audit_supplementary_funding` (
  `id` varchar(30) NOT NULL COMMENT 'ID',
  `product_id` varchar(30) DEFAULT NULL COMMENT '账套编号',
  `product_name` varchar(200) DEFAULT NULL COMMENT '账套名称',
  `data_date` varchar(10) DEFAULT NULL COMMENT '数据日期',
  `amount` decimal(32,4) DEFAULT NULL COMMENT '金额',
  `transaction_type` varchar(50) DEFAULT NULL COMMENT '流水类型',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_data_date` (`data_date`),
  KEY `idx_transaction_type` (`transaction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='补仓资金表';
```

## API接口列表

### 1. 分页查询补仓资金
- **接口地址**: `POST /audit/supplementary-funding/page`
- **请求参数**: 
```json
{
  "current": 1,
  "size": 20,
  "param": {
    "productId": "账套编号",
    "productName": "账套名称",
    "dataDate": "数据日期",
    "transactionType": "流水类型"
  },
  "like": ["productName"],
  "orderColumn": {
    "createTime": "desc"
  }
}
```
- **返回结果**: 
```json
{
  "code": 200,
  "message": null,
  "result": {
    "records": [...],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  }
}
```

### 2. 新增补仓资金
- **接口地址**: `POST /audit/supplementary-funding/save`
- **请求参数**: 
```json
{
  "productId": "账套编号",
  "productName": "账套名称",
  "dataDate": "2025-01-15",
  "amount": 1000000.00,
  "transactionType": "补仓",
  "createUser": "创建人",
  "updateUser": "更新人"
}
```
- **返回结果**: 
```json
{
  "code": 200,
  "message": "新增成功",
  "result": null
}
```

### 3. 更新补仓资金
- **接口地址**: `POST /audit/supplementary-funding/update`
- **请求参数**: 
```json
{
  "id": "记录ID",
  "productId": "账套编号",
  "productName": "账套名称",
  "dataDate": "2025-01-15",
  "amount": 1000000.00,
  "transactionType": "补仓",
  "updateUser": "更新人"
}
```
- **返回结果**: 
```json
{
  "code": 200,
  "message": "更新成功",
  "result": null
}
```

### 4. 根据ID查询补仓资金
- **接口地址**: `GET /audit/supplementary-funding/get?id=记录ID`
- **请求参数**: 查询参数 `id`
- **返回结果**: 
```json
{
  "code": 200,
  "message": null,
  "result": {
    "id": "记录ID",
    "productId": "账套编号",
    "productName": "账套名称",
    "dataDate": "2025-01-15",
    "amount": 1000000.00,
    "transactionType": "补仓",
    "createUser": "创建人",
    "createTime": "2025-01-15 10:00:00",
    "updateUser": "更新人",
    "updateTime": "2025-01-15 10:00:00"
  }
}
```

### 5. 删除补仓资金（支持多选）
- **接口地址**: `DELETE /audit/supplementary-funding/delete`
- **请求参数**: 
```json
["id1", "id2", "id3"]
```
- **返回结果**: 
```json
{
  "code": 200,
  "message": "删除成功",
  "result": null
}
```

### 6. 根据数据日期统计补仓资金数量
- **接口地址**: `GET /audit/supplementary-funding/count?dataDate=2025-01-15`
- **请求参数**: 查询参数 `dataDate`
- **返回结果**: 
```json
{
  "code": 200,
  "message": null,
  "result": 10
}
```

### 7. 根据账套编号查询补仓资金列表
- **接口地址**: `GET /audit/supplementary-funding/list/by-product-id?productId=PROD001`
- **请求参数**: 查询参数 `productId`
- **返回结果**:
```json
{
  "code": 200,
  "message": null,
  "result": [
    {
      "id": "记录ID",
      "productId": "PROD001",
      "productName": "账套名称",
      "dataDate": "2025-01-15",
      "amount": 1000000.00,
      "transactionType": "补仓",
      "createUser": "创建人",
      "createTime": "2025-01-15 10:00:00",
      "updateUser": "更新人",
      "updateTime": "2025-01-15 10:00:00"
    }
  ]
}
```

### 8. 根据流水日期、账套名称、流水类型计算金额总额
- **接口地址**: `GET /audit/supplementary-funding/calculate/amount-sum?dataDate=2025-01-15&productName=测试账套&transactionType=存入`
- **请求参数**:
  - `dataDate`: 流水日期，格式：yyyy-MM-dd
  - `productName`: 账套名称
  - `transactionType`: 流水类型
- **业务规则**:
  - 流水类型为"存入"时，金额为正数
  - 流水类型为"提取"时，金额为负数
  - 其他类型按原值计算
- **返回结果**:
```json
{
  "code": 200,
  "message": null,
  "result": 5000000.00
}
```

### 9. 根据流水日期和账套名称计算所有流水类型的金额总额
- **接口地址**: `GET /audit/supplementary-funding/calculate/total-amount?dataDate=2025-01-15&productName=测试账套`
- **请求参数**:
  - `dataDate`: 流水日期，格式：yyyy-MM-dd
  - `productName`: 账套名称
- **业务规则**:
  - 存入为正数，提取为负数，其他类型按原值计算
  - 计算该日期该账套下所有流水的净额
- **返回结果**:
```json
{
  "code": 200,
  "message": null,
  "result": 3000000.00
}
```

## 字段说明
- **id**: 主键ID，由MyBatis-Plus自动生成
- **productId**: 账套编号，varchar(30)
- **productName**: 账套名称，varchar(200)
- **dataDate**: 数据日期，varchar(10)，格式：yyyy-MM-dd
- **amount**: 金额，decimal(32,4)
- **transactionType**: 流水类型，varchar(50)
- **createUser**: 创建人，varchar(100)
- **createTime**: 创建时间，datetime
- **updateUser**: 更新人，varchar(100)
- **updateTime**: 更新时间，datetime

## 错误码说明
- **200**: 操作成功
- **500**: 操作失败，具体错误信息在message字段中

## Service层业务方法
- `pageQuery()`: 分页查询，包含参数校验和异常处理
- `saveSupplementaryFunding()`: 新增补仓资金，包含完整的参数校验
- `updateSupplementaryFunding()`: 更新补仓资金，包含存在性检查
- `getSupplementaryFundingById()`: 根据ID查询，包含参数校验
- `deleteSupplementaryFunding()`: 批量删除，包含ID有效性检查
- `getCountByDate()`: 按日期统计，包含参数校验
- `getListByProductId()`: 按账套编号查询，包含参数校验
- `calculateAmountSum()`: 根据条件计算特定流水类型的金额总额
- `calculateTotalAmountByDateAndProduct()`: 计算指定日期和账套的所有流水净额

## 注意事项
1. 所有时间字段格式为：`yyyy-MM-dd HH:mm:ss`
2. 分页查询支持模糊查询和排序
3. 删除操作支持批量删除
4. 新增时ID会自动生成，无需传入
5. 更新时必须传入ID字段
6. 所有业务逻辑和参数校验都在Service层完成
7. Controller层只负责接收请求和调用Service方法
8. Service层方法统一返回R<T>类型，包含完整的错误处理
