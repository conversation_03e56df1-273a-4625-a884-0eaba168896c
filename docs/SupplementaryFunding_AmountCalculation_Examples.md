# 补仓资金金额计算接口使用示例

## 业务场景说明

补仓资金管理系统需要根据流水记录计算金额总额，其中：
- **存入**：表示资金流入，金额为正数
- **提取**：表示资金流出，金额为负数  
- **其他类型**：按原值计算

## 示例数据

假设数据库中有以下补仓资金记录：

| ID | 账套名称 | 流水日期 | 金额 | 流水类型 |
|----|----------|----------|------|----------|
| 1  | 测试账套A | 2025-01-15 | 1000000.00 | 存入 |
| 2  | 测试账套A | 2025-01-15 | 500000.00  | 存入 |
| 3  | 测试账套A | 2025-01-15 | 200000.00  | 提取 |
| 4  | 测试账套A | 2025-01-15 | 100000.00  | 补仓 |
| 5  | 测试账套B | 2025-01-15 | 800000.00  | 存入 |

## 接口使用示例

### 1. 计算特定流水类型的金额总额

**场景**：计算2025-01-15日测试账套A的所有"存入"流水总额

**请求**：
```http
GET /audit/supplementary-funding/calculate/amount-sum?dataDate=2025-01-15&productName=测试账套A&transactionType=存入
```

**计算逻辑**：
- 查找符合条件的记录：ID=1(1000000.00) + ID=2(500000.00)
- 因为是"存入"类型，金额为正数
- 总额 = 1000000.00 + 500000.00 = 1500000.00

**返回结果**：
```json
{
  "code": 200,
  "message": null,
  "result": 1500000.00
}
```

### 2. 计算提取类型的金额总额

**场景**：计算2025-01-15日测试账套A的所有"提取"流水总额

**请求**：
```http
GET /audit/supplementary-funding/calculate/amount-sum?dataDate=2025-01-15&productName=测试账套A&transactionType=提取
```

**计算逻辑**：
- 查找符合条件的记录：ID=3(200000.00)
- 因为是"提取"类型，金额为负数
- 总额 = -200000.00

**返回结果**：
```json
{
  "code": 200,
  "message": null,
  "result": -200000.00
}
```

### 3. 计算所有流水类型的净额

**场景**：计算2025-01-15日测试账套A的所有流水净额

**请求**：
```http
GET /audit/supplementary-funding/calculate/total-amount?dataDate=2025-01-15&productName=测试账套A
```

**计算逻辑**：
- 存入流水：ID=1(1000000.00) + ID=2(500000.00) = +1500000.00
- 提取流水：ID=3(200000.00) = -200000.00
- 其他流水：ID=4(100000.00，补仓类型) = +100000.00
- 净额 = 1500000.00 - 200000.00 + 100000.00 = 1400000.00

**返回结果**：
```json
{
  "code": 200,
  "message": null,
  "result": 1400000.00
}
```

### 4. 无数据情况

**场景**：查询不存在的数据

**请求**：
```http
GET /audit/supplementary-funding/calculate/amount-sum?dataDate=2025-01-16&productName=不存在的账套&transactionType=存入
```

**返回结果**：
```json
{
  "code": 200,
  "message": null,
  "result": 0.00
}
```

## 错误处理示例

### 1. 参数缺失

**请求**：
```http
GET /audit/supplementary-funding/calculate/amount-sum?dataDate=2025-01-15&productName=
```

**返回结果**：
```json
{
  "code": 500,
  "message": "账套名称不能为空",
  "result": null
}
```

### 2. 参数为空

**请求**：
```http
GET /audit/supplementary-funding/calculate/total-amount?dataDate=&productName=测试账套A
```

**返回结果**：
```json
{
  "code": 500,
  "message": "流水日期不能为空",
  "result": null
}
```

## 业务应用场景

1. **日终对账**：计算每日各账套的资金净流入/流出
2. **资金监控**：实时监控账套资金变动情况
3. **报表统计**：生成资金流水汇总报表
4. **风险控制**：监控大额资金流动
5. **审计追踪**：提供资金流水的审计数据

## 注意事项

1. **金额精度**：使用BigDecimal确保计算精度
2. **流水类型**：严格按照"存入"、"提取"进行正负号处理
3. **参数校验**：所有参数都进行非空校验
4. **异常处理**：完善的异常捕获和错误信息返回
5. **性能考虑**：大数据量时建议添加分页或时间范围限制
