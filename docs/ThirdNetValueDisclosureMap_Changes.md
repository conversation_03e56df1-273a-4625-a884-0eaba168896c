# thirdNetValueDisclosureMap 相关修改总结

## 修改概述

将 `getThirdNetValueDisclosure` 方法的返回结构从 `key=productId, value=List<ProductId>` 改为 `key=investor, value=List<ProductId>`，并相应地更新了所有涉及 `thirdNetValueDisclosureMap` 的代码。

## 修改的文件列表

### 1. NetValueDisclosureService.java
- **修改方法**: `getThirdNetValueDisclosure`
- **修改内容**: 
  - 改变返回的Map结构，按投资人分组而不是按产品ID
  - 更新相关注释和日志信息

### 2. SendThirdSZTJob.java
- **修改内容**:
  - 更新所有变量名从 `thirdPartyName` 改为 `investorName`
  - 更新注释和日志信息以反映按投资人处理的逻辑
  - 更新方法名 `getSztPathForThirdParty` 改为 `getSztPathForInvestor`
  - 优化RPA调用参数，添加投资人信息到回调参数中



## 详细修改内容

### NetValueDisclosureService.java

#### 1. getThirdNetValueDisclosure 方法
```java
// 修改前
private Map<String, List<String>> getThirdNetValueDisclosure(Map<String, Set<InvestorContacts>> thirdSZTContacts){
    return thirdSZTContacts.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
        Set<InvestorContacts> investorContacts = entry.getValue();
        return investorContacts.stream().map(InvestorContacts::getProductId).collect(Collectors.toList());
    }));
}

// 修改后
private Map<String, List<String>> getThirdNetValueDisclosure(Map<String, Set<InvestorContacts>> thirdSZTContacts){
    Map<String, List<String>> result = new HashMap<>();
    for (Map.Entry<String, Set<InvestorContacts>> entry : thirdSZTContacts.entrySet()) {
        Set<InvestorContacts> investorContacts = entry.getValue();
        for (InvestorContacts contact : investorContacts) {
            String investor = contact.getInvestor();
            String productId = contact.getProductId();
            
            result.computeIfAbsent(investor, k -> new ArrayList<>()).add(productId);
        }
    }
    return result;
}
```

#### 2. sendToSZTThird 方法
- 更新注释说明现在是按投资人分组
- 添加日志记录投资人列表信息

### SendThirdSZTJob.java

#### 主要变更
1. **变量名更新**:
   - `thirdPartyName` → `investorName`
   - `getSztPathForThirdParty` → `getSztPathForInvestor`

2. **注释更新**:
   - "第三方机构" → "投资人"
   - "第三方净值披露数据映射" → "投资人净值披露数据映射"

3. **日志信息更新**:
   - 所有日志中的"第三方机构"改为"投资人"

#### 核心处理逻辑
```java
// 遍历每个投资人
for (Map.Entry<String, List<NetValueDisclosure>> entry : netValueDisclosureThirdMap.entrySet()) {
    String investorName = entry.getKey();  // 现在是投资人名称
    List<NetValueDisclosure> netValueDisclosures = entry.getValue();
    
    // 为该投资人生成文件
    byte[] fileBytes = thirdFileUtil.getAttachment(valuationTableDataList, investorName);
    String fileName = generateFileName(investorName, dataDate);
    
    // 上传并调用RPA
    // ...
}
```

## 数据流变化

### 修改前的数据流
```
thirdSZTContacts (产品ID -> 投资人联系信息)
    ↓
thirdNetValueDisclosureMap (产品ID -> [产品ID])
    ↓
needDeal (产品ID -> 净值披露数据)
    ↓
按产品ID处理，每个产品生成一个文件
```

### 修改后的数据流
```
thirdSZTContacts (产品ID -> 投资人联系信息)
    ↓
thirdNetValueDisclosureMap (投资人 -> [产品ID列表])
    ↓
needDeal (投资人 -> 净值披露数据)
    ↓
按投资人处理，每个投资人生成一个包含其所有产品的文件
```

## 业务影响

### 1. 文件生成
- **修改前**: 每个产品生成一个文件
- **修改后**: 每个投资人生成一个文件，包含其所有产品数据

### 2. 文件命名
- **修改前**: `{产品ID}_净值对接_{日期}.xlsx`
- **修改后**: `{投资人名称}_净值对接_{日期}.xlsx`

### 3. RPA处理
- **修改前**: 按产品维度调用RPA
- **修改后**: 按投资人维度调用RPA

### 4. 优势
1. **减少文件数量**: 一个投资人一个文件，而不是一个产品一个文件
2. **提高处理效率**: 减少RPA调用次数
3. **便于管理**: 投资人只需下载一个文件即可获得所有产品信息
4. **符合业务逻辑**: 按投资人维度处理更符合实际业务场景

## 配置要求

### 1. 数据库配置
确保 `investor_contacts` 表中的数据正确配置：
- `investor`: 投资人名称
- `product_id`: 产品ID
- `method`: 'SZT'
- `handler`: 'THIRD_NET_VALUE'

### 2. 模板文件
Excel模板需要支持多产品数据展示，模板文件路径：
`classpath:third/{投资人名称}净值对接--模板.xlsx`

## 测试建议

1. **单投资人单产品**: 验证基本功能
2. **单投资人多产品**: 验证数据聚合和文件生成
3. **多投资人多产品**: 验证分组逻辑
4. **边界情况**: 投资人名称特殊字符、产品数据缺失等

## 注意事项

1. **向后兼容性**: 此修改改变了数据处理逻辑，需要确保相关配置和模板文件已更新
2. **文件大小**: 对于拥有大量产品的投资人，需要考虑文件大小限制
3. **错误处理**: 需要处理某个投资人的部分产品数据缺失的情况
4. **性能考虑**: 大量产品数据的聚合可能影响性能，需要监控执行时间
