# getThirdNetValueDisclosure 方法修改说明

## 修改内容

将 `getThirdNetValueDisclosure` 方法的返回结构从 `key=productId, value=List<ProductId>` 改为 `key=investor, value=List<ProductId>`。

## 修改前后对比

### 修改前
```java
private Map<String, List<String>> getThirdNetValueDisclosure(Map<String, Set<InvestorContacts>> thirdSZTContacts){
    return thirdSZTContacts.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
        Set<InvestorContacts> investorContacts = entry.getValue();
        return investorContacts.stream().map(InvestorContacts::getProductId).collect(Collectors.toList());
    }));
}
```

**返回结果示例**:
```json
{
  "PROD001": ["PROD001"],
  "PROD002": ["PROD002"],
  "PROD003": ["PROD003"]
}
```

### 修改后
```java
private Map<String, List<String>> getThirdNetValueDisclosure(Map<String, Set<InvestorContacts>> thirdSZTContacts){
    Map<String, List<String>> result = new HashMap<>();
    for (Map.Entry<String, Set<InvestorContacts>> entry : thirdSZTContacts.entrySet()) {
        Set<InvestorContacts> investorContacts = entry.getValue();
        for (InvestorContacts contact : investorContacts) {
            String investor = contact.getInvestor();
            String productId = contact.getProductId();
            
            result.computeIfAbsent(investor, k -> new ArrayList<>()).add(productId);
        }
    }
    return result;
}
```

**返回结果示例**:
```json
{
  "投资人A": ["PROD001", "PROD002"],
  "投资人B": ["PROD003", "PROD004"],
  "投资人C": ["PROD005"]
}
```

## 数据流示例

### 输入数据 (thirdSZTContacts)
假设有以下投资人联系信息：

```java
Map<String, Set<InvestorContacts>> thirdSZTContacts = {
    "PROD001": [
        InvestorContacts{investor="投资人A", productId="PROD001", ...},
        InvestorContacts{investor="投资人B", productId="PROD001", ...}
    ],
    "PROD002": [
        InvestorContacts{investor="投资人A", productId="PROD002", ...}
    ],
    "PROD003": [
        InvestorContacts{investor="投资人C", productId="PROD003", ...}
    ]
}
```

### 处理逻辑
1. 遍历每个产品ID及其对应的投资人联系信息集合
2. 对于每个投资人联系信息，提取投资人名称和产品ID
3. 按投资人名称分组，将产品ID添加到对应投资人的产品列表中

### 输出结果
```java
Map<String, List<String>> result = {
    "投资人A": ["PROD001", "PROD002"],
    "投资人B": ["PROD001"],
    "投资人C": ["PROD003"]
}
```

## 影响范围

### 1. SendThirdSZTJob 的调用
修改后，`SendThirdSZTJob` 中的处理逻辑会按投资人分组处理：

```java
// 在 sendToSZTThird 方法中
Map<String, List<String>> thirdNetValueDisclosureMap = getThirdNetValueDisclosure(thirdSZTContacts);

// 获取需要处理的数据 - 现在按投资人分组
Map<String, List<NetValueDisclosure>> needDeal = thirdNetValueDisclosureMap.entrySet()
    .stream()
    .collect(Collectors.toMap(
        Map.Entry::getKey,  // 投资人名称
        entry -> getData(entry.getValue(), dataDate)  // 该投资人的产品净值披露数据
    ));
```

### 2. 文件生成逻辑
现在文件会按投资人生成，而不是按产品：

```java
// 在 SendThirdSZTJob.doSendThirdSZT 方法中
for (Map.Entry<String, List<NetValueDisclosure>> entry : netValueDisclosureThirdMap.entrySet()) {
    String investorName = entry.getKey();  // 现在是投资人名称
    List<NetValueDisclosure> netValueDisclosures = entry.getValue();
    
    // 为该投资人生成文件
    String fileName = generateFileName(investorName, dataDate);
    // ...
}
```

## 业务优势

### 1. 更符合业务逻辑
- 按投资人分组更符合实际业务场景
- 每个投资人可以获得包含其所有产品的综合报告

### 2. 文件管理优化
- 减少文件数量：一个投资人一个文件，而不是一个产品一个文件
- 便于投资人管理：投资人只需要下载一个文件即可获得所有产品信息

### 3. RPA处理优化
- 减少RPA调用次数
- 提高处理效率

## 注意事项

1. **数据完整性**: 确保每个投资人的所有产品数据都正确包含在结果中
2. **文件模板**: 可能需要调整Excel模板以支持多产品数据展示
3. **错误处理**: 需要考虑某个投资人的部分产品数据缺失的情况
4. **性能考虑**: 对于拥有大量产品的投资人，需要考虑文件大小和生成时间

## 测试建议

1. **单投资人单产品**: 验证基本功能
2. **单投资人多产品**: 验证数据聚合正确性
3. **多投资人多产品**: 验证分组逻辑正确性
4. **边界情况**: 投资人名称为空、产品ID重复等情况
