@echo off
echo Testing Mock RPA Server Debug...
echo.

echo 1. Testing health check...
curl -v http://localhost:8080/health
echo.
echo.

echo 2. Testing CallFunc.aom with GET...
curl -v http://localhost:8080/CallFunc.aom
echo.
echo.

echo 3. Testing CallFunc.aom with POST (empty body)...
curl -v -X POST http://localhost:8080/CallFunc.aom
echo.
echo.

echo 4. Testing CallFunc.aom with POST (simple JSON)...
curl -v -X POST http://localhost:8080/CallFunc.aom ^
  -H "Content-Type: application/json" ^
  -d "[]"
echo.
echo.

echo 5. Testing CallFunc.aom with POST (RPA request)...
curl -v -X POST http://localhost:8080/CallFunc.aom ^
  -H "Content-Type: application/json" ^
  -d "[{\"Type\":4,\"Name\":\"test\",\"Value\":\"test\"}]"
echo.
echo.

echo Debug testing completed!
pause
