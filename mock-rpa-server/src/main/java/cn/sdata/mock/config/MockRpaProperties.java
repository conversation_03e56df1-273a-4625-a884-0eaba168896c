package cn.sdata.mock.config;

import cn.sdata.mock.model.FlowConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Mock RPA 配置属性
 * 
 * <AUTHOR> RPA Server
 */
@Component
@ConfigurationProperties(prefix = "mock.rpa")
public class MockRpaProperties {
    
    private List<FlowConfig> flowPaths;
    private Execution execution;
    
    public List<FlowConfig> getFlowPaths() {
        return flowPaths;
    }
    
    public void setFlowPaths(List<FlowConfig> flowPaths) {
        this.flowPaths = flowPaths;
    }
    
    public Execution getExecution() {
        return execution;
    }
    
    public void setExecution(Execution execution) {
        this.execution = execution;
    }
    
    public static class Execution {
        private int executingCount = 9;
        private String successMessage = "执行成功";
        
        public int getExecutingCount() {
            return executingCount;
        }
        
        public void setExecutingCount(int executingCount) {
            this.executingCount = executingCount;
        }
        
        public String getSuccessMessage() {
            return successMessage;
        }
        
        public void setSuccessMessage(String successMessage) {
            this.successMessage = successMessage;
        }
    }
}
