package cn.sdata.mock.controller;

import cn.sdata.mock.model.RpaRequest;
import cn.sdata.mock.model.RpaResponse;
import cn.sdata.mock.service.RpaMockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * RPA Mock 控制器
 * 
 * <AUTHOR> RPA Server
 */
@RestController
public class RpaMockController {
    
    @Autowired
    private RpaMockService rpaMockService;
    
    /**
     * 模拟RPA接口调用
     * 接口路径: /CallFunc.aom
     * 请求方式: POST
     * 请求格式: JSON数组
     * 响应格式: JSON数组
     */
    @PostMapping(value = "/CallFunc.aom")
    public List<RpaResponse> callFunc(@RequestBody List<RpaRequest> requests) {

        System.out.println("=== Mock RPA Server 收到请求 ===");
        System.out.println("请求参数: " + requests);

        List<RpaResponse> responses = rpaMockService.processRequest(requests);

        System.out.println("响应结果: " + responses);
        System.out.println("=== 请求处理完成 ===");

        return responses;
    }

    /**
     * 处理form-data格式的请求（hutool HttpUtil.post默认格式）
     */
    @PostMapping(value = "/CallFunc.aom", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public List<RpaResponse> callFuncForm(HttpServletRequest request, @RequestBody String body) {

        System.out.println("=== Mock RPA Server 收到Form请求 ===");
        System.out.println("请求方法: " + request.getMethod());
        System.out.println("Content-Type: " + request.getContentType());
        System.out.println("原始请求体: " + body);

        try {
            // hutool的HttpUtil.post会将JSON字符串作为form data发送
            // 需要解析form data格式
            String jsonStr = body;
            if (body.contains("=")) {
                // 如果是form格式，提取JSON部分
                String[] parts = body.split("=", 2);
                if (parts.length > 1) {
                    jsonStr = java.net.URLDecoder.decode(parts[1], "UTF-8");
                }
            }

            System.out.println("解析后的JSON: " + jsonStr);

            // 手动解析JSON为RpaRequest列表
            List<RpaRequest> requests = rpaMockService.parseJsonToRequests(jsonStr);
            List<RpaResponse> responses = rpaMockService.processRequest(requests);

            System.out.println("响应结果: " + responses);
            System.out.println("=== 请求处理完成 ===");

            return responses;

        } catch (Exception e) {
            System.out.println("处理Form请求时出错: " + e.getMessage());
            e.printStackTrace();
            return rpaMockService.createErrorResponse("处理请求失败: " + e.getMessage());
        }
    }

    /**
     * 通用的CallFunc接口（处理所有其他请求）
     */
    @RequestMapping(value = "/CallFunc.aom")
    public Object callFuncGeneral(HttpServletRequest request, @RequestBody(required = false) Object body) {

        System.out.println("=== Mock RPA Server 收到通用请求 ===");
        System.out.println("请求方法: " + request.getMethod());
        System.out.println("Content-Type: " + request.getContentType());
        System.out.println("请求体: " + body);
        System.out.println("请求体类型: " + (body != null ? body.getClass() : "null"));

        // 如果是POST请求且有请求体，尝试处理
        if ("POST".equals(request.getMethod()) && body != null) {
            try {
                if (body instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<RpaRequest> requests = (List<RpaRequest>) body;
                    List<RpaResponse> responses = rpaMockService.processRequest(requests);
                    System.out.println("响应结果: " + responses);
                    return responses;
                }
            } catch (Exception e) {
                System.out.println("处理请求时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return "Mock RPA Server is working! Method: " + request.getMethod() + ", Body: " + body;
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public String health() {
        return "Mock RPA Server is running!";
    }
    
    /**
     * 获取服务信息
     */
    @GetMapping("/info")
    public String info() {
        return "Mock RPA Server v1.0.0 - 模拟RPA服务器，支持流程启动和状态查询";
    }
}
