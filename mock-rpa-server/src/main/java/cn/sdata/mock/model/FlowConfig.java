package cn.sdata.mock.model;

/**
 * 流程配置模型
 * 
 * <AUTHOR> RPA Server
 */
public class FlowConfig {
    
    private String path;
    private String flowId;
    private String name;
    
    public FlowConfig() {
    }
    
    public FlowConfig(String path, String flowId, String name) {
        this.path = path;
        this.flowId = flowId;
        this.name = name;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getFlowId() {
        return flowId;
    }
    
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String toString() {
        return "FlowConfig{" +
                "path='" + path + '\'' +
                ", flowId='" + flowId + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
