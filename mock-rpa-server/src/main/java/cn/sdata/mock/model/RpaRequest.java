package cn.sdata.mock.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * RPA请求参数模型
 * 
 * <AUTHOR> RPA Server
 */
public class RpaRequest {
    
    @JsonProperty("Type")
    private Integer type;
    
    @JsonProperty("Name")
    private String name;
    
    @JsonProperty("Value")
    private Object value;
    
    public RpaRequest() {
    }
    
    public RpaRequest(Integer type, String name, Object value) {
        this.type = type;
        this.name = name;
        this.value = value;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    @Override
    public String toString() {
        return "RpaRequest{" +
                "type=" + type +
                ", name='" + name + '\'' +
                ", value=" + value +
                '}';
    }
}
