server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: mock-rpa-server

logging:
  level:
    cn.sdata.mock: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Mock RPA 配置
mock:
  rpa:
    # 支持的流程路径配置
    flow-paths:
      - path: "安联资管_财务运营部\\安联资管_财务运营部_估值系统T0T1估值表\\财务运营部_估值系统估值系统T0T1估值表"
        flowId: "56CA11C83CEF473A87E25EA991C623CE"
        name: "T0T1估值表"
      - path: "安联资管_财务运营部\\安联资管_财务运营部_估值系统会计科目余额表\\财务运营部_估值系统会计科目余额表"
        flowId: "4A4F1BD4533F4745AA522407DEF4914A"
        name: "会计科目余额表"
    # 执行状态配置
    execution:
      # 前几次返回正在执行状态
      executing-count: 9
      # 执行成功后的结果消息
      success-message: "执行成功"
