@echo off
echo Starting Mock RPA Server...
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java 8 or higher
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: <PERSON>ven is not installed or not in PATH
    echo Trying to run with pre-built jar...
    if exist target\mock-rpa-server-1.0.0.jar (
        java -jar target\mock-rpa-server-1.0.0.jar
    ) else (
        echo Error: No pre-built jar found and Maven is not available
        echo Please install Maven or build the project manually
        pause
        exit /b 1
    )
) else (
    echo Building and starting the application...
    mvn clean package -DskipTests
    if %errorlevel% equ 0 (
        java -jar target\mock-rpa-server-1.0.0.jar
    ) else (
        echo Build failed!
        pause
        exit /b 1
    )
)

pause
