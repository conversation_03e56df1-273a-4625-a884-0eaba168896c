#!/bin/bash

echo "Starting Mock RPA Server..."
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 8 or higher"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "Warning: <PERSON>ven is not installed or not in PATH"
    echo "Trying to run with pre-built jar..."
    if [ -f "target/mock-rpa-server-1.0.0.jar" ]; then
        java -jar target/mock-rpa-server-1.0.0.jar
    else
        echo "Error: No pre-built jar found and <PERSON><PERSON> is not available"
        echo "Please install <PERSON><PERSON> or build the project manually"
        exit 1
    fi
else
    echo "Building and starting the application..."
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        java -jar target/mock-rpa-server-1.0.0.jar
    else
        echo "Build failed!"
        exit 1
    fi
fi
