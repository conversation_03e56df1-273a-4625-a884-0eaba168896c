@echo off
echo Testing Mock RPA Server API...
echo.

REM 测试健康检查
echo 1. Testing health check...
curl -s http://localhost:8080/health
echo.
echo.

REM 测试获取流程ID
echo 2. Testing GetFlowIDByFullPath...
curl -s -X POST http://localhost:8080/CallFunc.aom ^
  -H "Content-Type: application/json" ^
  -d "[{\"Type\":4,\"Name\":\"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}\",\"Value\":\"TFlowDM\"},{\"Type\":4,\"Name\":\"{2881E26D-62CE-4937-B4BB-8998440417C4}\",\"Value\":\"GetFlowIDByFullPath\"},{\"Type\":4,\"Name\":\"FlowPath\",\"Value\":\"安联资管_财务运营部\\\\安联资管_财务运营部_估值系统T0T1估值表\\\\财务运营部_估值系统估值系统T0T1估值表\"}]"
echo.
echo.

REM 测试启动流程
echo 3. Testing StartFlow...
curl -s -X POST http://localhost:8080/CallFunc.aom ^
  -H "Content-Type: application/json" ^
  -d "[{\"Type\":4,\"Name\":\"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}\",\"Value\":\"TFlowDM\"},{\"Type\":4,\"Name\":\"{2881E26D-62CE-4937-B4BB-8998440417C4}\",\"Value\":\"StartFlow\"},{\"Type\":4,\"Name\":\"FlowID\",\"Value\":\"56CA11C83CEF473A87E25EA991C623CE\"}]"
echo.
echo.

REM 测试查询流程状态
echo 4. Testing GetFlowExecedState...
curl -s -X POST http://localhost:8080/CallFunc.aom ^
  -H "Content-Type: application/json" ^
  -d "[{\"Type\":4,\"Name\":\"{9F8E5ECB-5976-4315-B8F3-43B8502B694D}\",\"Value\":\"TUserDM\"},{\"Type\":4,\"Name\":\"{2881E26D-62CE-4937-B4BB-8998440417C4}\",\"Value\":\"GetFlowExecedState\"},{\"Type\":4,\"Name\":\"IDs\",\"Value\":\"56CA11C83CEF473A87E25EA991C623CE\"},{\"Type\":4,\"Name\":\"DBDate\",\"Value\":\"20241201\"}]"
echo.
echo.

echo Testing completed!
pause
