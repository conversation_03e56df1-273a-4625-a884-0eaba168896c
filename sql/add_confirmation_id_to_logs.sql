-- 为开基确认单日志表添加缺失的 confirmation_id 字段

-- 为上传日志表添加 confirmation_id 字段
ALTER TABLE `log_open_fund_confirmation_upload` 
ADD COLUMN `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID' AFTER `file_id`,
ADD INDEX `idx_confirmation_id` (`confirmation_id`);

-- 为下载日志表添加 confirmation_id 字段
ALTER TABLE `log_open_fund_confirmation_download` 
ADD COLUMN `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID' AFTER `file_id`,
ADD INDEX `idx_confirmation_id` (`confirmation_id`);

-- 为邮件发送日志表添加 confirmation_id 字段
ALTER TABLE `log_open_fund_confirmation_mail` 
ADD COLUMN `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID' AFTER `id`,
ADD INDEX `idx_confirmation_id` (`confirmation_id`);
