-- 分红公告信息表
DROP TABLE IF EXISTS `dividend_announcement_data`;

CREATE TABLE `dividend_announcement_data` (
  `juyuan_id` varchar(50) NOT NULL COMMENT '聚源数据ID',
  `inner_code` varchar(50) DEFAULT NULL COMMENT '聚源内部代码',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码',
  `security_name` varchar(100) DEFAULT NULL COMMENT '证券名称',
  `security_type` varchar(50) DEFAULT NULL COMMENT '证券类型',
  `security_market` varchar(20) DEFAULT NULL COMMENT '交易市场',
  `data_source_type` varchar(20) DEFAULT NULL COMMENT '数据来源类型(STOCK-股票,HKSTOCK-港股,BOND-债券,FUND-基金)',
  `registration_date` date DEFAULT NULL COMMENT '登记日',
  `ex_rights_date` date DEFAULT NULL COMMENT '除权日',
  `bonus_share_payment_date` date DEFAULT NULL COMMENT '红股发放日',
  `dividend_payment_date` date DEFAULT NULL COMMENT '红利发放日',
  `bonus_share_ratio` decimal(18,6) DEFAULT NULL COMMENT '红股分红比例',
  `cash_dividend_ratio` decimal(18,6) DEFAULT NULL COMMENT '现金分红比例',
  `sync_date` date NOT NULL COMMENT '同步日期',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`juyuan_id`),
  KEY `idx_security_code` (`security_code`),
  KEY `idx_sync_date` (`sync_date`),
  KEY `idx_ex_rights_date` (`ex_rights_date`),
  KEY `idx_security_code_sync_date` (`security_code`, `sync_date`),
  KEY `idx_security_market` (`security_market`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分红公告信息表';

-- 添加索引说明
-- idx_security_code: 按证券代码查询
-- idx_sync_date: 按同步日期查询，用于数据清理
-- idx_ex_rights_date: 按除权日查询
-- idx_security_code_sync_date: 复合索引，用于按证券代码和同步日期查询
-- idx_security_market: 按交易市场查询
