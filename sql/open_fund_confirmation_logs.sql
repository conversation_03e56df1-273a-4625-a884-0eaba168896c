CREATE TABLE `log_open_fund_confirmation_delete` (
                                                     `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                     `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID',
                                                     `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                     `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
                                                     `delete_time` datetime NOT NULL COMMENT '删除时间',
                                                     `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                     `delete_status` varchar(20) NOT NULL DEFAULT 'DELETED' COMMENT '删除状态',
                                                     `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                     `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                     `transaction_channel` varchar(255) DEFAULT NULL COMMENT '交易渠道',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_delete_time` (`delete_time`),
                                                     <PERSON><PERSON>Y `idx_operator` (`operator`),
                                                     KEY `idx_confirmation_id` (`confirmation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基确认单删除记录日志表';

CREATE TABLE `log_open_fund_confirmation_download` (
                                                       `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                       `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                       `file_path` varchar(500) NOT NULL COMMENT '文件路径',
                                                       `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
                                                       `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID',
                                                       `download_time` datetime NOT NULL COMMENT '下载时间',
                                                       `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                       `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
                                                       `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                       `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY `idx_download_time` (`download_time`),
                                                       KEY `idx_operator` (`operator`),
                                                       KEY `idx_confirmation_id` (`confirmation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基确认单下载日志表';

CREATE TABLE `log_open_fund_confirmation_mail` (
                                                   `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                   `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID',
                                                   `task_name` varchar(255) NOT NULL COMMENT '邮件发送任务名称',
                                                   `send_method` varchar(20) NOT NULL COMMENT '发送方式(AUTO/MANUAL)',
                                                   `mail_status` varchar(20) NOT NULL COMMENT '邮件发送状态',
                                                   `send_time` datetime NOT NULL COMMENT '邮件发送时间',
                                                   `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                   `mail_log_id` varchar(30) DEFAULT NULL COMMENT '邮件日志ID',
                                                   `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                   `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_send_time` (`send_time`),
                                                   KEY `idx_operator` (`operator`),
                                                   KEY `idx_mail_log_id` (`mail_log_id`),
                                                   KEY `idx_confirmation_id` (`confirmation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基确认单邮件发送日志表';

CREATE TABLE `log_open_fund_confirmation_ocr` (
                                                  `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                  `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID',
                                                  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                  `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
                                                  `confirm_time` datetime NOT NULL COMMENT '确认时间',
                                                  `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                  `confirm_status` varchar(20) NOT NULL DEFAULT 'CONFIRMED' COMMENT '确认状态',
                                                  `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                  `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                  `transaction_channel` varchar(255) DEFAULT NULL COMMENT '交易渠道',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_confirm_time` (`confirm_time`),
                                                  KEY `idx_operator` (`operator`),
                                                  KEY `idx_confirmation_id` (`confirmation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基确认单OCR确认日志表';

CREATE TABLE `log_open_fund_confirmation_upload` (
                                                     `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                     `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                     `file_path` varchar(500) NOT NULL COMMENT '文件路径',
                                                     `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
                                                     `confirmation_id` varchar(30) NOT NULL COMMENT '确认单ID',
                                                     `upload_time` datetime NOT NULL COMMENT '上传时间',
                                                     `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                     `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
                                                     `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                     `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_upload_time` (`upload_time`),
                                                     KEY `idx_operator` (`operator`),
                                                     KEY `idx_confirmation_id` (`confirmation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基确认单上传日志表';

CREATE TABLE `log_open_fund_reconciliation_delete` (
                                                       `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                       `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
                                                       `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                       `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
                                                       `delete_time` datetime NOT NULL COMMENT '删除时间',
                                                       `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                       `delete_status` varchar(50) NOT NULL COMMENT '删除状态',
                                                       `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                       `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                       `transaction_channel` varchar(255) DEFAULT NULL COMMENT '交易渠道',
                                                       PRIMARY KEY (`id`),
                                                       KEY `idx_delete_time` (`delete_time`),
                                                       KEY `idx_operator` (`operator`),
                                                       KEY `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单删除记录日志表';

CREATE TABLE `log_open_fund_reconciliation_download` (
                                                         `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                         `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                         `file_path` varchar(500) NOT NULL COMMENT '文件路径',
                                                         `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
                                                         `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
                                                         `download_time` datetime NOT NULL COMMENT '下载时间',
                                                         `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                         `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
                                                         `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                         `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                         PRIMARY KEY (`id`),
                                                         KEY `idx_download_time` (`download_time`),
                                                         KEY `idx_operator` (`operator`),
                                                         KEY `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单下载日志表';

CREATE TABLE `log_open_fund_reconciliation_mail` (
                                                     `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                     `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
                                                     `task_name` varchar(255) NOT NULL COMMENT '邮件发送任务名称',
                                                     `send_method` varchar(20) NOT NULL COMMENT '发送方式',
                                                     `mail_status` varchar(20) NOT NULL COMMENT '邮件发送状态',
                                                     `send_time` datetime NOT NULL COMMENT '邮件发送时间',
                                                     `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                     `mail_log_id` varchar(30) DEFAULT NULL COMMENT '邮件日志ID',
                                                     `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                     `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_send_time` (`send_time`),
                                                     KEY `idx_operator` (`operator`),
                                                     KEY `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单邮件发送日志表';

CREATE TABLE `log_open_fund_reconciliation_ocr` (
                                                    `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                    `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
                                                    `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                    `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
                                                    `confirm_time` datetime NOT NULL COMMENT '确认时间',
                                                    `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                    `confirm_status` varchar(50) NOT NULL COMMENT '确认状态',
                                                    `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                    `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                    `transaction_channel` varchar(255) DEFAULT NULL COMMENT '交易渠道',
                                                    PRIMARY KEY (`id`),
                                                    KEY `idx_confirm_time` (`confirm_time`),
                                                    KEY `idx_operator` (`operator`),
                                                    KEY `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单OCR确认日志表';

CREATE TABLE `log_open_fund_reconciliation_upload` (
                                                       `id` varchar(30) NOT NULL COMMENT '主键ID',
                                                       `file_name` varchar(255) NOT NULL COMMENT '文件名称',
                                                       `file_path` varchar(500) NOT NULL COMMENT '文件路径',
                                                       `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
                                                       `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
                                                       `upload_time` datetime NOT NULL COMMENT '上传时间',
                                                       `operator` varchar(100) NOT NULL COMMENT '操作人',
                                                       `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
                                                       `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                       `update_time` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
                                                       PRIMARY KEY (`id`),
                                                       KEY `idx_upload_time` (`upload_time`),
                                                       KEY `idx_operator` (`operator`),
                                                       KEY `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单上传日志表';

