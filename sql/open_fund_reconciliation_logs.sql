-- 开基对账单日志表结构

-- 上传日志表
CREATE TABLE `log_open_fund_reconciliation_upload` (
  `id` varchar(30) NOT NULL COMMENT '主键ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
  `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `operator` varchar(100) NOT NULL COMMENT '操作人',
  `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_upload_time` (`upload_time`),
  INDEX `idx_operator` (`operator`),
  INDEX `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单上传日志表';

-- 下载日志表
CREATE TABLE `log_open_fund_reconciliation_download` (
  `id` varchar(30) NOT NULL COMMENT '主键ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_id` varchar(30) NOT NULL COMMENT '文件唯一标识',
  `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
  `download_time` datetime NOT NULL COMMENT '下载时间',
  `operator` varchar(100) NOT NULL COMMENT '操作人',
  `operation_status` varchar(20) NOT NULL COMMENT '操作状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_download_time` (`download_time`),
  INDEX `idx_operator` (`operator`),
  INDEX `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单下载日志表';

-- 邮件发送日志表
CREATE TABLE `log_open_fund_reconciliation_mail` (
  `id` varchar(30) NOT NULL COMMENT '主键ID',
  `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
  `task_name` varchar(255) NOT NULL COMMENT '邮件发送任务名称',
  `send_method` varchar(20) NOT NULL COMMENT '发送方式',
  `mail_status` varchar(20) NOT NULL COMMENT '邮件发送状态',
  `send_time` datetime NOT NULL COMMENT '邮件发送时间',
  `operator` varchar(100) NOT NULL COMMENT '操作人',
  `mail_log_id` varchar(30) COMMENT '邮件日志ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_send_time` (`send_time`),
  INDEX `idx_operator` (`operator`),
  INDEX `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单邮件发送日志表';

-- OCR确认日志表
CREATE TABLE `log_open_fund_reconciliation_ocr` (
  `id` varchar(30) NOT NULL COMMENT '主键ID',
  `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
  `confirm_time` datetime NOT NULL COMMENT '确认时间',
  `operator` varchar(100) NOT NULL COMMENT '操作人',
  `confirm_status` varchar(50) NOT NULL COMMENT '确认状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_confirm_time` (`confirm_time`),
  INDEX `idx_operator` (`operator`),
  INDEX `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单OCR确认日志表';

-- 删除记录日志表
CREATE TABLE `log_open_fund_reconciliation_delete` (
  `id` varchar(30) NOT NULL COMMENT '主键ID',
  `reconciliation_id` varchar(30) NOT NULL COMMENT '对账单ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `account_set_name` varchar(255) NOT NULL COMMENT '账套名称',
  `delete_time` datetime NOT NULL COMMENT '删除时间',
  `operator` varchar(100) NOT NULL COMMENT '操作人',
  `delete_status` varchar(50) NOT NULL COMMENT '删除状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_delete_time` (`delete_time`),
  INDEX `idx_operator` (`operator`),
  INDEX `idx_reconciliation_id` (`reconciliation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开基对账单删除记录日志表';
