-- 创建净值异常波动和净值回撤表
CREATE TABLE `audit_portfolio_net_value_fluctuation` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `product_id` varchar(32) DEFAULT NULL COMMENT '账套编码',
  `product_code` varchar(50) DEFAULT NULL COMMENT '账套编号',
  `product_name` varchar(255) DEFAULT NULL COMMENT '账套名称',
  `data_date` varchar(10) DEFAULT NULL COMMENT '数据日期',
  `valuation_time` varchar(10) DEFAULT NULL COMMENT '估值日期（T0/T1）',
  `abnormal_fluctuation` int(1) DEFAULT '0' COMMENT '净值异常波动（0:否, 1:是）',
  `fluctuation_rate` decimal(20,8) DEFAULT NULL COMMENT '净值波动率',
  `retracement_flag` int(1) DEFAULT '0' COMMENT '净值回撤标识(0:否, 1:是)',
  `retracement_reason` varchar(500) DEFAULT NULL COMMENT '回撤原因',
  `current_net_value` decimal(20,8) DEFAULT NULL COMMENT '当日单位净值',
  `previous_net_value` decimal(20,8) DEFAULT NULL COMMENT '上一日单位净值',
  `net_value_difference` decimal(20,8) DEFAULT NULL COMMENT '单位净值差值',
  `current_ten_thousand_income` decimal(20,8) DEFAULT NULL COMMENT '当日万份收益',
  `previous_ten_thousand_income` decimal(20,8) DEFAULT NULL COMMENT '上一日万份收益',
  `ten_thousand_income_difference` decimal(20,8) DEFAULT NULL COMMENT '万分收益差值',
  `email_sent` int(1) DEFAULT '0' COMMENT '邮件发送状态(0:未发送, 1:已发送)',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_data_date` (`data_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='净值异常波动和净值回撤表'; 