-- 股票和开基除权价格检查功能相关表结构

-- 1. 股票和开基除权价格检查主表
CREATE TABLE `audit_stock_fund_ex_rights_check` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `data_date` varchar(10) NOT NULL COMMENT '数据日期',
    `product_id` varchar(32) NOT NULL COMMENT '账套编号',
    `product_name` varchar(100) DEFAULT NULL COMMENT '账套名称',
    `security_type` varchar(50) DEFAULT NULL COMMENT '证券类型',
    `security_type_name` varchar(50) DEFAULT NULL COMMENT '证券类型名称',
    `security_name` varchar(100) DEFAULT NULL COMMENT '证券名称',
    `security_code` varchar(20) NOT NULL COMMENT '证券代码',
    `security_market` varchar(20) DEFAULT NULL COMMENT '交易市场',
    `security_market_name` varchar(50) DEFAULT NULL COMMENT '交易市场名称',
    `security_internal_code` varchar(50) DEFAULT NULL COMMENT '证券内部代码',

    -- 聚源数据字段
    `registration_date` varchar(10) DEFAULT NULL COMMENT '登记日',
    `ex_rights_date` varchar(10) DEFAULT NULL COMMENT '除权日',
    `bonus_share_payment_date` varchar(10) DEFAULT NULL COMMENT '红股发放日',
    `dividend_payment_date` varchar(10) DEFAULT NULL COMMENT '红利发放日',
    `bonus_share_ratio` decimal(18,6) DEFAULT NULL COMMENT '红股分红比例',
    `cash_dividend_ratio` decimal(18,6) DEFAULT NULL COMMENT '现金分红比例',

    -- 流水检查结果字段（使用枚举）
    `ex_rights_flow_check` tinyint(1) DEFAULT NULL COMMENT '除权流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失',
    `bonus_share_flow_check` tinyint(1) DEFAULT NULL COMMENT '红股流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失',
    `dividend_flow_check` tinyint(1) DEFAULT NULL COMMENT '红利发放流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失',

    -- 流水详情字段
    `ex_rights_flow_exists` tinyint(1) DEFAULT 0 COMMENT '除权流水是否存在：0-不存在，1-存在',
    `bonus_share_flow_exists` tinyint(1) DEFAULT 0 COMMENT '红股流水是否存在：0-不存在，1-存在',
    `dividend_flow_exists` tinyint(1) DEFAULT 0 COMMENT '红利发放流水是否存在：0-不存在，1-存在',

    -- 审计字段
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',

    PRIMARY KEY (`id`),
    KEY `idx_data_date` (`data_date`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_security_code` (`security_code`),
    KEY `idx_security_market` (`security_market`),
    KEY `idx_security_internal_code` (`security_internal_code`),
    KEY `idx_ex_rights_date` (`ex_rights_date`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票和开基除权价格检查表';


