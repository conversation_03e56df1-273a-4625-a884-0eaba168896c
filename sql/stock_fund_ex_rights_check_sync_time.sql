-- 股票和开基除权价格检查同步时间记录表
CREATE TABLE `audit_stock_fund_ex_rights_check_sync_time` (
    `id` varchar(32) NOT NULL COMMENT 'ID',
    `data_date` varchar(10) NOT NULL COMMENT '数据日期',
    `sync_time` datetime NOT NULL COMMENT '同步时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_data_date` (`data_date`),
    KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票和开基除权价格检查同步时间记录表';
