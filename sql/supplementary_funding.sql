-- 补仓资金表
CREATE TABLE `audit_supplementary_funding` (
  `id` varchar(30) NOT NULL COMMENT 'ID',
  `product_id` varchar(30) DEFAULT NULL COMMENT '账套编号',
  `product_name` varchar(200) DEFAULT NULL COMMENT '账套名称',
  `data_date` varchar(10) DEFAULT NULL COMMENT '数据日期',
  `amount` decimal(32,4) DEFAULT NULL COMMENT '金额',
  `transaction_type` varchar(50) DEFAULT NULL COMMENT '流水类型',
  `update_user` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `create_user` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='补仓资金表';


alter table audit_portfolio_net_value_warning
    modify net_value decimal(18, 8) null;

alter table audit_portfolio_net_value_warning
    modify warning_value decimal(18, 8) null;

alter table audit_portfolio_net_value_warning
    modify liquidation_value decimal(18, 8) null;

alter table audit_portfolio_net_value_warning
    modify forfeiture_value decimal(18, 8) null;

ALTER TABLE audit_portfolio_net_value_warning
    MODIFY required_funding decimal(20, 8) NULL COMMENT '应补仓资金';

