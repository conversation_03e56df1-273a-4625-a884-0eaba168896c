-- 更新分红公告信息表的日期字段类型
-- 将varchar类型的日期字段改为date类型

-- 如果表已存在，先备份数据
-- CREATE TABLE dividend_announcement_data_backup AS SELECT * FROM dividend_announcement_data;

-- 修改日期字段类型
ALTER TABLE dividend_announcement_data 
MODIFY COLUMN registration_date DATE DEFAULT NULL COMMENT '登记日';

ALTER TABLE dividend_announcement_data 
MODIFY COLUMN ex_rights_date DATE DEFAULT NULL COMMENT '除权日';

ALTER TABLE dividend_announcement_data 
MODIFY COLUMN bonus_share_payment_date DATE DEFAULT NULL COMMENT '红股发放日';

ALTER TABLE dividend_announcement_data 
MODIFY COLUMN dividend_payment_date DATE DEFAULT NULL COMMENT '红利发放日';

ALTER TABLE dividend_announcement_data
MODIFY COLUMN sync_date DATE NOT NULL COMMENT '同步日期';

-- 添加数据来源类型字段
ALTER TABLE dividend_announcement_data
ADD COLUMN data_source_type varchar(20) DEFAULT NULL COMMENT '数据来源类型(STOCK-股票,HKSTOCK-港股,BOND-债券,FUND-基金)'
AFTER security_market;

-- 如果需要更新现有数据的日期格式（从varchar转换为date）
-- UPDATE dividend_announcement_data SET 
--   registration_date = STR_TO_DATE(registration_date, '%Y-%m-%d'),
--   ex_rights_date = STR_TO_DATE(ex_rights_date, '%Y-%m-%d'),
--   bonus_share_payment_date = STR_TO_DATE(bonus_share_payment_date, '%Y-%m-%d'),
--   dividend_payment_date = STR_TO_DATE(dividend_payment_date, '%Y-%m-%d'),
--   sync_date = STR_TO_DATE(sync_date, '%Y-%m-%d')
-- WHERE registration_date IS NOT NULL OR ex_rights_date IS NOT NULL 
--    OR bonus_share_payment_date IS NOT NULL OR dividend_payment_date IS NOT NULL 
--    OR sync_date IS NOT NULL;
