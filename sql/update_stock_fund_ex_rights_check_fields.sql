-- 更新股票基金除权价格检查表，添加新字段

-- 添加交易市场名称字段
ALTER TABLE audit_stock_fund_ex_rights_check
ADD COLUMN security_market_name varchar(50) DEFAULT NULL COMMENT '交易市场名称' 
AFTER security_market;

-- 添加证券类型名称字段
ALTER TABLE audit_stock_fund_ex_rights_check
ADD COLUMN security_type_name varchar(50) DEFAULT NULL COMMENT '证券类型名称' 
AFTER security_type;

-- 添加证券内部代码字段
ALTER TABLE audit_stock_fund_ex_rights_check
ADD COLUMN security_internal_code varchar(50) DEFAULT NULL COMMENT '证券内部代码' 
AFTER security_code;

-- 移除流水日期字段
ALTER TABLE audit_stock_fund_ex_rights_check DROP COLUMN IF EXISTS ex_rights_flow_date;
ALTER TABLE audit_stock_fund_ex_rights_check DROP COLUMN IF EXISTS bonus_share_flow_date;
ALTER TABLE audit_stock_fund_ex_rights_check DROP COLUMN IF EXISTS dividend_flow_date;

-- 移除持仓数量字段（如果存在）
ALTER TABLE audit_stock_fund_ex_rights_check DROP COLUMN IF EXISTS security_holding;

-- 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_security_market ON audit_stock_fund_ex_rights_check(security_market);
CREATE INDEX IF NOT EXISTS idx_security_internal_code ON audit_stock_fund_ex_rights_check(security_internal_code);
