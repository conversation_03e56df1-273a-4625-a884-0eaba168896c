-- 股票公告信息
SELECT ab.id,
       ab.INNERCODE,
       c.SECUCOD<PERSON> as "securityCode",
       c.SECUMARKET,
       c.SECUCATEGORYONE as "securityType",
       c.SECUABBR as "securityName",
       ab.ENDDATE,
       ab.RIGHTREGDATE as "registrationDate",
       ab.EXDIVIDATE as "exRightsDate",
       ab.TOACCOUNTDATE as "bonusSharePaymentDate",
       ab.BONUSSHAREARRIVALDATE as "dividendPaymentDate",
       ab.CASHDIVIRMBADJUSTED as "cashDividendRatio",
       ab.BONUSSHARERATIO as "bonusShareRatio"
FROM (SELECT a.*
      FROM LC_DIVIDEND a
               INNER JOIN (SELECT max(id) AS id,
                                  INNERCODE
                           FROM LC_DIVIDEND
                           GROUP BY INNERCODE) b ON
          a.id = b.id) ab
         INNER JOIN SECUMAINALL c ON
    ab.INNERCODE = c.INNERCODE
ORDER BY ab.id DESC;

-- 港股公告信息
SELECT ab.id,
       ab.INNERCODE,
       c.SECUCODE as "securityCode",
       c.SECUMARKET,
       c.SECUCATEGORY as "securityName",
       c.SECUABBR,
       ab.ENDDATE,
       ab.RECORDDATE as "registrationDate",
       ab.EXDATE as "exRightsDate",
       ab.DIVIDENDPAYABLEDATE as "dividendPaymentDate",
       ab.PAYOUTDATE as "bonusSharePaymentDate",
       ab.CASHDIVIDENDPSHKD as "cashDividendRatio",
       CASE
           WHEN ab.ShareDividendRateY > 0 THEN
               ab.ShareDividendRateX / ab.ShareDividendRateY
           ELSE 0
           END as "bonusShareRatio"
FROM (SELECT a.*
      FROM HK_DIVIDEND a
               INNER JOIN (SELECT max(id) AS id,
                                  INNERCODE
                           FROM HK_DIVIDEND
                           GROUP BY INNERCODE) b ON
          a.id = b.id) ab
         INNER JOIN (SELECT DISTINCT INNERCODE, SECUCODE, SECUMARKET, SECUCATEGORY, SECUABBR FROM HK_SECUMAIN) c ON
    ab.INNERCODE = c.INNERCODE
ORDER BY ab.id DESC;

--债券公告信息
SELECT ab.id,
       ab.INNERCODE,
       c.SECUCODE as "securityCode",
       c.SECUMARKET,
       c.SECUCATEGORYONE as "securityType",
       c.SECUABBR as "securityName",
       ab.REGDATE as "registrationDate",
       ab.EXDIVIDATE as "exRightsDate",
       ab.PAYMENTDATE as "dividendPaymentDate",
       ab.INTERESTPER as "cashDividendRatio"
FROM (SELECT a.*
      FROM BOND_CASHFLOW a
               INNER JOIN (SELECT max(id) AS id,
                                  INNERCODE
                           FROM BOND_CASHFLOW
                           GROUP BY INNERCODE) b ON
          a.id = b.id
         -- WHERE INSERTTIME BETWEEN to_date('2025-08-11', 'YYYY-MM-DD') AND to_date('2025-08-12', 'YYYY-MM-DD')
     ) ab
         INNER JOIN SECUMAINALL c ON
    ab.INNERCODE = c.INNERCODE
ORDER BY ab.id DESC;


--基金公告信息

SELECT ab.id as "id",
       ab.INNERCODE,
       c.MAINCODE as "securityCode",
       c.SECUCODE,
       c.SECUMARKET,
       c.SECUCATEGORYONE as "securityType",
       c.SECUABBR as "securityName",
       ab.ENDDATE,
       ab.REDATE as "registrationDate",
       ab.EXRIGHTDATE as "exRightsDate",
       ab.EXECUTEDATE as "dividendPaymentDate",
       ab.ACCOUNTDAY as "bonusSharePaymentDate",
       ab.DIVIDENDRATIOBEFORETAX as "cashDividendRatio"
FROM (SELECT a.*
      FROM MF_DIVIDEND a
               INNER JOIN (SELECT max(id) AS id,
                                  INNERCODE
                           FROM MF_DIVIDEND
                           GROUP BY INNERCODE) b ON
          a.id = b.id) ab
         INNER JOIN SECUMAINALL c ON
    ab.INNERCODE = c.INNERCODE
ORDER BY ab.id DESC;