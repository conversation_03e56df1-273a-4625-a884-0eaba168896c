package cn.sdata.om.al;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAspectJAutoProxy(exposeProxy = true)
@MapperScan({"cn.sdata.om.al.mapper", "cn.sdata.om.al.audit.mapper"})
@EnableScheduling
public class OmBackendAlApplication {

	public static void main(String[] args) {
		System.setProperty("mail.mime.splitlongparameters", "false");
		SpringApplication.run(OmBackendAlApplication.class, args);
	}

}
