package cn.sdata.om.al.aspect;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.enums.MailMacroEnum;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.ValuationTableDataService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class MacroAspect {

    private final AccountInformationService accountInformationService;
    private final ValuationTableDataService valuationTableDataService;

    @Pointcut("execution (* cn.sdata.om.al.disclosure.*.execute(..))")
    public void executeMethod() {
    }

    @After("executeMethod()")
    public void executeAround(JoinPoint joinPoint) {
        try {
            Class<?> aClass = joinPoint.getTarget().getClass();
            log.info("执行{}", aClass.getSimpleName());
            Object[] args = joinPoint.getArgs();
            SendMailInfo sendMailInfo = (SendMailInfo) args[0];
            doReplace(sendMailInfo);
            log.info("替换后邮件内容: {}", sendMailInfo);
        } catch (Throwable e) {
            log.error("替换邮件内容失败", e);
        }
    }

    private void doReplace(SendMailInfo sendMailInfo) {
        MailMacroEnum[] mailMacroEnums = MailMacroEnum.values();
        String subject = sendMailInfo.getSubject();
        String content = sendMailInfo.getContent();
        for (MailMacroEnum mailMacroEnum : mailMacroEnums) {
            try {
                subject = StringUtil.dealMacro(subject, composeVariables(mailMacroEnum, sendMailInfo, subject));
                content = StringUtil.dealMacro(content, composeVariables(mailMacroEnum, sendMailInfo, content));
            } catch (Exception e) {
                log.info("替换失败", e);
            }
        }
        sendMailInfo.setSubject(subject);
        sendMailInfo.setContent(content);
    }

    private Map<String, String> composeVariables(MailMacroEnum mailMacroEnum, SendMailInfo sendMailInfo, String original) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        if (original == null || !original.contains(mailMacroEnum.getReplace())) {
            return new HashMap<>();
        }
        List<String> productIds = sendMailInfo.getProductIds();
        /*if (productIds == null || productIds.isEmpty()) {
            return new HashMap<>();
        }*/
        log.info("替换宏的key为:{}", mailMacroEnum.getSimpleReplace());
        // log.info("需要替换的邮件数据对象为:{}", JSON.toJSONString(sendMailInfo));
        switch (mailMacroEnum) {
            case PRODUCT_CODE:
                Map<String, String> idCodes = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getProductCode, (oldOne, newOne) -> newOne));
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                List<String> productCodes = productIds.stream().map(idCodes::get).filter(Objects::nonNull).collect(Collectors.toList());
                String codeReplacement = String.join(",", productCodes);
                return Map.of(mailMacroEnum.getSimpleReplace(), codeReplacement);
            case PRODUCT_NAME:
                Map<String, String> idNames = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getFullProductName, (oldOne, newOne) -> newOne));
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                List<String> productNames = productIds.stream().map(idNames::get).filter(Objects::nonNull).collect(Collectors.toList());
                String nameReplacement = String.join(",", productNames);
                return Map.of(mailMacroEnum.getSimpleReplace(), nameReplacement);
            case DATE:
                return Map.of(mailMacroEnum.getSimpleReplace(), new SimpleDateFormat(BaseConstant.DATE_FORMAT_PATTERN).format(Calendar.getInstance().getTime()));
            case DATA_DATE:
                log.info("需要替换的数据日期为:{}", sendMailInfo.getDataDate());
                return Map.of(mailMacroEnum.getSimpleReplace(), sendMailInfo.getDataDate());
            case WARN:
                Map<String, String> idWarn = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getWarnValue, (oldOne, newOne) -> newOne));
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                List<String> warnValues = productIds.stream().map(idWarn::get).filter(Objects::nonNull).collect(Collectors.toList());
                String warnReplacement = String.join(",", warnValues);
                return Map.of(mailMacroEnum.getSimpleReplace(), warnReplacement);
            case CLOSE:
                Map<String, String> idClose = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getCloseValue, (oldOne, newOne) -> newOne));
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                List<String> closeValues = productIds.stream().map(idClose::get).filter(Objects::nonNull).collect(Collectors.toList());
                String closeReplacement = String.join(",", closeValues);
                return Map.of(mailMacroEnum.getSimpleReplace(), closeReplacement);
            case NET_VALUE:
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                LambdaQueryWrapper<ValuationTableData> netValueWrapper = new LambdaQueryWrapper<>();
                netValueWrapper.eq(ValuationTableData::getValuationDate, sendMailInfo.getDataDate());
                netValueWrapper.in(ValuationTableData::getProductId, sendMailInfo.getProductIds());
                List<ValuationTableData> list = valuationTableDataService.list(netValueWrapper);
                List<String> netValues = list.stream().map(ValuationTableData::getNetValue).collect(Collectors.toList());
                String netValueReplacement = String.join(",", netValues);
                return Map.of(mailMacroEnum.getSimpleReplace(), netValueReplacement);
            case PRODUCT_NET_VALUE:
                if (productIds == null || productIds.isEmpty()) {
                    return new HashMap<>();
                }
                LambdaQueryWrapper<ValuationTableData> productNetValueWrapper = new LambdaQueryWrapper<>();
                productNetValueWrapper.eq(ValuationTableData::getValuationDate, sendMailInfo.getDataDate());
                productNetValueWrapper.in(ValuationTableData::getProductId, sendMailInfo.getProductIds());
                List<ValuationTableData> tableData = valuationTableDataService.list(productNetValueWrapper);
                List<String> result = tableData.stream().map(valuationTableData -> {
                    String productCode = valuationTableData.getProductCode();
                    String netValue = valuationTableData.getNetValue();
                    return productCode + " " + netValue;
                }).collect(Collectors.toList());
                String resultReplacement = String.join(",", result);
                return Map.of(mailMacroEnum.getSimpleReplace(), resultReplacement);
        }
        return new HashMap<>();
    }

}
