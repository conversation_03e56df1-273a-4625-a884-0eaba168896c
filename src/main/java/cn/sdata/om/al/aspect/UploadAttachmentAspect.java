package cn.sdata.om.al.aspect;

import cn.hutool.core.io.FileUtil;
import cn.sdata.om.al.entity.SendMailInfo;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

@Component
@Aspect
@Slf4j
public class UploadAttachmentAspect {

    @Value("${file.dir}")
    private String fileDir;

    @Pointcut("execution (* cn.sdata.om.al.disclosure.MailHandler.execute(..))")
    public void executeMethod() {
    }

    @Around("executeMethod()")
    public Object doExecuteAround(ProceedingJoinPoint joinPoint) {
        SendMailInfo sendMailInfo = null;
        try {
            sendMailInfo = (SendMailInfo) joinPoint.proceed();
            if (sendMailInfo != null) {
                Map<String, byte[]> attachments = sendMailInfo.getAttachment();
                if (!attachments.isEmpty()) {
                    String id = IdWorker.getIdStr();
                    List<String> attachmentPaths = sendMailInfo.getAttachmentPaths();
                    attachments.forEach((fileName, bytes) -> {
                        String fullFilePath = getFullFilePath(id, fileName);
                        try {
                            FileUtil.writeBytes(bytes, fullFilePath);
                        } catch (Exception e) {
                            log.error("{}文件上传失败", fullFilePath, e);
                        }
                        attachmentPaths.add(fullFilePath);
                    });
                }
            }
        } catch (Throwable e) {
            log.error("本地文件转换失败", e);
        }
        return sendMailInfo;
    }

    @NonNull
    private String getFullFilePath(String id, String fileName) {
        return Paths.get(fileDir, id, fileName).toString();
    }

}
