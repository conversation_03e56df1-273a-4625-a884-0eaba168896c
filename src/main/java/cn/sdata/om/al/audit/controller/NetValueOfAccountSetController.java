package cn.sdata.om.al.audit.controller;

import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.service.NetValueOfAccountSetService;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.mapper.NetValueDisclosureMapper;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多账套净值信息查询
 */
@RestController
@RequestMapping("/audit/net_value_of_account_set")
@AllArgsConstructor
public class NetValueOfAccountSetController {

    private NetValueOfAccountSetService netValueOfAccountSetService;
    private final NetValueDisclosureMapper netValueDisclosureMapper;

    @PostMapping("page")
    public R<Page<NetValueOfAccountSetVO>> page(@RequestBody NetValueOfAccountSetVO commonPageParam) {
        Page<NetValueOfAccountSetVO> page = netValueOfAccountSetService.selectPageInfo(commonPageParam);
        return R.ok(page);
    }


    /**
     * 手动同步多账套净值信息
     * @return 波动范围规则列表
     */
    @GetMapping("/manual-sync")
    public R<Boolean> manualSync(
            @RequestParam(required = false,value = "startDate") String startDate,
            @RequestParam(required = false,value = "endDate") String endDate) {
        return R.ok(netValueOfAccountSetService.manualSync(startDate,endDate));
    }

    /**
     * 获取组合产品账套代码
     *
     * @return 账套值
     */
    @GetMapping("/account-code-list")
    public R<List<String>> accountList() {
        List<String> commonEntities = netValueOfAccountSetService.listAll();
        return R.ok(commonEntities);
    }

    /**
     * 获取组合产品账套名称
     *
     * @return 账套值
     */
    @GetMapping("/account-name-list")
    public R<List<String>> listAllAccountName() {
        List<String> commonEntities = netValueOfAccountSetService.listAllAccountName();
        return R.ok(commonEntities);
    }


    @GetMapping("getComparisonDay")
    public R<String> getComparisonDay() {
        return R.ok(netValueOfAccountSetService.getDate(),"");
    }

    @GetMapping("setComparisonDay")
    public R<String> setComparisonDay(@RequestParam String comparisonDay) {
        netValueOfAccountSetService.setComparisonDay(comparisonDay);
        return R.ok();
    }
}
