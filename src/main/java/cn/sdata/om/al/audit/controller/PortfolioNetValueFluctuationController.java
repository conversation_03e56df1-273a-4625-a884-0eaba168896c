package cn.sdata.om.al.audit.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CommonQueryService;
import cn.sdata.om.al.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 净值异常波动和净值回撤控制器
 */
@RestController
@RequestMapping("/audit/fluctuation")
@RequiredArgsConstructor
@Slf4j
public class PortfolioNetValueFluctuationController {

    private final PortfolioNetValueFluctuationService portfolioNetValueFluctuationService;

    private final CommonQueryService<PortfolioNetValueFluctuation> commonQueryService;

    @Setter
    @Getter
    public static class BatchRetracementReasonDTO {
        private String id; // 账套ID，对应PortfolioNetValueFluctuation的id
        private String retracementReason;
    }

    /**
     * 分页查询
     */
    @PostMapping("/page")
    public R<Page<PortfolioNetValueFluctuation>> page(@RequestBody CommonPageParam<PortfolioNetValueFluctuation> commonPageParam) {
        Page<PortfolioNetValueFluctuation> page = new Page<>();
        page.setSize(commonPageParam.getSize());
        page.setCurrent(commonPageParam.getCurrent());
        Map<String, String> orderColumn = commonPageParam.getOrderColumn();
        if (orderColumn == null) {
            orderColumn = new HashMap<>();
        }
        orderColumn.put("data_date", "desc");
        commonPageParam.setOrderColumn(orderColumn);
        Page<PortfolioNetValueFluctuation> result = commonQueryService.commonPage(commonPageParam, PortfolioNetValueFluctuation.class);
        if (result != null) {
            List<PortfolioNetValueFluctuation> records = result.getRecords();
            if (CollectionUtil.isNotEmpty(records)) {
                for (PortfolioNetValueFluctuation fluctuation : records) {
                    BigDecimal netValueDifference = fluctuation.getNetValueDifference();
                    if (netValueDifference != null) {
                        // 取消指数显示
                        fluctuation.setNetValueDifferenceStr(netValueDifference.toPlainString());
                    }
                }
            }
        }
        return R.ok(result);
    }

    /**
     * 批量标记回撤原因
     */
    @PostMapping("/batch-mark-retracement-reason")
    public R<?> batchMarkRetracementReason(@RequestBody List<BatchRetracementReasonDTO> list) {
        for (BatchRetracementReasonDTO item : list) {
            PortfolioNetValueFluctuation update = new PortfolioNetValueFluctuation();
            update.setId(item.getId());
            update.setRetracementReason(item.getRetracementReason());
            portfolioNetValueFluctuationService.updateById(update);
        }
        return R.ok("批量标记成功");
    }

    /**
     * 发送净值回撤邮件
     */
    @PostMapping("/mail/retracement")
    @SuppressWarnings("unchecked")
    public R<?> sendRetracementMail(@RequestBody Map<String, Object> param) {
        List<String> productIds = (List<String>) param.get("productIds");
        Object startDate = param.get("startDate");
        Object endDate = param.get("endDate");
        if (startDate == null || endDate == null) {
            startDate = DateUtils.today();
            endDate = DateUtils.today();
        }
        String startDateStr = String.valueOf(startDate);
        String endDateStr = String.valueOf(endDate);
        List<String> dates = DateUtils.getDatesBetweenToStr(startDateStr, endDateStr);
        log.info("回撤邮件发送邮件选择的时间为:{}", dates);
        portfolioNetValueFluctuationService.sendRetracementMailV2(productIds, dates);
        return R.ok("发送成功");
    }

    /**
     * 发送净值波动邮件
     */
    @PostMapping("/mail/fluctuation")
    @SuppressWarnings("unchecked")
    public R<?> sendFluctuationMail(@RequestBody Map<String, Object> param) {
        List<String> productIds = (List<String>) param.get("productIds");
        Object startDate = param.get("startDate");
        Object endDate = param.get("endDate");
        if (startDate == null || endDate == null) {
            startDate = DateUtils.today();
            endDate = DateUtils.today();
        }
        String startDateStr = String.valueOf(startDate);
        String endDateStr = String.valueOf(endDate);
        List<String> dates = DateUtils.getDatesBetweenToStr(startDateStr, endDateStr);
        log.info("波动发送邮件选择的时间为:{}", dates);
        portfolioNetValueFluctuationService.sendFluctuationMailV2(productIds, dates);
        return R.ok("发送成功");
    }

    /**
     * 组合产品净值回撤/波动数量统计
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param valuationTime 估值时间，可选
     * @return 回撤和波动数量统计
     */
    @GetMapping("/count")
    public R<Map<String, Long>> count(@RequestParam("startDate") String startDate,
                                      @RequestParam("endDate") String endDate,
                                      @RequestParam(value = "valuationTime", required = false) String valuationTime) {
        LambdaQueryWrapper<PortfolioNetValueFluctuation> retracementWrapper = new LambdaQueryWrapper<>();
        retracementWrapper.ge(PortfolioNetValueFluctuation::getDataDate, startDate);
        retracementWrapper.le(PortfolioNetValueFluctuation::getDataDate, endDate);
        retracementWrapper.eq(PortfolioNetValueFluctuation::getRetracementFlag, 1);
        if (valuationTime != null && !valuationTime.isEmpty()) {
            retracementWrapper.eq(PortfolioNetValueFluctuation::getValuationTime, valuationTime);
        }
        long retracementCount = portfolioNetValueFluctuationService.count(retracementWrapper);

        LambdaQueryWrapper<PortfolioNetValueFluctuation> fluctuationWrapper = new LambdaQueryWrapper<>();
        fluctuationWrapper.ge(PortfolioNetValueFluctuation::getDataDate, startDate);
        fluctuationWrapper.le(PortfolioNetValueFluctuation::getDataDate, endDate);
        fluctuationWrapper.eq(PortfolioNetValueFluctuation::getAbnormalFluctuation, 1);
        if (valuationTime != null && !valuationTime.isEmpty()) {
            fluctuationWrapper.eq(PortfolioNetValueFluctuation::getValuationTime, valuationTime);
        }
        long fluctuationCount = portfolioNetValueFluctuationService.count(fluctuationWrapper);

        Map<String, Long> result = new HashMap<>();
        result.put("retracementCount", retracementCount);
        result.put("fluctuationCount", fluctuationCount);
        return R.ok(result);
    }

    /**
     * 区间处理净值波动与回撤
     */
    @PostMapping("/process-range")
    public R<Map<String, String>> processRange(@RequestBody Map<String, String> param) {
        String startDate = param.get("startDate");
        String endDate = param.get("endDate");
        portfolioNetValueFluctuationService.processFluctuationForDateRange(startDate, endDate);
        return R.ok("同步成功");
    }


    /**
     * 同步波动规则
     * @return 是否成功
     */
    @GetMapping("/syncFluctuationRate")
    public R<Boolean> syncFluctuationRate() {
        boolean synced = portfolioNetValueFluctuationService.syncFluctuationRate();
        return synced ? R.ok("同步成功") : R.failed("同步失败");
    }
}
