package cn.sdata.om.al.audit.controller;

import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.audit.dto.StockFundExRightsCheckQueryDTO;
import cn.sdata.om.al.audit.entity.StockFundExRightsCheckSyncTime;
import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import cn.sdata.om.al.audit.service.StockFundExRightsCheckService;
import cn.sdata.om.al.audit.service.StockFundExRightsCheckSyncTimeService;
import cn.sdata.om.al.audit.vo.StockFundExRightsCheckVO;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.result.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 股票和开基除权价格检查控制器
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@RestController
@RequestMapping("/audit/stock-fund-ex-rights-check")
@AllArgsConstructor
public class StockFundExRightsCheckController {

    private final StockFundExRightsCheckService stockFundExRightsCheckService;
    private final StockFundExRightsCheckSyncTimeService syncTimeService;

    /**
     * 分页查询股票和开基除权价格检查数据
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    public R<Page<StockFundExRightsCheckVO>> page(@RequestBody StockFundExRightsCheckQueryDTO query) {
        Page<StockFundExRightsCheckVO> page = stockFundExRightsCheckService.selectPageInfo(query);
        return R.ok(page);
    }

    /**
     * 手动执行股票和开基除权价格检查
     * 
     * @param dataDate 数据日期
     * @return 执行结果
     */
    @PostMapping("/execute")
    public R<String> executeCheck(@RequestParam(value = "dataDate", required = false) String dataDate) {
        if (dataDate == null || dataDate.isEmpty()) {
            dataDate = DateUtil.today();
        }
        return stockFundExRightsCheckService.executeExRightsCheck(dataDate);
    }

    /**
     * 同步聚源除权数据
     * 
     * @param dataDate 数据日期
     * @return 同步结果
     */
    @PostMapping("/sync-juyuan-data")
    public R<String> syncJuyuanData(@RequestParam(value = "dataDate", required = false) String dataDate) {
        return stockFundExRightsCheckService.syncJuyuanExRightsData(dataDate);
    }

    /**
     * 获取汇总统计数据
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param valuationTime 估值时间，可选
     * @return 异常数据总数
     */
    @GetMapping("/summary-statistics")
    public R<Long> getSummaryStatistics(@RequestParam("startDate") String startDate,
                                        @RequestParam("endDate") String endDate,
                                        @RequestParam(value = "valuationTime", required = false) String valuationTime) {
        Long totalCount = stockFundExRightsCheckService.getSummaryStatistics(startDate, endDate, valuationTime);
        return R.ok(totalCount);
    }

    /**
     * 获取异常数据统计
     * 
     * @param dataDate 数据日期
     * @return 异常统计结果
     */
    @GetMapping("/abnormal-statistics")
    public R<Map<String, Object>> getAbnormalStatistics(@RequestParam("dataDate") String dataDate) {
        Map<String, Object> statistics = stockFundExRightsCheckService.getAbnormalStatistics(dataDate);
        return R.ok(statistics);
    }

    /**
     * 重新检查指定数据
     * 
     * @param id 检查记录ID
     * @return 检查结果
     */
    @PostMapping("/recheck/{id}")
    public R<String> recheckById(@PathVariable("id") String id) {
        return stockFundExRightsCheckService.recheckById(id);
    }

    /**
     * 批量重新检查
     * 
     * @param dataDate 数据日期
     * @return 检查结果
     */
    @PostMapping("/batch-recheck")
    public R<String> batchRecheck(@RequestParam("dataDate") String dataDate) {
        return stockFundExRightsCheckService.batchRecheck(dataDate);
    }

    /**
     * 获取检查状态枚举值
     *
     * @return 枚举值列表
     */
    @GetMapping("/check-status-enum")
    public R<Map<String, Object>> getCheckStatusEnum() {
        Map<String, Object> enumMap = Map.of(
            "NORMAL", Map.of("value", FlowCheckStatus.NORMAL.getValue(), "label", FlowCheckStatus.NORMAL.getCode()),
            "DATE_ABNORMAL", Map.of("value", FlowCheckStatus.DATE_ABNORMAL.getValue(), "label", FlowCheckStatus.DATE_ABNORMAL.getCode()),
            "FLOW_MISSING", Map.of("value", FlowCheckStatus.FLOW_MISSING.getValue(), "label", FlowCheckStatus.FLOW_MISSING.getCode())
        );
        return R.ok(enumMap);
    }

    /**
     * 获取账套ID和名称映射
     *
     * @return 账套ID和名称映射
     */
    @GetMapping("/product-ids")
    public R<Map<String, String>> getProductIds() {
        List<CommonEntity> productList = stockFundExRightsCheckService.getProductIds();
        Map<String, String> productIds = productList.stream()
                .collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
        return R.ok(productIds);
    }

    /**
     * 根据账套ID获取证券类型列表
     *
     * @param productIds 账套ID列表
     * @return 证券类型列表
     */
    @PostMapping("/security-types")
    public R<Map<String, String>> getSecurityTypesByProductIds(@RequestBody List<String> productIds) {
        List<CommonEntity> securityTypeList = stockFundExRightsCheckService.getSecurityTypesByProductIds(productIds);
        Map<String, String> securityTypes = securityTypeList.stream()
                .collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
        return R.ok(securityTypes);
    }

    /**
     * 根据账套ID和证券类型获取证券代码列表
     *
     * @param request 查询条件
     * @return 证券代码列表
     */
    @PostMapping("/security-codes")
    public R<Map<String, String>> getSecurityCodesByConditions(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> productIds = (List<String>) request.get("productIds");
        @SuppressWarnings("unchecked")
        List<String> securityTypes = (List<String>) request.get("securityTypes");

        List<CommonEntity> securityCodeList = stockFundExRightsCheckService.getSecurityCodesByConditions(productIds, securityTypes);
        Map<String, String> securityCodes = securityCodeList.stream()
                .collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
        return R.ok(securityCodes);
    }

    /**
     * 查询同步时间
     *
     * @param dataDate 数据日期，可选，默认为当天日期
     * @return 同步时间记录
     */
    @GetMapping("/sync-time")
    public R<StockFundExRightsCheckSyncTime> getSyncTime(@RequestParam(value = "dataDate", required = false) String dataDate) {
        if (dataDate == null || dataDate.isEmpty()) {
            dataDate = DateUtil.today();
        }
        StockFundExRightsCheckSyncTime syncTime = syncTimeService.getSyncTimeByDataDate(dataDate);
        return R.ok(syncTime);
    }
}
