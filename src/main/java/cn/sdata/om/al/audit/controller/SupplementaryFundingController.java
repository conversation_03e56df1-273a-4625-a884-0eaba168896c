package cn.sdata.om.al.audit.controller;

import cn.sdata.om.al.audit.dto.AmountSumRequest;
import cn.sdata.om.al.audit.entity.SupplementaryFunding;
import cn.sdata.om.al.audit.service.SupplementaryFundingService;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.result.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 补仓资金Controller
 */
@RestController
@RequestMapping("audit/supplementary-funding")
@AllArgsConstructor
public class SupplementaryFundingController {

    private final SupplementaryFundingService supplementaryFundingService;

    /**
     * 分页查询补仓资金
     *
     * @param commonPageParam 分页参数
     * @return 分页结果
     */
    @PostMapping("page")
    public R<Page<SupplementaryFunding>> page(@RequestBody CommonPageParam<SupplementaryFunding> commonPageParam) {
        return supplementaryFundingService.pageQuery(commonPageParam);
    }

    /**
     * 新增补仓资金
     *
     * @param supplementaryFunding 补仓资金信息
     * @return 操作结果
     */
    @PostMapping("save")
    public R<String> save(@RequestBody SupplementaryFunding supplementaryFunding) {
        return supplementaryFundingService.saveSupplementaryFunding(supplementaryFunding);
    }

    /**
     * 更新补仓资金
     *
     * @param supplementaryFunding 补仓资金信息
     * @return 操作结果
     */
    @PostMapping("update")
    public R<String> update(@RequestBody SupplementaryFunding supplementaryFunding) {
        return supplementaryFundingService.updateSupplementaryFunding(supplementaryFunding);
    }

    /**
     * 根据ID查询补仓资金
     *
     * @param id 主键ID
     * @return 补仓资金信息
     */
    @GetMapping("get")
    public R<SupplementaryFunding> getById(@RequestParam("id") String id) {
        return supplementaryFundingService.getSupplementaryFundingById(id);
    }

    /**
     * 删除补仓资金（支持多选）
     *
     * @param ids ID列表
     * @return 操作结果
     */
    @PostMapping("delete")
    public R<String> delete(@RequestBody List<String> ids) {
        return supplementaryFundingService.deleteSupplementaryFunding(ids);
    }

    /**
     * 根据数据日期统计补仓资金数量
     *
     * @param dataDate 数据日期，格式：yyyy-MM-dd
     * @return 数量统计
     */
    @GetMapping("count")
    public R<Long> getCountByDate(@RequestParam("dataDate") String dataDate) {
        return supplementaryFundingService.getCountByDate(dataDate);
    }

    /**
     * 根据账套编号查询补仓资金列表
     *
     * @param productId 账套编号
     * @return 补仓资金列表
     */
    @GetMapping("list/by-product-id")
    public R<List<SupplementaryFunding>> getListByProductId(@RequestParam("productId") String productId) {
        return supplementaryFundingService.getListByProductId(productId);
    }

    /**
     * 根据日期范围、账套编号、流水类型计算金额总额
     * 流水类型为"存入"时金额为正数，"提取"时金额为负数
     *
     * @param request 计算请求参数
     * @return 金额总额
     */
    @PostMapping("calculate/amount-sum")
    public R<BigDecimal> calculateAmountSum(@RequestBody AmountSumRequest request) {
        return supplementaryFundingService.calculateAmountSum(request.getStartDate(), request.getEndDate(),
                request.getProductId(), request.getTransactionType());
    }


}
