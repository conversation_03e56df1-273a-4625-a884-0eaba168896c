package cn.sdata.om.al.audit.dto;

import lombok.Data;

import java.util.List;

/**
 * 金额总额计算请求参数
 */
@Data
public class AmountSumRequest {

    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    private String startDate;

    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    private String endDate;

    /**
     * 账套编号列表，支持多选
     */
    private List<String> productId;

    /**
     * 流水类型列表，支持多选
     */
    private List<String> transactionType;
}
