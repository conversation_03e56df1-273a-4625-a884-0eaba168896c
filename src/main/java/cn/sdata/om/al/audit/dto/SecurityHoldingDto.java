package cn.sdata.om.al.audit.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 证券持仓数据DTO
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
public class SecurityHoldingDto {

    /**
     * 产品ID
     */
    private String productId;

    private String productName;

    /**
     * 证券内部代码
     */
    private String securityInternalCode;

    /**
     * 证券类型
     */
    private String securityType;

    /**
     * 证券类型名称
     */
    private String securityTypeName;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 证券市场
     */
    private String securityMarket;

    /**
     * 证券市场名称
     */
    private String securityMarketName;

    /**
     * 证券持仓数量
     */
    private BigDecimal securityHolding;
}
