package cn.sdata.om.al.audit.dto;

import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import cn.sdata.om.al.entity.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 股票和开基除权价格检查查询参数DTO
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockFundExRightsCheckQueryDTO extends PageParam {

    /**
     * 查询日期
     */
    private String dataDate;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 账套编号
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 账套ID列表（多选）
     */
    private List<String> productIds;

    /**
     * 账套类型（估值时间）
     */
    private String valuationTime;

    /**
     * 证券类型
     */
    private String securityType;

    /**
     * 证券类型列表（多选）
     */
    private List<String> securityTypes;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 证券代码列表（多选）
     */
    private List<String> securityCodes;

    /**
     * 除权日
     */
    private String exRightsDate;

    /**
     * 红股发放日
     */
    private String bonusSharePaymentDate;

    /**
     * 红利发放日
     */
    private String dividendPaymentDate;

    /**
     * 除权流水检查结果
     */
    private FlowCheckStatus exRightsFlowCheck;

    /**
     * 红股流水检查结果
     */
    private FlowCheckStatus bonusShareFlowCheck;

    /**
     * 红利发放流水检查结果
     */
    private FlowCheckStatus dividendFlowCheck;
}
