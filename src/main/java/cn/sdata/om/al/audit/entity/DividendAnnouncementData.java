package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 分红公告信息实体类
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@TableName(value = "dividend_announcement_data")
public class DividendAnnouncementData {

    /**
     * 聚源数据ID
     */
    @TableId(value = "juyuan_id", type = IdType.INPUT)
    private String juyuanId;

    /**
     * 聚源内部代码
     */
    @TableField(value = "inner_code")
    private String innerCode;

    /**
     * 证券代码
     */
    @TableField(value = "security_code")
    private String securityCode;

    /**
     * 证券名称
     */
    @TableField(value = "security_name")
    private String securityName;

    /**
     * 证券类型
     */
    @TableField(value = "security_type")
    private String securityType;

    /**
     * 交易市场
     */
    @TableField(value = "security_market")
    private String securityMarket;

    /**
     * 数据来源类型
     */
    @TableField(value = "data_source_type")
    private String dataSourceType;

    /**
     * 登记日
     */
    @TableField(value = "registration_date")
    private LocalDate registrationDate;

    /**
     * 除权日
     */
    @TableField(value = "ex_rights_date")
    private LocalDate exRightsDate;

    /**
     * 红股发放日
     */
    @TableField(value = "bonus_share_payment_date")
    private LocalDate bonusSharePaymentDate;

    /**
     * 红利发放日
     */
    @TableField(value = "dividend_payment_date")
    private LocalDate dividendPaymentDate;

    /**
     * 红股分红比例
     */
    @TableField(value = "bonus_share_ratio")
    private BigDecimal bonusShareRatio;

    /**
     * 现金分红比例
     */
    @TableField(value = "cash_dividend_ratio")
    private BigDecimal cashDividendRatio;

    /**
     * 同步日期
     */
    @TableField(value = "sync_date")
    private LocalDate syncDate;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
