package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 月末存款及非标行情检查
 * @TableName month_end_and_non_standard
 */
@TableName(value ="month_end_and_non_standard")
@Data
public class MonthEndAndNonStandard implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 账套名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 证券类型
     */
    @TableField(value = "security_type")
    private String securityType;

    /**
     * 证券名称
     */
    @TableField(value = "security_name")
    private String securityName;

    /**
     * 证券代码
     */
    @TableField(value = "security_code")
    private String securityCode;

    /**
     * 证券成本
     */
    @TableField(value = "security_cost")
    private String securityCost;

    /**
     * 证券市值
     */
    @TableField(value = "security_market_value")
    private String securityMarketValue;

    /**
     * 估值与本金的比例
     */
    @TableField(value = "ROI")
    private String roi;

    /**
     * 比例异常提示
     */
    @TableField(value = "proportion_prompt")
    private String proportionPrompt;

    /**
     * 本月末估值价格
     */
    @TableField(value = "month_end_valuation_price")
    private String monthEndValuationPrice;

    /**
     * 上月末估值价格
     */
    @TableField(value = "valuation_price_end_last_month")
    private String valuationPriceEndLastMonth;

    /**
     * 价格波动率
     */
    @TableField(value = "price_volatility")
    private String priceVolatility;

    /**
     * 波动率提示
     */
    @TableField(value = "volatility_alert")
    private String volatilityAlert;

    /**
     * 付息日
     */
    @TableField(value = "interest_payment_date")
    private String interestPaymentDate;

    /**
     * 行权日
     */
    @TableField(value = "exercise_date")
    private String exerciseDate;

    /**
     * 到期日
     */
    @TableField(value = "due_date")
    private String dueDate;

    /**
     * 市场代码
     */
    @TableField(exist = false)
    private String vcScdm;
}