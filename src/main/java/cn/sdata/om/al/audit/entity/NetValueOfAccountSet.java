package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 多账套净值信息
 * @TableName net_value_of_account_set
 */
@TableName(value ="net_value_of_account_set")
@Data
public class NetValueOfAccountSet implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 账套名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 结构化名称
     */
    @TableField(value = "structured_name")
    private String structuredName;

    /**
     * 产品总份额
     */
    @TableField(value = "en_jjzfe")
    private String enJjzfe;

    /**
     * 产品总净值
     */
    @TableField(value = "en_jjzjz")
    private String enJjzjz;

    /**
     * 今日单位净值
     */
    @TableField(value = "en_jjdwjz")
    private String enJjdwjz;

    /**
     * 累计单位净值
     */
    @TableField(value = "en_ljjz")
    private String enLjjz;

    /**
     * 万份单位基金收益
     */
    @TableField(value = "en_dwjjsy")
    private String enDwjjsy;

    /**
     * 7日年化收益率(%)
     */
    @TableField(value = "en_fdsy")
    private String enFdsy;

    /**
     * 单位净值波动-单日差额
     */
    @TableField(value = "drce")
    private String drce;

    /**
     * 单位净值波动-单日涨跌幅（%）
     */
    @TableField(value = "drzdf")
    private String drzdf;

    /**
     * 当年收益率（年化）-单位净值（%）
     */
    @TableField(value = "dwjz_year")
    private String dwjzYear;

    /**
     * 当年收益率（年化）-累计单位净值（%）
     */
    @TableField(value = "ljdwjz_year")
    private String ljdwjzYear;
}