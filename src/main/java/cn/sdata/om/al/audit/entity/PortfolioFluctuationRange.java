package cn.sdata.om.al.audit.entity;

import cn.sdata.om.al.audit.enums.CompareType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 组合净值波动范围规则实体类
 */
@Data
@TableName("audit_portfolio_fluctuation_range")
public class PortfolioFluctuationRange implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 规则标识，同一规则下的记录标识相同
     */
    private String ruleKey;

    /**
     * 产品/账套ID
     */
    private String productId;

    /**
     * 产品/账套名称
     */
    private String productName;

    /**
     * 下限比较类型：> 或 >=
     */
    private CompareType lowerCompareType;

    /**
     * 上限比较类型：< 或 <=
     */
    private CompareType upperCompareType;

    /**
     * 下限值
     */
    private BigDecimal lowerLimit;

    /**
     * 上限值
     */
    private BigDecimal upperLimit;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
} 