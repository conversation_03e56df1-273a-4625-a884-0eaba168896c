package cn.sdata.om.al.audit.entity;

import cn.sdata.om.al.enums.MailStatus;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 净值异常波动和净值回撤类
 */
@Data
@TableName("audit_portfolio_net_value_fluctuation")
public class PortfolioNetValueFluctuation {

    @TableId
    private String id;

    // 账套编码
    @TableField(value = "product_id")
    private String productId;

    // 账套编号
    @TableField(value = "product_code")
    private String productCode;

    // 账套名称
    @TableField(value = "product_name")
    private String productName;

    // 数据日期
    @TableField(value = "data_date")
    private String dataDate;

    // 估值日期（T0/T1)
    @TableField(value = "valuation_time")
    private String valuationTime;

    // 净值异常波动（是/否）
    @TableField(value = "abnormal_fluctuation")
    private Integer abnormalFluctuation = 0;

    // 净值波动率
    @TableField(value = "fluctuation_rate")
    private BigDecimal fluctuationRate;

    // 净值回撤标识(是/否)
    @TableField(value = "retracement_flag")
    private Integer retracementFlag = 0;

    // 回撤原因
    @TableField(value = "retracement_reason")
    private String retracementReason;

    // 当日单位净值
    @TableField(value = "current_net_value")
    private BigDecimal currentNetValue;

    // 上一日单位净值
    @TableField(value = "previous_net_value")
    private BigDecimal previousNetValue;

    // 单位净值差值
    @TableField(value = "net_value_difference")
    private BigDecimal netValueDifference;

    // 当日万份收益
    @TableField(value = "current_ten_thousand_income")
    private BigDecimal currentTenThousandIncome;

    // 上一日万份收益
    @TableField(value = "previous_ten_thousand_income")
    private BigDecimal previousTenThousandIncome;

    // 万分收益差值
    @TableField(value = "ten_thousand_income_difference")
    private BigDecimal tenThousandIncomeDifference;

    // 邮件发送状态
    @TableField(value = "email_sent")
    private MailStatus emailSent = MailStatus.UNSENT;


    @TableField(exist = false)
    private String netValueDifferenceStr;
}
