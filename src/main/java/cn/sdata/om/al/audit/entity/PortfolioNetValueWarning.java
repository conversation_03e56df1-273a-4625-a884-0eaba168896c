package cn.sdata.om.al.audit.entity;

import cn.sdata.om.al.audit.enums.WarningStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.ValuationTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 组合产品净值预警类
 */
@Data
@TableName("audit_portfolio_net_value_warning")
public class PortfolioNetValueWarning {

    @TableId
    private String id;

    // 账套编码
    @TableField(value = "product_id")
    private String productId;

    // 账套编号
    @TableField(value = "product_code")
    private String productCode;

    // 账套名称
    @TableField(value = "product_name")
    private String productName;

    // 数据日期
    @TableField(value = "data_date")
    private String dataDate;

    // 净值数据
    @TableField(value = "net_value")
    private BigDecimal netValue;

    // 预警值
    @TableField(value = "warning_value")
    private BigDecimal warningValue;

    // 平仓值
    @TableField(value = "liquidation_value")
    private BigDecimal liquidationValue;

    // 罚没值
    @TableField(value = "forfeiture_value")
    private BigDecimal forfeitureValue;

    // 触发预警状态
    @TableField(value = "warning_triggered")
    private Integer warningTriggered = 0;

    // 应补仓资金
    @TableField(value = "required_funding")
    private BigDecimal requiredFunding;

    //份额
    @TableField(value = "fund_share")
    private BigDecimal fundShare;

    //触发预警预警范围
    @TableField(value = "warning_range")
    private BigDecimal warningRange = new BigDecimal("0.05");

    // 状态信息
    @TableField(value = "status_message")
    private WarningStatus statusMessage;

    // 邮件发送状态
    @TableField(value = "email_sent")
    private MailStatus emailSent = MailStatus.UNSENT;

    //估值时间 T0 T1
    @TableField(value = "valuation_time")
    private String valuationTime;
}

