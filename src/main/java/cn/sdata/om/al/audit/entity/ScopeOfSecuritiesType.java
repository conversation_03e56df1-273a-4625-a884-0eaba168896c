package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 证券类型波动范围
 * @TableName scope_of_securities_type
 */
@TableName(value ="scope_of_securities_type")
@Data
public class ScopeOfSecuritiesType implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 证券类型
     */
    @TableField(value = "sec_name")
    private String secType;

    /**
     * 估增下限
     */
    @TableField(value = "gz_low_limit")
    private BigDecimal gzLowLimit;

    /**
     * 估增上线
     */
    @TableField(value = "gz_up_limit")
    private BigDecimal gzUpLimit;

    /**
     * 价格波动率下限
     */
    @TableField(value = "jg_low_limit")
    private BigDecimal jgLowLimit;

    /**
     * 价格波动率上线
     */
    @TableField(value = "jg_up_limit")
    private BigDecimal jgUpLimit;

    /**
     *
     */
    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}