package cn.sdata.om.al.audit.entity;

import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 股票和开基除权价格检查实体类
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Data
@TableName(value = "audit_stock_fund_ex_rights_check")
public class StockFundExRightsCheck {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 账套名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 证券类型
     */
    @TableField(value = "security_type")
    private String securityType;

    /**
     * 证券名称
     */
    @TableField(value = "security_name")
    private String securityName;

    /**
     * 证券代码
     */
    @TableField(value = "security_code")
    private String securityCode;

    /**
     * 交易市场
     */
    @TableField(value = "security_market")
    private String securityMarket;

    /**
     * 交易市场名称
     */
    @TableField(value = "security_market_name")
    private String securityMarketName;

    /**
     * 证券类型名称
     */
    @TableField(value = "security_type_name")
    private String securityTypeName;

    /**
     * 证券内部代码
     */
    @TableField(value = "security_internal_code")
    private String securityInternalCode;



    /**
     * 登记日
     */
    @TableField(value = "registration_date")
    private String registrationDate;

    /**
     * 除权日
     */
    @TableField(value = "ex_rights_date")
    private String exRightsDate;

    /**
     * 红股发放日
     */
    @TableField(value = "bonus_share_payment_date")
    private String bonusSharePaymentDate;

    /**
     * 红利发放日
     */
    @TableField(value = "dividend_payment_date")
    private String dividendPaymentDate;

    /**
     * 红股分红比例
     */
    @TableField(value = "bonus_share_ratio")
    private BigDecimal bonusShareRatio;

    /**
     * 现金分红比例
     */
    @TableField(value = "cash_dividend_ratio")
    private BigDecimal cashDividendRatio;

    /**
     * 除权流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失
     */
    @TableField(value = "ex_rights_flow_check")
    private FlowCheckStatus exRightsFlowCheck;

    /**
     * 红股流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失
     */
    @TableField(value = "bonus_share_flow_check")
    private FlowCheckStatus bonusShareFlowCheck;

    /**
     * 红利发放流水检查结果：0-正常，1-流水日期异常，2-业务流水缺失
     */
    @TableField(value = "dividend_flow_check")
    private FlowCheckStatus dividendFlowCheck;

    /**
     * 除权流水是否存在：0-不存在，1-存在
     */
    @TableField(value = "ex_rights_flow_exists")
    private Boolean exRightsFlowExists;

    /**
     * 红股流水是否存在：0-不存在，1-存在
     */
    @TableField(value = "bonus_share_flow_exists")
    private Boolean bonusShareFlowExists;

    /**
     * 红利发放流水是否存在：0-不存在，1-存在
     */
    @TableField(value = "dividend_flow_exists")
    private Boolean dividendFlowExists;



    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 更新人
     */
    @TableField(value = "update_user")
    private String updateUser;
}
