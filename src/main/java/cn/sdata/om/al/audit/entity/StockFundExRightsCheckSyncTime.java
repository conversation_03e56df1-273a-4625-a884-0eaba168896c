package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 股票和开基除权价格检查同步时间记录实体类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@TableName(value = "audit_stock_fund_ex_rights_check_sync_time")
public class StockFundExRightsCheckSyncTime {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 同步时间
     */
    @TableField(value = "sync_time")
    private LocalDateTime syncTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
