package cn.sdata.om.al.audit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName tjjjz
 */
@TableName(value ="tjjjz")
@Data
public class Tjjjz {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 
     */
    @TableField(value = "L_ZTBH")
    private Integer lZtbh;

    /**
     * 
     */
    @TableField(value = "D_RQ")
    private Date dRq;

    /**
     * 
     */
    @TableField(value = "VC_JJDM")
    private String vcJjdm;

    /**
     * 
     */
    @TableField(value = "VC_JJMC")
    private String vcJjmc;

    /**
     * 
     */
    @TableField(value = "EN_JJZJZ")
    private BigDecimal enJjzjz;

    /**
     * 
     */
    @TableField(value = "EN_JJZFE")
    private BigDecimal enJjzfe;

    /**
     * 
     */
    @TableField(value = "EN_JJDWJZ")
    private BigDecimal enJjdwjz;

    /**
     * 
     */
    @TableField(value = "EN_JJSY")
    private BigDecimal enJjsy;

    /**
     * 
     */
    @TableField(value = "EN_DWJJSY")
    private BigDecimal enDwjjsy;

    /**
     * 
     */
    @TableField(value = "EN_NSYL")
    private BigDecimal enNsyl;

    /**
     * 
     */
    @TableField(value = "EN_XSFWF")
    private BigDecimal enXsfwf;

    /**
     * 
     */
    @TableField(value = "EN_LJJZ")
    private BigDecimal enLjjz;

    /**
     * 
     */
    @TableField(value = "EN_GPZJZ")
    private BigDecimal enGpzjz;

    /**
     * 
     */
    @TableField(value = "L_ISGZR")
    private String lIsgzr;

    /**
     * 
     */
    @TableField(value = "EN_TZGWF")
    private BigDecimal enTzgwf;

    /**
     * 
     */
    @TableField(value = "EN_KFPSY")
    private BigDecimal enKfpsy;

    /**
     * 
     */
    @TableField(value = "EN_FDSY")
    private BigDecimal enFdsy;

    /**
     * 
     */
    @TableField(value = "EN_30RNHSYL")
    private BigDecimal en30rnhsyl;

    /**
     * 
     */
    @TableField(value = "EN_JZNZZL")
    private BigDecimal enJznzzl;

    /**
     * 
     */
    @TableField(value = "L_SFQR")
    private Integer lSfqr;

    /**
     * 
     */
    @TableField(value = "EN_GPCW")
    private BigDecimal enGpcw;

    /**
     * 
     */
    @TableField(value = "EN_JZLJZZL")
    private BigDecimal enJzljzzl;

    /**
     * 
     */
    @TableField(value = "EN_YHCK")
    private BigDecimal enYhck;

    /**
     * 
     */
    @TableField(value = "EN_JSF")
    private BigDecimal enJsf;

    /**
     * 
     */
    @TableField(value = "EN_JSS")
    private BigDecimal enJss;

    /**
     * 
     */
    @TableField(value = "EN_GLF")
    private BigDecimal enGlf;

    /**
     * 
     */
    @TableField(value = "EN_MRJG")
    private BigDecimal enMrjg;

    /**
     * 
     */
    @TableField(value = "EN_MCJG")
    private BigDecimal enMcjg;

    /**
     * 
     */
    @TableField(value = "EN_FDFY")
    private BigDecimal enFdfy;

    /**
     * 
     */
    @TableField(value = "EN_TGF")
    private BigDecimal enTgf;
}