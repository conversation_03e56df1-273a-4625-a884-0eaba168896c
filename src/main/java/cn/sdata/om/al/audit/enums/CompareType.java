package cn.sdata.om.al.audit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 比较类型枚举
 */
@Getter
@AllArgsConstructor
public enum CompareType {
    
    /**
     * 大于
     */
    GREATER_THAN(">", "大于"),
    
    /**
     * 大于等于
     */
    GREATER_EQUAL(">=", "大于等于"),
    
    /**
     * 小于
     */
    LESS_THAN("<", "小于"),
    
    /**
     * 小于等于
     */
    LESS_EQUAL("<=", "小于等于");
    
    private final String symbol;
    private final String description;
    
    /**
     * 根据符号获取枚举
     */
    public static CompareType fromSymbol(String symbol) {
        for (CompareType type : values()) {
            if (type.symbol.equals(symbol)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown compare type symbol: " + symbol);
    }
    
    /**
     * 根据描述获取枚举
     */
    public static CompareType fromDescription(String description) {
        for (CompareType type : values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown compare type description: " + description);
    }
} 