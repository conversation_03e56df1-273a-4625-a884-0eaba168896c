package cn.sdata.om.al.audit.enums;

/**
 * 流水检查状态枚举
 *
 * <AUTHOR>
 * @date 2025-08-09
 */
public enum FlowCheckStatus {

    /**
     * 正常
     */
    NORMAL(0, "业务流水正常"),

    /**
     * 流水日期异常
     */
    DATE_ABNORMAL(1, "流水日期异常"),

    /**
     * 业务流水缺失
     */
    FLOW_MISSING(2, "业务流水缺失");

    private final Integer value;
    private final String code;

    FlowCheckStatus(Integer value, String code) {
        this.value = value;
        this.code = code;
    }

    public Integer getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
