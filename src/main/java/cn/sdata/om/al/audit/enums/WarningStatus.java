package cn.sdata.om.al.audit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态信息枚举
 */
@Getter
@AllArgsConstructor
public enum WarningStatus {
    /**
     * 无预警状态
     */
    NONE("无", "#A9AEB8"),

    /**
     * 接近预警值状态
     */
    NEAR_WARNING("接近预警值", "#E8BBBA"),

    /**
     * 已触发预警值状态
     */
    WARNING_TRIGGERED("已触发预警值", "#E48484"),

    /**
     * 接近平仓值状态
     */
    NEAR_LIQUIDATION("已触发预警值，接近平仓值", "#DE5151"),

    /**
     * 已触发平仓值状态
     */
    LIQUIDATION_TRIGGERED("已触发平仓值", "#D91C1C");
    private final String displayName;
    private final String color;
}
