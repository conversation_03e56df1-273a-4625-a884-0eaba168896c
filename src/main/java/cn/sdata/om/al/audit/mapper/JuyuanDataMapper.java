package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聚源数据查询Mapper
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Mapper
@DS("juyuan")
public interface JuyuanDataMapper {

    /**
     * 查询股票分红公告信息
     * 
     * @return 股票分红公告列表
     */
    List<DividendAnnouncementData> queryStockDividendData();

    /**
     * 查询港股分红公告信息
     * 
     * @return 港股分红公告列表
     */
    List<DividendAnnouncementData> queryHkStockDividendData();

    /**
     * 查询债券分红公告信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 债券分红公告列表
     */
    List<DividendAnnouncementData> queryBondDividendData(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询基金分红公告信息
     *
     * @return 基金分红公告列表
     */
    List<DividendAnnouncementData> queryFundDividendData();

    /**
     * 查询股票指标分红公告信息
     *
     * @return 股票指标分红公告列表
     */
    List<DividendAnnouncementData> queryStockIndicatorDividendData();

    /**
     * 查询所有债券分红公告信息（不限制日期）
     *
     * @return 所有债券分红公告列表
     */
    List<DividendAnnouncementData> queryAllBondDividendData();


}
