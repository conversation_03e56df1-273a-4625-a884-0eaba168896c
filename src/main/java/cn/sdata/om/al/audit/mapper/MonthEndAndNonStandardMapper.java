package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【month_end_and_non_standard(月末存款及非标行情检查)】的数据库操作Mapper
* @createDate 2025-07-29 17:15:20
* @Entity generator.domain.MonthEndAndNonStandard
*/
public interface MonthEndAndNonStandardMapper extends BaseMapper<MonthEndAndNonStandard> {

    List<MonthEndAndNonStandardVO> selectInfo(@Param("vo") MonthEndAndNonStandardVO vo);

    String seleDataDate();

    String getSecondDay(@Param("date") String date);

    List<KeyValueVO> secListInfo(@Param("dataDate") String dataDate, @Param("securityTypes") List<String> securityTypes);
}




