package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【net_value_of_account_set(多账套净值信息)】的数据库操作Mapper
* @createDate 2025-07-23 10:09:52
* @Entity generator.domain.NetValueOfAccountSet
*/
public interface NetValueOfAccountSetMapper extends BaseMapper<NetValueOfAccountSet> {

    List<NetValueOfAccountSetVO> selectNetValueFluctuation(@Param("today") String today);

    List<NetValueOfAccountSetVO> selectLastYearNetValue(@Param("today") String today);

    Integer getActualNumberOfDaysInIntervalTime(@Param("startDay") String startDay, @Param("today") String today);

    Page<NetValueOfAccountSetVO> selectPageInfo(@Param("page") Page<NetValueOfAccountSetVO> page, @Param("vo") NetValueOfAccountSetVO vo);

    List<String> selectProductGroups(@Param("productGroups") List<String> productGroups);

    List<KeyValueVO> selectAccountInfo();
}




