package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.audit.dto.StockFundExRightsCheckQueryDTO;
import cn.sdata.om.al.audit.entity.StockFundExRightsCheck;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.audit.vo.StockFundExRightsCheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * 股票和开基除权价格检查Mapper
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Mapper
public interface StockFundExRightsCheckMapper extends BaseMapper<StockFundExRightsCheck> {

    /**
     * 分页查询股票和开基除权价格检查数据
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    Page<StockFundExRightsCheckVO> selectPageInfo(Page<StockFundExRightsCheckVO> page, @Param("query") StockFundExRightsCheckQueryDTO query);

    /**
     * 根据数据日期删除数据
     * 
     * @param dataDate 数据日期
     * @return 删除记录数
     */
    int deleteByDataDate(@Param("dataDate") String dataDate);

    /**
     * 批量插入数据
     *
     * @param list 数据列表
     * @return 插入记录数
     */
    int batchInsert(@Param("list") List<StockFundExRightsCheck> list);

    /**
     * 获取账套ID和名称映射
     *
     * @return 账套ID和名称映射
     */
    List<CommonEntity> getProductIds();

    /**
     * 根据账套ID获取证券类型列表
     *
     * @param productIds 账套ID列表
     * @return 证券类型列表
     */
    List<CommonEntity> getSecurityTypesByProductIds(@Param("productIds") List<String> productIds);

    /**
     * 根据账套ID和证券类型获取证券代码列表
     *
     * @param productIds 账套ID列表
     * @param securityTypes 证券类型列表
     * @return 证券代码列表
     */
    List<CommonEntity> getSecurityCodesByConditions(@Param("productIds") List<String> productIds,
                                              @Param("securityTypes") List<String> securityTypes);

    /**
     * 获取汇总统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param valuationTime 估值时间，可选
     * @return 异常数据总数
     */
    Long getSummaryStatistics(@Param("startDate") String startDate,
                             @Param("endDate") String endDate,
                             @Param("valuationTime") String valuationTime);

    /**
     * 获取异常数据统计
     * 
     * @param dataDate 数据日期
     * @return 异常统计结果
     */
    Map<String, Object> getAbnormalStatistics(@Param("dataDate") String dataDate);
}
