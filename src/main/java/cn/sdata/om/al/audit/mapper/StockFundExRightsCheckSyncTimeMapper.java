package cn.sdata.om.al.audit.mapper;

import cn.sdata.om.al.audit.entity.StockFundExRightsCheckSyncTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 股票和开基除权价格检查同步时间记录 Mapper 接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Mapper
public interface StockFundExRightsCheckSyncTimeMapper extends BaseMapper<StockFundExRightsCheckSyncTime> {

    /**
     * 根据数据日期查询同步时间
     *
     * @param dataDate 数据日期
     * @return 同步时间记录
     */
    StockFundExRightsCheckSyncTime selectByDataDate(@Param("dataDate") String dataDate);

    /**
     * 插入或更新同步时间记录
     *
     * @param dataDate 数据日期
     * @param syncTime 同步时间
     * @return 影响行数
     */
    int insertOrUpdateSyncTime(@Param("dataDate") String dataDate, @Param("syncTime") LocalDateTime syncTime);
}
