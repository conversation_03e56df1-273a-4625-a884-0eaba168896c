package cn.sdata.om.al.audit.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.sdata.om.al.audit.entity.Tjjjz;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tjjjz】的数据库操作Mapper
* @createDate 2025-07-22 19:35:45
* @Entity generator.domain.Tjjjz
*/
@Mapper
public interface TjjjzMapper extends BaseMapper<Tjjjz> {

//    List<Tjjjz> syncTjjjzInfo(@Param("startDate") String startDate, @Param("today") String today);
}




