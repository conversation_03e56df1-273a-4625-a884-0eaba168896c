package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.result.R;

import java.util.List;

/**
 * 分红公告数据同步服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface DividendAnnouncementSyncService {

    /**
     * 同步聚源分红公告数据
     * 
     * @param syncDate 同步日期
     * @return 同步结果
     */
    R<String> syncJuyuanDividendData(String syncDate);

    /**
     * 从聚源数据库获取股票分红公告数据
     * 
     * @return 股票分红公告数据列表
     */
    List<DividendAnnouncementData> fetchStockDividendData();

    /**
     * 从聚源数据库获取港股分红公告数据
     * 
     * @return 港股分红公告数据列表
     */
    List<DividendAnnouncementData> fetchHkStockDividendData();

    /**
     * 从聚源数据库获取债券分红公告数据
     * 
     * @return 债券分红公告数据列表
     */
    List<DividendAnnouncementData> fetchBondDividendData();

    /**
     * 从聚源数据库获取基金分红公告数据
     *
     * @return 基金分红公告数据列表
     */
    List<DividendAnnouncementData> fetchFundDividendData();

    /**
     * 获取股票指标分红公告数据
     *
     * @return 股票指标分红公告数据列表
     */
    List<DividendAnnouncementData> fetchStockIndicatorDividendData();

    /**
     * 单独同步所有债券聚源数据
     *
     * @return 同步结果
     */
    R<String> syncAllBondDividendData();



    /**
     * 过滤新增数据（避免重复插入）
     * 
     * @param allData 所有数据
     * @return 新增数据列表
     */
    List<DividendAnnouncementData> filterNewData(List<DividendAnnouncementData> allData);
}
