package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【net_value_of_account_set(多账套净值信息)】的数据库操作Service
* @createDate 2025-07-23 10:09:52
*/
public interface NetValueOfAccountSetService extends IService<NetValueOfAccountSet> {

    void syncNetValueFluctuation(String today);

    Page<NetValueOfAccountSetVO> selectPageInfo(NetValueOfAccountSetVO commonPageParam);

    String getDate();

    void setComparisonDay(String comparisonDay);

    List<String> listAll();

    Boolean manualSync(String startDate,String endDate);

    List<String> listAllAccountName();
}
