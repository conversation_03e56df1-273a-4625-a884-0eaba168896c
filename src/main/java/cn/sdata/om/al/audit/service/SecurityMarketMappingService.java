package cn.sdata.om.al.audit.service;

import java.util.List;

/**
 * 证券市场代码映射服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface SecurityMarketMappingService {

    /**
     * 将聚源市场代码转换为估值市场代码
     * 
     * @param juyuanMarketCode 聚源市场代码
     * @return 估值市场代码列表（港股可能对应多个估值市场）
     */
    List<String> convertJuyuanToValuationMarket(String juyuanMarketCode);

    /**
     * 将估值市场代码转换为聚源市场代码
     * 
     * @param valuationMarketCode 估值市场代码
     * @return 聚源市场代码
     */
    String convertValuationToJuyuanMarket(String valuationMarketCode);

    /**
     * 获取所有市场代码映射关系
     * 
     * @return 映射关系说明
     */
    String getAllMappingInfo();
}
