package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.dto.StockFundExRightsCheckQueryDTO;
import cn.sdata.om.al.audit.entity.StockFundExRightsCheck;
import cn.sdata.om.al.audit.vo.StockFundExRightsCheckVO;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.result.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 股票和开基除权价格检查服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
public interface StockFundExRightsCheckService extends IService<StockFundExRightsCheck> {

    /**
     * 分页查询股票和开基除权价格检查数据
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<StockFundExRightsCheckVO> selectPageInfo(StockFundExRightsCheckQueryDTO query);

    /**
     * 执行股票和开基除权价格检查
     * 
     * @param dataDate 数据日期
     * @return 执行结果
     */
    R<String> executeExRightsCheck(String dataDate);

    /**
     * 同步聚源除权数据
     * 
     * @param dataDate 数据日期
     * @return 同步结果
     */
    R<String> syncJuyuanExRightsData(String dataDate);

    /**
     * 获取汇总统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param valuationTime 估值时间，可选
     * @return 异常数据总数
     */
    Long getSummaryStatistics(String startDate, String endDate, String valuationTime);

    /**
     * 获取异常数据统计
     * 
     * @param dataDate 数据日期
     * @return 异常统计结果
     */
    Map<String, Object> getAbnormalStatistics(String dataDate);

    /**
     * 重新检查指定数据
     * 
     * @param id 检查记录ID
     * @return 检查结果
     */
    R<String> recheckById(String id);

    /**
     * 批量重新检查
     *
     * @param dataDate 数据日期
     * @return 检查结果
     */
    R<String> batchRecheck(String dataDate);

    /**
     * 获取账套ID和名称映射
     *
     * @return 账套ID和名称映射
     */
    List<CommonEntity> getProductIds();

    /**
     * 根据账套ID获取证券类型列表
     *
     * @param productIds 账套ID列表
     * @return 证券类型列表
     */
    List<CommonEntity> getSecurityTypesByProductIds(List<String> productIds);

    /**
     * 根据账套ID和证券类型获取证券代码列表
     *
     * @param productIds 账套ID列表
     * @param securityTypes 证券类型列表
     * @return 证券代码列表
     */
    List<CommonEntity> getSecurityCodesByConditions(List<String> productIds, List<String> securityTypes);
}
