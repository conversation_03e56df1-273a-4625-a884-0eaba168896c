package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.entity.StockFundExRightsCheckSyncTime;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
 * 股票和开基除权价格检查同步时间记录服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface StockFundExRightsCheckSyncTimeService extends IService<StockFundExRightsCheckSyncTime> {

    /**
     * 根据数据日期查询同步时间
     *
     * @param dataDate 数据日期
     * @return 同步时间记录
     */
    StockFundExRightsCheckSyncTime getSyncTimeByDataDate(String dataDate);

    /**
     * 记录同步时间
     *
     * @param dataDate 数据日期
     * @param syncTime 同步时间
     */
    void recordSyncTime(String dataDate, LocalDateTime syncTime);

    /**
     * 记录当前时间为同步时间
     *
     * @param dataDate 数据日期
     */
    void recordCurrentSyncTime(String dataDate);
}
