package cn.sdata.om.al.audit.service;

import cn.sdata.om.al.audit.entity.SupplementaryFunding;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.mapper.audit.SupplementaryFundingMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CommonQueryService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 补仓资金Service类
 */
@Service
@RequiredArgsConstructor
public class SupplementaryFundingService extends ServiceImpl<SupplementaryFundingMapper, SupplementaryFunding> {

    private final CommonQueryService<SupplementaryFunding> commonQueryService;

    /**
     * 分页查询补仓资金
     *
     * @param commonPageParam 分页参数
     * @return 分页结果
     */
    public R<Page<SupplementaryFunding>> pageQuery(CommonPageParam<SupplementaryFunding> commonPageParam) {
        try {
            Page<SupplementaryFunding> page = new Page<>();
            page.setSize(commonPageParam.getSize());
            page.setCurrent(commonPageParam.getCurrent());
            Page<SupplementaryFunding> supplementaryFundingPage = commonQueryService.commonPage(commonPageParam, SupplementaryFunding.class);
            return R.ok(supplementaryFundingPage);
        } catch (Exception e) {
            return R.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增补仓资金
     *
     * @param supplementaryFunding 补仓资金信息
     * @return 操作结果
     */
    public R<String> saveSupplementaryFunding(SupplementaryFunding supplementaryFunding) {
        try {
            // 参数校验
            if (!StringUtils.hasText(supplementaryFunding.getProductId())) {
                return R.failed("账套编号不能为空");
            }
            if (!StringUtils.hasText(supplementaryFunding.getProductName())) {
                return R.failed("账套名称不能为空");
            }
            if (!StringUtils.hasText(supplementaryFunding.getDataDate())) {
                return R.failed("数据日期不能为空");
            }
            if (supplementaryFunding.getAmount() == null) {
                return R.failed("金额不能为空");
            }
            if (!StringUtils.hasText(supplementaryFunding.getTransactionType())) {
                return R.failed("流水类型不能为空");
            }

            // 设置创建时间和更新时间，ID由MyBatis-Plus自动生成
            String user = SecureUtil.currentUserName();
            Date now = new Date();
            supplementaryFunding.setCreateTime(now);
            supplementaryFunding.setCreateUser(user);
            supplementaryFunding.setUpdateTime(now);
            supplementaryFunding.setUpdateUser(user);

            boolean result = this.save(supplementaryFunding);
            return result ? R.ok("新增成功") : R.failed("新增失败");
        } catch (Exception e) {
            return R.failed("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新补仓资金
     *
     * @param supplementaryFunding 补仓资金信息
     * @return 操作结果
     */
    public R<String> updateSupplementaryFunding(SupplementaryFunding supplementaryFunding) {
        try {
            // 参数校验
            if (!StringUtils.hasText(supplementaryFunding.getId())) {
                return R.failed("ID不能为空");
            }

            // 检查记录是否存在
            SupplementaryFunding existing = this.getById(supplementaryFunding.getId());
            if (existing == null) {
                return R.failed("记录不存在");
            }

            // 设置更新时间
            supplementaryFunding.setUpdateTime(new Date());
            String user = SecureUtil.currentUserName();
            supplementaryFunding.setUpdateUser(user);
            boolean result = this.updateById(supplementaryFunding);
            return result ? R.ok("更新成功") : R.failed("更新失败");
        } catch (Exception e) {
            return R.failed("更新失败：" + e.getMessage());
        }
    }
    /**
     * 根据ID查询补仓资金
     *
     * @param id 主键ID
     * @return 补仓资金信息
     */
    public R<SupplementaryFunding> getSupplementaryFundingById(String id) {
        try {
            if (!StringUtils.hasText(id)) {
                return R.failed("ID不能为空");
            }

            SupplementaryFunding supplementaryFunding = this.getById(id);
            return supplementaryFunding != null ? R.ok(supplementaryFunding) : R.failed("数据不存在");
        } catch (Exception e) {
            return R.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 删除补仓资金（支持多选）
     *
     * @param ids ID列表
     * @return 操作结果
     */
    @Transactional
    public R<String> deleteSupplementaryFunding(List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return R.failed("请选择要删除的数据");
            }

            // 检查是否有无效的ID
            for (String id : ids) {
                if (!StringUtils.hasText(id)) {
                    return R.failed("存在无效的ID");
                }
            }

            boolean result = this.removeByIds(ids);
            return result ? R.ok("删除成功") : R.failed("删除失败");
        } catch (Exception e) {
            return R.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据数据日期统计补仓资金数量
     *
     * @param dataDate 数据日期，格式：yyyy-MM-dd
     * @return 数量统计
     */
    public R<Long> getCountByDate(String dataDate) {
        try {
            if (!StringUtils.hasText(dataDate)) {
                return R.failed("数据日期不能为空");
            }

            LambdaQueryWrapper<SupplementaryFunding> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SupplementaryFunding::getDataDate, dataDate);
            long count = this.count(queryWrapper);
            return R.ok(count);
        } catch (Exception e) {
            return R.failed("统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据账套编号查询补仓资金列表
     *
     * @param productId 账套编号
     * @return 补仓资金列表
     */
    public R<List<SupplementaryFunding>> getListByProductId(String productId) {
        try {
            if (!StringUtils.hasText(productId)) {
                return R.failed("账套编号不能为空");
            }

            LambdaQueryWrapper<SupplementaryFunding> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SupplementaryFunding::getProductId, productId);
            queryWrapper.orderByDesc(SupplementaryFunding::getCreateTime);
            List<SupplementaryFunding> list = this.list(queryWrapper);
            return R.ok(list);
        } catch (Exception e) {
            return R.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据日期范围、账套编号、流水类型计算金额总额
     * 流水类型为"存入"时金额为正数，"提取"时金额为负数
     *
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param productIds 账套编号列表，支持多选
     * @param transactionTypes 流水类型列表，支持多选
     * @return 金额总额
     */
    public R<BigDecimal> calculateAmountSum(String startDate, String endDate, List<String> productIds, List<String> transactionTypes) {
        try {
            // 参数校验
            if (!StringUtils.hasText(startDate)) {
                return R.failed("开始日期不能为空");
            }
            if (!StringUtils.hasText(endDate)) {
                return R.failed("结束日期不能为空");
            }


            // 构建查询条件
            LambdaQueryWrapper<SupplementaryFunding> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(SupplementaryFunding::getDataDate, startDate);
            queryWrapper.le(SupplementaryFunding::getDataDate, endDate);
            if (productIds != null && !productIds.isEmpty()) {
                queryWrapper.in(SupplementaryFunding::getProductId, productIds);
            }
            if (transactionTypes != null && !transactionTypes.isEmpty()) {
                queryWrapper.in(SupplementaryFunding::getTransactionType, transactionTypes);
            }
            // 查询符合条件的记录
            List<SupplementaryFunding> list = this.list(queryWrapper);

            if (list.isEmpty()) {
                return R.ok(BigDecimal.ZERO);
            }

            // 计算金额总额
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (SupplementaryFunding funding : list) {
                if (funding.getAmount() != null) {
                    String transactionType = funding.getTransactionType();
                    if ("存入".equals(transactionType)) {
                        // 存入为正数
                        totalAmount = totalAmount.add(funding.getAmount());
                    } else if ("提取".equals(transactionType)) {
                        // 提取为负数
                        totalAmount = totalAmount.subtract(funding.getAmount());
                    } else {
                        // 其他类型按原值计算
                        totalAmount = totalAmount.add(funding.getAmount());
                    }
                }
            }

            return R.ok(totalAmount);
        } catch (Exception e) {
            return R.failed("计算失败：" + e.getMessage());
        }
    }



}