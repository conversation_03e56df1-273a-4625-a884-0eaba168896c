package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.DividendAnnouncementData;
import cn.sdata.om.al.audit.mapper.DividendAnnouncementDataMapper;
import cn.sdata.om.al.audit.mapper.JuyuanDataMapper;
import cn.sdata.om.al.audit.service.DividendAnnouncementSyncService;
import cn.sdata.om.al.result.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分红公告数据同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendAnnouncementSyncServiceImpl implements DividendAnnouncementSyncService {

    private final JuyuanDataMapper juyuanDataMapper;
    private final DividendAnnouncementDataMapper dividendAnnouncementDataMapper;

    @Override
    public R<String> syncJuyuanDividendData(String syncDate) {
        try {
            log.info("开始同步聚源分红公告数据，同步日期：{}", syncDate);

            // 1. 从聚源数据库获取所有类型的分红公告数据
            List<DividendAnnouncementData> allData = new ArrayList<>();
            
            // 获取股票分红数据
            List<DividendAnnouncementData> stockData = fetchStockDividendData();
            if (CollectionUtil.isNotEmpty(stockData)) {
                allData.addAll(stockData);
                log.info("获取股票分红公告数据：{} 条", stockData.size());
            }

            // 获取港股分红数据
            List<DividendAnnouncementData> hkStockData = fetchHkStockDividendData();
            if (CollectionUtil.isNotEmpty(hkStockData)) {
                allData.addAll(hkStockData);
                log.info("获取港股分红公告数据：{} 条", hkStockData.size());
            }

            // 获取债券分红数据（使用日期过滤，从今天到明天）
            String nextDate = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(syncDate), 1));
            List<DividendAnnouncementData> bondData = fetchBondDividendData(syncDate, nextDate);
            if (CollectionUtil.isNotEmpty(bondData)) {
                allData.addAll(bondData);
                log.info("获取债券分红公告数据：{} 条", bondData.size());
            }

            // 获取基金分红数据
            List<DividendAnnouncementData> fundData = fetchFundDividendData();
            if (CollectionUtil.isNotEmpty(fundData)) {
                allData.addAll(fundData);
                log.info("获取基金分红公告数据：{} 条", fundData.size());
            }

            // 获取股票指标分红数据
            List<DividendAnnouncementData> stockIndicatorData = fetchStockIndicatorDividendData();
            if (CollectionUtil.isNotEmpty(stockIndicatorData)) {
                allData.addAll(stockIndicatorData);
                log.info("获取股票指标分红公告数据：{} 条", stockIndicatorData.size());
            }

            if (CollectionUtil.isEmpty(allData)) {
                log.info("未获取到聚源分红公告数据");
                return R.ok("未获取到分红公告数据");
            }



            // 2. 过滤新增数据（避免重复插入）
            List<DividendAnnouncementData> newData = filterNewData(allData);
            log.info("数据过滤结果：总数据量 {} 条，过滤后新增数据 {} 条", allData.size(), newData.size());

            if (CollectionUtil.isEmpty(newData)) {
                log.info("没有新的分红公告数据需要同步");
                return R.ok("没有新数据需要同步");
            }

            // 3. 设置同步日期和时间戳
            LocalDateTime now = LocalDateTime.now();
            LocalDate syncLocalDate = LocalDate.parse(syncDate);
            newData.forEach(data -> {
                data.setSyncDate(syncLocalDate);
                data.setCreateTime(now);
                data.setUpdateTime(now);
            });

            // 4. 批量保存新数据
            ((DividendAnnouncementSyncServiceImpl) AopContext.currentProxy()).saveDividendDataInTransaction(newData);
            log.info("聚源分红公告数据同步完成，成功入库 {} 条记录", newData.size());
            
            return R.ok("同步完成，新增记录数：" + newData.size());

        } catch (Exception e) {
            log.error("同步聚源分红公告数据失败", e);
            return R.failed("同步失败：" + e.getMessage());
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchStockDividendData() {
        try {
            return juyuanDataMapper.queryStockDividendData();
        } catch (Exception e) {
            log.error("获取股票分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchHkStockDividendData() {
        try {
            return juyuanDataMapper.queryHkStockDividendData();
        } catch (Exception e) {
            log.error("获取港股分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchBondDividendData() {
        try {
            // 默认查询当天到明天的数据
            String today = DateUtil.formatDate(new java.util.Date());
            String tomorrow = DateUtil.formatDate(DateUtil.offsetDay(new java.util.Date(), 1));
            return juyuanDataMapper.queryBondDividendData(today, tomorrow);
        } catch (Exception e) {
            log.error("获取债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据日期范围获取债券分红公告数据
     */
    public List<DividendAnnouncementData> fetchBondDividendData(String startDate, String endDate) {
        try {
            return juyuanDataMapper.queryBondDividendData(startDate, endDate);
        } catch (Exception e) {
            log.error("获取债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchStockIndicatorDividendData() {
        try {
            return juyuanDataMapper.queryStockIndicatorDividendData();
        } catch (Exception e) {
            log.error("获取股票指标分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public R<String> syncAllBondDividendData() {
        log.info("开始单独同步所有债券聚源数据");

        try {
            // 1. 获取所有债券分红数据
            List<DividendAnnouncementData> bondData = fetchAllBondDividendData();
            if (CollectionUtil.isEmpty(bondData)) {
                log.info("无债券分红公告数据");
                return R.ok("无债券分红公告数据");
            }

            log.info("获取所有债券分红公告数据：{} 条", bondData.size());

            // 2. 过滤新增数据（避免重复插入）
            List<DividendAnnouncementData> newData = filterNewData(bondData);
            log.info("债券数据过滤结果：总数据量 {} 条，过滤后新增数据 {} 条", bondData.size(), newData.size());

            if (CollectionUtil.isEmpty(newData)) {
                log.info("没有新的债券分红公告数据需要同步");
                return R.ok("没有新的债券数据需要同步");
            }

            // 3. 设置同步日期和时间戳
            LocalDateTime now = LocalDateTime.now();
            LocalDate syncLocalDate = LocalDate.now();
            newData.forEach(data -> {
                data.setSyncDate(syncLocalDate);
                data.setCreateTime(now);
                data.setUpdateTime(now);
            });

            // 4. 批量保存新数据
            ((DividendAnnouncementSyncServiceImpl) AopContext.currentProxy()).saveDividendDataInTransaction(newData);
            log.info("所有债券聚源分红公告数据同步完成，成功入库 {} 条记录", newData.size());

            return R.ok("所有债券数据同步成功，新增 " + newData.size() + " 条记录");

        } catch (Exception e) {
            log.error("所有债券聚源数据同步失败", e);
            return R.failed("所有债券数据同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有债券分红公告数据
     */
    public List<DividendAnnouncementData> fetchAllBondDividendData() {
        try {
            return juyuanDataMapper.queryAllBondDividendData();
        } catch (Exception e) {
            log.error("获取所有债券分红公告数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DividendAnnouncementData> fetchFundDividendData() {
        try {
            return juyuanDataMapper.queryFundDividendData();
        } catch (Exception e) {
            log.error("获取基金分红公告数据失败", e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<DividendAnnouncementData> filterNewData(List<DividendAnnouncementData> allData) {
        if (CollectionUtil.isEmpty(allData)) {
            return new ArrayList<>();
        }

        try {
            // 提取所有聚源ID
            List<String> juyuanIds = allData.stream()
                    .map(DividendAnnouncementData::getJuyuanId)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(juyuanIds)) {
                return new ArrayList<>();
            }

            // 分批查询已存在的ID（避免IN子句过长）
            List<String> existingIds = new ArrayList<>();
            int batchSize = 1000;
            for (int i = 0; i < juyuanIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, juyuanIds.size());
                List<String> batchIds = juyuanIds.subList(i, endIndex);
                List<String> batchExistingIds = dividendAnnouncementDataMapper.queryExistingJuyuanIds(batchIds);
                if (CollectionUtil.isNotEmpty(batchExistingIds)) {
                    existingIds.addAll(batchExistingIds);
                }
            }

            // 过滤出新数据
            return allData.stream()
                    .filter(data -> !existingIds.contains(data.getJuyuanId()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("过滤新数据时发生异常", e);
            // 如果过滤失败，返回空列表避免重复数据
            return new ArrayList<>();
        }
    }

    /**
     * 在事务中保存分红公告数据
     *
     * @param dataList 数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDividendDataInTransaction(List<DividendAnnouncementData> dataList) {
        log.info("开始批量保存分红公告数据，数据量：{} 条", dataList.size());
        dividendAnnouncementDataMapper.batchInsert(dataList);
        log.info("批量保存分红公告数据完成");
    }
}
