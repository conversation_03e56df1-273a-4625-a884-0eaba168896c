package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.ScopeOfSecuritiesType;
import cn.sdata.om.al.audit.service.ScopeOfSecuritiesTypeService;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.OtherContacts;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.mapper.AccountInformationMapper;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.OtherContactsService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.PageUtil;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import cn.sdata.om.al.audit.mapper.MonthEndAndNonStandardMapper;
import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【month_end_and_non_standard(月末存款及非标行情检查)】的数据库操作Service实现
* @createDate 2025-07-29 17:15:20
*/
@Service
@AllArgsConstructor
@Slf4j
public class MonthEndAndNonStandardServiceImpl extends ServiceImpl<MonthEndAndNonStandardMapper, MonthEndAndNonStandard>
    implements MonthEndAndNonStandardService{

    private ValuationDBMapper valuationDBMapper;

    private AccountInformationService accountInformationService;

    private final MarketTradeDayService marketTradeDayService;

    private ScopeOfSecuritiesTypeService securitiesTypeService;

    private MailInfoService mailInfoService;

    private final OtherContactsService otherContactsService;

    private static final BiMap<String,String> SECURITY_TYPE_MAPPING = new BiMap<>(new HashMap<>());
    static {
//        SECURITY_TYPE_MAPPING.put("存款", "1");
        SECURITY_TYPE_MAPPING.put("债权投资计划", "15");
        SECURITY_TYPE_MAPPING.put("股权投资计划", "16");
        SECURITY_TYPE_MAPPING.put("信托计划", "6");
        SECURITY_TYPE_MAPPING.put("资产支持计划", "11");
    }
    @Override
    public Page<MonthEndAndNonStandardVO> selectPageInfo(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = baseMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new Page<>(1L, 20L);
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }
        List<MonthEndAndNonStandardVO> infoList = this.baseMapper.selectInfo(vo);
        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();

        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity(),(v1, v2) -> v1));
        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        for (MonthEndAndNonStandardVO record : infoList) {
            String securityType = record.getSecurityType();
            if (rangeMap.containsKey(securityType)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(securityType);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = record.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        record.setProportionPrompt(1);
                    }else{
                        record.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = record.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        record.setVolatilityAlert(1);
                    }else{
                        record.setVolatilityAlert(0);
                    }
                }
            }
            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(record.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(record.getVolatilityAlert());
            }
            if (flag){
                result.add(record);
            }
        }

        return PageUtil.listToPage(result, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
    }


    @Override
    public long countInfo(String string) {
        MonthEndAndNonStandardVO vo = new MonthEndAndNonStandardVO();
        vo.setDataDate(string);
        List<AccountInformation> accountList = accountInformationService.list();
        vo.setProductNames(accountList.stream().map(AccountInformation::getId).collect(Collectors.toList()));

        vo.setSecurityTypes(new ArrayList<>(SECURITY_TYPE_MAPPING.values()));
        vo.setFlag("1");

        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        vo.setDataDate(lastDay.toString());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        if (CollUtil.isEmpty(thisMonth)){
            vo.setDataDate(thisMonthTradeDay);
            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        }
        return CollUtil.isEmpty(thisMonth) ? 0L : thisMonth.size();
    }

    @Override
    public long countInfoV2(String string) {
        LambdaQueryWrapper<MonthEndAndNonStandard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(MonthEndAndNonStandard::getDataDate, string);
        List<MonthEndAndNonStandard> thisMonth = this.baseMapper.selectList(queryWrapper);
        return CollUtil.isEmpty(thisMonth) ? 0L : thisMonth.size();
    }

    @Override
    public void syncMonthEndAndNonStandardInfo(String startDate, String endDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 转换为 YearMonth（只保留到月份）
        YearMonth start = YearMonth.parse(startDate.substring(0, 7), formatter);
        YearMonth end = YearMonth.parse(endDate.substring(0, 7), formatter);

        // 循环遍历月份
        List<String> months = new ArrayList<>();
        YearMonth current = start;
        while (!current.isAfter(end)) {
            months.add(current.format(formatter));
            current = current.plusMonths(1);
        }

        for (String month : months) {
            MonthEndAndNonStandardVO vo = new MonthEndAndNonStandardVO();
            vo.setDataDate(month);
            List<MonthEndAndNonStandard> standards = syncMonthEndAndNonStandardInfoList(vo);
            if (CollUtil.isNotEmpty(standards)){
                LambdaQueryWrapper<MonthEndAndNonStandard> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.likeRight(MonthEndAndNonStandard::getDataDate, month);
                this.remove(queryWrapper);
                this.saveOrUpdateBatch(standards);
            }
        }



    }

    private List<MonthEndAndNonStandard> syncMonthEndAndNonStandardInfoList(MonthEndAndNonStandardVO vo) {
        List<AccountInformation> accountList = accountInformationService.list();
        Map<String, AccountInformation> accountInformationMap = accountList.stream()
                .collect(Collectors.toMap(AccountInformation::getId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(vo.getProductNames())){
            List<String> productIds = new ArrayList<>();
            Map<String, AccountInformation> informationMap = accountList.stream()
                    .collect(Collectors.toMap(AccountInformation::getFullProductName, x -> x, (a, b) -> a));
            for (String productName : vo.getProductNames()) {
                if (informationMap.containsKey(productName)){
                    String id = informationMap.get(productName).getId();
                    productIds.add(id);
                }
            }
            vo.setProductNames(productIds);
        }else{
            vo.setProductNames(accountList.stream().map(AccountInformation::getId).collect(Collectors.toList()));
        }

        if (CollUtil.isNotEmpty(vo.getSecurityTypes())){

            List<String> securityTypes = new ArrayList<>();
            for (String securityType : vo.getSecurityTypes()) {
                if ("存款".equals(securityType)){
                    vo.setFlag("1");
                    continue;
                }
                securityTypes.add(SECURITY_TYPE_MAPPING.get(securityType));
            }
            vo.setSecurityTypes(securityTypes);
        }else {
            vo.setSecurityTypes(new ArrayList<>(SECURITY_TYPE_MAPPING.values()));
            vo.setFlag("1");
        }


        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDay = marketTradeDayService.isMarketTradeDay(lastDay.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");

        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        if(isFirstDay){
            vo.setDataDate(lastDay.toString());
        }else {
            vo.setDataDate(thisMonthTradeDay);
        }
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);



        // 上个月的最后一天
        LocalDate lastDayPrevMonth = yearMonth.minusMonths(1).atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDayLast = marketTradeDayService.isMarketTradeDay(lastDayPrevMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");
        //该月上一交易日
        String lastDayPrevMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDayPrevMonth.toString());
        // 根据数据日期获取上月末估值价格
        if (isFirstDayLast){
            vo.setDataDate(lastDayPrevMonth.toString());
        }else{
            vo.setDataDate(lastDayPrevMonthTradeDay);
        }
        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
                .collect(Collectors.toMap(m -> m.getProductId() +"-"+ m.getSecurityCode(),Function.identity(),(v1, v2) -> v1));

        for (MonthEndAndNonStandard record : thisMonth) {
            String securityMarketValue = StrUtil.isBlank(record.getSecurityMarketValue()) ? "0" : record.getSecurityMarketValue();
            String securityCost = record.getSecurityCost();
            //估值与本金的比例 （估增=市值-成本）/成本
            if (StrUtil.isBlank(securityCost) || "0".equals(securityCost)){
                record.setRoi("0");
            }else {
                BigDecimal roi = new BigDecimal(securityMarketValue).subtract(new BigDecimal(securityCost))
                        .divide(new BigDecimal(securityCost), 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                record.setRoi(roi.toPlainString());
            }

            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId()+"-"+record.getSecurityCode());
            //本月末估值价格
            String monthEndValuationPrice = StrUtil.isBlank(record.getMonthEndValuationPrice()) ? "0" : record.getMonthEndValuationPrice();


            if (ObjectUtil.isNotNull(lastMonthInfo)){
                //上月末估值价格
                String valuationPriceEndLastMonth = lastMonthInfo.getMonthEndValuationPrice();
                record.setValuationPriceEndLastMonth(valuationPriceEndLastMonth);
                if (StrUtil.isBlank(valuationPriceEndLastMonth) || "0".equals(valuationPriceEndLastMonth)){
                    record.setPriceVolatility("0");
                }else{
                    //价格波动率 （本月-上月）/上月
                    BigDecimal priceVolatility = new BigDecimal(monthEndValuationPrice)
                            .subtract(new BigDecimal(valuationPriceEndLastMonth))
                            .divide(new BigDecimal(valuationPriceEndLastMonth), 10, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    record.setPriceVolatility(priceVolatility.toPlainString());
                }
            }


            AccountInformation accountInformation = accountInformationMap.get(record.getProductId());
            if (accountInformation != null){
                record.setProductName(accountInformation.getFullProductName());
                record.setProductName(accountInformation.getFullProductName());
            }



            String securityType = record.getSecurityType();
            String type = SECURITY_TYPE_MAPPING.getKey(securityType);
            if (type == null){
                type = "存款";
                record.setSecurityCode("");
            }
            record.setSecurityType(type);


        }
        return thisMonth;
    }

    @Override
    public Page<MonthEndAndNonStandardVO> selectPageInfoV2(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = valuationDBMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new Page<>(1L, 20L);
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }

        List<AccountInformation> accountList = accountInformationService.list();
        Map<String, AccountInformation> accountInformationMap = accountList.stream()
                .collect(Collectors.toMap(AccountInformation::getId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(vo.getProductNames())){
            List<String> productIds = new ArrayList<>();
            Map<String, AccountInformation> informationMap = accountList.stream()
                    .collect(Collectors.toMap(AccountInformation::getFullProductName, x -> x, (a, b) -> a));
            for (String productName : vo.getProductNames()) {
                if (informationMap.containsKey(productName)){
                    String id = informationMap.get(productName).getId();
                    productIds.add(id);
                }
            }
            vo.setProductNames(productIds);
        }else{
            vo.setProductNames(accountList.stream().map(AccountInformation::getId).collect(Collectors.toList()));
        }

        if (CollUtil.isNotEmpty(vo.getSecurityTypes())){

            List<String> securityTypes = new ArrayList<>();
            for (String securityType : vo.getSecurityTypes()) {
                if ("存款".equals(securityType)){
                    vo.setFlag("1");
                    continue;
                }
                securityTypes.add(SECURITY_TYPE_MAPPING.get(securityType));
            }
            vo.setSecurityTypes(securityTypes);
        }else {
            vo.setSecurityTypes(new ArrayList<>(SECURITY_TYPE_MAPPING.values()));
            vo.setFlag("1");
        }


        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDay = marketTradeDayService.isMarketTradeDay(lastDay.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");

        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        if(isFirstDay){
            vo.setDataDate(lastDay.toString());
        }else {
            vo.setDataDate(thisMonthTradeDay);
        }
//        vo.setDataDate(lastDay.toString());
//        log.info("根据月末自然日获取本月末估值价格,查询数据日期：{}", vo.getDataDate());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        if (CollUtil.isEmpty(thisMonth)){
//            vo.setDataDate(thisMonthTradeDay);
//            log.info("根据月末最后一个交易日获取本月末估值价格,查询数据日期：{}", vo.getDataDate());
//            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        }



        // 上个月的最后一天
        LocalDate lastDayPrevMonth = yearMonth.minusMonths(1).atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDayLast = marketTradeDayService.isMarketTradeDay(lastDayPrevMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");

        //该月上一交易日
        String lastDayPrevMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDayPrevMonth.toString());
        // 根据数据日期获取上月末估值价格
        if (isFirstDayLast){
            vo.setDataDate(lastDayPrevMonth.toString());
        }else{
            vo.setDataDate(lastDayPrevMonthTradeDay);
        }
//        log.info("根据上月末自然日获取上月末估值价格,查询数据日期：{}", vo.getDataDate());
        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        if (CollUtil.isEmpty(lastMonth)){
//            vo.setDataDate(lastDayPrevMonthTradeDay);
//            log.info("根据上月末最后一个交易日获取上月末估值价格,查询数据日期：{}", vo.getDataDate());
//            lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        }
        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
                .collect(Collectors.toMap(m -> m.getProductId() +"-"+ m.getSecurityCode(),Function.identity(),(v1, v2) -> v1));


        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();

        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity(),(v1, v2) -> v1));


        for (MonthEndAndNonStandard record : thisMonth) {

            String securityMarketValue = StrUtil.isBlank(record.getSecurityMarketValue()) ? "0" : record.getSecurityMarketValue();
            String securityCost = record.getSecurityCost();
            //估值与本金的比例 （估增=市值-成本）/成本
            if (StrUtil.isBlank(securityCost) || "0".equals(securityCost)){
                record.setRoi("0");
            }else {
                BigDecimal roi = new BigDecimal(securityMarketValue).subtract(new BigDecimal(securityCost))
                        .divide(new BigDecimal(securityCost), 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                record.setRoi(roi.toPlainString());
            }


            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId()+"-"+record.getSecurityCode());
            //本月末估值价格
            String monthEndValuationPrice = StrUtil.isBlank(record.getMonthEndValuationPrice()) ? "0" : record.getMonthEndValuationPrice();



            if (ObjectUtil.isNotNull(lastMonthInfo)){
                //上月末估值价格
                String valuationPriceEndLastMonth = lastMonthInfo.getMonthEndValuationPrice();
                record.setValuationPriceEndLastMonth(valuationPriceEndLastMonth);
                if (StrUtil.isBlank(valuationPriceEndLastMonth) || "0".equals(valuationPriceEndLastMonth)){
                    record.setPriceVolatility("0");
                }else{
                    //价格波动率 （本月-上月）/上月
                    BigDecimal priceVolatility = new BigDecimal(monthEndValuationPrice)
                            .subtract(new BigDecimal(valuationPriceEndLastMonth))
                            .divide(new BigDecimal(valuationPriceEndLastMonth), 10, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    record.setPriceVolatility(priceVolatility.toPlainString());
                }
            }




            MonthEndAndNonStandardVO recordVO = new MonthEndAndNonStandardVO();
            BeanUtil.copyProperties(record, recordVO);
            AccountInformation accountInformation = accountInformationMap.get(record.getProductId());
            if (accountInformation != null){
                recordVO.setProductName(accountInformation.getFullProductName());
                recordVO.setProductCode(accountInformation.getProductCode());
            }




            String securityType = record.getSecurityType();
            String type = SECURITY_TYPE_MAPPING.getKey(securityType);
            if (type == null){
                type = "存款";
            }
            recordVO.setSecurityType(type);
            if (rangeMap.containsKey(type)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(type);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = recordVO.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        recordVO.setProportionPrompt(1);
                    }else{
                        recordVO.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = recordVO.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        recordVO.setVolatilityAlert(1);
                    }else{
                        recordVO.setVolatilityAlert(0);
                    }
                }
            }

            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(recordVO.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(recordVO.getVolatilityAlert());
            }
            if (flag){
                result.add(recordVO);
            }

        }

        return PageUtil.listToPage(result, Math.toIntExact(vo.getCurrent()), Math.toIntExact(vo.getSize()));
    }


    @Override
    public List<MonthEndAndNonStandardVO> startExport(MonthEndAndNonStandardVO vo) {
        if (StrUtil.isBlank(vo.getDataDate())){
            String record = valuationDBMapper.seleDataDate();
            if (ObjectUtil.isNull(record)){
                return new ArrayList<>();
            }
            String result = record.substring(0, 7);
            vo.setDataDate(result);
        }else {
            vo.setDataDate(vo.getDataDate().substring(0, 7));
        }

        List<AccountInformation> accountList = accountInformationService.list();
        Map<String, AccountInformation> accountInformationMap = accountList.stream()
                .collect(Collectors.toMap(AccountInformation::getId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(vo.getProductNames())){
            List<String> productIds = new ArrayList<>();
            Map<String, AccountInformation> informationMap = accountList.stream()
                    .collect(Collectors.toMap(AccountInformation::getFullProductName, x -> x, (a, b) -> a));
            for (String productName : vo.getProductNames()) {
                if (informationMap.containsKey(productName)){
                    String id = informationMap.get(productName).getId();
                    productIds.add(id);
                }
            }
            vo.setProductNames(productIds);
        }else{
            vo.setProductNames(accountList.stream().map(AccountInformation::getId).collect(Collectors.toList()));
        }

        if (CollUtil.isNotEmpty(vo.getSecurityTypes())){

            List<String> securityTypes = new ArrayList<>();
            for (String securityType : vo.getSecurityTypes()) {
                if ("存款".equals(securityType)){
                    vo.setFlag("1");
                    continue;
                }
                securityTypes.add(SECURITY_TYPE_MAPPING.get(securityType));
            }
            vo.setSecurityTypes(securityTypes);
        }else {
            vo.setSecurityTypes(new ArrayList<>(SECURITY_TYPE_MAPPING.values()));
            vo.setFlag("1");
        }


        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));

        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDay = marketTradeDayService.isMarketTradeDay(lastDay.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");

        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        if(isFirstDay){
            vo.setDataDate(lastDay.toString());
        }else {
            vo.setDataDate(thisMonthTradeDay);
        }
//        vo.setDataDate(lastDay.toString());
//        log.info("根据月末自然日获取本月末估值价格,查询数据日期：{}", vo.getDataDate());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        if (CollUtil.isEmpty(thisMonth)){
//            vo.setDataDate(thisMonthTradeDay);
//            log.info("根据月末最后一个交易日获取本月末估值价格,查询数据日期：{}", vo.getDataDate());
//            thisMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        }



        // 上个月的最后一天
        LocalDate lastDayPrevMonth = yearMonth.minusMonths(1).atEndOfMonth();
        //判断月末是否为交易日
        boolean isFirstDayLast = marketTradeDayService.isMarketTradeDay(lastDayPrevMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "01");

        //该月上一交易日
        String lastDayPrevMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDayPrevMonth.toString());
        // 根据数据日期获取上月末估值价格
        if (isFirstDayLast){
            vo.setDataDate(lastDayPrevMonth.toString());
        }else{
            vo.setDataDate(lastDayPrevMonthTradeDay);
        }
//        log.info("根据上月末自然日获取上月末估值价格,查询数据日期：{}", vo.getDataDate());
        List<MonthEndAndNonStandard> lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        if (CollUtil.isEmpty(lastMonth)){
//            vo.setDataDate(lastDayPrevMonthTradeDay);
//            log.info("根据上月末最后一个交易日获取上月末估值价格,查询数据日期：{}", vo.getDataDate());
//            lastMonth = valuationDBMapper.selectMonthEndAndNonStandardInfo(vo);
//        }
        Map<String, MonthEndAndNonStandard> lastMonthMap = lastMonth.stream()
                .collect(Collectors.toMap(m -> m.getProductId() +"-"+ m.getSecurityCode(),Function.identity(),(v1, v2) -> v1));


        //证券类型范围 lowLimit upLimit
        List<ScopeOfSecuritiesType> list = securitiesTypeService.list();

        List<MonthEndAndNonStandardVO> result = new ArrayList<>();
        Map<String, ScopeOfSecuritiesType> rangeMap = list.stream()
                .collect(Collectors.toMap(ScopeOfSecuritiesType::getSecType, Function.identity(),(v1, v2) -> v1));


        for (MonthEndAndNonStandard record : thisMonth) {

            String securityMarketValue = StrUtil.isBlank(record.getSecurityMarketValue()) ? "0" : record.getSecurityMarketValue();
            String securityCost = record.getSecurityCost();
            //估值与本金的比例 （估增=市值-成本）/成本
            if (StrUtil.isBlank(securityCost) || "0".equals(securityCost)){
                record.setRoi("0");
            }else {
                BigDecimal roi = new BigDecimal(securityMarketValue).subtract(new BigDecimal(securityCost))
                        .divide(new BigDecimal(securityCost), 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                record.setRoi(roi.toPlainString());
            }


            MonthEndAndNonStandard lastMonthInfo = lastMonthMap.get(record.getProductId()+"-"+record.getSecurityCode());
            //本月末估值价格
            String monthEndValuationPrice = StrUtil.isBlank(record.getMonthEndValuationPrice()) ? "0" : record.getMonthEndValuationPrice();



            if (ObjectUtil.isNotNull(lastMonthInfo)){
                //上月末估值价格
                String valuationPriceEndLastMonth = lastMonthInfo.getMonthEndValuationPrice();
                record.setValuationPriceEndLastMonth(valuationPriceEndLastMonth);
                if (StrUtil.isBlank(valuationPriceEndLastMonth) || "0".equals(valuationPriceEndLastMonth)){
                    record.setPriceVolatility("0");
                }else{
                    //价格波动率 （本月-上月）/上月
                    BigDecimal priceVolatility = new BigDecimal(monthEndValuationPrice)
                            .subtract(new BigDecimal(valuationPriceEndLastMonth))
                            .divide(new BigDecimal(valuationPriceEndLastMonth), 10, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                    record.setPriceVolatility(priceVolatility.toPlainString());
                }
            }




            MonthEndAndNonStandardVO recordVO = new MonthEndAndNonStandardVO();
            BeanUtil.copyProperties(record, recordVO);
            AccountInformation accountInformation = accountInformationMap.get(record.getProductId());
            if (accountInformation != null){
                recordVO.setProductName(accountInformation.getFullProductName());
                recordVO.setProductCode(accountInformation.getProductCode());
            }




            String securityType = record.getSecurityType();
            String type = SECURITY_TYPE_MAPPING.getKey(securityType);
            if (type == null){
                type = "存款";
            }
            recordVO.setSecurityType(type);
            if (rangeMap.containsKey(type)){
                ScopeOfSecuritiesType scopeOfSecuritiesType = rangeMap.get(type);
                BigDecimal gzLowLimit = scopeOfSecuritiesType.getGzLowLimit();
                BigDecimal gzUpLimit = scopeOfSecuritiesType.getGzUpLimit();
                //比例异常提示
                String roi = recordVO.getRoi();
                if (StrUtil.isNotBlank(roi)){
                    BigDecimal roiBigDecimal = new BigDecimal(roi);
                    if (roiBigDecimal.compareTo(gzLowLimit) <= 0 || roiBigDecimal.compareTo(gzUpLimit) >= 0){
                        recordVO.setProportionPrompt(1);
                    }else{
                        recordVO.setProportionPrompt(0);
                    }
                }
                BigDecimal jgLowLimit = scopeOfSecuritiesType.getJgLowLimit();
                BigDecimal jgUpLimit = scopeOfSecuritiesType.getJgUpLimit();
                String priceVolatility = recordVO.getPriceVolatility();
                if (StrUtil.isNotBlank(priceVolatility)){
                    BigDecimal priceVolatilityBigDecimal = new BigDecimal(priceVolatility);
                    if (priceVolatilityBigDecimal.compareTo(jgLowLimit) <= 0 || priceVolatilityBigDecimal.compareTo(jgUpLimit) >= 0){
                        recordVO.setVolatilityAlert(1);
                    }else{
                        recordVO.setVolatilityAlert(0);
                    }
                }
            }

            boolean flag = true;
            if (vo.getProportionPrompt() != null){
                flag = vo.getProportionPrompt().equals(recordVO.getProportionPrompt());
            }
            if (vo.getVolatilityAlert() != null){
                flag = vo.getVolatilityAlert().equals(recordVO.getVolatilityAlert());
            }
            if (flag){
                result.add(recordVO);
            }

        }
        return result;
    }

    @Override
    public List<KeyValueVO> secListInfo(String dataDate,List<String> securityTypes) {
        if (securityTypes != null && securityTypes.size() == 1 && securityTypes.get(0).equals("存款")){
            return new ArrayList<>();
        }
        if (StrUtil.isBlank(dataDate)) {
            YearMonth now = YearMonth.now();
            LocalDate lastDayPrevMonth = now.atEndOfMonth();
//            String record = valuationDBMapper.seleDataDate();
//            if (ObjectUtil.isNull(record)) {
//                return new ArrayList<>();
//            }
            dataDate = lastDayPrevMonth.toString().substring(0, 7);
        }


        MonthEndAndNonStandardVO vo = new MonthEndAndNonStandardVO();
        vo.setDataDate(dataDate);
        List<AccountInformation> accountList = accountInformationService.list();
        vo.setProductNames(accountList.stream().map(AccountInformation::getId).collect(Collectors.toList()));

        if (CollUtil.isNotEmpty(securityTypes)){

            List<String> types = new ArrayList<>();
            for (String securityType : securityTypes) {
                if (SECURITY_TYPE_MAPPING.containsKey(securityType)){
                    types.add(SECURITY_TYPE_MAPPING.get(securityType));
                }
            }
            vo.setSecurityTypes(types);
        }else {
            vo.setSecurityTypes(new ArrayList<>(SECURITY_TYPE_MAPPING.values()));
        }

        // 解析字符串为 YearMonth
        YearMonth yearMonth = YearMonth.parse(vo.getDataDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        // 获取该月的最后一天
        LocalDate lastDay = yearMonth.atEndOfMonth();
        //该月上一交易日
        String thisMonthTradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(lastDay.toString());
//        先用月末自然日去查询每个证券的估值价格，如果没有获取到的就用月末最后一个交易日进行查询。每月数据只有一个，查询到以后就退出下一次的查询
        //根据数据日期获取本月末估值价格
        vo.setDataDate(lastDay.toString());
        List<MonthEndAndNonStandard> thisMonth = valuationDBMapper.selectSecInfo(vo);
        if (CollUtil.isEmpty(thisMonth)){
            vo.setDataDate(thisMonthTradeDay);
            thisMonth = valuationDBMapper.selectSecInfo(vo);
        }
        if (CollUtil.isEmpty(thisMonth)) {
            return new ArrayList<>();
        }

        return thisMonth.stream()
                .filter(item -> item.getSecurityCode() != null && item.getSecurityName() != null)
                .map(item -> new KeyValueVO(item.getSecurityCode(), item.getSecurityName()))
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<KeyValueVO> secListInfoV2(String dataDate,List<String> securityTypes) {
        return this.baseMapper.secListInfo(dataDate,securityTypes);
    }

    @Override
    public void sendMonthEndAndNonStandardEmail() {
        //月初第二个银行间工作日，汇总处标红，并发送邮件提醒
        boolean flag = isSecondBankWorkdayToday();
        if(!flag){
            return;
        }
        LocalDate today = LocalDate.now();
        // 上个月
        LocalDate lastMonthDate = today.minusMonths(1);

        // 只取月份 (MM)
        String lastMonth = lastMonthDate.format(DateTimeFormatter.ofPattern("MM"));

        MonthEndAndNonStandardVO vo = new MonthEndAndNonStandardVO();
        vo.setDataDate(lastMonthDate.toString());
        Page<MonthEndAndNonStandardVO> page = selectPageInfoV2(vo);
        String dataDate = "";
        if (CollUtil.isNotEmpty(page.getRecords())){
            List<MonthEndAndNonStandardVO> infoList = page.getRecords();
            dataDate = infoList.get(0).getDataDate();
        }else {
            // 解析字符串为 YearMonth
            YearMonth yearMonth = YearMonth.parse(lastMonthDate.toString().substring(0, 7), DateTimeFormatter.ofPattern("yyyy-MM"));

            // 获取该月的最后一天
            LocalDate lastDay = yearMonth.atEndOfMonth();
            dataDate = lastDay.toString();
        }
//        邮件内容及发送时间：
//        邮件内容：标题为“月末存款及非标行情检查提醒—YYYYMMDD”（YYYYMMDD为月末数据日期）
//        正文：请估值核算人员当天进行XX月末数据的月末存款及非标行情检查。
//        发送时间为每月初第二个工作日（同银行间工作日）上午十一点。
        List<SendMailInfo> sendMailInfos = new ArrayList<>();
        SendMailInfo sendMailInfo = new SendMailInfo();

        sendMailInfo.setSubject("月末存款及非标行情检查提醒—".concat(dataDate));
        sendMailInfo.setContent("请估值核算人员当天进行"+lastMonth+"月末数据的月末存款及非标行情检查。");
        OtherContacts otherContacts = otherContactsService.lambdaQuery().eq(OtherContacts::getName, "月末存款及非标行情检查").one();
        if (otherContacts == null){
            return;
        }

        List<String> recipient = List.of(otherContacts.getRecipient().split(";"));
        sendMailInfo.setRecipient(recipient);
        if (StrUtil.isNotBlank(otherContacts.getRecipientCc())){
            sendMailInfo.setCc(new ArrayList<>(List.of(otherContacts.getRecipientCc().split(";"))));
        }else {
            sendMailInfo.setCc(new ArrayList<>());
        }
        sendMailInfos.add(sendMailInfo);
        List<SendMailInfo> mailInfos = mailInfoService.doSendMailInfo(sendMailInfos);

    }

    /**
     * 判断今天是否是月初第二个银行间工作日
     */
    @Override
    public boolean isSecondBankWorkdayToday() {
        YearMonth now = YearMonth.now();
        // 格式化为 yyyyMM
        String yearMonth = now.format(DateTimeFormatter.ofPattern("yyyyMM"));

        // 查询当月第二个银行间工作日（yyyyMMdd）
        String secondDay = this.baseMapper.getSecondDay(yearMonth);

        if (secondDay == null) {
            return false; // 数据库没有查询到
        }

        // 转 LocalDate
        LocalDate secondLocalDate = LocalDate.parse(secondDay, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate today = LocalDate.now();

        return today.equals(secondLocalDate);
    }


}




