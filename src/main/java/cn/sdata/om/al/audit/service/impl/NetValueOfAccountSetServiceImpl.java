package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.Tjjjz;
import cn.sdata.om.al.audit.mapper.NetValueOfAccountSetMapper;
import cn.sdata.om.al.audit.service.NetValueOfAccountSetService;
import cn.sdata.om.al.audit.service.TjjjzService;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.mapper.NetValueDisclosureMapper;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.vo.NetValueOfAccountSetVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;
import lombok.AllArgsConstructor;
import org.quartz.JobDataMap;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【net_value_of_account_set(多账套净值信息)】的数据库操作Service实现
* @createDate 2025-07-23 10:09:52
*/
@Service
@AllArgsConstructor
public class NetValueOfAccountSetServiceImpl extends ServiceImpl<NetValueOfAccountSetMapper, NetValueOfAccountSet>
    implements NetValueOfAccountSetService {

    private static final DecimalFormat formatter = new DecimalFormat("#,##0.########");
    private final MarketTradeDayService marketTradeDayService;
    private final NetValueDisclosureMapper netValueDisclosureMapper;
    private final TjjjzService tjjzService;



    @Override
    public Page<NetValueOfAccountSetVO> selectPageInfo(NetValueOfAccountSetVO vo) {
        Page<NetValueOfAccountSetVO> page = new Page<>(vo.getCurrent(), vo.getSize());
        if (CollUtil.isNotEmpty(vo.getProductGroups())){
            List<String> productGroups = this.baseMapper.selectProductGroups(vo.getProductGroups());
            List<String> result = productGroups.stream()
                    .flatMap(line -> Arrays.stream(line.split(",")))
                    .map(String::trim)  // 去除空格（如有）
                    .collect(Collectors.toList());
            vo.setProductGroups(result);
        }
        this.baseMapper.selectPageInfo(page, vo);
        //获取对比日 前一自然日
        String comparisonDay = vo.getComparisonDay();
        if (StrUtil.isBlank(comparisonDay)){
            comparisonDay = netValueDisclosureMapper.getDate("comparisonDay");
            if (StrUtil.isBlank(comparisonDay)){
                return page;
            }
        }
        LocalDate yesterdayLocal = LocalDate.parse(comparisonDay, DateTimeFormatter.ofPattern("yyyy-MM-dd")).minusDays(1);
        String comparisonDayYesterday = yesterdayLocal.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<NetValueOfAccountSetVO> tradeDayList = this.baseMapper.selectNetValueFluctuation(comparisonDayYesterday);
        Map<String, NetValueOfAccountSetVO> tradeDayListValueMap = tradeDayList.stream().collect(Collectors.toMap(NetValueOfAccountSetVO::getProductId, x -> x, (a, b) -> a));

        List<String> collect = page.getRecords().stream().map(current -> current.getDataDate()).distinct().collect(Collectors.toList());

        Map<String,Integer> actualDaysMap = new HashMap(collect.size());
        for (String s : collect) {
            Integer actualDays = this.baseMapper.getActualNumberOfDaysInIntervalTime(comparisonDayYesterday, s);
            actualDaysMap.put(s, actualDays);
        }

        for (NetValueOfAccountSetVO current : page.getRecords()) {
            //获取区间时间实际天数
            Integer actualDays = actualDaysMap.get(current.getDataDate());

            BigDecimal todayDwjz = new BigDecimal(Optional.ofNullable(current.getEnJjdwjz()).orElse("0"));

            NetValueOfAccountSetVO netValueOfAccountSetVO = tradeDayListValueMap.get(current.getProductId());
            BigDecimal intervalEndDwjz = BigDecimal.ONE;
            BigDecimal intervalEndLjjz = BigDecimal.ONE;
            if (netValueOfAccountSetVO != null) {
                intervalEndDwjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO.getEnJjdwjz()).orElse("1"));
                intervalEndLjjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO.getEnLjjz()).orElse("1"));
            }

            BigDecimal divide = intervalEndDwjz.multiply(BigDecimal.valueOf(365)).divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP);
            if (divide.compareTo(BigDecimal.ZERO) == 0){
                current.setDwjzInterval("0");
            }else{
                BigDecimal dwjzYear = todayDwjz.subtract(intervalEndDwjz)
                        .divide(divide, 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                current.setDwjzInterval(dwjzYear.stripTrailingZeros().toPlainString());
            }



            // 区间段收益率（年化）-累计单位净值（%）
            BigDecimal todayLjjz = new BigDecimal(Optional.ofNullable(current.getEnLjjz()).orElse("0"));

            BigDecimal divide1 = intervalEndLjjz.multiply(BigDecimal.valueOf(365)).divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP);
            if (divide1.compareTo(BigDecimal.ZERO) == 0){
                current.setLjdwjzInterval("0");
            }else{
                BigDecimal ljdwjzYear = todayLjjz.subtract(intervalEndLjjz)
                        .divide(divide1, 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                current.setLjdwjzInterval(ljdwjzYear.stripTrailingZeros().toPlainString());
            }
            String dateType = current.getDateType();
            if ("1".equals(dateType)){
                current.setDateType("交易日");
            }
            if ("2".equals(dateType)){
                current.setDateType("自然日");
            }

            current.setEnJjzfe(formatWithThousands(current.getEnJjzfe()));
            current.setEnJjzjz(formatWithThousands(current.getEnJjzjz()));
            current.setEnJjdwjz(formatWithThousands(current.getEnJjdwjz()));
            current.setEnLjjz(formatWithThousands(current.getEnLjjz()));
            current.setEnDwjjsy(formatWithThousands(current.getEnDwjjsy()));
            current.setDrce(formatWithThousands(current.getDrce()));


        }
        return page;
    }


    @Override
    public String getDate() {
        NetValueOfAccountSet netValueOfAccountSet = new NetValueOfAccountSet();
        netValueOfAccountSet.setId("1");
        NetValueOfAccountSet byId = this.getById(netValueOfAccountSet);
        if (ObjectUtil.isNull(byId)){
            return "2023-12-31";
        }
        return byId.getDataDate();
    }


    @Override
    public List<String> listAll() {
        return this.baseMapper.selectAccountInfo().stream()
                .map(x -> String.valueOf(x.getKey())).distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> listAllAccountName() {
        return this.baseMapper.selectAccountInfo().stream()
                .map(x -> String.valueOf(x.getValue())).distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<CommonEntity> getPortfolioList() {
        List<KeyValueVO> allAccountInfo = this.baseMapper.selectAccountInfo();

        // 返回所有组合产品信息，格式与accountSet/list相同
        return allAccountInfo.stream()
                .map(x -> new CommonEntity(String.valueOf(x.getKey()), String.valueOf(x.getValue())))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Boolean manualSync(String startDate,String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        if (StrUtil.isBlank(startDate)) {
            startDate = LocalDate.now().format(formatter);
        }
        if (StrUtil.isBlank(endDate)) {
            endDate = LocalDate.now().format(formatter);
        }

        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        List<String> dateList = new ArrayList<>();
        while (!start.isAfter(end)) {
            dateList.add(start.format(formatter));
            start = start.plusDays(1);
        }
        if(CollUtil.isEmpty(dateList)){
            return false;
        }
        for (String dataDate : dateList) {
            tjjzService.syncValuationInfo(new JobDataMap(),dataDate);
            syncNetValueFluctuation(dataDate);
        }

        return true;
    }

    @Override
    public void setComparisonDay(String comparisonDay) {
        NetValueOfAccountSet netValueOfAccountSet = new NetValueOfAccountSet();
        netValueOfAccountSet.setId("1");
        NetValueOfAccountSet byId = this.getById(netValueOfAccountSet);
        if (ObjectUtil.isNull(byId)){
            byId = new NetValueOfAccountSet();
            byId.setId("1");
            byId.setDataDate(comparisonDay);
            this.save(byId);
        }else{
            byId.setDataDate(comparisonDay);
            this.updateById(byId);
        }
    }

    private static String formatWithThousands(String numberStr) {
        if (StrUtil.isBlank(numberStr)) return null;
        try {
            BigDecimal value = new BigDecimal(numberStr);
//            DecimalFormat formatter = new DecimalFormat("#,##0.########"); // 最多保留8位小数
            return formatter.format(value);
        } catch (Exception e) {
            return numberStr; // 解析失败返回原值
        }
    }

    @Override
    public void syncNetValueFluctuation(String today) {

        //获取今日多账套净值信息
        List<NetValueOfAccountSetVO> list = this.baseMapper.selectNetValueFluctuation(today);
        if (list.isEmpty()){
            return;
        }
        List<NetValueOfAccountSet> results = new ArrayList<>();
        //获取区间时间实际天数
        Integer actualDays = this.baseMapper.getActualNumberOfDaysInIntervalTime(today.substring(0, 4)+"-01-01", today);

        //获取对应上年末净值信息
        List<NetValueOfAccountSetVO> lastYearNetValueList = this.baseMapper.selectLastYearNetValue(Integer.valueOf(today.substring(0, 4)) - 1 + "");
        Map<String, NetValueOfAccountSetVO> lastYearNetValueMap = lastYearNetValueList.stream().collect(Collectors.toMap(NetValueOfAccountSetVO::getProductId, x -> x, (a, b) -> a));


        //上一交易日
        String tradeDay = marketTradeDayService.getDefaultMarketPreTradeDay(today);
        List<NetValueOfAccountSetVO> tradeDayList = this.baseMapper.selectNetValueFluctuation(tradeDay);
        Map<String, NetValueOfAccountSetVO> tradeDayListValueMap = tradeDayList.stream().collect(Collectors.toMap(NetValueOfAccountSetVO::getProductId, x -> x, (a, b) -> a));

        //获取上一自然日
        LocalDate yesterdayLocal = LocalDate.parse(today, DateTimeFormatter.ofPattern("yyyy-MM-dd")).minusDays(1);
        String yesterday = yesterdayLocal.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<NetValueOfAccountSetVO> yesterdayList = this.baseMapper.selectNetValueFluctuation(yesterday);
        Map<String, NetValueOfAccountSetVO> yesterdayListValueMap = yesterdayList.stream().collect(Collectors.toMap(NetValueOfAccountSetVO::getProductId, x -> x, (a, b) -> a));


        for (NetValueOfAccountSetVO current : list) {
            // ------- 1. 单位净值波动-单日差额 ---------
            BigDecimal todayDwjz = new BigDecimal(Optional.ofNullable(current.getEnJjdwjz()).orElse("0"));
            BigDecimal prevDwjz = BigDecimal.ZERO;
            if ("T0".equalsIgnoreCase(current.getValuationTime())) {
                NetValueOfAccountSetVO netValueOfAccountSetVO1 = yesterdayListValueMap.get(current.getProductId());
                if (netValueOfAccountSetVO1 != null) {
                    prevDwjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO1.getEnJjdwjz()).orElse("0"));
                }
            }else{
                NetValueOfAccountSetVO netValueOfAccountSetVO1 = tradeDayListValueMap.get(current.getProductId());
                if (netValueOfAccountSetVO1 != null) {
                    prevDwjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO1.getEnJjdwjz()).orElse("0"));
                }
            }
            BigDecimal drce = todayDwjz.subtract(prevDwjz);
            current.setDrce(drce.stripTrailingZeros().toPlainString());

            // ------- 2. 单位净值波动-单日涨跌幅（%） ---------
            BigDecimal drzdf = BigDecimal.ZERO;
            if (prevDwjz.compareTo(BigDecimal.ZERO) != 0) {
                drzdf = drce.divide(prevDwjz, 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            }
            current.setDrzdf(drzdf.stripTrailingZeros().toPlainString());

            // ------- 3. 年化收益率（单位净值） ----------
            NetValueOfAccountSetVO netValueOfAccountSetVO = lastYearNetValueMap.get(current.getProductId());
            BigDecimal yearEndDwjz = BigDecimal.ONE;
            BigDecimal yearEndLjjz = BigDecimal.ONE;
            if (netValueOfAccountSetVO != null) {
                yearEndDwjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO.getEnJjdwjz()).orElse("1"));
                yearEndLjjz = new BigDecimal(Optional.ofNullable(netValueOfAccountSetVO.getEnLjjz()).orElse("1"));
            }

            BigDecimal divide = yearEndDwjz.multiply(BigDecimal.valueOf(365)).divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP);
            if (divide.compareTo(BigDecimal.ZERO) == 0){
                current.setDwjzYear("0");
            }else{
                BigDecimal dwjzYear = todayDwjz.subtract(yearEndDwjz)
                        .divide(divide, 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                current.setDwjzYear(dwjzYear.stripTrailingZeros().toPlainString());
            }



            // ------- 4. 年化收益率（累计净值） ----------
            BigDecimal todayLjjz = new BigDecimal(Optional.ofNullable(current.getEnLjjz()).orElse("0"));

            BigDecimal divide1 = yearEndLjjz.multiply(BigDecimal.valueOf(365)).divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP);
            if (divide1.compareTo(BigDecimal.ZERO) == 0){
                current.setLjdwjzYear("0");
            }else{
                BigDecimal ljdwjzYear = todayLjjz.subtract(yearEndLjjz)
                        .divide(divide1, 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                current.setLjdwjzYear(ljdwjzYear.stripTrailingZeros().toPlainString());
            }


            NetValueOfAccountSet netValueOfAccountSet = new NetValueOfAccountSet();
            BeanUtil.copyProperties(current, netValueOfAccountSet);
            netValueOfAccountSet.setEnJjzfe(ObjectUtil.isNull(current.getEnJjzfe()) ? null : new BigDecimal(current.getEnJjzfe()).stripTrailingZeros().toPlainString());
            netValueOfAccountSet.setEnJjzjz(ObjectUtil.isNull(current.getEnJjzjz()) ? null : new BigDecimal(current.getEnJjzjz()).stripTrailingZeros().toPlainString());
            netValueOfAccountSet.setEnJjdwjz(ObjectUtil.isNull(current.getEnJjdwjz()) ? null : new BigDecimal(current.getEnJjdwjz()).stripTrailingZeros().toPlainString());
            netValueOfAccountSet.setEnLjjz(ObjectUtil.isNull(current.getEnLjjz()) ? null : new BigDecimal(current.getEnLjjz()).stripTrailingZeros().toPlainString());
            netValueOfAccountSet.setEnDwjjsy(ObjectUtil.isNull(current.getEnDwjjsy()) ? null : new BigDecimal(current.getEnDwjjsy()).stripTrailingZeros().toPlainString());
            netValueOfAccountSet.setEnFdsy(ObjectUtil.isNull(current.getEnFdsy()) ? null : new BigDecimal(current.getEnFdsy()).stripTrailingZeros().toPlainString());
            results.add(netValueOfAccountSet);
        }

        if (!results.isEmpty()){
            this.remove(new QueryWrapper<NetValueOfAccountSet>().eq("data_date", today));
            this.saveBatch(results);
        }
    }

    public void calculateNetValueMetrics(List<NetValueOfAccountSetVO> list) {
        // 按产品分组
        Map<String, List<NetValueOfAccountSetVO>> groupedByProduct = list.stream()
                .collect(Collectors.groupingBy(NetValueOfAccountSetVO::getProductId));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        int currentYear = LocalDate.now().getYear();
        LocalDate yearStart = LocalDate.of(currentYear, 1, 1);
        LocalDate yearEnd = LocalDate.of(currentYear - 1, 12, 31);

        for (Map.Entry<String, List<NetValueOfAccountSetVO>> entry : groupedByProduct.entrySet()) {
            List<NetValueOfAccountSetVO> productList = entry.getValue();

            // 按日期升序
            productList.sort(Comparator.comparing(p -> LocalDate.parse(p.getDataDate(), formatter)));

            // 准备找上年末净值
            BigDecimal yearEndDwjz = null;
            BigDecimal yearEndLjjz = null;

            // 区间实际天数
            long actualDays = productList.stream()
                    .map(NetValueOfAccountSetVO::getDataDate)
                    .map(dateStr -> LocalDate.parse(dateStr, formatter))
                    .filter(date -> !date.isBefore(yearStart))
                    .count();

            for (int i = 0; i < productList.size(); i++) {
                NetValueOfAccountSetVO current = productList.get(i);
                LocalDate currentDate = LocalDate.parse(current.getDataDate(), formatter);

                // ------- 1. 单位净值波动-单日差额 ---------
                BigDecimal todayDwjz = new BigDecimal(Optional.ofNullable(current.getEnJjdwjz()).orElse("0"));
                BigDecimal prevDwjz = BigDecimal.ZERO;

                // 找上一日
                for (int j = i - 1; j >= 0; j--) {
                    NetValueOfAccountSetVO prev = productList.get(j);
                    LocalDate prevDate = LocalDate.parse(prev.getDataDate(), formatter);
                    if (current.getValuationTime().equalsIgnoreCase("T0")) {
                        if (prevDate.equals(currentDate.minusDays(1))) {
                            prevDwjz = new BigDecimal(Optional.ofNullable(prev.getEnJjdwjz()).orElse("0"));
                            break;
                        }
                    } else {
                        // T1：直接取上一个交易日（已排好序）
                        prevDwjz = new BigDecimal(Optional.ofNullable(prev.getEnJjdwjz()).orElse("0"));
                        break;
                    }
                }

                BigDecimal drce = todayDwjz.subtract(prevDwjz);
                current.setDrce(drce.stripTrailingZeros().toPlainString());

                // ------- 2. 单位净值波动-单日涨跌幅（%） ---------
                BigDecimal drzdf = BigDecimal.ZERO;
                if (prevDwjz.compareTo(BigDecimal.ZERO) != 0) {
                    drzdf = drce.divide(prevDwjz, 8, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                }
                current.setDrzdf(drzdf.stripTrailingZeros().toPlainString());

                // ------- 3. 年化收益率（单位净值） ----------
                if (currentDate.getYear() == currentYear) {
                    if (yearEndDwjz == null) {
                        // 尝试获取去年末的单位净值
                        for (int j = i - 1; j >= 0; j--) {
                            NetValueOfAccountSetVO candidate = productList.get(j);
                            LocalDate candidateDate = LocalDate.parse(candidate.getDataDate(), formatter);
                            if (!candidateDate.isAfter(yearEnd)) {
                                yearEndDwjz = new BigDecimal(Optional.ofNullable(candidate.getEnJjdwjz()).orElse("1"));
                                break;
                            }
                        }
                        if (yearEndDwjz == null) {
                            yearEndDwjz = BigDecimal.ONE;
                        }
                    }

                    BigDecimal dwjzYear = todayDwjz.subtract(yearEndDwjz)
                            .divide(yearEndDwjz, 8, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(365))
                            .divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    current.setDwjzYear(dwjzYear.stripTrailingZeros().toPlainString());
                }

                // ------- 4. 年化收益率（累计净值） ----------
                BigDecimal todayLjjz = new BigDecimal(Optional.ofNullable(current.getEnLjjz()).orElse("0"));
                if (currentDate.getYear() == currentYear) {
                    if (yearEndLjjz == null) {
                        // 尝试获取去年末的累计净值
                        for (int j = i - 1; j >= 0; j--) {
                            NetValueOfAccountSetVO candidate = productList.get(j);
                            LocalDate candidateDate = LocalDate.parse(candidate.getDataDate(), formatter);
                            if (!candidateDate.isAfter(yearEnd)) {
                                yearEndLjjz = new BigDecimal(Optional.ofNullable(candidate.getEnLjjz()).orElse("1"));
                                break;
                            }
                        }
                        if (yearEndLjjz == null) {
                            yearEndLjjz = BigDecimal.ONE;
                        }
                    }

                    BigDecimal ljdwjzYear = todayLjjz.subtract(yearEndLjjz)
                            .divide(yearEndLjjz, 8, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(365))
                            .divide(BigDecimal.valueOf(actualDays == 0 ? 1 : actualDays), 8, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    current.setLjdwjzYear(ljdwjzYear.stripTrailingZeros().toPlainString());
                }
            }
        }
    }



}




