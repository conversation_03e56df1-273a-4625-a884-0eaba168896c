package cn.sdata.om.al.audit.service.impl;

import cn.sdata.om.al.audit.mapper.ScopeOfSecuritiesTypeMapper;
import cn.sdata.om.al.audit.service.ScopeOfSecuritiesTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.ScopeOfSecuritiesType;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【scope_of_securities_type(证券类型波动范围)】的数据库操作Service实现
* @createDate 2025-08-11 13:52:40
*/
@Service
public class ScopeOfSecuritiesTypeServiceImpl extends ServiceImpl<ScopeOfSecuritiesTypeMapper, ScopeOfSecuritiesType>
    implements ScopeOfSecuritiesTypeService {

}




