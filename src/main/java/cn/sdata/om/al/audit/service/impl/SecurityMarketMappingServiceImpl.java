package cn.sdata.om.al.audit.service.impl;

import cn.sdata.om.al.audit.service.SecurityMarketMappingService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 证券市场代码映射服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class SecurityMarketMappingServiceImpl implements SecurityMarketMappingService {

    // 聚源市场代码到估值市场代码的映射
    private static final Map<String, List<String>> JUYUAN_TO_VALUATION_MAP = new HashMap<>();
    
    // 估值市场代码到聚源市场代码的映射
    private static final Map<String, String> VALUATION_TO_JUYUAN_MAP = new HashMap<>();

    static {
        // 初始化映射关系
        JUYUAN_TO_VALUATION_MAP.put("83", Arrays.asList("1")); // 上海证券交易所 -> 上交所
        JUYUAN_TO_VALUATION_MAP.put("90", Arrays.asList("2")); // 深圳证券交易所 -> 深交所
        JUYUAN_TO_VALUATION_MAP.put("18", Arrays.asList("52")); // 北京证券交易所 -> 北交所
        JUYUAN_TO_VALUATION_MAP.put("89", Arrays.asList("3")); // 银行间债券市场 -> 银行间
        JUYUAN_TO_VALUATION_MAP.put("72", Arrays.asList("44", "30", "4")); // 香港联交所 -> 沪港通、深港通、港交所
        JUYUAN_TO_VALUATION_MAP.put("71", Arrays.asList("3", "14", "99", "96", "97", "98")); // 柜台交易市场 -> 场外、股转公司、OTC

        // 反向映射
        VALUATION_TO_JUYUAN_MAP.put("1", "83");   // 上交所 -> 上海证券交易所
        VALUATION_TO_JUYUAN_MAP.put("2", "90");   // 深交所 -> 深圳证券交易所
        VALUATION_TO_JUYUAN_MAP.put("52", "18");  // 北交所 -> 北京证券交易所
        VALUATION_TO_JUYUAN_MAP.put("3", "89");   // 银行间 -> 银行间债券市场
        VALUATION_TO_JUYUAN_MAP.put("44", "72");  // 沪港通 -> 香港联交所
        VALUATION_TO_JUYUAN_MAP.put("30", "72");  // 深港通 -> 香港联交所
        VALUATION_TO_JUYUAN_MAP.put("4", "72");   // 港交所 -> 香港联交所
        VALUATION_TO_JUYUAN_MAP.put("14", "71");  // 股转公司 -> 柜台交易市场
        VALUATION_TO_JUYUAN_MAP.put("99", "71");  // OTC -> 柜台交易市场
        VALUATION_TO_JUYUAN_MAP.put("96", "71");  // OTC -> 柜台交易市场
        VALUATION_TO_JUYUAN_MAP.put("97", "71");  // OTC -> 柜台交易市场
        VALUATION_TO_JUYUAN_MAP.put("98", "71");  // OTC -> 柜台交易市场
    }

    @Override
    public List<String> convertJuyuanToValuationMarket(String juyuanMarketCode) {
        if (juyuanMarketCode == null) {
            return new ArrayList<>();
        }
        return JUYUAN_TO_VALUATION_MAP.getOrDefault(juyuanMarketCode, new ArrayList<>());
    }

    @Override
    public String convertValuationToJuyuanMarket(String valuationMarketCode) {
        if (valuationMarketCode == null) {
            return null;
        }
        return VALUATION_TO_JUYUAN_MAP.get(valuationMarketCode);
    }

    @Override
    public String getAllMappingInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("聚源市场代码 -> 估值市场代码映射关系：\n");
        sb.append("83 上海证券交易所 -> 1 上交所\n");
        sb.append("90 深圳证券交易所 -> 2 深交所\n");
        sb.append("18 北京证券交易所 -> 52 北交所\n");
        sb.append("89 银行间债券市场 -> 3 银行间\n");
        sb.append("72 香港联交所 -> 44 沪港通, 30 深港通, 4 港交所\n");
        sb.append("71 柜台交易市场 -> 3 场外, 14 股转公司, 99/96/97/98 OTC\n");
        return sb.toString();
    }
}
