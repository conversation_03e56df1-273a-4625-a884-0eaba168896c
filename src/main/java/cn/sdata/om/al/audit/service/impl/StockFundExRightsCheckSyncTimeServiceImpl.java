package cn.sdata.om.al.audit.service.impl;

import cn.sdata.om.al.audit.entity.StockFundExRightsCheckSyncTime;
import cn.sdata.om.al.audit.mapper.StockFundExRightsCheckSyncTimeMapper;
import cn.sdata.om.al.audit.service.StockFundExRightsCheckSyncTimeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 股票和开基除权价格检查同步时间记录服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StockFundExRightsCheckSyncTimeServiceImpl extends ServiceImpl<StockFundExRightsCheckSyncTimeMapper, StockFundExRightsCheckSyncTime> implements StockFundExRightsCheckSyncTimeService {

    @Override
    public StockFundExRightsCheckSyncTime getSyncTimeByDataDate(String dataDate) {
        return this.baseMapper.selectByDataDate(dataDate);
    }

    @Override
    public void recordSyncTime(String dataDate, LocalDateTime syncTime) {
        try {
            this.baseMapper.insertOrUpdateSyncTime(dataDate, syncTime);
            log.info("记录股票和开基除权价格检查同步时间成功，数据日期：{}，同步时间：{}", dataDate, syncTime);
        } catch (Exception e) {
            log.error("记录股票和开基除权价格检查同步时间失败，数据日期：{}，同步时间：{}", dataDate, syncTime, e);
            throw new RuntimeException("记录同步时间失败", e);
        }
    }

    @Override
    public void recordCurrentSyncTime(String dataDate) {
        recordSyncTime(dataDate, LocalDateTime.now());
    }
}
