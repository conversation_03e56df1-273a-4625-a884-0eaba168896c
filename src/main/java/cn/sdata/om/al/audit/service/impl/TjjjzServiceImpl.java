package cn.sdata.om.al.audit.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.audit.entity.NetValueOfAccountSet;
import cn.sdata.om.al.audit.mapper.TjjjzMapper;
import cn.sdata.om.al.audit.service.TjjjzService;
import cn.sdata.om.al.mapper.ValuationDBMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.audit.entity.Tjjjz;
import lombok.AllArgsConstructor;
import org.quartz.JobDataMap;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【tjjjz】的数据库操作Service实现 net_value_of_account_set  NetValueOfAccountSet
* @createDate 2025-07-22 19:35:45
*/
@Service
@AllArgsConstructor
public class TjjjzServiceImpl extends ServiceImpl<TjjjzMapper, Tjjjz>
    implements TjjjzService {

    private ValuationDBMapper valuationDBMapper;

    @Override
    public void syncValuationInfo(JobDataMap mergedJobDataMap,String today) {
        Tjjjz tjjjz = this.baseMapper.selectOne(new LambdaQueryWrapper<Tjjjz>().orderByAsc(Tjjjz::getDRq).last("LIMIT 1"));
        String startDate = "";
        if (ObjectUtil.isNull(tjjjz)){
            //第一次同步数据设置起始日
            String navDate = (String) mergedJobDataMap.get("navDate");
            startDate = StrUtil.isBlank(navDate) ? "2023-12-31" : navDate;
        }else{
            startDate = today;
        }
        List<Tjjjz> tjjjzList = valuationDBMapper.syncTjjjzInfo(startDate,today);
        //同步落库前转换code
        Map<String,Tjjjz> codeMap =  valuationDBMapper.selectStructuredTableData();


        if (CollUtil.isNotEmpty(tjjjzList)){
            tjjjzList.forEach(item -> {
                if (codeMap.containsKey(item.getVcJjdm())){
                    item.setVcJjdm(codeMap.get(item.getVcJjdm()).getVcJjdm());
                }
            });
            this.remove(new QueryWrapper<Tjjjz>().eq("D_RQ", today));
            this.saveBatch(tjjjzList);
        }
    }
}




