package cn.sdata.om.al.audit.util;

import cn.sdata.om.al.audit.enums.CompareType;

import java.math.BigDecimal;

/**
 * 比较工具类
 */
public class CompareUtil {
    
    /**
     * 根据比较类型比较两个数值
     * @param value 要比较的值
     * @param compareType 比较类型
     * @param target 目标值
     * @return 比较结果
     */
    public static boolean compare(BigDecimal value, CompareType compareType, BigDecimal target) {
        if (value == null || target == null) {
            return false;
        }
        
        switch (compareType) {
            case GREATER_THAN:
                return value.compareTo(target) > 0;
            case GREATER_EQUAL:
                return value.compareTo(target) >= 0;
            case LESS_THAN:
                return value.compareTo(target) < 0;
            case LESS_EQUAL:
                return value.compareTo(target) <= 0;
            default:
                throw new IllegalArgumentException("Unsupported compare type: " + compareType);
        }
    }
    
    /**
     * 检查值是否在指定范围内
     * @param value 要检查的值
     * @param lowerLimit 下限值
     * @param lowerCompareType 下限比较类型
     * @param upperLimit 上限值
     * @param upperCompareType 上限比较类型
     * @return 是否在范围内
     */
    public static boolean isInRange(BigDecimal value, BigDecimal lowerLimit, CompareType lowerCompareType,
                                   BigDecimal upperLimit, CompareType upperCompareType) {
        if (value == null) {
            return false;
        }
        
        boolean lowerCheck = true;
        if (lowerLimit != null && lowerCompareType != null) {
            lowerCheck = compare(value, lowerCompareType, lowerLimit);
        }
        
        boolean upperCheck = true;
        if (upperLimit != null && upperCompareType != null) {
            upperCheck = compare(value, upperCompareType, upperLimit);
        }
        
        return lowerCheck && upperCheck;
    }
} 