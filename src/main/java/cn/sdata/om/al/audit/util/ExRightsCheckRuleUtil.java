package cn.sdata.om.al.audit.util;

import cn.sdata.om.al.audit.enums.FlowCheckStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 除权价格检查业务规则工具类
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Slf4j
public class ExRightsCheckRuleUtil {

    /**
     * 判断流水检查状态
     * 
     * 业务规则：
     * 1. 有流水且今日是除权日 → 业务流水正常
     * 2. 无流水且不是除权日 → 业务流水正常  
     * 3. 有流水且当日不是除权日 → 流水日期异常
     * 4. 没有流水且是除权日 → 业务流水缺失
     * 
     * @param hasFlow 是否有流水
     * @param isTargetDate 是否是目标日期（除权日/红股发放日/红利发放日）
     * @return 检查状态
     */
    public static FlowCheckStatus determineFlowCheckStatus(boolean hasFlow, boolean isTargetDate) {
        if (hasFlow && isTargetDate) {
            // 有流水且今日是目标日期 → 正常
            return FlowCheckStatus.NORMAL;
        } else if (!hasFlow && !isTargetDate) {
            // 无流水且不是目标日期 → 正常
            return FlowCheckStatus.NORMAL;
        } else if (hasFlow && !isTargetDate) {
            // 有流水且当日不是目标日期 → 流水日期异常
            return FlowCheckStatus.DATE_ABNORMAL;
        } else {
            // 没有流水且是目标日期 → 业务流水缺失
            return FlowCheckStatus.FLOW_MISSING;
        }
    }

    /**
     * 判断除权流水检查状态
     * 
     * @param hasFlow 是否有除权流水
     * @param currentDate 当前日期
     * @param exRightsDate 除权日
     * @return 检查状态
     */
    public static FlowCheckStatus checkExRightsFlow(boolean hasFlow, String currentDate, String exRightsDate) {
        boolean isExRightsDate = currentDate.equals(exRightsDate);
        FlowCheckStatus status = determineFlowCheckStatus(hasFlow, isExRightsDate);
        
        log.info("除权流水检查 - 有流水: {}, 当前日期: {}, 除权日: {}, 结果: {}",
                hasFlow, currentDate, exRightsDate, status.getCode());
        
        return status;
    }

    /**
     * 判断红股流水检查状态
     * 
     * @param hasFlow 是否有红股流水
     * @param currentDate 当前日期
     * @param bonusSharePaymentDate 红股发放日
     * @return 检查状态
     */
    public static FlowCheckStatus checkBonusShareFlow(boolean hasFlow, String currentDate, String bonusSharePaymentDate) {
        boolean isBonusShareDate = currentDate.equals(bonusSharePaymentDate);
        FlowCheckStatus status = determineFlowCheckStatus(hasFlow, isBonusShareDate);
        
        log.debug("红股流水检查 - 有流水: {}, 当前日期: {}, 红股发放日: {}, 结果: {}", 
                hasFlow, currentDate, bonusSharePaymentDate, status.getCode());
        
        return status;
    }

    /**
     * 判断红利发放流水检查状态
     * 
     * @param hasFlow 是否有红利发放流水
     * @param currentDate 当前日期
     * @param dividendPaymentDate 红利发放日
     * @return 检查状态
     */
    public static FlowCheckStatus checkDividendFlow(boolean hasFlow, String currentDate, String dividendPaymentDate) {
        boolean isDividendDate = currentDate.equals(dividendPaymentDate);
        FlowCheckStatus status = determineFlowCheckStatus(hasFlow, isDividendDate);
        
        log.debug("红利发放流水检查 - 有流水: {}, 当前日期: {}, 红利发放日: {}, 结果: {}", 
                hasFlow, currentDate, dividendPaymentDate, status.getCode());
        
        return status;
    }

    /**
     * 获取检查状态的中文描述
     * 
     * @param status 检查状态
     * @return 中文描述
     */
    public static String getStatusDescription(FlowCheckStatus status) {
        if (status == null) {
            return "未知状态";
        }
        return status.getCode();
    }

    /**
     * 判断是否为异常状态
     * 
     * @param status 检查状态
     * @return 是否异常
     */
    public static boolean isAbnormalStatus(FlowCheckStatus status) {
        return status == FlowCheckStatus.DATE_ABNORMAL || status == FlowCheckStatus.FLOW_MISSING;
    }

    /**
     * 判断是否为异常状态（根据状态值）
     * 
     * @param statusValue 状态值
     * @return 是否异常
     */
    public static boolean isAbnormalStatus(Integer statusValue) {
        if (statusValue == null) {
            return false;
        }
        return statusValue.equals(FlowCheckStatus.DATE_ABNORMAL.getValue()) 
                || statusValue.equals(FlowCheckStatus.FLOW_MISSING.getValue());
    }

    /**
     * 获取异常状态的处理建议
     * 
     * @param status 检查状态
     * @return 处理建议
     */
    public static String getHandlingSuggestion(FlowCheckStatus status) {
        if (status == null) {
            return "无建议";
        }
        
        switch (status) {
            case NORMAL:
                return "无需处理";
            case DATE_ABNORMAL:
                return "建议检查流水日期是否正确，或确认除权日期信息";
            case FLOW_MISSING:
                return "建议检查是否遗漏相关业务流水，或确认除权信息";
            default:
                return "请联系系统管理员";
        }
    }
}
