package cn.sdata.om.al.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@Data
public class BankReconciliationSyncConfig implements CommandLineRunner {

    private volatile CompletableFuture<Void> completableFuture = null;

    private volatile String status = "noStart";

    @Override
    public void run(String... args) {
        log.debug("检查同步银行持仓状态线程开始...");
        new Thread(() -> {
            while (true) {
                log.debug("同步银行对账状态为:{}", status);
                if (completableFuture != null) {
                    // 如果是 执行中 则需要判断是否完成
                    if ("executing".equals(status)) {
                        if (completableFuture.isDone()) {
                            setStatus("completed");
                        }
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException e) {
                    log.error(e.getMessage());
                }
            }
        }).start();
    }
}
