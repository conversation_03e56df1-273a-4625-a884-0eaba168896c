package cn.sdata.om.al.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/2/17 16:10
 * @Version 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "aes")
public class EncryptionConfig {

    private String key;

    private String algorithm;

    private String transformation;

    private String iv;

}
