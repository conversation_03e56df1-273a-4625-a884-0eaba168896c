package cn.sdata.om.al.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/10/27 15:32
 */
@Data
@Component
@ConfigurationProperties(prefix = "ftp")
public class FtpProperties {

    /**
     * ftp服务器的地址
     */
    private String host;
    /**
     * ftp服务器的端口号（连接端口号）
     */
    private int port;
    /**
     * ftp的用户名
     */
    private String ftpName;
    /**
     * ftp的密码
     */
    private String password;
    /**
     * ftp上传的根目录
     */
    private String basePath;

    private String encoding = "GBK";

    /**
     * 港股通风控金归档ftp上传的根目录
     */
    private String documentationBasePath;
}
