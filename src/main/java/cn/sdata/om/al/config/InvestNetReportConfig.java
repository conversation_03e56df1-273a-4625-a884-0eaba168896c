package cn.sdata.om.al.config;

import cn.hutool.core.map.MapUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@Data
public class InvestNetReportConfig implements CommandLineRunner {

    public static Map<String, CompletableFuture> map = new HashMap<>();

    @Override
    public void run(String... args) {
        log.debug("检查投连播报线程开始...");
        new Thread(() -> {
            while (true) {
                if (MapUtil.isNotEmpty(map)) {
                    Iterator<Map.Entry<String, CompletableFuture>> iterator = map.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, CompletableFuture> next = iterator.next();
                        CompletableFuture value = next.getValue();
                        if (value.isDone()) {
                            // 移除任务记录
                            iterator.remove();
                        }
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException e) {
                    log.error(e.getMessage());
                }
            }
        }).start();
    }
}
