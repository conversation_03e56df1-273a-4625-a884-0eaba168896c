package cn.sdata.om.al.config;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.mail.Session;
import javax.mail.Store;
import java.time.Duration;
import java.util.Properties;

@Configuration
@Slf4j
public class MailConfiguration {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .setConnectTimeout(Duration.ofSeconds(15))
                .setReadTimeout(Duration.ofSeconds(15))
                .build();
    }

    private Store cachestore = null;

    @Value("${receive-email.prefix-url}")
    private String prefixUrl;

    @Value("${receive-email.tenant-id}")
    private String tenantId;

    @Value("${receive-email.client-id}")
    private String clientId;

    @Value("${receive-email.client-secret}")
    private String clientSecret;

    @Value("${receive-email.user}")
    private String username;

    @Value("${receive-email.password}")
    private String password;

    @Value("${receive-email.port}")
    private String port;

    @Value("${receive-email.host}")
    private String host;

    /**
     * 初始化邮箱连接
     *
     * @return 邮件存储对象
     */
    public Store initEmailConnect() {
        initStore();
        return cachestore;
    }


    private void initStore() {
        try {
            if (cachestore != null && cachestore.isConnected()) {
                return;
            }
            String scope = "https://partner.outlook.cn/.default";
            String url = prefixUrl + tenantId + "/oauth2/v2.0/token";
            log.info("url = {}", url);
            HttpEntity<MultiValueMap<String, String>> request = getMapHttpEntity(scope);
            ResponseEntity<String> response = SpringUtil.getBean(RestTemplate.class).postForEntity(url, request, String.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("outlook令牌获取失败");
            }
            JSONObject responseBody = JSONObject.parseObject(response.getBody());
            assert responseBody != null;
            String accessToken = responseBody.getString("access_token");
            if (!StringUtils.hasText(accessToken)) {
                throw new RuntimeException("outlook令无效");
            }
            log.info("outlook的accessToken = {}", accessToken);
            String protocol = "imap";
            Properties properties = new Properties();
            properties.put("mail.imap.host", host);
            properties.put("mail.imap.port", port);
            properties.put("mail.imap.protocol", protocol);
            properties.put("mail.imap.ssl.enable", "true");
            properties.put("mail.imap.auth.mechanisms", "XOAUTH2");
            Session session = Session.getDefaultInstance(properties);
            log.info("imap host:{}", host);
            cachestore = session.getStore(protocol);
            cachestore.connect(host, username, accessToken);
            log.info("连接成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private HttpEntity<MultiValueMap<String, String>> getMapHttpEntity(String scope) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/x-www-form-urlencoded;charset=GBK");
        headers.set("accept", "*/*");
        headers.set("connection", "Keep-Alive");
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("grant_type", "password");
        map.add("scope", scope);
        map.add("client_id", clientId);
        map.add("client_secret", clientSecret);
        map.add("username", username);
        map.add("password", password);
        return new HttpEntity<>(map, headers);
    }
}
