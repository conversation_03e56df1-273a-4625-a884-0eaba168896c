package cn.sdata.om.al.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "email")
public class MailProperties {

    /**
     * 发件人邮箱的邮件服务器地址(SMTP)
     */
    private String host;

    /**
     * 发件人姓名
     */
    public String name;


    /**
     * 发件人邮箱地址
     */
    public String sender;

    /**
     * 发送人邮箱密码
     * 某些邮箱服务器为了增加邮箱本身密码的安全性，给SMTP客户端设置了独立密码(有的邮箱称为"授权码"),
     * 对于开启了独立密码的邮箱,这里的邮箱密码必需使用这个独立密码(授权码)。
     */
    public String password;

    /**
     * 邮箱默认编码
     */
    private String defaultEncoding;


    private String port;

    /**
     * 协议
     */
    private String protocol;

    /**
     * 对话是否开启调试模式
     */
    private boolean isDebug = false;
}
