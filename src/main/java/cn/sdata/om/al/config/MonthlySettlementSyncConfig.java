package cn.sdata.om.al.config;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.service.MonthlySettlementService;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

@Component
@Slf4j
@Data
public class MonthlySettlementSyncConfig implements CommandLineRunner {

    public static final BlockingQueue<JSONObject> taskQueue = new ArrayBlockingQueue<>(100);

    private MonthlySettlementService monthlySettlementService;

    @Autowired
    public void setMonthlySettlementService(MonthlySettlementService monthlySettlementService) {
        this.monthlySettlementService = monthlySettlementService;
    }

    @Override
    public void run(String... args) {
        log.debug("检查同步月结任务状态线程开始...");
        new Thread(() -> {
            while (true) {
                JSONObject poll;
                try {
                    log.info("等待任务执行...");
                    poll = taskQueue.take();
                    log.info("从队列中取出的任务是:{}", poll);
                    String date = poll.getString("date");
                    String order = poll.getString("order");
                    String username = poll.getString("username");
                    String type = poll.getString("type");
                    log.info("开始处理任务...");
                    monthlySettlementService.generateFilesSync(date, CollectionUtil.newArrayList(order), username, type);
                } catch (InterruptedException e) {
                    log.error(e.getMessage());
                }
            }
        }).start();
    }
}
