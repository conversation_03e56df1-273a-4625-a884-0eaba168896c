package cn.sdata.om.al.config;

import lombok.Data;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "rpa")
public class RpaProperties {

    /**
     * rpa内网访问地址
     */
    private String interNetAddress;

    /**
     * rpa内网AppName
     */
    private String interNetAppName;

    /**
     * rpa内网AppPass
     */
    private String interNetAppPass;

    /**
     * rpa外网访问地址
     */
    private String outerNetAddress;

    /**
     * rpa外网AppName
     */
    private String outerNetAppName;

    /**
     * rpa外网AppPass
     */
    private String outerNetAppPass;

    /**
     * 请求模块名称
     */
    private String modelKey;

    /**
     * 请求方法名称
     */
    private String methodKey;

    /**
     * rpa错误信息key
     */
    private String errorMsgKey;

    /**
     * 港股通风控金rpa文件base路径
     */
    private String riskControlFundBasePath;

    /**
     * 港股通风控金rpa流程路径
     */
    private String riskControlFundFlowPtah;

    /**
     * 上清rpa流程路径
     */
    private String shanghaiClearFlowPtah;

    /**
     * 中债rpa流程路径
     */
    private String chinaBondsFlowPtah;

    /**
     * 开基交易确认-有o32待确认数据-调用rpa流程路径
     */
    private String haveO32FlowPtah;

    /**
     * 开基交易确认-无o32待确认数据-调用rpa流程路径
     */
    private String noO32FlowPtah;

    /**
     * 开基交易确认-货币基金红利转投-调用rpa流程路径
     */
    private String imfHlztFlowPtah;


    /**
     * O32系统资金调整记录文件上传记录
     */
    private String adjustFilePath;


    /**
     * O32系统资金调整记录文件flowPath
     */
    private String adjustFileFlowPath;

    @PostConstruct
    void init() {
        log.info("rpa接口配置初始化:{}", this);
    }

}
