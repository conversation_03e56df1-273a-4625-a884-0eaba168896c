package cn.sdata.om.al.constant;

public interface BaseConstant {

    String START_DATE_NAME = "startDate";

    String END_DATE_NAME = "endDate";

    String DATE_FORMAT_PATTERN = "yyyyMMdd";
    String HORIZONTAL_DATE_FORMAT_PATTERN = "yyyy-MM-dd";
    String CHINESE_DATE_FORMAT_PATTERN = "yyyy年MM月dd日";

    /**
     * 数据日期字段
     */
    String SYSTEM_DATE_NAME = "data_date";

    /**
     * 差异字段
     */
    String DIFFERENCE_STR = "difference";

    /**
     * 无差异
     */
    Integer NO_DIFFERENCE = 0;

    String DEFAULT_USERNAME = "运营管理平台";

    String SENDER = "运营管理平台";

    String RPA_START_DATE_NAME = "开始日期";
    String RPA_END_DATE_NAME = "结束日期";
    String RPA_PRODUCT_IDS = "账套编码";

    String ZZ_BR_ACCOUNT_NUMBER = "账户账号";

    String PRODUCT_TYPE_FLAT = "flat";
    String PRODUCT_TYPE_STRUCTURED = "structured";

    String SUPERIOR = "优先级";
    String SUPERIOR_2 = "优选级";
    String INFERIOR = "劣后级";

    String INVESTOR_TYPE = "INVESTOR";
    String INVESTOR_THIRD = "THIRD";

    String DEFAULT_MAP_KEY = "default";

    String SHEET_NAME = "净值录入";

    short NORMAL_FONT_SIZE = (short) 11;

    String T1_SECOND_VALUATION = "安联";

    String DEFAULT_TIME_ZONE = "Asia/Shanghai";

    String DATE_KEY_T0 = "default_date_T0";
    String DATE_KEY_T1 = "default_date_T1";
}
