package cn.sdata.om.al.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MailFilterConditionEnum {

    AND("and", " && "), OR("or", " || ");

    private final String type;

    private final String value;

    @Override
    public String toString() {
        return type;
    }

    public static String getValue(String type) {
        for (MailFilterConditionEnum e : values()) {
            if (e.getType().equals(type.toLowerCase())) {
                return e.value;
            }
        }
        throw new RuntimeException("没有找到枚举项或value为空,请检查...");
    }
}
