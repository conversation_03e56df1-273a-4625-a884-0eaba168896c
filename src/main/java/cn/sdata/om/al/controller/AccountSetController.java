package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.AccountSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/accountSet")
public class AccountSetController {

    private AccountSetService accountSetService;

    @Autowired
    public void setAccountSetService(AccountSetService accountSetService) {
        this.accountSetService = accountSetService;
    }

    /**
     * 获取全部账套的值
     *
     * @return 账套值
     */
    @GetMapping("/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> commonEntities = accountSetService.listAll();
        return R.ok(commonEntities);
    }


    /**
     * 获取全部账套的值
     *
     * @return 账套值
     */
    @GetMapping("/unbindGroupList")
    public R<List<CommonEntity>> unbindGroupList() {
        List<CommonEntity> commonEntities = accountSetService.list();
        return R.ok(commonEntities);
    }


    /**
     * 获取全部账套的值
     *
     * @return 账套值
     */
    @GetMapping("/bindGroupList")
    public R<List<CommonEntity>> bindGroupList() {
        List<CommonEntity> commonEntities = accountSetService.bindGroupList();
        return R.ok(commonEntities);
    }

    /**
     * 获取产品代码下拉框
     *
     * @return 产品代码
     */
    @GetMapping("/productCodeList")
    public R<List<CommonEntity>> productCodeList() {
        List<CommonEntity> commonEntities = accountSetService.productCodeList();
        return R.ok(commonEntities);
    }

    /**
     * 获取账套编码下拉框
     *
     * @return 账套编码
     */
    @GetMapping("/accountSetCodeList")
    public R<List<CommonEntity>> accountSetCodeList() {
        List<CommonEntity> commonEntities = accountSetService.accountSetCodeList();
        return R.ok(commonEntities);
    }

    /**
     * 获取账套名称下拉框
     *
     * @return 账套名称
     */
    @GetMapping("/accountSetNameList")
    public R<List<CommonEntity>> accountSetNameList() {
        List<CommonEntity> commonEntities = accountSetService.accountSetNameList();
        return R.ok(commonEntities);
    }
}
