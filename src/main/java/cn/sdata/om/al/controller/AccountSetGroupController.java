package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSetGroup;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.ProjectGroupService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 账套组管理
 */
@RestController
@RequestMapping("/accountSetGroup")
public class AccountSetGroupController {

    private ProjectGroupService projectGroupService;

    @Autowired
    public void setProjectGroupService(ProjectGroupService projectGroupService) {
        this.projectGroupService = projectGroupService;
    }

    @PostMapping("/page")
    public R<Page<AccountSetGroup>> page(@RequestBody JSONObject jsonObject) {
        Integer pageNo = jsonObject.getInteger("pageNo");
        Integer pageSize = jsonObject.getInteger("pageSize");
        List<String> productIds = jsonObject.getList("productIds", String.class);
        List<String> accountSetGroupIds = jsonObject.getList("accountSetGroupIds", String.class);
        PageInfo<AccountSetGroup> accountSetGroupPageInfo = projectGroupService.page(pageNo, pageSize, accountSetGroupIds, productIds);
        Page<AccountSetGroup> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(accountSetGroupPageInfo.getList()) ? accountSetGroupPageInfo.getList() : new ArrayList<>());
        page.setSize(pageSize);
        page.setTotal(accountSetGroupPageInfo.getTotal());
        return R.ok(page);
    }

    /**
     * 新增或修改账套组
     *
     * @param accountSetGroup 参数
     * @return 是否成功
     */
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody AccountSetGroup accountSetGroup) {
        return projectGroupService.saveOrUpdate(accountSetGroup);
    }

    /**
     * 根据id查询账套组信息
     *
     * @param id 主键
     * @return 分页列表
     */
    @GetMapping("")
    public R<AccountSetGroup> getById(@RequestParam("id") String id) {
        AccountSetGroup accountSetGroup = projectGroupService.getById(id);
        return R.ok(accountSetGroup);
    }

    /**
     * 账套组下拉
     *
     * @return 下拉列表
     */
    @GetMapping("/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> commonEntities = projectGroupService.list();
        return R.ok(commonEntities);
    }

    /**
     * 批量删除账套组
     *
     * @param ids 参数
     * @return 是否成功
     */
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody List<String> ids) {
        boolean res = projectGroupService.delete(ids);
        return res ? R.ok() : R.failed("删除失败");
    }

    /**
     * 批量删除账套组
     *
     * @return 是否成功
     */
    @PostMapping("/accountSetListNoGroup")
    public R<List<CommonEntity>> accountSetListNoGroup() {
        List<CommonEntity> list = projectGroupService.accountSetListNoGroup();
        return R.ok(list);
    }
}
