package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.sdata.om.al.entity.BankReconciliation;
import cn.sdata.om.al.entity.BankReconciliationQuery;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.BankReconciliationSQRpaJob;
import cn.sdata.om.al.job.BankReconciliationZZRpaJob;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.BankReconciliationService;
import cn.sdata.om.al.utils.CommonUtil;
import cn.sdata.om.al.utils.DateUtils;
import cn.sdata.om.al.utils.LogBRUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.lang.reflect.Field;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bankReconciliation")
@Slf4j
public class BankReconciliationController {

    private BankReconciliationService bankReconciliationService;

    @Value("${file.br-export-log-file-path}")
    private String exportBRFilePath;

    @Autowired
    public void setBankReconciliationService(BankReconciliationService bankReconciliationService) {
        this.bankReconciliationService = bankReconciliationService;
    }

    /**
     * 银行对账分页信息
     *
     * @param bankReconciliationQuery 查询参数
     * @return 分页信息
     */
    @PostMapping("/page")
    public R<Page<BankReconciliation>> page(@RequestBody BankReconciliationQuery bankReconciliationQuery) {
        PageInfo<BankReconciliation> bankReconciliationPageInfo = bankReconciliationService.page(bankReconciliationQuery);
        Page<BankReconciliation> page = new Page<>();
        page.setCurrent(bankReconciliationQuery.getPageNo());
        page.setRecords(CollectionUtil.isNotEmpty(bankReconciliationPageInfo.getList()) ? bankReconciliationPageInfo.getList() : new ArrayList<>());
        page.setSize(bankReconciliationQuery.getPageSize());
        page.setTotal(bankReconciliationPageInfo.getTotal());
        return R.ok(page);
    }

    /**
     * 结算场所下拉框
     *
     * @return 结算场所
     */
    @GetMapping("/settlementLocationList")
    public R<List<CommonEntity>> settlementLocationList() {
        List<CommonEntity> commonEntities = bankReconciliationService.settlementLocationList();
        return R.ok(commonEntities);
    }

    /**
     * 证券名称下拉框
     *
     * @return 证券名称
     */
    @GetMapping("/securityNameList")
    public R<List<CommonEntity>> securityNameList() {
        List<CommonEntity> commonEntities = bankReconciliationService.securityNameList();
        return R.ok(commonEntities);
    }

    /**
     * 证券代码下拉框
     *
     * @return 证券代码
     */
    @GetMapping("/securityCodeList")
    public R<List<CommonEntity>> securitiesCodeList() {
        List<CommonEntity> commonEntities = bankReconciliationService.securitiesCodeList();
        return R.ok(commonEntities);
    }


    /**
     * 结算中心证券代码下拉框
     *
     * @return 证券代码
     */
    @GetMapping("/sourceCodeList")
    public R<List<CommonEntity>> sourceCodeList() {
        List<CommonEntity> commonEntities = bankReconciliationService.sourceCodeList();
        return R.ok(commonEntities);
    }

    /**
     * 差异标记
     *
     * @return 证券代码
     */
    @PostMapping("/markDifference")
    public R<Boolean> markDifference(@RequestBody JSONObject jsonObject) {
        List<String> ids = jsonObject.getList("ids", String.class);
        if (CollectionUtil.isEmpty(ids)) {
            BusinessException.throwException("没有选择行进行标记");
        }
        String currentUserId = SecureUtil.currentUserId();
        jsonObject.put("userId", currentUserId);
        List<JSONObject> idAndDateDatas = bankReconciliationService.selectDataDateByIds(ids);
        if (CollectionUtil.isNotEmpty(idAndDateDatas)) {
            Map<String, List<JSONObject>> dataDateInfo = idAndDateDatas.stream().collect(Collectors.groupingBy(n -> n.getString("dataDate")));
            dataDateInfo.forEach((date, info) -> {
                JSONObject params = new JSONObject();
                try {
                    jsonObject.put("ids", info.stream().map(n -> n.getString("id")).collect(Collectors.toList()));
                    params.put("username", SecureUtil.currentUserName());
                    params.put("beginTime", DateUtil.now());
                    String logId = IdUtil.getSnowflakeNextIdStr();
                    params.put("userId", currentUserId);
                    params.put("params", jsonObject.getList("ids", String.class));
                    params.put("differenceReasons", jsonObject.getString("differenceReasons"));
                    params.put("ids", ids);
                    params.put("logId", logId);
                    params.put("dataDate", date);
                    LogBRUtil.preMarkDiffLog(params);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Boolean res = false;
                try {
                    res = bankReconciliationService.markDifference(jsonObject);
                    params.put("endTime", DateUtil.now());
                    params.put("result", res);
                    params.put("status", CommonStatus.SUCCESS.name());
                } catch (Exception e) {
                    params.put("result", res);
                    params.put("exception", e.getMessage());
                    params.put("endTime", DateUtil.now());
                    params.put("status", CommonStatus.FAIL.name());
                } finally {
                    try {
                        LogBRUtil.postMarkDiffLog(params);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
        return R.ok();
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    @PostMapping("/getSyncTime")
    public R<JSONObject> getSyncTime(@RequestBody JSONObject jsonObject) {
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        List<JSONObject> res = bankReconciliationService.selectAllSyncTime();
        if (CollectionUtil.isNotEmpty(res)) {
            List<String> bankReconciliationDates = new ArrayList<>();
            List<String> settlementCompanyDates = new ArrayList<>();
            List<String> datesBetweenToStr = DateUtils.getDatesBetweenToStr(beginDate, endDate);
            if (CollectionUtil.isNotEmpty(datesBetweenToStr)) {
                for (String date : datesBetweenToStr) {
                    JSONObject jsonObject1 = bankReconciliationService.selectSyncTimeByEndDate(date);
                    if (jsonObject1 != null && !jsonObject1.isEmpty()) {
                        bankReconciliationDates.add(jsonObject1.getString("bankReconciliation"));
                        settlementCompanyDates.add(jsonObject1.getString("settlementCompany"));
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(bankReconciliationDates)) {
                jsonObject.put("bankReconciliation", bankReconciliationDates.get(bankReconciliationDates.size() - 1));
            } else {
                jsonObject.put("bankReconciliation", "");
            }
            if (CollectionUtil.isNotEmpty(settlementCompanyDates)) {
                jsonObject.put("settlementCompany", settlementCompanyDates.get(settlementCompanyDates.size() - 1));
            } else {
                jsonObject.put("settlementCompany", "");
            }
            return R.ok(jsonObject);
        }
        jsonObject.put("bankReconciliation", "");
        jsonObject.put("settlementCompany", "");
        return R.ok(jsonObject);
    }

    /**
     * 同步估值系统持仓
     *
     * @param jsonObject 选中的证券代码
     * @return 是否成功
     */
    @PostMapping("/syncBankReconciliation")
    public R<JSONObject> syncBankReconciliation(@RequestBody JSONObject jsonObject) {
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        // 记录截至日期的即时时间
        JSONObject jsonObject1 = bankReconciliationService.selectSyncTimeByEndDate(endDate);
        if (jsonObject1 != null && !jsonObject1.isEmpty()) {
            bankReconciliationService.updateSyncTime(endDate, DateUtil.now());
        } else {
            bankReconciliationService.insertSyncTime(endDate, DateUtil.now());
        }
        try {
            String status = bankReconciliationService.syncBankReconciliation(beginDate, endDate);
            if ("executing".equals(status)) {
                JSONObject object = new JSONObject();
                object.put("status", "executing");
                return R.ok(object);
            }
            JSONObject object = new JSONObject();
            object.put("status", "completed");
            return R.ok(object);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
    }


    /**
     * 同步估值系统持仓
     *
     * @param jsonObject 选中的证券代码
     * @return 是否成功
     */
    @PostMapping("/syncBankReconciliationV2")
    public R<JSONObject> syncBankReconciliationV2(@RequestBody JSONObject jsonObject) {
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        // 记录截至日期的即时时间
        JSONObject jsonObject1 = bankReconciliationService.selectSyncTimeByEndDate(endDate);
        if (jsonObject1 != null && !jsonObject1.isEmpty()) {
            bankReconciliationService.updateSyncTime(endDate, DateUtil.now());
        } else {
            bankReconciliationService.insertSyncTime(endDate, DateUtil.now());
        }
        try {
            String status = bankReconciliationService.syncBankReconciliationV2(beginDate, endDate);
            if ("executing".equals(status)) {
                JSONObject object = new JSONObject();
                object.put("status", "executing");
                return R.ok(object);
            }
            JSONObject object = new JSONObject();
            object.put("status", "completed");
            return R.ok(object);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
    }


    /**
     * 下载结算公司持仓文件
     */
    @GetMapping("/downloadSettlementCompanyFiles")
    public R<Boolean> downloadSettlementCompanyFiles(HttpServletResponse response, @RequestParam(value = "beginDate", required = false) String beginDate,
                                                     @RequestParam(value = "endDate", required = false) String endDate) {
        JSONObject jsonObject1 = bankReconciliationService.selectSyncTimeByEndDate(endDate);
        if (jsonObject1 != null && !jsonObject1.isEmpty()) {
            bankReconciliationService.updateSettlementSyncTime(endDate, DateUtil.now());
        } else {
            bankReconciliationService.insertSettlementSyncTime(endDate, DateUtil.now());
        }
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> fyZZRpaJobIds = cronService.getJobIdByClass(BankReconciliationZZRpaJob.class);
        if (CollectionUtil.isNotEmpty(fyZZRpaJobIds)) {
            JobDataMap zzJobDataMap = new JobDataMap();
            // 中债 对账单下载
            zzJobDataMap.put("username", SecureUtil.currentUserName());
            zzJobDataMap.put("beginDate", beginDate);
            zzJobDataMap.put("endDate", endDate);
            zzJobDataMap.put("dataDate", beginDate);
            zzJobDataMap.put("type", "MANUAL");
            cronService.startJobNow(fyZZRpaJobIds, zzJobDataMap);
        }
        List<String> fySQRpaJobIds = cronService.getJobIdByClass(BankReconciliationSQRpaJob.class);
        if (CollectionUtil.isNotEmpty(fySQRpaJobIds)) {
            JobDataMap sqJobDataMap = new JobDataMap();
            sqJobDataMap.put("beginDate", beginDate);
            sqJobDataMap.put("endDate", endDate);
            sqJobDataMap.put("dataDate", beginDate);
            sqJobDataMap.put("username", SecureUtil.currentUserName());
            sqJobDataMap.put("type", "MANUAL");
            cronService.startJobNow(fySQRpaJobIds, sqJobDataMap);
        }
        return R.ok(true);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody BankReconciliationQuery bankReconciliationQuery, HttpServletResponse response) throws Exception {
        List<String> columns = bankReconciliationQuery.getColumns();
        boolean isContainsDataDate = columns.contains("dataDate");
        String beginDataDate = bankReconciliationQuery.getBeginDataDate();
        String endDataDate = bankReconciliationQuery.getEndDataDate();
        bankReconciliationQuery.setPageNo(1);
        bankReconciliationQuery.setPageSize(Integer.MAX_VALUE);
        R<Page<BankReconciliation>> page = page(bankReconciliationQuery);
        Page<BankReconciliation> result = page.getResult();
        if (result != null) {
            JSONObject params = new JSONObject();
            try {
                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                params.put("username", SecureUtil.currentUserName());
                params.put("beginTime", DateUtil.now());
                params.put("params", JSON.toJSONString(bankReconciliationQuery));
                params.put("dataDate", beginDataDate);
                LogBRUtil.preExportLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                List<BankReconciliation> records = result.getRecords();
                if (CollectionUtil.isNotEmpty(records)) {
                    List<List<Object>> data = new ArrayList<>();
                    // 有列的选择
                    if (CollectionUtil.isEmpty(columns)) {
                        Field[] fields = ReflectUtil.getFields(BankReconciliation.class);
                        columns = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
                    }
                    // 为了判断日期，如果查询不带此列则自动加上日期列
                    if (!columns.contains("dataDate")) {
                        columns.add(0, "dataDate");
                    }
                    for (BankReconciliation bankReconciliation : records) {
                        // 每一行数据
                        List<Object> row = new ArrayList<>();
                        for (String column : columns) {
                            if (StringUtils.isNotBlank(column)) {
                                if (ReflectUtil.hasField(bankReconciliation.getClass(), column)) {
                                    Object fieldValue = ReflectUtil.getFieldValue(bankReconciliation, column);
                                    if ("valuationPositionQuantity".equals(column) || "settlementCompanyPositionQuantity".equals(column) || "difference".equals(column)) {
                                        // 处理数字每3位添加逗号
                                        row.add(CommonUtil.handleNumberStr(fieldValue));
                                    } else {
                                        if ("productCategory".equals(column)) {
                                            String s = String.valueOf(fieldValue);
                                            switch (s) {
                                                case "1":
                                                    fieldValue = "委受托";
                                                    break;
                                                case "2":
                                                    fieldValue = "债权投资计划";
                                                    break;
                                                case "3":
                                                    fieldValue = "组合产品";
                                                    break;
                                            }
                                        }
                                        row.add(Objects.requireNonNullElse(fieldValue, ""));
                                    }
                                } else {
                                    row.add("");
                                }
                            } else {
                                row.add("");
                            }
                        }
                        data.add(row);
                    }
                    List<Date> dates = new ArrayList<>();
                    for (List<Object> row : data) {
                        DateTime dateTime = DateUtil.parse(String.valueOf(row.get(0)), new SimpleDateFormat("yyyy年MM月dd日"));
                        dates.add(dateTime);
                    }
                    dates = dates.stream().sorted().collect(Collectors.toList());
                    if (!isContainsDataDate) {
                        // 如果原先选择的列中不包含日期 则手动去掉日期列
                        for (List<Object> row : data) {
                            row.remove(0);
                        }
                        columns.remove("dataDate");
                    }
                    // 创建一个excel文件
                    File file;
                    String fileName;
                    if (!beginDataDate.equals(endDataDate)) {
                        file = FileUtil.createTempFile("银行间持仓对账-", ".xlsx", true);
                        fileName = "银行间持仓对账-" +
                                (StringUtils.isNotBlank(beginDataDate) ? DateUtil.format(DateUtil.parseDate(beginDataDate), "yyyyMMdd") : DateUtil.format(dates.get(0), "yyyyMMdd"))
                                + "-"
                                + (StringUtils.isNotBlank(endDataDate) ? DateUtil.format(DateUtil.parseDate(endDataDate), "yyyyMMdd") : DateUtil.format(dates.get(dates.size() - 1), "yyyyMMdd"));
                    } else {
                        file = FileUtil.createTempFile("银行间持仓对账-", ".xlsx", true);
                        fileName = "银行间持仓对账-" +
                                (StringUtils.isNotBlank(beginDataDate) ? DateUtil.format(DateUtil.parseDate(beginDataDate), "yyyyMMdd") : DateUtil.format(dates.get(0), "yyyyMMdd"));
                    }
                    file = FileUtil.rename(file, fileName + "." + FileUtil.extName(file), true);
                    // 写入到文件
                    ExcelWriter excelWriter = new ExcelWriter(true);
                    excelWriter.writeHeadRow(getHeaderNames(columns));
                    excelWriter.write(data);
                    excelWriter.flush(file);
                    excelWriter.close();
                    try {
                        File dest = new File(exportBRFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                        FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                        params.put("fileUrl", dest.getAbsolutePath());
                        params.put("endTime", DateUtil.now());
                        params.put("status", CommonStatus.SUCCESS.name());
                        LogBRUtil.postExportLog(params);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    CommonUtil.downloadFile(response, file);
                }
            } catch (Exception e) {
                e.printStackTrace();
                params.put("endTime", DateUtil.now());
                params.put("exception", e.getMessage());
                params.put("status", CommonStatus.FAIL.name());
                LogBRUtil.postExportLog(params);
            }
        }
    }

    /**
     * 获取导出列中文名
     *
     * @param columns 原列名
     * @return 列中文名
     */
    private List<String> getHeaderNames(List<String> columns) {
        List<String> headers = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(columns)) {
            for (String column : columns) {
                if (StringUtils.isNotBlank(column)) {
                    switch (column) {
                        case "dataDate":
                            headers.add("数据日期");
                            break;
                        case "accountSetGroupName":
                            headers.add("账套组名称");
                            break;
                        case "accountSetName":
                            headers.add("账套名称");
                            break;
                        case "productCategory":
                            headers.add("账套分类");
                            break;
                        case "valuationTime":
                            headers.add("估值日期");
                            break;
                        case "productCode":
                            headers.add("账套代码");
                            break;
                        case "settlementLocation":
                            headers.add("结算场所");
                            break;
                        case "sourceCode":
                            headers.add("证券代码");
                            break;
                        case "securityName":
                            headers.add("证券名称");
                            break;
                        case "valuationPositionQuantity":
                            headers.add("估值持仓数量");
                            break;
                        case "settlementCompanyPositionQuantity":
                            headers.add("结算公司持仓数量");
                            break;
                        case "difference":
                            headers.add("差异");
                            break;
                        case "differenceReasons":
                            headers.add("差异原因");
                            break;
                        case "updateByName":
                            headers.add("更新人");
                            break;
                        case "updateTime":
                            headers.add("更新时间");
                            break;
                    }
                } else {
                    headers.add(column);
                }
            }
        }
        return headers;
    }

    /**
     * 临时上传文件
     *
     * @param files 文件
     * @return 是否成功
     */
    @PostMapping("/upload")
    public R<JSONObject> upload(@RequestParam("files") List<MultipartFile> files, @RequestParam(value = "date", required = false) String date) {
        try {
            log.info("获取到的日期为:{}", date);
            date = StringUtils.isBlank(date) ? DateUtil.today() : date;
            String status = bankReconciliationService.upload(files, "MANUAL", date);
            if ("executing".equals(status)) {
                JSONObject object = new JSONObject();
                object.put("status", "executing");
                return R.ok(object);
            }
            JSONObject object = new JSONObject();
            object.put("status", "completed");
            return R.ok(object);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
    }


    /**
     * 临时上传文件
     *
     * @param files 文件
     * @return 是否成功
     */
    @PostMapping("/uploadV2")
    public R<JSONObject> uploadV2(@RequestParam("files") List<MultipartFile> files, @RequestParam(value = "date", required = false) String date) {
        try {
            date = StringUtils.isBlank(date) ? DateUtil.today() : date;
            String status = bankReconciliationService.uploadV2(files, "MANUAL", date);
            if ("executing".equals(status)) {
                JSONObject object = new JSONObject();
                object.put("status", "executing");
                return R.ok(object);
            }
            JSONObject object = new JSONObject();
            object.put("status", "completed");
            return R.ok(object);
        } catch (Exception e) {
            return R.failed(e.getMessage());
        }
    }

}
