package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.BankReconciliationLogService;
import cn.sdata.om.al.utils.CommonUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/bankReconciliation/log")
public class BankReconciliationLogController {

    private BankReconciliationLogService bankReconciliationLogService;

    @Autowired
    public void setBankReconciliationLogService(BankReconciliationLogService bankReconciliationLogService) {
        this.bankReconciliationLogService = bankReconciliationLogService;
    }

    @GetMapping("/getLogList")
    public R<JSONObject> getLogList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "beginDataDate") String beginDataDate,
                                    @RequestParam(value = "endDataDate") String endDataDate) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("logBRImportFileRecord", new ArrayList<>());
        jsonObject.put("logBRExportFileRecord", new ArrayList<>());
        jsonObject.put("logBRMarkDiffRecord", new ArrayList<>());
        jsonObject.put("logBRSyncValuationRecord", new ArrayList<>());
        jsonObject.put("logBRRpaRecord", new ArrayList<>());
        R<BankReconciliationLog> bankReconciliationLogR = getLog(pageNo, pageSize, beginDataDate, endDataDate);
        if (bankReconciliationLogR != null && bankReconciliationLogR.isSuccess()) {
            BankReconciliationLog result = bankReconciliationLogR.getResult();
            Page<LogBRImportFileRecord> bankReconciliationImportLogList = result.getBankReconciliationImportLogList();
            Page<LogBRExportFileRecord> bankReconciliationLogExportList = result.getBankReconciliationLogExportList();
            Page<LogBRMarkDiffRecord> bankReconciliationLogDiffList = result.getBankReconciliationLogDiffList();
            Page<LogBRSyncValuationRecord> bankReconciliationLogSyncValuationList = result.getBankReconciliationLogSyncValuationList();
            Page<LogBRRPARecord> brrpaRecordPage = result.getBankReconciliationLogRpaRecordList();
            if (bankReconciliationImportLogList != null) {
                List<LogBRImportFileRecord> records = bankReconciliationImportLogList.getRecords();
                jsonObject.put("logBRImportFileRecord", records);
            }
            if (bankReconciliationImportLogList != null) {
                List<LogBRExportFileRecord> records = bankReconciliationLogExportList.getRecords();
                jsonObject.put("logBRExportFileRecord", records);
            }
            if (bankReconciliationImportLogList != null) {
                List<LogBRMarkDiffRecord> records = bankReconciliationLogDiffList.getRecords();
                jsonObject.put("logBRMarkDiffRecord", records);
            }
            if (bankReconciliationImportLogList != null) {
                List<LogBRSyncValuationRecord> records = bankReconciliationLogSyncValuationList.getRecords();
                jsonObject.put("logBRSyncValuationRecord", records);
            }
            if (brrpaRecordPage != null) {
                List<LogBRRPARecord> records = brrpaRecordPage.getRecords();
                jsonObject.put("logBRRpaRecord", records);
            }
        }
        return R.ok(jsonObject);
    }

    @GetMapping("/getLog")
    public R<BankReconciliationLog> getLog(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                           @RequestParam(value = "beginDataDate") String beginDataDate,
                                           @RequestParam(value = "endDataDate") String endDataDate) {
        BankReconciliationLog bankReconciliationLog = new BankReconciliationLog();
        R<Page<LogBRImportFileRecord>> logBRImportFileRecord = getLogBRImportFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogBRExportFileRecord>> logBRExportFileRecord = getLogBRExportFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogBRMarkDiffRecord>> logBRMarkDiffRecord = getLogBRMarkDiffRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogBRSyncValuationRecord>> logBRSyncValuationRecord = getLogBRSyncValuationRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogBRRPARecord>> brrpaRecordList = getLogBRRPARecordList(pageNo, pageSize, beginDataDate, endDataDate);
        if (logBRImportFileRecord != null && logBRImportFileRecord.isSuccess()) {
            Page<LogBRImportFileRecord> result = logBRImportFileRecord.getResult();
            bankReconciliationLog.setBankReconciliationImportLogList(result);
        }
        if (logBRExportFileRecord != null && logBRExportFileRecord.isSuccess()) {
            Page<LogBRExportFileRecord> result = logBRExportFileRecord.getResult();
            bankReconciliationLog.setBankReconciliationLogExportList(result);
        }
        if (logBRMarkDiffRecord != null && logBRMarkDiffRecord.isSuccess()) {
            Page<LogBRMarkDiffRecord> result = logBRMarkDiffRecord.getResult();
            bankReconciliationLog.setBankReconciliationLogDiffList(result);
        }
        if (logBRSyncValuationRecord != null && logBRSyncValuationRecord.isSuccess()) {
            Page<LogBRSyncValuationRecord> result = logBRSyncValuationRecord.getResult();
            bankReconciliationLog.setBankReconciliationLogSyncValuationList(result);
        }
        if (brrpaRecordList != null && brrpaRecordList.isSuccess()) {
            Page<LogBRRPARecord> result = brrpaRecordList.getResult();
            bankReconciliationLog.setBankReconciliationLogRpaRecordList(result);
        }
        return R.ok(bankReconciliationLog);
    }


    @GetMapping("/getLogBRImportFileRecord")
    public R<Page<LogBRImportFileRecord>> getLogBRImportFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                   @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                   @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogBRImportFileRecord> logBRImportFileRecordPageInfo = bankReconciliationLogService.pageLogImport(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogBRImportFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRImportFileRecordPageInfo.getList()) ? logBRImportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRImportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRImportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogBRMarkDiffRecord")
    public R<Page<LogBRMarkDiffRecord>> getLogBRMarkDiffRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                               @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                               @RequestParam(value = "beginDataDate") String beginDataDate,
                                                               @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogBRMarkDiffRecord> logBRMarkDiffRecordPageInfo = bankReconciliationLogService.pageLogDiff(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogBRMarkDiffRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRMarkDiffRecordPageInfo.getList()) ? logBRMarkDiffRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRMarkDiffRecordPageInfo.getPageSize());
        page.setTotal(logBRMarkDiffRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogBRExportFileRecord")
    public R<Page<LogBRExportFileRecord>> getLogBRExportFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                   @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                   @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogBRExportFileRecord> logBRExportFileRecordPageInfo = bankReconciliationLogService.pageLogExport(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogBRExportFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRExportFileRecordPageInfo.getList()) ? logBRExportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRExportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRExportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogBRSyncValuationRecord")
    public R<Page<LogBRSyncValuationRecord>> getLogBRSyncValuationRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogBRSyncValuationRecord> logBRSyncValuationRecordPageInfo = bankReconciliationLogService.pageLogSync(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogBRSyncValuationRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRSyncValuationRecordPageInfo.getList()) ? logBRSyncValuationRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRSyncValuationRecordPageInfo.getPageSize());
        page.setTotal(logBRSyncValuationRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/download")
    public void downloadFileByUrl(@RequestParam("url") String url, HttpServletResponse response) {
        File file = new File(url);
        try {
            if (file.exists()) {
                CommonUtil.downloadFile(response, file);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @GetMapping("/getLogBRRPARecordList")
    public R<Page<LogBRRPARecord>> getLogBRRPARecordList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogBRRPARecord> logBRRPARecordPageInfo = bankReconciliationLogService.getLogBRRPARecordList(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogBRRPARecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRRPARecordPageInfo.getList()) ? logBRRPARecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRRPARecordPageInfo.getPageSize());
        page.setTotal(logBRRPARecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/reExecuteRpa")
    public R<Boolean> reExecuteRpa(@RequestParam("rpaLogId") String rpaLogId) {
        Boolean res = bankReconciliationLogService.reExecuteRpa(rpaLogId);
        return res ? R.ok() : R.failed();
    }
}
