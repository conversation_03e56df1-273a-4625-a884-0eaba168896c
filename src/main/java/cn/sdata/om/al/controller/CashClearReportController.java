package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.cashClearReport.CashClearReportService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.io.Files;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资金清算报表控制器
 *
 * <AUTHOR>
 * @Date 2025/4/15 9:24
 * @Version 1.0
 */
@RestController
@RequestMapping("/cashClearReport")
@RequiredArgsConstructor
@Slf4j
public class CashClearReportController {

    private final CashClearReportService clearService;

    /**
     * 分页接口
     *
     * @return
     */
    @PostMapping("/page")
    public R<?> page(@RequestBody JSONObject params) {
        try {
            String dataDate = params.getString("dataDate");
            List<String> productIds = params.getList("productIds", String.class);
            String mailSendStatus = params.getString("mailSendStatus");
            int pageNo = params.getInteger("pageNo");
            int pageSize = params.getInteger("pageSize");
            return R.restResult(clearService.page(dataDate, dataDate, productIds, mailSendStatus, pageNo, pageSize), 200, "success");
        } catch (Exception e) {
            log.error("CashClearReportController_page_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 执行rpa
     *
     * @param dataDate yyyy-MM-dd
     * @return
     */
    @GetMapping("/exeRpa")
    public R<?> exeRpa(@RequestParam(value = "dataDate", required = false) String dataDate) {
        if (StringUtils.isBlank(dataDate)) {
            dataDate = DateUtil.today();
        }
        try {
            Assert.notNull(dataDate, "数据日期为空");
            clearService.exeRpa(dataDate);
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("CashClearReportController_exeRpa_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @GetMapping("/sendMail")
    public R<?> sendMail(@RequestParam("dateDate") String dataDate) {
        if (StringUtils.isBlank(dataDate)) {
            dataDate = DateUtil.today();
        }
        try {
            return clearService.sendMail(dataDate) ? R.ok("发送中") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("clearService_sendMail_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发邮件
     *
     * @param jsonObject 参数
     * @return 结果
     */
    @PostMapping("/sendMailV2")
    public R<?> sendMail(@RequestBody JSONObject jsonObject) {
        String dataDate = jsonObject.getString("dataDate");
        List<String> ids = jsonObject.getList("ids", String.class);
        if (StringUtils.isBlank(dataDate)) {
            dataDate = DateUtil.today();
        }
        try {
            return clearService.sendMailV2(dataDate, ids) ? R.ok("发送中") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("clearService_sendMail_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }


    @PostMapping("/download")
    public void downloadAllFiles(HttpServletResponse response, @RequestBody List<String> ids) {
        JSONObject params = new JSONObject();
        params.put("logId", IdUtil.getSnowflakeNextIdStr());
        try {
            List<CashClearReportEntity> cashClearReportEntities = clearService.getBaseMapper().selectBatchIds(ids);
            if (CollectionUtil.isEmpty(cashClearReportEntities)) {
                BusinessException.throwException("没有选择可下载的文件");
            }
            Set<String> fileIds = cashClearReportEntities.stream().map(CashClearReportEntity::getRemoteFileId).collect(Collectors.toSet());
            if (CollectionUtil.isEmpty(fileIds)) {
                BusinessException.throwException("没有选择可下载的文件");
            }
            List<RemoteFileInfo> remoteFileInfos = SpringUtil.getBean(RemoteFileInfoService.class).getBaseMapper().selectBatchIds(fileIds);
            if (CollectionUtil.isEmpty(remoteFileInfos)) {
                BusinessException.throwException("没有选择可下载的文件");
            }
            try {
                params.put("username", SecureUtil.currentUserName());
                params.put("beginTime", DateUtil.now());
                params.put("params", JSON.toJSONString(ids));
                params.put("dataDate", cashClearReportEntities.stream().map(n -> DateUtil.format(DateUtil.parseDate(n.getDataDate()), "yyyy-MM-dd")).findFirst().orElse(""));
                LogCCRUtil.preExportLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            File file = FileUtil.createTempFile("资金清算报表", ".zip", true);
            file = FileUtil.rename(file, "资金清算报表" + "." + FileUtil.extName(file), true);
            OmFileUtil.zipFiles(transformToFiles(remoteFileInfos), file);
            try {
                String exportCCRFilePath = SpringUtil.getProperty("file.ccr-export-file-path");
                File dest = new File(exportCCRFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                params.put("fileUrl", dest.getAbsolutePath());
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.SUCCESS.name());
                LogCCRUtil.postExportLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("exception", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogCCRUtil.postExportLog(params);
        }
    }


    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam("id") String id, HttpServletResponse response) {
        CashClearReportEntity cashClearReportEntity = clearService.getBaseMapper().selectById(id);
        if (ObjectUtils.isEmpty(cashClearReportEntity)) {
            BusinessException.throwException("未找到此文件");
        }
        String remoteFileId = cashClearReportEntity.getRemoteFileId();
        if (StringUtils.isEmpty(remoteFileId)) {
            BusinessException.throwException("未找到此文件");
        }
        RemoteFileInfo remoteFileInfo = SpringUtil.getBean(RemoteFileInfoService.class).getBaseMapper().selectById(remoteFileId);
        if (ObjectUtils.isEmpty(remoteFileInfo)) {
            BusinessException.throwException("未找到此文件");
        }
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", id);
            params.put("dataDate", DateUtil.format(DateUtil.parseDate(cashClearReportEntity.getDataDate()), "yyyy-MM-dd"));
            LogCCRUtil.preExportLog(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            String fileName = remoteFileInfo.getFileName();
            byte[] bytes = SpringUtil.getBean(SMBManager.class).downloadFile(remoteFileInfo.getRelativePath() + File.separator + remoteFileInfo.getFileName());
            File file = FileUtil.createTempFile(FileUtil.getPrefix(fileName), "." + FileUtil.extName(fileName), true);
            file = FileUtil.rename(file, FileUtil.getPrefix(fileName) + "." + FileUtil.extName(file), true);
            Files.write(bytes, file);
            try {
                String exportCCRFilePath = SpringUtil.getProperty("file.ccr-export-file-path");
                File dest = new File(exportCCRFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                params.put("fileUrl", dest.getAbsolutePath());
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.SUCCESS.name());
                LogCCRUtil.postExportLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("exception", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogCCRUtil.postExportLog(params);
        }
    }

    private List<File> transformToFiles(List<RemoteFileInfo> remoteFileInfos) {
        SMBManager smbManager = SpringUtil.getBean(SMBManager.class);
        List<File> res = new ArrayList<>();
        for (RemoteFileInfo remoteFileInfo : remoteFileInfos) {
            String fileName = remoteFileInfo.getFileName();
            byte[] bytes = smbManager.downloadFile(remoteFileInfo.getRelativePath() + File.separator + remoteFileInfo.getFileName());
            try {
                File file = FileUtil.createTempFile(FileUtil.getPrefix(fileName), "." + FileUtil.extName(fileName), true);
                file = FileUtil.rename(file, FileUtil.getPrefix(fileName) + "." + FileUtil.extName(file), true);
                Files.write(bytes, file);
                res.add(file);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return res;
    }
}
