package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CashClearReportLog;
import cn.sdata.om.al.entity.LogCCRExportRecord;
import cn.sdata.om.al.entity.LogCCRRPARecord;
import cn.sdata.om.al.entity.LogCCRSendMailRecord;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CashClearReportLogService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/cashClearReport/log")
public class CashClearReportLogController {

    private CashClearReportLogService cashClearReportLogService;

    @Autowired
    public void setCashClearReportLogService(CashClearReportLogService cashClearReportLogService) {
        this.cashClearReportLogService = cashClearReportLogService;
    }

    @GetMapping("/getLogList")
    public R<JSONObject> getLogList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "beginDataDate") String beginDataDate,
                                    @RequestParam(value = "endDataDate") String endDataDate) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("logCCRRPARecordPage", new ArrayList<>());
        jsonObject.put("logCCRExportRecordPage", new ArrayList<>());
        jsonObject.put("logCCRSendMailRecordPage", new ArrayList<>());
        R<CashClearReportLog> cashClearReportLogR = getLog(pageNo, pageSize, beginDataDate, endDataDate);
        if (cashClearReportLogR != null && cashClearReportLogR.isSuccess()) {
            CashClearReportLog result = cashClearReportLogR.getResult();
            Page<LogCCRRPARecord> logCCRRPARecordPage = result.getLogCCRRPARecordPage();
            Page<LogCCRExportRecord> logCCRExportRecordPage = result.getLogCCRExportRecordPage();
            Page<LogCCRSendMailRecord> logCCRSendMailRecordPage = result.getLogCCRSendMailRecordPage();
            if (logCCRRPARecordPage != null) {
                List<LogCCRRPARecord> records = logCCRRPARecordPage.getRecords();
                jsonObject.put("logCCRRPARecordPage", records);
            }
            if (logCCRExportRecordPage != null) {
                List<LogCCRExportRecord> records = logCCRExportRecordPage.getRecords();
                jsonObject.put("logCCRExportRecordPage", records);
            }
            if (logCCRSendMailRecordPage != null) {
                List<LogCCRSendMailRecord> records = logCCRSendMailRecordPage.getRecords();
                jsonObject.put("logCCRSendMailRecordPage", records);
            }
        }
        return R.ok(jsonObject);
    }

    @GetMapping("/getLog")
    public R<CashClearReportLog> getLog(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                        @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                        @RequestParam(value = "beginDataDate") String beginDataDate,
                                        @RequestParam(value = "endDataDate") String endDataDate) {
        CashClearReportLog cashClearReportLog = new CashClearReportLog();
        R<Page<LogCCRRPARecord>> logCCRRPARecordList = getLogCCRRPARecordList(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogCCRExportRecord>> logCCRExportRecordList = getLogCCRExportRecordList(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogCCRSendMailRecord>> logCCRSendMailRecordList = getLogCCRSendMailRecordList(pageNo, pageSize, beginDataDate, endDataDate);
        if (logCCRRPARecordList != null && logCCRRPARecordList.isSuccess()) {
            Page<LogCCRRPARecord> result = logCCRRPARecordList.getResult();
            cashClearReportLog.setLogCCRRPARecordPage(result);
        }
        if (logCCRExportRecordList != null && logCCRExportRecordList.isSuccess()) {
            Page<LogCCRExportRecord> result = logCCRExportRecordList.getResult();
            cashClearReportLog.setLogCCRExportRecordPage(result);
        }
        if (logCCRSendMailRecordList != null && logCCRSendMailRecordList.isSuccess()) {
            Page<LogCCRSendMailRecord> result = logCCRSendMailRecordList.getResult();
            cashClearReportLog.setLogCCRSendMailRecordPage(result);
        }
        return R.ok(cashClearReportLog);
    }


    @GetMapping("/getLogCCRRPARecordList")
    public R<Page<LogCCRRPARecord>> getLogCCRRPARecordList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                           @RequestParam(value = "beginDataDate") String beginDataDate,
                                                           @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogCCRRPARecord> logBRImportFileRecordPageInfo = cashClearReportLogService.getLogCCRRPARecordList(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogCCRRPARecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRImportFileRecordPageInfo.getList()) ? logBRImportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRImportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRImportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogCCRExportRecordList")
    public R<Page<LogCCRExportRecord>> getLogCCRExportRecordList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                 @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                 @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogCCRExportRecord> logBRMarkDiffRecordPageInfo = cashClearReportLogService.getLogCCRExportRecordList(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogCCRExportRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRMarkDiffRecordPageInfo.getList()) ? logBRMarkDiffRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRMarkDiffRecordPageInfo.getPageSize());
        page.setTotal(logBRMarkDiffRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogCCRSendMailRecordList")
    public R<Page<LogCCRSendMailRecord>> getLogCCRSendMailRecordList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                     @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                     @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogCCRSendMailRecord> logBRExportFileRecordPageInfo = cashClearReportLogService.getLogCCRSendMailRecordList(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogCCRSendMailRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRExportFileRecordPageInfo.getList()) ? logBRExportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRExportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRExportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }


    @GetMapping("/reExecuteRpa")
    public R<Boolean> reExecuteRpa(@RequestParam("rpaLogId") String rpaLogId) {
        Boolean res = cashClearReportLogService.reExecuteRpa(rpaLogId);
        return res ? R.ok() : R.failed();
    }
}
