package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CustodianBankContactsService;
import cn.sdata.om.al.vo.CustodianBankContactsVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@RequestMapping("custodian")
public class CustodianBankContactsController {

    private final CustodianBankContactsService custodianBankContactsService;

    @PostMapping("page")
    public R<?> page(@RequestBody CommonPageParam<CustodianBankContactsVO> commonPageParam){
        Page<CustodianBankContactsVO> page = new Page<>();
        page.setSize(commonPageParam.getSize());
        page.setCurrent(commonPageParam.getCurrent());
        Page<CustodianBankContactsVO> pageResult = custodianBankContactsService.getPageResult(page, commonPageParam.getParam());
        return R.ok(pageResult);
    }

    @PostMapping("add")
    public R<?> saveBatch(@RequestBody CustodianBankContactsParam custodianBankContactsParam){
        String id = custodianBankContactsParam.getId();
        if (id == null) {
            custodianBankContactsService.saveFromParam(custodianBankContactsParam);
        }else{
            custodianBankContactsService.updateFromParam(custodianBankContactsParam);
        }
        return R.ok("新增完成");
    }

    @GetMapping("get")
    public R<?> get(String id){
        CustodianBankContactsParam custodianBankContactsParam = custodianBankContactsService.getParamById(id);
        return R.ok(custodianBankContactsParam);
    }

    @GetMapping("remove")
    public R<?> remove(String id){
        custodianBankContactsService.removeAll(id);
        return R.ok("删除完成");
    }

    @GetMapping("banks")
    public R<?> listBank(){
        return R.ok(custodianBankContactsService.list().stream().map(CustodianBankContacts::getCustodianBank).distinct().collect(Collectors.toList()));
    }

    @GetMapping("products")
    public R<?> getProduct(){
        return R.ok(custodianBankContactsService.getBaseMapper().listProduct());
    }

    @GetMapping("list")
    public R<?> list(String role){
        return R.ok(custodianBankContactsService.getListResult(role));
    }

}
