package cn.sdata.om.al.controller;

import cn.sdata.om.al.dto.DividendParamDto;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.dividendDetail.DividendDetailService;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;


/**
 * 分红信息明细controller
 *
 * <AUTHOR>
 * @Date 2025/4/27 15:19
 * @Version 1.0
 */
@RestController
@RequestMapping("/dividendDetail")
@RequiredArgsConstructor
@Slf4j
public class DividendDetailController {

    private final DividendDetailService dividendDetailService;


    /**
     * 发送账套邮件
     *
     * @param dto
     * @return
     */
    @PostMapping("/sendMail")
    public R<?> sendMail(@RequestBody DividendParamDto dto) {
        try {
            Assert.notNull(dto.getDetails(), "账套信息参数为空");
            dividendDetailService.sendMail(dto.getDetails());
            return R.ok("发送成功");
        } catch (Exception e) {
            log.error("DividendDetailController_sendCustodianBankMail_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发送账套邮件-托管行
     *
     * @param dto
     * @return
     */
    @PostMapping("/sendMailBank")
    public R<?> sendMailBank(@RequestBody DividendParamDto dto) {
        try {
            Assert.notNull(dto.getDetails(), "账套信息参数为空");
            dividendDetailService.sendMailBank(dto.getDetails());
            return R.ok("发送成功");
        } catch (Exception e) {
            log.error("DividendDetailController_sendMailBank_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发送账套邮件
     *
     * @param dto
     * @return
     */
    @PostMapping("/sendMailInvestor")
    public R<?> sendMailInvestor(@RequestBody DividendParamDto dto) {
        try {
            Assert.notNull(dto.getDetails(), "账套信息参数为空");
            dividendDetailService.sendMailInvestor(dto.getDetails());
            return R.ok("发送成功");
        } catch (Exception e) {
            log.error("DividendDetailController_sendMailInvestor_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 执行rpa生成文件
     *
     * @param dto
     * @return
     */
    @PostMapping("/exeRpa")
    public R<?> exeRpa(@RequestBody DividendParamDto dto) {
        try {
            Assert.notNull(dto.getDataDate(), "数据日期为空");
            dividendDetailService.exeRpa(dto.getDataDate(), dto.getProductIds());
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("DividendDetailController_exeRpa_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * page接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/page")
    public R<?> page(@RequestBody DividendParamDto dto) {
        try {
            return R.ok(dividendDetailService.detailPage(dto));
        } catch (Exception e) {
            log.error("DividendDetailController_page_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取历史邮件
     *
     * @param detailId
     * @param dataDate
     * @return
     */
    @GetMapping("/historySendMails")
    public R<?> historySendMails(@RequestParam("detailId") String detailId, @RequestParam("dataDate") String dataDate) {
        try {
            return R.ok(dividendDetailService.historySendMails(detailId, dataDate));
        } catch (Exception e) {
            log.error("DividendDetailController_getSendMails_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取账套名称列表
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/getProductNames")
    public R<?> getProductNames(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        try {
            return R.ok(dividendDetailService.getProductNames(startDate, endDate));
        } catch (Exception e) {
            log.error("DividendDetailController_getProductNames_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 下载具体的单文件
     *
     * @param fileId
     * @return
     */
    @PostMapping("/downloadById")
    public ResponseEntity<InputStreamResource> downloadById(@RequestParam("fileId") String fileId) {
        try {
            return dividendDetailService.downloadById(fileId);
        } catch (Exception e) {
            log.error("DividendDetailController_downloadById_error:{},{}", e, e.getMessage());
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 获取日志记录数据
     *
     * @param startDate 数据日期
     * @param endDate   数据日期
     * @return
     */
    @GetMapping("/getDividendDetailLogs")
    public R<?> getDividendDetailLogs(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        try {
            return R.ok(dividendDetailService.getDividendDetailLogs(startDate, endDate));
        } catch (Exception e) {
            log.error("DividendDetailController_getDividendDetailLogs_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType
     * @return
     */
    @PostMapping("/downloadByLogId")
    public ResponseEntity<InputStreamResource> downloadByLogId(@RequestParam("id") String id,
                                                               @RequestParam("logType") String logType) {
        try {
            return dividendDetailService.downloadByLogId(id, logType);
        } catch (Exception e) {
            log.error("DividendDetailController_downloadByLogId_error:{}", e);
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 从rpa操作日志处操作rpa调用
     *
     * @param id rpa调用日志id
     * @return
     */
    @PostMapping("/logCallRpa")
    public R<?> logCallRpa(@RequestParam("id") String id) {
        try {
            dividendDetailService.logCallRpa(id);
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("DividendDetailController_logCallRpa_error:{}", e);
            return R.failed(e.getMessage());
        }
    }
}
