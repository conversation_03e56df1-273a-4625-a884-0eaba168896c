package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.InterbankFeesMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.InsuranceRegistrationFeesService;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/insuranceRegistrationFees")
public class InsuranceRegistrationFeesController {

    private InsuranceRegistrationFeesService insuranceRegistrationFeesService;

    @Autowired
    public void setInsuranceRegistrationFeesService(InsuranceRegistrationFeesService insuranceRegistrationFeesService) {
        this.insuranceRegistrationFeesService = insuranceRegistrationFeesService;
    }

    /**
     * 分页列表
     *
     * @param insuranceRegistrationFeesQuery 查询参数
     * @return 分页数据
     */
    @PostMapping("/page")
    public R<Page<InsuranceRegistrationFees>> page(@RequestBody InsuranceRegistrationFeesQuery insuranceRegistrationFeesQuery) {
        PageInfo<InsuranceRegistrationFees> insuranceRegistrationFeesPageInfo = insuranceRegistrationFeesService.page(insuranceRegistrationFeesQuery);
        Page<InsuranceRegistrationFees> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(insuranceRegistrationFeesPageInfo.getList()) ? insuranceRegistrationFeesPageInfo.getList() : new ArrayList<>());
        page.setCurrent(insuranceRegistrationFeesQuery.getPageNo());
        page.setTotal(insuranceRegistrationFeesPageInfo.getTotal());
        page.setSize(insuranceRegistrationFeesQuery.getPageSize());
        return R.ok(page);
    }

    /**
     * 上传缴费通知单
     *
     * @param files 通知单文件
     * @return 是否成功
     */
    @PostMapping("/upload")
    public R<JSONObject> upload(@RequestParam("files") MultipartFile[] files) {
        String status = insuranceRegistrationFeesService.upload(files, null, null);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    /**
     * 发送缴费通知单邮件
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/sendPaymentNotice")
    public R<Boolean> sendPaymentNotice(@RequestBody List<String> ids) {
        boolean res = insuranceRegistrationFeesService.sendEmail(ids);
        return R.ok(res);
    }

    /**
     * 导入O32
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/importO32")
    public R<JSONObject> importO32(@RequestBody List<String> ids) {
        String status = insuranceRegistrationFeesService.importO32(ids);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }


    /**
     * 获取详情 用于OCR确定功能
     *
     * @return 费用详细信息
     */
    @GetMapping("/getById")
    public R<List<JSONObject>> getById(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate) {
        List<JSONObject> jsonObjects = insuranceRegistrationFeesService.getById(beginDate, endDate);
        return R.ok(jsonObjects);
    }


    /**
     * 获取预览文件信息
     *
     * @param fileId 文件id
     * @return 预览信息
     */
    @GetMapping("/previewFile")
    public R<String> previewFile(@RequestParam("fileId") String fileId) {
        String jsonObjects = insuranceRegistrationFeesService.previewFile(fileId);
        return R.restResult(jsonObjects, 200, "ok");
    }

    /**
     * 更新中保登费用信息
     *
     * @param jsonObjects 修改对象
     * @return 是否成功
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody List<JSONObject> jsonObjects) {
        JSONObject params = new JSONObject();
        List<String> ids = new ArrayList<>();
        for (JSONObject jsonObject : jsonObjects) {
            List<JSONObject> subList = jsonObject.getList("list", JSONObject.class);
            for (JSONObject sub : subList) {
                ids.add(sub.getString("id"));
            }
        }
        List<String> productIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ids)) {
            List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesService.selectList(ids);
            if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                productIds = insuranceRegistrationFees.stream().map(InsuranceRegistrationFees::getProductId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            }
        }
        params.put("logId", IdUtil.getSnowflakeNextIdStr());
        params.put("beginTime", DateUtil.now());
        params.put("username", SecureUtil.currentUserName());
        params.put("params", JSON.toJSONString(jsonObjects));
        params.put("status", CommonStatus.EXECUTING.name());
        params.put("productIds", productIds);
        LogFYIUtils.preO32ConfirmLog(params);
        boolean res = insuranceRegistrationFeesService.update(jsonObjects);
        params.put("endTime", DateUtil.now());
        params.put("status", CommonStatus.SUCCESS.name());
        LogFYIUtils.postO32ConfirmLog(params);
        return res ? R.ok() : R.failed();
    }

    /**
     * 下载缴费通知单
     *
     * @param response 响应对象
     * @param ids      ids
     */
    @PostMapping("/download")
    public void downloadAllFiles(HttpServletResponse response, @RequestBody List<String> ids) {
        List<InsuranceRegistrationFeesFile> insuranceRegistrationFeesFiles = insuranceRegistrationFeesService.getFileByIds(ids);
        if (CollectionUtil.isEmpty(insuranceRegistrationFeesFiles)) {
            BusinessException.throwException("查询出的缴费通知单文件数据为空");
        }
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", JSON.toJSONString(ids));
            params.put("status", CommonStatus.EXECUTING.name());
            LogFYIUtils.preDownloadFile(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        File file = FileUtil.createTempFile("缴费通知单", ".zip", true);
        file = FileUtil.rename(file, "缴费通知单." + FileUtil.extName(file), true);
        try {
            List<File> fileList = new ArrayList<>();
            for (InsuranceRegistrationFeesFile insuranceRegistrationFeesFile : insuranceRegistrationFeesFiles) {
                String filePath = insuranceRegistrationFeesFile.getFilePath();
                if (StringUtils.isNotBlank(filePath)) {
                    File resFile = new File(filePath);
                    fileList.add(resFile);
                }
            }
            OmFileUtil.zipFiles(fileList, file);
            try {
                String downloadFYIFilePath = SpringUtil.getProperty("file.fyi-download-file-path");
                File dest = new File(downloadFYIFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.SUCCESS.name());
                params.put("fileUrl", dest.getAbsolutePath());
                LogFYIUtils.postDownloadFile(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.FAIL.name());
            params.put("errorMsg", e.getMessage());
            LogFYIUtils.postDownloadFile(params);
        }
    }

    /**
     * 更新支付状态
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/syncPaymentStatus")
    public R<JSONObject> syncPaymentStatus(@RequestBody List<String> ids) {
        String status = insuranceRegistrationFeesService.syncPaymentStatus(ids);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    @PostMapping("/downloadAndGenerateO32")
    public void downloadAndGenerateO32(HttpServletResponse response, @RequestBody List<String> ids) {
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", JSON.toJSONString(ids));
            params.put("status", CommonStatus.EXECUTING.name());
            LogFYIUtils.preGenerateO32File(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        File file = FileUtil.createTempFile("中保登O32文件", ".zip", true);
        file = FileUtil.rename(file, "中保登O32文件." + FileUtil.extName(file), true);
        List<InsuranceRegistrationFees> fees = insuranceRegistrationFeesService.selectList(ids);
        try {
            // 查询业务日切时间
            String initDate = null;
            try {
                List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectO32Date();
                if (CollectionUtil.isNotEmpty(jsonObjects)) {
                    JSONObject object = jsonObjects.get(0);
                    initDate = object.getString("L_INIT_DATE");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (StringUtils.isBlank(initDate)) {
                initDate = DateUtil.format(new Date(), "yyyyMMdd");
            }
            List<FileAndIds> fileList = SpringUtil.getBean(InterbankFeesService.class).generateInsuranceRegistrationFeesO32File(fees, 1, initDate);
            // 生成后需要上传至共享文件夹
            SpringUtil.getBean(InterbankFeesService.class).uploadShareFolder(fileList, 1, false, SecureUtil.currentUserName(), "MANUAL");
            if (CollectionUtil.isNotEmpty(fileList)) {
                List<File> collect = fileList.stream().map(FileAndIds::getFile).filter(n -> n != null && n.exists()).collect(Collectors.toList());
                OmFileUtil.zipFiles(collect, file);
                try {
                    params.put("endTime", DateUtil.now());
                    params.put("productIds", fees.stream().map(InsuranceRegistrationFees::getProductId).collect(Collectors.toList()));
                    String generateO32FYIFilePath = SpringUtil.getProperty("file.fyi-generate-file-path");
                    File dest = new File(generateO32FYIFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                    FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                    params.put("fileUrl", dest.getAbsolutePath());
                    params.put("status", CommonStatus.SUCCESS.name());
                    LogFYIUtils.postGenerateO32File(params);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                CommonUtil.downloadFile(response, file);
            }
        } catch (Exception e) {
            params.put("endTime", DateUtil.now());
            params.put("errorMsg", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogFYIUtils.postGenerateO32File(params);
            e.printStackTrace();
        }
    }

    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") String id) {
        boolean res = insuranceRegistrationFeesService.deleteById(id);
        return R.ok(res);
    }
}
