package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.FileAndIds;
import cn.sdata.om.al.entity.InterBankFees;
import cn.sdata.om.al.entity.InterBankFeesFile;
import cn.sdata.om.al.entity.InterbankFeesQuery;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.SQPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.job.ZZPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.mapper.InterbankFeesMapper;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.utils.CommonUtil;
import cn.sdata.om.al.utils.LogFYBUtils;
import cn.sdata.om.al.utils.OmFileUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/interbankFees")
public class InterbankFeesController {

    private InterbankFeesService interbankFeesService;

    @Autowired
    public void setInterbankFeesService(InterbankFeesService interbankFeesService) {
        this.interbankFeesService = interbankFeesService;
    }

    /**
     * 分页列表
     *
     * @param interbankFeesQuery 查询参数
     * @return 分页数据
     */
    @PostMapping("/page")
    public R<Page<InterBankFees>> page(@RequestBody InterbankFeesQuery interbankFeesQuery) {
        PageInfo<InterBankFees> interbankFeesPageInfo = interbankFeesService.page(interbankFeesQuery);
        Page<InterBankFees> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(interbankFeesPageInfo.getList()) ? interbankFeesPageInfo.getList() : new ArrayList<>());
        page.setCurrent(interbankFeesQuery.getPageNo());
        page.setTotal(interbankFeesPageInfo.getTotal());
        page.setSize(interbankFeesQuery.getPageSize());
        return R.ok(page);
    }

    /**
     * 上传缴费通知单
     *
     * @param files 通知单文件
     * @return 是否成功
     */
    @PostMapping("/upload")
    public R<JSONObject> upload(@RequestParam("files") MultipartFile[] files) {
        String status = interbankFeesService.upload(files, null, null);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    /**
     * 发送缴费通知单邮件
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/sendPaymentNotice")
    public R<Boolean> sendPaymentNotice(@RequestBody List<String> ids) {
        boolean res = interbankFeesService.sendPaymentNotice(ids);
        return R.ok(res);
    }

    /**
     * 导入O32
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/importO32")
    public R<JSONObject> importO32(@RequestBody List<String> ids) {
        String status = interbankFeesService.importO32(ids);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    /**
     * 更新支付状态
     *
     * @param ids 所选id
     * @return 是否成功
     */
    @PostMapping("/syncPaymentStatus")
    public R<JSONObject> syncPaymentStatus(@RequestBody List<String> ids) {
        String status = interbankFeesService.syncPaymentStatus(ids, 0);
        if ("executing".equals(status)) {
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    /**
     * 获取详情 用于OCR确定功能
     *
     * @return 费用详细信息
     */
    @GetMapping("/getById")
    public R<InterBankFees> getById(@RequestParam("id") String id) {
        InterBankFees interbankFees = interbankFeesService.getById(id);
        return R.ok(interbankFees);
    }

    /**
     * 更新银行间费用信息
     *
     * @param interbankFees 修改对象
     * @return 是否成功
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody InterBankFees interbankFees) {
        InterBankFees obj = new InterBankFees();
        BeanUtils.copyProperties(interbankFees, obj);
        JSONObject params = new JSONObject();
        if (StringUtils.isBlank(obj.getPaymentStatus())) {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("beginTime", DateUtil.now());
            params.put("username", SecureUtil.currentUserName());
            params.put("params", JSON.toJSONString(interbankFees));
            params.put("status", CommonStatus.EXECUTING.name());
            params.put("productIds", CollectionUtil.newArrayList(interbankFees.getProductId()));
            LogFYBUtils.preO32ConfirmLog(params);
        }
        boolean res = interbankFeesService.update(interbankFees);
        if (StringUtils.isBlank(obj.getPaymentStatus())) {
            if (StringUtils.isNotBlank(params.getString("logId"))) {
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.SUCCESS.name());
                LogFYBUtils.postO32ConfirmLog(params);
            }
        }
        return res ? R.ok() : R.failed();
    }

    /**
     * 下载缴费通知单
     *
     * @param response 响应对象
     * @param ids      ids
     */
    @PostMapping("/download")
    public void downloadAllFiles(HttpServletResponse response, @RequestBody List<String> ids) {
        List<InterBankFeesFile> interBankFeesFiles = interbankFeesService.getFileByIds(ids);
        if (CollectionUtil.isEmpty(interBankFeesFiles)) {
            BusinessException.throwException("查询出的缴费通知单文件数据为空");
        }
        JSONObject params = new JSONObject();
        params.put("logId", IdUtil.getSnowflakeNextIdStr());
        params.put("username", SecureUtil.currentUserName());
        params.put("beginTime", DateUtil.now());
        params.put("params", JSON.toJSONString(ids));
        params.put("status", CommonStatus.EXECUTING.name());
        LogFYBUtils.preDownloadFile(params);
        File file = FileUtil.createTempFile("缴费通知单", ".zip", true);
        file = FileUtil.rename(file, "缴费通知单." + FileUtil.extName(file), true);
        try {
            List<File> fileList = new ArrayList<>();
            for (InterBankFeesFile interBankFeesFile : interBankFeesFiles) {
                String filePath = interBankFeesFile.getFilePath();
                if (StringUtils.isNotBlank(filePath)) {
                    File resFile = new File(filePath);
                    fileList.add(resFile);
                }
            }
            OmFileUtil.zipFiles(fileList, file);
            String downloadFYBFilePath = SpringUtil.getProperty("file.fyb-download-file-path");
            File dest = new File(downloadFYBFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
            FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.SUCCESS.name());
            params.put("fileUrl", dest.getAbsolutePath());
            LogFYBUtils.postDownloadFile(params);
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.FAIL.name());
            params.put("errorMsg", e.getMessage());
            LogFYBUtils.postDownloadFile(params);
        }
    }

    @PostMapping("/downloadAndGenerateO32")
    public void downloadAndGenerateO32(HttpServletResponse response, @RequestBody List<String> ids) {
        JSONObject params = new JSONObject();
        params.put("logId", IdUtil.getSnowflakeNextIdStr());
        params.put("username", SecureUtil.currentUserName());
        params.put("beginTime", DateUtil.now());
        params.put("params", JSON.toJSONString(ids));
        params.put("status", CommonStatus.EXECUTING.name());
        LogFYBUtils.preGenerateO32File(params);
        File file = FileUtil.createTempFile("银行间O32文件", ".zip", true);
        file = FileUtil.rename(file, "银行间O32文件." + FileUtil.extName(file), true);
        List<InterBankFees> fees = interbankFeesService.selectList(ids);
        try {
            if (CollectionUtil.isEmpty(fees)) {
                BusinessException.throwException("查询的账套信息为空");
            }
            // 查询业务日切时间
            String initDate = null;
            try {
                List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectO32Date();
                if (CollectionUtil.isNotEmpty(jsonObjects)) {
                    JSONObject object = jsonObjects.get(0);
                    initDate = object.getString("L_INIT_DATE");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (StringUtils.isBlank(initDate)) {
                initDate = DateUtil.format(new Date(), "yyyyMMdd");
            }
            List<FileAndIds> fileList = interbankFeesService.generateO32File(fees, 0, initDate);
            // 生成后需要上传至共享文件夹
            interbankFeesService.uploadShareFolder(fileList, 0, false, SecureUtil.currentUserName(), "MANUAL");
            if (CollectionUtil.isNotEmpty(fileList)) {
                List<File> collect = fileList.stream().map(FileAndIds::getFile).filter(n -> n != null && n.exists()).collect(Collectors.toList());
                OmFileUtil.zipFiles(collect, file);
                params.put("endTime", DateUtil.now());
                params.put("productIds", fees.stream().map(InterBankFees::getProductId).collect(Collectors.toList()));
                String generateO32FYBFilePath = SpringUtil.getProperty("file.fyb-generate-file-path");
                File dest = new File(generateO32FYBFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                params.put("fileUrl", dest.getAbsolutePath());
                params.put("status", CommonStatus.SUCCESS.name());
                LogFYBUtils.postGenerateO32File(params);
                CommonUtil.downloadFile(response, file);
            }
        } catch (Exception e) {
            params.put("endTime", DateUtil.now());
            params.put("errorMsg", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogFYBUtils.postGenerateO32File(params);
            e.printStackTrace();
        }
    }

    @GetMapping("/executeRPA")
    public R<Boolean> executeRPA(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate) {
        // 解析日期并获取季度
        int startQuarter = getQuarterFromYearMonth(beginDate);
        int endQuarter = getQuarterFromYearMonth(endDate);
        if (startQuarter != endQuarter) {
            return R.failed("开始和结束日期不在同一个季度内");
        }
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> fyZZRpaJobIds = cronService.getJobIdByClass(ZZPaymentReceiptDownloadRpaJob.class);
        if (CollectionUtil.isNotEmpty(fyZZRpaJobIds)) {
            JobDataMap zzJobDataMap = new JobDataMap();
            // 中债 执行季度的第一个月 最后一个月  2025-1  2025-3
            zzJobDataMap.put("username", SecureUtil.currentUserName());
            List<String> zzDates = getZZDates(beginDate);
            zzJobDataMap.put("beginDate", zzDates.get(0));
            zzJobDataMap.put("endDate", zzDates.get(1));
            zzJobDataMap.put("type", "MANUAL");
            cronService.startJobNow(fyZZRpaJobIds, zzJobDataMap);
        }
        List<String> fySQRpaJobIds = cronService.getJobIdByClass(SQPaymentReceiptDownloadRpaJob.class);
        if (CollectionUtil.isNotEmpty(fySQRpaJobIds)) {
            JobDataMap sqJobDataMap = new JobDataMap();
            List<String> sqDates = getSQDates(beginDate);
            // 上清 执行季度的最后一个月 月初 月末 2025-03-01 2025-03-31
            sqJobDataMap.put("beginDate", sqDates.get(0));
            sqJobDataMap.put("endDate", sqDates.get(1));
            sqJobDataMap.put("username", SecureUtil.currentUserName());
            sqJobDataMap.put("type", "MANUAL");
            cronService.startJobNow(fySQRpaJobIds, sqJobDataMap);
        }
        return R.ok(true);
    }

    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") String id) {
        boolean res = interbankFeesService.deleteById(id);
        return R.ok(res);
    }


    private List<String> getZZDates(String inputMonth) {
        YearMonth targetMonth = YearMonth.parse(inputMonth);
        // 计算季度首尾月份
        YearMonth firstMonth = getFirstMonthOfQuarter(targetMonth);
        YearMonth lastMonth = getLastMonthOfQuarter(targetMonth);
        // 格式化为 yyyy-M
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M");
        return List.of(firstMonth.format(formatter), lastMonth.format(formatter));
    }

    private YearMonth getFirstMonthOfQuarter(YearMonth yearMonth) {
        int quarter = (yearMonth.getMonthValue() - 1) / 3 + 1; // 计算季度（1~4）
        int startMonth = (quarter - 1) * 3 + 1; // 季度首月月份（1月/4月/7月/10月）
        return YearMonth.of(yearMonth.getYear(), startMonth);
    }

    private List<String> getSQDates(String inputMonth) {
        YearMonth targetMonth = YearMonth.parse(inputMonth, DateTimeFormatter.ofPattern("yyyy-MM"));
        // 获取季度最后一个月
        YearMonth lastMonthOfQuarter = getLastMonthOfQuarter(targetMonth);
        // 计算首末日期
        LocalDate firstDay = lastMonthOfQuarter.atDay(1);
        LocalDate lastDay = lastMonthOfQuarter.atEndOfMonth();
        return List.of(LocalDateTimeUtil.format(firstDay, "yyyy-MM-dd"), LocalDateTimeUtil.format(lastDay, "yyyy-MM-dd"));
    }

    // 核心逻辑：获取季度最后一个月
    private YearMonth getLastMonthOfQuarter(YearMonth yearMonth) {
        int quarter = yearMonth.get(IsoFields.QUARTER_OF_YEAR); // 获取季度 (1~4)
        int lastMonth = quarter * 3; // 季度最后一个月 = 季度值 × 3
        return YearMonth.of(yearMonth.getYear(), lastMonth);
    }

    // 从"yyyy-MM"格式字符串获取季度
    private int getQuarterFromYearMonth(String yearMonthStr) {
        // 解析为YearMonth对象（如 "2025-01" -> 2025年1月）
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);
        // 转换为当月第一天（如 2025-01-01）
        LocalDate date = yearMonth.atDay(1);
        // 直接获取季度（1~4）
        return date.get(IsoFields.QUARTER_OF_YEAR);
    }
}
