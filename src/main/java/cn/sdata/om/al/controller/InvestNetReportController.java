package cn.sdata.om.al.controller;

import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.InvestNetReportService;
import cn.sdata.om.al.service.MarketTradeDayService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;

/**
 * 投连净值播报
 *
 * <AUTHOR>
 * @Date 2025/3/10 13:43
 * @Version 1.0
 */
@RestController
@RequestMapping("/investNetReport")
@RequiredArgsConstructor
@Slf4j
public class InvestNetReportController {

    private final InvestNetReportService reportService;

    private final MarketTradeDayService marketTradeDayService;

    /**
     * 上传【index reportYYYYMMDD】文件
     *
     * @param file
     * @param dataDate
     * @return
     */
    @PostMapping("/uploadIndexFile")
    public R<?> uploadIndexFile(@RequestParam("file") MultipartFile file, String dataDate) {
        try {
            if (file == null) return R.restResult(null, 400, "附件为空");
            return reportService.uploadIndexFile(file, dataDate) ? R.ok("上传成功") : R.failed("上传失败");
        } catch (Exception e) {
            log.error("investNetReport_uploadIndexFile_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }


    /**
     * 净值播报-分页接口
     *
     * @param dataDate            数据日期
     * @param dataStartDate       数据开始日期
     * @param dataEndDate         数据结束日期
     * @param downloadStatus      下载状态
     * @param netReportSendStatus 净值播报发送状态
     * @param netValueSendStatus  净值发送状态
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/page")
    public R<?> page(String dataDate,
                     String downloadStatus,
                     String netReportSendStatus,
                     String netValueSendStatus,
                     @RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                     @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        try {
            return R.restResult(reportService.page(dataDate, downloadStatus, netReportSendStatus, netValueSendStatus, pageNo, pageSize), 200, "success");
        } catch (Exception e) {
            log.error("investNetReport_page_error:{}", e);
            return R.failed(new Page<>(pageNo, pageSize));
        }
    }

    /**
     * 执行rpa
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/exeRpa")
    public R<?> exeRpa(@RequestParam("dataDate") String dataDate) {
        try {
            reportService.exeRpaAndHandleExcel(dataDate);
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("investNetReport_exeRpa_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发净值邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/sendNetValueMail")
    public R<?> sendNetValueMail(@RequestParam("dataDate") String dataDate) {
        try {
            return reportService.sendNetValueMail(dataDate) ? R.ok("发送成功") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("investNetReport_sendNetValueMail_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发播报邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/sendNetReportMail")
    public R<?> sendNetReportMail(@RequestParam("dataDate") String dataDate) {
        try {
            return reportService.sendNetReportMail(dataDate) ? R.ok("发送成功") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("investNetReport_sendNetReportMail_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 通过交易日获取前一交易日（沪深交易日）
     *
     * @param bizDate
     * @return
     */
    @GetMapping("/getDataDateByBizDate")
    public R<?> getDataDateByBizDate(String bizDate) {
        try {
            if (StringUtils.isBlank(bizDate)) bizDate = DateUtil.date().toDateStr();
            return R.restResult(marketTradeDayService.getDefaultMarketPreTradeDay(bizDate), 200, "success");
        } catch (Exception e) {
            log.error("investNetReport_getDataDateByBizDate_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 下载到本地
     *
     * @param dataDate yyyy-mm-dd
     * @return
     */
    @PostMapping("/downloadToLocal")
    public ResponseEntity<InputStreamResource> downloadToLocal(@RequestParam("dataDate") String dataDate) {
        try {
            return reportService.downloadReport4Files(dataDate);
        } catch (Exception e) {
            log.error("investNetReport_downloadToLocal_error:{}", e);
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 下载具体的单文件
     *
     * @param id
     * @return
     */
    @PostMapping("/downloadById")
    public ResponseEntity<InputStreamResource> downloadById(@RequestParam("id") String id) {
        try {
            return reportService.downloadById(id);
        } catch (Exception e) {
            log.error("investNetReport_downloadById_error:{}", e);
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType 功能日志类型 fileOper rpa fileGen mail
     * @return
     */
    @PostMapping("/downloadByLogId")
    public ResponseEntity<InputStreamResource> downloadByLogId(@RequestParam("id") String id,
                                                               @RequestParam("logType") String logType) {
        try {
            return reportService.downloadByLogId(id, logType);
        } catch (Exception e) {
            log.error("investNetReport_downloadByLogId_error:{}", e);
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 获取日志记录数据
     *
     * @param dataDate 数据日期
     * @return
     */
    @GetMapping("/getNetRportLogs")
    public R<?> getNetRportLogs(@RequestParam("dataDate") String dataDate) {
        try {
            return R.ok(reportService.getNetRportLogs(dataDate));
        } catch (Exception e) {
            log.error("investNetReport_getNetRportLogs_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 从rpa操作日志处操作rpa调用
     *
     * @param id rpa调用日志id
     * @return
     */
    @PostMapping("/logCallRpa")
    public R<?> logCallRpa(@RequestParam("id") String id) {
        try {
            reportService.logCallRpa(id);
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("investNetReport_logCallRpa_error:{}", e);
            return R.failed(e.getMessage());
        }
    }
}
