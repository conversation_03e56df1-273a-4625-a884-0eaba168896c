package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.entity.CustodianBankContacts;
import cn.sdata.om.al.entity.InvestorContacts;
import cn.sdata.om.al.entity.InvestorContactsParam;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.InvestorContactsService;
import cn.sdata.om.al.vo.InvestorContactVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.INVESTOR_TYPE;

@RestController
@RequestMapping("investor")
@AllArgsConstructor
public class InvestorContactsController {

    private final InvestorContactsService investorContactsService;

    @PostMapping("page")
    public R<?> page(@RequestBody CommonPageParam<InvestorContacts> commonPageParam){
        Page<InvestorContactVO> pageResult = investorContactsService.getPageResult(commonPageParam, INVESTOR_TYPE);
        return R.ok(pageResult);
    }

    @GetMapping("list")
    public R<?> list(){
        List<InvestorContactVO> listResult = investorContactsService.getListResult(INVESTOR_TYPE);
        return R.ok(listResult);
    }

    @PostMapping("add")
    public R<?> saveBatch(@RequestBody InvestorContactsParam investorContactsParam){
        String id = investorContactsParam.getId();
        if (id == null) {
            investorContactsService.saveFromParam(investorContactsParam, INVESTOR_TYPE);
        }else{
            investorContactsService.updateFromParam(investorContactsParam, INVESTOR_TYPE);
        }
        return R.ok("新增完成");
    }

    @GetMapping("get")
    public R<?> get(String id){
        InvestorContactsParam investorContactsParam = investorContactsService.getParamById(id);
        return R.ok(investorContactsParam);
    }

    @GetMapping("remove")
    public R<?> remove(String id){
        investorContactsService.removeAll(id, INVESTOR_TYPE);
        return R.ok("删除完成");
    }

    @GetMapping("investors")
    public R<?> listBank(){
        return R.ok(investorContactsService.list().stream().map(InvestorContacts::getInvestor).distinct().collect(Collectors.toList()));
    }

    @GetMapping("products")
    public R<?> getProducts(){
        return R.ok(investorContactsService.getBaseMapper().listProduct());
    }

}
