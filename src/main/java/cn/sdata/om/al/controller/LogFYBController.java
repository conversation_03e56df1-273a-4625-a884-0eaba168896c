package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.LogFYBService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/interbankFees/log")
public class LogFYBController {

    private LogFYBService logFYBService;

    @Autowired
    public void setLogFYBService(LogFYBService logFYBService) {
        this.logFYBService = logFYBService;
    }

    @GetMapping("/getLogList")
    public R<JSONObject> getLogList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "beginDataDate") String beginDataDate,
                                    @RequestParam(value = "endDataDate") String endDataDate) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("logFYBGenerateFileRecord", new ArrayList<>());
        jsonObject.put("logFYBDownloadFileRecord", new ArrayList<>());
        jsonObject.put("logFYBImportO32Record", new ArrayList<>());
        jsonObject.put("logFYBO32ConfirmRecord", new ArrayList<>());
        jsonObject.put("logFYBSendMailRecord", new ArrayList<>());
        jsonObject.put("logFYBSyncPayStatusRecord", new ArrayList<>());
        jsonObject.put("logFYBUpdatePayStatusRecord", new ArrayList<>());
        jsonObject.put("logFYBRPARecord", new ArrayList<>());
        R<FYBLog> fybLogR = getLog(pageNo, pageSize, beginDataDate, endDataDate);
        if (fybLogR != null && fybLogR.isSuccess()) {
            FYBLog result = fybLogR.getResult();
            Page<LogFYBGenerateFileRecord> logFYBGenerateFileRecord = result.getLogFYBGenerateFileRecord();
            Page<LogFYBDownloadFileRecord> logFYBDownloadFileRecord = result.getLogFYBDownloadFileRecord();
            Page<LogFYBImportO32Record> logFYBImportO32Record = result.getLogFYBImportO32Record();
            Page<LogFYBO32ConfirmRecord> logFYBO32ConfirmRecord = result.getLogFYBO32ConfirmRecord();
            Page<LogFYBSendMailRecord> logFYBSendMailRecord = result.getLogFYBSendMailRecord();
            Page<LogFYBSyncPayStatusRecord> logFYBSyncPayStatusRecord = result.getLogFYBSyncPayStatusRecord();
            Page<LogFYBUpdatePayStatusRecord> logFYBUpdatePayStatusRecord = result.getLogFYBUpdatePayStatusRecord();
            Page<LogFYBRPARecord> logFYBRPARecord = result.getLogFYBRPARecord();
            if (logFYBGenerateFileRecord != null) {
                List<LogFYBGenerateFileRecord> records = logFYBGenerateFileRecord.getRecords();
                jsonObject.put("logFYBGenerateFileRecord", records);
            }
            if (logFYBDownloadFileRecord != null) {
                List<LogFYBDownloadFileRecord> records = logFYBDownloadFileRecord.getRecords();
                jsonObject.put("logFYBDownloadFileRecord", records);
            }
            if (logFYBImportO32Record != null) {
                List<LogFYBImportO32Record> records = logFYBImportO32Record.getRecords();
                jsonObject.put("logFYBImportO32Record", records);
            }
            if (logFYBO32ConfirmRecord != null) {
                List<LogFYBO32ConfirmRecord> records = logFYBO32ConfirmRecord.getRecords();
                jsonObject.put("logFYBO32ConfirmRecord", records);
            }
            if (logFYBSendMailRecord != null) {
                List<LogFYBSendMailRecord> records = logFYBSendMailRecord.getRecords();
                jsonObject.put("logFYBSendMailRecord", records);
            }
            if (logFYBSyncPayStatusRecord != null) {
                List<LogFYBSyncPayStatusRecord> records = logFYBSyncPayStatusRecord.getRecords();
                jsonObject.put("logFYBSyncPayStatusRecord", records);
            }
            if (logFYBUpdatePayStatusRecord != null) {
                List<LogFYBUpdatePayStatusRecord> records = logFYBUpdatePayStatusRecord.getRecords();
                jsonObject.put("logFYBUpdatePayStatusRecord", records);
            }
            if (logFYBRPARecord != null) {
                List<LogFYBRPARecord> records = logFYBRPARecord.getRecords();
                jsonObject.put("logFYBRPARecord", records);
            }
        }
        return R.ok(jsonObject);
    }

    @GetMapping("/getLog")
    public R<FYBLog> getLog(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                            @RequestParam(value = "pageSize", required = false) Integer pageSize,
                            @RequestParam(value = "beginDataDate") String beginDataDate,
                            @RequestParam(value = "endDataDate") String endDataDate) {
        FYBLog fybLog = new FYBLog();
        R<Page<LogFYBGenerateFileRecord>> logFYBGenerateFileRecord = getLogFYBGenerateFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBDownloadFileRecord>> logFYBDownloadFileRecord = getLogFYBDownloadFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBImportO32Record>> logFYBImportO32Record = getLogFYBImportO32Record(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBO32ConfirmRecord>> logFYBO32ConfirmRecord = getLogFYBO32ConfirmRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBSendMailRecord>> logFYBSendMailRecord = getLogFYBSendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBSyncPayStatusRecord>> logFYBSyncPayStatusRecord = getLogFYBSyncPayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBUpdatePayStatusRecord>> logFYBUpdatePayStatusRecord = getLogFYBUpdatePayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYBRPARecord>> logFYBRPARecord = getLogFYBRPARecord(pageNo, pageSize, beginDataDate, endDataDate);
        if (logFYBGenerateFileRecord != null && logFYBGenerateFileRecord.isSuccess()) {
            Page<LogFYBGenerateFileRecord> result = logFYBGenerateFileRecord.getResult();
            fybLog.setLogFYBGenerateFileRecord(result);
        }
        if (logFYBDownloadFileRecord != null && logFYBDownloadFileRecord.isSuccess()) {
            Page<LogFYBDownloadFileRecord> result = logFYBDownloadFileRecord.getResult();
            fybLog.setLogFYBDownloadFileRecord(result);
        }
        if (logFYBImportO32Record != null && logFYBImportO32Record.isSuccess()) {
            Page<LogFYBImportO32Record> result = logFYBImportO32Record.getResult();
            fybLog.setLogFYBImportO32Record(result);
        }
        if (logFYBO32ConfirmRecord != null && logFYBO32ConfirmRecord.isSuccess()) {
            Page<LogFYBO32ConfirmRecord> result = logFYBO32ConfirmRecord.getResult();
            fybLog.setLogFYBO32ConfirmRecord(result);
        }
        if (logFYBSendMailRecord != null && logFYBSendMailRecord.isSuccess()) {
            Page<LogFYBSendMailRecord> result = logFYBSendMailRecord.getResult();
            fybLog.setLogFYBSendMailRecord(result);
        }
        if (logFYBSyncPayStatusRecord != null && logFYBSyncPayStatusRecord.isSuccess()) {
            Page<LogFYBSyncPayStatusRecord> result = logFYBSyncPayStatusRecord.getResult();
            fybLog.setLogFYBSyncPayStatusRecord(result);
        }
        if (logFYBUpdatePayStatusRecord != null && logFYBUpdatePayStatusRecord.isSuccess()) {
            Page<LogFYBUpdatePayStatusRecord> result = logFYBUpdatePayStatusRecord.getResult();
            fybLog.setLogFYBUpdatePayStatusRecord(result);
        }
        if (logFYBRPARecord != null && logFYBRPARecord.isSuccess()) {
            Page<LogFYBRPARecord> result = logFYBRPARecord.getResult();
            fybLog.setLogFYBRPARecord(result);
        }
        return R.ok(fybLog);
    }

    @GetMapping("/getLogFYBRPARecord")
    public R<Page<LogFYBRPARecord>> getLogFYBRPARecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                       @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                       @RequestParam(value = "beginDataDate") String beginDataDate,
                                                       @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBRPARecord> logFYBRPARecordPageInfo = logFYBService.getLogFYBRPARecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBRPARecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logFYBRPARecordPageInfo.getList()) ? logFYBRPARecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logFYBRPARecordPageInfo.getPageSize());
        page.setTotal(logFYBRPARecordPageInfo.getTotal());
        return R.ok(page);
    }


    @GetMapping("/getLogFYBGenerateFileRecord")
    public R<Page<LogFYBGenerateFileRecord>> getLogFYBGenerateFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBGenerateFileRecord> fybGenerateFileRecordPageInfo = logFYBService.getLogFYBGenerateFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBGenerateFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybGenerateFileRecordPageInfo.getList()) ? fybGenerateFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybGenerateFileRecordPageInfo.getPageSize());
        page.setTotal(fybGenerateFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYBDownloadFileRecord")
    public R<Page<LogFYBDownloadFileRecord>> getLogFYBDownloadFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBDownloadFileRecord> fybDownloadFileRecordPageInfo = logFYBService.getLogFYBDownloadFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBDownloadFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybDownloadFileRecordPageInfo.getList()) ? fybDownloadFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybDownloadFileRecordPageInfo.getPageSize());
        page.setTotal(fybDownloadFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYBImportO32Record")
    public R<Page<LogFYBImportO32Record>> getLogFYBImportO32Record(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                   @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                   @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBImportO32Record> fybImportO32RecordPageInfo = logFYBService.getLogFYBImportO32Record(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBImportO32Record> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybImportO32RecordPageInfo.getList()) ? fybImportO32RecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybImportO32RecordPageInfo.getPageSize());
        page.setTotal(fybImportO32RecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYBO32ConfirmRecord")
    public R<Page<LogFYBO32ConfirmRecord>> getLogFYBO32ConfirmRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                     @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                     @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBO32ConfirmRecord> fybo32ConfirmRecordPageInfo = logFYBService.getLogFYBO32ConfirmRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBO32ConfirmRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybo32ConfirmRecordPageInfo.getList()) ? fybo32ConfirmRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybo32ConfirmRecordPageInfo.getPageSize());
        page.setTotal(fybo32ConfirmRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYBSendMailRecord")
    public R<Page<LogFYBSendMailRecord>> getLogFYBSendMailRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                 @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                 @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBSendMailRecord> fybSendMailRecordPageInfo = logFYBService.getLogFYBSendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBSendMailRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybSendMailRecordPageInfo.getList()) ? fybSendMailRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybSendMailRecordPageInfo.getPageSize());
        page.setTotal(fybSendMailRecordPageInfo.getTotal());
        return R.ok(page);
    }


    @GetMapping("/getLogFYBSyncPayStatusRecord")
    public R<Page<LogFYBSyncPayStatusRecord>> getLogFYBSyncPayStatusRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                           @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                           @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBSyncPayStatusRecord> fybSyncPayStatusRecordPageInfo = logFYBService.getLogFYBSyncPayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBSyncPayStatusRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybSyncPayStatusRecordPageInfo.getList()) ? fybSyncPayStatusRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybSyncPayStatusRecordPageInfo.getPageSize());
        page.setTotal(fybSyncPayStatusRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYBUpdatePayStatusRecord")
    public R<Page<LogFYBUpdatePayStatusRecord>> getLogFYBUpdatePayStatusRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                               @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                               @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                               @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYBUpdatePayStatusRecord> fybUpdatePayStatusRecordPageInfo = logFYBService.getLogFYBUpdatePayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYBUpdatePayStatusRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybUpdatePayStatusRecordPageInfo.getList()) ? fybUpdatePayStatusRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybUpdatePayStatusRecordPageInfo.getPageSize());
        page.setTotal(fybUpdatePayStatusRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/reExecuteRpa")
    public R<Boolean> reExecuteRpa(@RequestParam("rpaLogId") String rpaLogId) {
        Boolean res = logFYBService.reExecuteRpa(rpaLogId);
        return res ? R.ok() : R.failed();
    }
}
