package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.LogFYIService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/insuranceRegistrationFees/log")
public class LogFYIController {

    private LogFYIService logFYIService;

    @Autowired
    public void setLogFYIService(LogFYIService logFYIService) {
        this.logFYIService = logFYIService;
    }

    @GetMapping("/getLogList")
    public R<JSONObject> getLogList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "beginDataDate") String beginDataDate,
                                    @RequestParam(value = "endDataDate") String endDataDate) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("logFYIGenerateFileRecord", new ArrayList<>());
        jsonObject.put("logFYIDownloadFileRecord", new ArrayList<>());
        jsonObject.put("logFYIImportO32Record", new ArrayList<>());
        jsonObject.put("logFYIO32ConfirmRecord", new ArrayList<>());
        jsonObject.put("logFYISendMailRecord", new ArrayList<>());
        jsonObject.put("logFYISyncPayStatusRecord", new ArrayList<>());
        jsonObject.put("logFYIUploadRecord", new ArrayList<>());
        R<FYILog> fybLogR = getLog(pageNo, pageSize, beginDataDate, endDataDate);
        if (fybLogR != null && fybLogR.isSuccess()) {
            FYILog result = fybLogR.getResult();
            Page<LogFYIGenerateFileRecord> logFYIGenerateFileRecord = result.getLogFYIGenerateFileRecord();
            Page<LogFYIDownloadFileRecord> logFYIDownloadFileRecord = result.getLogFYIDownloadFileRecord();
            Page<LogFYIImportO32Record> logFYIImportO32Record = result.getLogFYIImportO32Record();
            Page<LogFYIO32ConfirmRecord> logFYIO32ConfirmRecord = result.getLogFYIO32ConfirmRecord();
            Page<LogFYISendMailRecord> logFYISendMailRecord = result.getLogFYISendMailRecord();
            Page<LogFYISyncPayStatusRecord> logFYISyncPayStatusRecord = result.getLogFYISyncPayStatusRecord();
            Page<LogFYIUploadRecord> logFYIUploadRecord = result.getLogFYIUploadRecord();
            if (logFYIGenerateFileRecord != null) {
                List<LogFYIGenerateFileRecord> records = logFYIGenerateFileRecord.getRecords();
                jsonObject.put("logFYIGenerateFileRecord", records);
            }
            if (logFYIDownloadFileRecord != null) {
                List<LogFYIDownloadFileRecord> records = logFYIDownloadFileRecord.getRecords();
                jsonObject.put("logFYIDownloadFileRecord", records);
            }
            if (logFYIImportO32Record != null) {
                List<LogFYIImportO32Record> records = logFYIImportO32Record.getRecords();
                jsonObject.put("logFYIImportO32Record", records);
            }
            if (logFYIO32ConfirmRecord != null) {
                List<LogFYIO32ConfirmRecord> records = logFYIO32ConfirmRecord.getRecords();
                jsonObject.put("logFYIO32ConfirmRecord", records);
            }
            if (logFYISendMailRecord != null) {
                List<LogFYISendMailRecord> records = logFYISendMailRecord.getRecords();
                jsonObject.put("logFYISendMailRecord", records);
            }
            if (logFYISyncPayStatusRecord != null) {
                List<LogFYISyncPayStatusRecord> records = logFYISyncPayStatusRecord.getRecords();
                jsonObject.put("logFYISyncPayStatusRecord", records);
            }
            if (logFYIUploadRecord != null) {
                List<LogFYIUploadRecord> records = logFYIUploadRecord.getRecords();
                jsonObject.put("logFYIUploadRecord", records);
            }
        }
        return R.ok(jsonObject);
    }

    @GetMapping("/getLog")
    public R<FYILog> getLog(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                            @RequestParam(value = "pageSize", required = false) Integer pageSize,
                            @RequestParam(value = "beginDataDate") String beginDataDate,
                            @RequestParam(value = "endDataDate") String endDataDate) {
        FYILog fybLog = new FYILog();
        R<Page<LogFYIGenerateFileRecord>> logFYIGenerateFileRecord = getLogFYIGenerateFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYIDownloadFileRecord>> logFYIDownloadFileRecord = getLogFYIDownloadFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYIImportO32Record>> logFYIImportO32Record = getLogFYIImportO32Record(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYIO32ConfirmRecord>> logFYIO32ConfirmRecord = getLogFYIO32ConfirmRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYISendMailRecord>> logFYISendMailRecord = getLogFYISendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYISyncPayStatusRecord>> logFYISyncPayStatusRecord = getLogFYISyncPayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogFYIUploadRecord>> logFYIUploadRecord = getLogFYIUploadRecord(pageNo, pageSize, beginDataDate, endDataDate);
        if (logFYIGenerateFileRecord != null && logFYIGenerateFileRecord.isSuccess()) {
            Page<LogFYIGenerateFileRecord> result = logFYIGenerateFileRecord.getResult();
            fybLog.setLogFYIGenerateFileRecord(result);
        }
        if (logFYIDownloadFileRecord != null && logFYIDownloadFileRecord.isSuccess()) {
            Page<LogFYIDownloadFileRecord> result = logFYIDownloadFileRecord.getResult();
            fybLog.setLogFYIDownloadFileRecord(result);
        }
        if (logFYIImportO32Record != null && logFYIImportO32Record.isSuccess()) {
            Page<LogFYIImportO32Record> result = logFYIImportO32Record.getResult();
            fybLog.setLogFYIImportO32Record(result);
        }
        if (logFYIO32ConfirmRecord != null && logFYIO32ConfirmRecord.isSuccess()) {
            Page<LogFYIO32ConfirmRecord> result = logFYIO32ConfirmRecord.getResult();
            fybLog.setLogFYIO32ConfirmRecord(result);
        }
        if (logFYISendMailRecord != null && logFYISendMailRecord.isSuccess()) {
            Page<LogFYISendMailRecord> result = logFYISendMailRecord.getResult();
            fybLog.setLogFYISendMailRecord(result);
        }
        if (logFYISyncPayStatusRecord != null && logFYISyncPayStatusRecord.isSuccess()) {
            Page<LogFYISyncPayStatusRecord> result = logFYISyncPayStatusRecord.getResult();
            fybLog.setLogFYISyncPayStatusRecord(result);
        }
        if (logFYIUploadRecord != null && logFYIUploadRecord.isSuccess()) {
            Page<LogFYIUploadRecord> result = logFYIUploadRecord.getResult();
            fybLog.setLogFYIUploadRecord(result);
        }
        return R.ok(fybLog);
    }


    @GetMapping("/getLogFYIGenerateFileRecord")
    public R<Page<LogFYIGenerateFileRecord>> getLogFYIGenerateFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYIGenerateFileRecord> fybGenerateFileRecordPageInfo = logFYIService.getLogFYIGenerateFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYIGenerateFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybGenerateFileRecordPageInfo.getList()) ? fybGenerateFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybGenerateFileRecordPageInfo.getPageSize());
        page.setTotal(fybGenerateFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYIDownloadFileRecord")
    public R<Page<LogFYIDownloadFileRecord>> getLogFYIDownloadFileRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                         @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYIDownloadFileRecord> fybDownloadFileRecordPageInfo = logFYIService.getLogFYIDownloadFileRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYIDownloadFileRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybDownloadFileRecordPageInfo.getList()) ? fybDownloadFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybDownloadFileRecordPageInfo.getPageSize());
        page.setTotal(fybDownloadFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYIImportO32Record")
    public R<Page<LogFYIImportO32Record>> getLogFYIImportO32Record(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                   @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                   @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYIImportO32Record> fybImportO32RecordPageInfo = logFYIService.getLogFYIImportO32Record(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYIImportO32Record> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybImportO32RecordPageInfo.getList()) ? fybImportO32RecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybImportO32RecordPageInfo.getPageSize());
        page.setTotal(fybImportO32RecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYIO32ConfirmRecord")
    public R<Page<LogFYIO32ConfirmRecord>> getLogFYIO32ConfirmRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                     @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                     @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYIO32ConfirmRecord> fybo32ConfirmRecordPageInfo = logFYIService.getLogFYIO32ConfirmRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYIO32ConfirmRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybo32ConfirmRecordPageInfo.getList()) ? fybo32ConfirmRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybo32ConfirmRecordPageInfo.getPageSize());
        page.setTotal(fybo32ConfirmRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYISendMailRecord")
    public R<Page<LogFYISendMailRecord>> getLogFYISendMailRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                 @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                 @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYISendMailRecord> fybSendMailRecordPageInfo = logFYIService.getLogFYISendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYISendMailRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybSendMailRecordPageInfo.getList()) ? fybSendMailRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybSendMailRecordPageInfo.getPageSize());
        page.setTotal(fybSendMailRecordPageInfo.getTotal());
        return R.ok(page);
    }


    @GetMapping("/getLogFYISyncPayStatusRecord")
    public R<Page<LogFYISyncPayStatusRecord>> getLogFYISyncPayStatusRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                           @RequestParam(value = "beginDataDate") String beginDataDate,
                                                                           @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYISyncPayStatusRecord> fybSyncPayStatusRecordPageInfo = logFYIService.getLogFYISyncPayStatusRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYISyncPayStatusRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fybSyncPayStatusRecordPageInfo.getList()) ? fybSyncPayStatusRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fybSyncPayStatusRecordPageInfo.getPageSize());
        page.setTotal(fybSyncPayStatusRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogFYIUploadRecord")
    public R<Page<LogFYIUploadRecord>> getLogFYIUploadRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                             @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                             @RequestParam(value = "beginDataDate") String beginDataDate,
                                                             @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogFYIUploadRecord> fyiUploadRecordPageInfo = logFYIService.getLogFYIUploadRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogFYIUploadRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(fyiUploadRecordPageInfo.getList()) ? fyiUploadRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(fyiUploadRecordPageInfo.getPageSize());
        page.setTotal(fyiUploadRecordPageInfo.getTotal());
        return R.ok(page);
    }
}
