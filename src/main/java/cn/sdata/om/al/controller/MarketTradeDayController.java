package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MarketTradeDayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("trade-day")
@Slf4j
public class MarketTradeDayController {

    @Resource
    private MarketTradeDayService marketTradeDayService;

    /**
     * 通过市场获取交易日
     *
     * @param baseDate  日期
     * @param tradeType 交易类型
     * @return 交易日信息
     */
    @GetMapping(value = "get")
    public R<String> getTradeDay(String baseDate, String tradeType, int offset) {
        String netValueTradeDay = marketTradeDayService.getNetValueTradeDay(baseDate, tradeType, offset);
        return R.ok(netValueTradeDay, "");
    }

    @GetMapping(value = "between")
    public R<Map<String, String>> getBetweenTradeDay(String baseDate, String tradeType, int offset) {
        Map<String, String> netValueBetweenTradeDay = marketTradeDayService.getNetValueBetweenTradeDay(baseDate, tradeType, offset);
        return R.ok(netValueBetweenTradeDay);
    }

    /**
     * 获取表中非交易日的日期
     *
     * @return 非交易日的日期
     */
    @GetMapping("/nonTradingDays")
    public R<List<String>> getNonTradingDays() {
        DateTime date = DateUtil.date();
        DateTime beginOfYear = DateUtil.beginOfYear(date);
        DateTime endOfYear = DateUtil.endOfYear(date);
        String beginDate = DateUtil.format(beginOfYear, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endDate = DateUtil.format(endOfYear, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("beginDate:{}, endDate:{}", beginDate, endDate);
        List<String> nonTradingDays = marketTradeDayService.getNonTradingDays(beginDate, endDate);
        if (CollectionUtil.isNotEmpty(nonTradingDays)) {
            return R.ok(nonTradingDays.stream().map(n -> {
                DateTime dateTime = DateUtil.parse(n, DateTimeFormatter.ofPattern("yyyyMMdd"));
                return DateUtil.format(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }).collect(Collectors.toList()));
        }
        return R.ok(new ArrayList<>());
    }
}
