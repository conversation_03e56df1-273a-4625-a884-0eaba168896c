package cn.sdata.om.al.controller;

import cn.sdata.om.al.dto.MonthlyDataParamDto;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.monthlyData.MonthlyDataService;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 月度数据ctr
 *
 * <AUTHOR>
 * @Date 2025/4/7 13:35
 * @Version 1.0
 */
@RestController
@RequestMapping("/monthlyData")
@RequiredArgsConstructor
@Slf4j
public class MonthlyDataController {

    private final MonthlyDataService monthlyDataService;

    /**
     * 月度数据-分页接口
     *
     * @param dataDate       数据日期 yyyy-MM
     * @param taskName       任务名称
     * @param downloadStatus 文件状态
     * @param mailSendStatus 邮件发送状态
     * @param pageNo         页码
     * @param pageSize       每页数
     * @return
     */
    @GetMapping("/page")
    public R<?> page(String dataDate,
                     String taskName,
                     Integer downloadStatus,
                     String mailSendStatus,
                     @RequestParam(name = "pageNo", defaultValue = "1") int pageNo,
                     @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        try {
            return R.restResult(monthlyDataService.page(dataDate, taskName, downloadStatus, mailSendStatus, pageNo, pageSize), 200, "success");
        } catch (Exception e) {
            log.error("MonthlyDataService_page_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 执行rpa
     *
     * @param dto
     * @return
     */
    @PostMapping("/exeRpa")
    public R<?> exeRpa(@RequestBody MonthlyDataParamDto dto) {
        try {
            Assert.notNull(dto.getDataDate(), "数据日期为空");
            Assert.notNull(dto.getTaskTypes(), "任务类型为空");
            monthlyDataService.callRpa(dto.getDataDate(), dto.getTaskTypes());
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("MonthlyDataService_exeRpa_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发增值税台账邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/sendZZSTZMail")
    public R<?> sendZZSTZMail(@RequestParam("dataDate") String dataDate) {
        try {
            return monthlyDataService.sendZZSTZMail(dataDate) ? R.ok("发送中") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("MonthlyDataService_sendZZSTZMail_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 发债权投资计划净值邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/sendZQTZJHJZMail")
    public R<?> sendZQTZJHJZMail(@RequestParam("dataDate") String dataDate) {
        try {
            return monthlyDataService.sendZQTZJHJZMail(dataDate) ? R.ok("发送中") : R.failed("发送失败");
        } catch (Exception e) {
            log.error("MonthlyDataService_sendZQTZJHJZMail_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 下载到本地
     *
     * @param dto
     * @return
     */
    @PostMapping("/downloadToLocal")
    public ResponseEntity<InputStreamResource> downloadToLocal(@RequestBody MonthlyDataParamDto dto) {
        try {
            Assert.notNull(dto.getDataDate(), "数据日期为空");
            Assert.notNull(dto.getTaskTypes(), "任务类型为空");
            return monthlyDataService.downloadToLocal(dto.getDataDate(), dto.getTaskTypes());
        } catch (Exception e) {
            log.error("MonthlyDataService_downloadToLocal_error:{},{}", e, e.getMessage());
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 下载具体的单文件
     *
     * @param id
     * @return
     */
    @PostMapping("/downloadById")
    public ResponseEntity<InputStreamResource> downloadById(@RequestParam("id") String id) {
        try {
            return monthlyDataService.downloadById(id);
        } catch (Exception e) {
            log.error("MonthlyDataService_downloadById_error:{},{}", e, e.getMessage());
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 上传文件
     *
     * @param file     文件
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     */
    @PostMapping("/uploadZQFile")
    public R<?> uploadZQFile(@RequestParam("file") MultipartFile file, @RequestParam("dataDate") String dataDate) {
        try {
            if (file == null)
                return R.restResult(null, 400, "附件为空");
            monthlyDataService.uploadZQFile(file, dataDate);
            return R.ok("上传成功");
        } catch (Exception e) {
            log.error("monthlyDataService_uploadZQFile_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取任务名称列表接口
     *
     * @return
     */
    @GetMapping("/getTaskNames")
    public R<?> getTaskNames() {
        try {
            List<String> taskNames = Arrays.stream(MonthlyDataFileEnum.values()).map(MonthlyDataFileEnum::getTaskName).distinct().collect(Collectors.toList());
            return R.restResult(taskNames, 200, "success");
        } catch (Exception e) {
            log.error("monthlyDataService_getTaskNames_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }

    /**
     * 获取日志记录数据
     *
     * @param dataDate 数据日期
     * @return
     */
    @GetMapping("/getMonthlyDataLogs")
    public R<?> getMonthlyDataLogs(@RequestParam("dataDate") String dataDate) {
        try {
            return R.ok(monthlyDataService.getMonthlyDataLogs(dataDate));
        } catch (Exception e) {
            log.error("monthlyDataService_getMonthlyDataLogs_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType
     * @return
     */
    @PostMapping("/downloadByLogId")
    public ResponseEntity<InputStreamResource> downloadByLogId(@RequestParam("id") String id,
                                                               @RequestParam("logType") String logType) {
        try {
            return monthlyDataService.downloadByLogId(id, logType);
        } catch (Exception e) {
            log.error("monthlyDataService_downloadByLogId_error:{}", e);
            /*return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(e.getMessage().getBytes())));*/
            JSONObject res = new JSONObject();
            res.put("message", e.getMessage());
            res.put("code", HttpStatus.EXPECTATION_FAILED.value());
            return ResponseEntity
                    .status(HttpStatus.EXPECTATION_FAILED)
                    .body(new InputStreamResource(new ByteArrayInputStream(res.toJSONString().getBytes())));
        }
    }

    /**
     * 从rpa操作日志处操作rpa调用
     *
     * @param id
     * @return
     */
    @PostMapping("/logCallRpa")
    public R<?> logCallRpa(@RequestParam("id") String id) {
        try {
            Assert.notNull(id, "参数不能为空");
            monthlyDataService.logCallRpa(id);
            JSONObject object = new JSONObject();
            object.put("status", "executing");
            return R.ok(object);
        } catch (Exception e) {
            log.error("MonthlyDataService_logCallRpa_error:{},{}", e, e.getMessage());
            return R.failed(e.getMessage());
        }
    }
}
