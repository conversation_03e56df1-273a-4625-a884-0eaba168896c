package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.config.MonthlySettlementSyncConfig;
import cn.sdata.om.al.entity.MonthlySettlementList;
import cn.sdata.om.al.entity.MonthlySettlementListQuery;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.MonthlySettlementMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MonthlySettlementService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 月结模块
 */
@RestController
@RequestMapping("/monthlySettlement")
@Slf4j
public class MonthlySettlementController {

    private MonthlySettlementService monthlySettlementService;

    private MonthlySettlementMapper monthlySettlementMapper;

    @Autowired
    public void setMonthlySettlementMapper(MonthlySettlementMapper monthlySettlementMapper) {
        this.monthlySettlementMapper = monthlySettlementMapper;
    }

    @Autowired
    public void setMonthlySettlementService(MonthlySettlementService monthlySettlementService) {
        this.monthlySettlementService = monthlySettlementService;
    }

    /**
     * 分页列表
     *
     * @param monthlySettlementListQuery 查询参数
     * @return 分页列表
     */
    @PostMapping("/page")
    public R<Page<MonthlySettlementList>> page(@RequestBody MonthlySettlementListQuery monthlySettlementListQuery) {
        PageInfo<MonthlySettlementList> monthlySettlementListPageInfo = monthlySettlementService.page(monthlySettlementListQuery);
        Page<MonthlySettlementList> page = new Page<>();
        page.setCurrent(monthlySettlementListQuery.getPageNo());
        page.setRecords(CollectionUtil.isNotEmpty(monthlySettlementListPageInfo.getList()) ? monthlySettlementListPageInfo.getList() : new ArrayList<>());
        page.setSize(monthlySettlementListQuery.getPageSize());
        page.setTotal(monthlySettlementListPageInfo.getTotal());
        return R.ok(page);
    }

    /**
     * 生成文件
     *
     * @return 完成状态
     */
    @PostMapping("/generateFiles")
    public R<JSONObject> generateFiles(@RequestBody JSONObject jsonObject) {
        // 数据日期
        String date = jsonObject.getString("date");
        // 选择的顺序号
        List<String> orders = jsonObject.getList("orders", String.class);
        String type = jsonObject.getString("type");
        type = "AUTO".equals(type) ? "AUTO" : "MANUAL";
        if (CollectionUtil.isEmpty(orders)) {
            return R.failed("没有选择可从RPA下载的文件");
        }
        orders = orders.stream().distinct().collect(Collectors.toList());
        if (orders.size() == 1) {
            if (orders.get(0).equals("8") || orders.get(0).equals("4")) {
                BusinessException.throwException("此文件夹需要手工导入");
            }
        }
        // 用户自定义上传的不需要rpa
        if (orders.contains("8") || orders.contains("4")) {
            orders.remove("8");
            orders.remove("4");
        }
        Iterator<String> iterator = orders.iterator();
        while (iterator.hasNext()) {
            String order = iterator.next();
            int count = monthlySettlementMapper.selectDownloadStatus(date, order);
            if (orders.size() == 1) {
                if (count > 0) {
                    BusinessException.throwException("此任务正在下载中请稍后重试");
                }
            } else {
                if (count > 0) {
                    iterator.remove();
                }
            }
        }
        // 先更新此文件目录中的所有文件为排队中
        monthlySettlementMapper.updateFileDownloadStatus(date, orders, "3");
        String username;
        if (StringUtils.isNotBlank(jsonObject.getString("username"))) {
            username = jsonObject.getString("username");
        } else {
            username = SecureUtil.currentUser().getAccount();
        }
        // 将任务放置进队列中
        for (String order : orders) {
            JSONObject task = new JSONObject();
            task.put("order", order);
            task.put("date", date);
            task.put("username", username);
            task.put("type", type);
            log.info("-----提交任务到队列:{}", task);
            MonthlySettlementSyncConfig.taskQueue.add(task);
        }
        JSONObject object = new JSONObject();
        object.put("status", "completed");
        return R.ok(object);
    }

    /**
     * 下载文件
     */
    @PostMapping("/download")
    public void downloadAllFiles(HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        String date = jsonObject.getString("date");
        List<String> orders = jsonObject.getList("orders", String.class);
        if (CollectionUtil.isEmpty(orders)) {
            BusinessException.throwException("没有选择可下载的文件");
        }
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", JSON.toJSONString(jsonObject));
            params.put("dataDate", date);
            LogMSUtil.preExportLog(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<MonthlySettlementList> monthlySettlementLists = monthlySettlementMapper.selectMonthlySettlementByDateAndOrders(date, orders);
        if (CollectionUtil.isEmpty(monthlySettlementLists)) {
            BusinessException.throwException("查询出的月结文件数据为空");
        }
        File file = FileUtil.createTempFile("月结数据", ".7z", true);
        file = FileUtil.rename(file, "I39月度结账数据-" + date.replaceAll("-", ".") + "." + FileUtil.extName(file), true);
        try {
            OmFileUtil.getZipFile(monthlySettlementLists, file);
            String exportMSRFilePath = SpringUtil.getProperty("file.ms-export-file-path");
            File dest = new File(exportMSRFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
            FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
            params.put("fileUrl", dest.getAbsolutePath());
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.SUCCESS.name());
            LogMSUtil.postExportLog(params);
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("exception", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogMSUtil.postExportLog(params);
        }
    }


    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam("id") String id, HttpServletResponse response) {
        List<MonthlySettlementList> monthlySettlementLists = monthlySettlementService.selectMonthlySettlementByIds(CollectionUtil.newArrayList(id));
        if (CollectionUtil.isEmpty(monthlySettlementLists)) {
            BusinessException.throwException("未找到此文件");
        }
        MonthlySettlementList settlementList = monthlySettlementLists.get(0);
        String filePath = settlementList.getFilePath();
        int downloadStatus = settlementList.getDownloadStatus();
        if (StringUtils.isBlank(filePath) || downloadStatus != 1) {
            BusinessException.throwException("此文件未下载");
        }
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", id);
            params.put("dataDate", settlementList.getDataDate());
            LogMSUtil.preExportLog(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        File file = new File(filePath);
        try {
            String exportMSFilePath = SpringUtil.getProperty("file.ms-export-file-path");
            File dest = new File(exportMSFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
            FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
            params.put("fileUrl", dest.getAbsolutePath());
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.SUCCESS.name());
            LogMSUtil.postExportLog(params);
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            e.printStackTrace();
            params.put("endTime", DateUtil.now());
            params.put("exception", e.getMessage());
            params.put("status", CommonStatus.FAIL.name());
            LogMSUtil.postExportLog(params);
        }
    }


    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public R<List<String>> upload(@RequestParam("files") MultipartFile[] files, @RequestParam("date") String date) {
        List<String> failFileNames = new ArrayList<>();
        try {
            failFileNames = monthlySettlementService.upload(files, date);
            if (CollectionUtil.isNotEmpty(failFileNames)) {
                return R.failed(failFileNames, String.join(",", failFileNames) + " 上传匹配失败");
            }
            return R.ok(failFileNames, "上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            return R.failed(failFileNames, e.getMessage());
        }
    }

    /**
     * 发送邮件
     *
     * @return 是否成功
     */
    @PostMapping("/sendMail")
    public R<Boolean> sendMail(@RequestBody JSONObject jsonObject) {
        String date = jsonObject.getString("date");
        List<String> orders = jsonObject.getList("orders", String.class);
        if (CollectionUtil.isEmpty(orders)) {
            BusinessException.throwException("没有选择可发送的文件");
        }
        try {
            // 发送邮件中不包含 国际I9分类的
            orders.remove("12");
            orders.remove("13");
            List<MonthlySettlementList> settlementLists = monthlySettlementMapper.selectMonthlySettlementByDateAndOrders(date, orders);
            if (CollectionUtil.isEmpty(settlementLists)) {
                BusinessException.throwException("没有查询到要发送的文件");
            }
            monthlySettlementService.sendMailV1(date, orders, settlementLists);
            return R.ok();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

}
