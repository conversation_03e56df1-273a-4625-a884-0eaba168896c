package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MonthlySettlementLogService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/monthlySettlement/log")
public class MonthlySettlementLogController {

    private MonthlySettlementLogService monthlySettlementLogService;

    @Autowired
    public void setMonthlySettlementLogService(MonthlySettlementLogService monthlySettlementLogService) {
        this.monthlySettlementLogService = monthlySettlementLogService;
    }

    @GetMapping("/getLogList")
    public R<JSONObject> getLogList(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                    @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                    @RequestParam(value = "beginDataDate") String beginDataDate,
                                    @RequestParam(value = "endDataDate") String endDataDate) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("logMSExportRecordPage", new ArrayList<>());
        jsonObject.put("logMSFileOptRecordPage", new ArrayList<>());
        jsonObject.put("logMSRPARecordPage", new ArrayList<>());
        jsonObject.put("logMSSendMailRecordPage", new ArrayList<>());
        R<MonthlySettlementLog> monthlySettlementLogR = getLog(pageNo, pageSize, beginDataDate, endDataDate);
        if (monthlySettlementLogR != null && monthlySettlementLogR.isSuccess()) {
            MonthlySettlementLog result = monthlySettlementLogR.getResult();
            Page<LogMSExportRecord> logMSExportRecordPage = result.getLogMSExportRecordPage();
            Page<LogMSFileOptRecord> logMSFileOptRecordPage = result.getLogMSFileOptRecordPage();
            Page<LogMSRPARecord> logMSRPARecordPage = result.getLogMSRPARecordPage();
            Page<LogMSSendMailRecord> logMSSendMailRecordPage = result.getLogMSSendMailRecordPage();
            if (logMSExportRecordPage != null) {
                List<LogMSExportRecord> records = logMSExportRecordPage.getRecords();
                jsonObject.put("logMSExportRecordPage", records);
            }
            if (logMSFileOptRecordPage != null) {
                List<LogMSFileOptRecord> records = logMSFileOptRecordPage.getRecords();
                jsonObject.put("logMSFileOptRecordPage", records);
            }
            if (logMSRPARecordPage != null) {
                List<LogMSRPARecord> records = logMSRPARecordPage.getRecords();
                jsonObject.put("logMSRPARecordPage", records);
            }
            if (logMSSendMailRecordPage != null) {
                List<LogMSSendMailRecord> records = logMSSendMailRecordPage.getRecords();
                jsonObject.put("logMSSendMailRecordPage", records);
            }
        }
        return R.ok(jsonObject);
    }

    @GetMapping("/getLog")
    public R<MonthlySettlementLog> getLog(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                          @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                          @RequestParam(value = "beginDataDate") String beginDataDate,
                                          @RequestParam(value = "endDataDate") String endDataDate) {
        MonthlySettlementLog monthlySettlementLog = new MonthlySettlementLog();
        R<Page<LogMSExportRecord>> logBRImportFileRecord = getLogMSExportRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogMSFileOptRecord>> logBRExportFileRecord = getLogMSFileOptRecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogMSRPARecord>> logBRMarkDiffRecord = getLogMSRPARecord(pageNo, pageSize, beginDataDate, endDataDate);
        R<Page<LogMSSendMailRecord>> logBRSyncValuationRecord = getLogMSSendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        if (logBRImportFileRecord != null && logBRImportFileRecord.isSuccess()) {
            Page<LogMSExportRecord> result = logBRImportFileRecord.getResult();
            monthlySettlementLog.setLogMSExportRecordPage(result);
        }
        if (logBRExportFileRecord != null && logBRExportFileRecord.isSuccess()) {
            Page<LogMSFileOptRecord> result = logBRExportFileRecord.getResult();
            monthlySettlementLog.setLogMSFileOptRecordPage(result);
        }
        if (logBRMarkDiffRecord != null && logBRMarkDiffRecord.isSuccess()) {
            Page<LogMSRPARecord> result = logBRMarkDiffRecord.getResult();
            monthlySettlementLog.setLogMSRPARecordPage(result);
        }
        if (logBRSyncValuationRecord != null && logBRSyncValuationRecord.isSuccess()) {
            Page<LogMSSendMailRecord> result = logBRSyncValuationRecord.getResult();
            monthlySettlementLog.setLogMSSendMailRecordPage(result);
        }
        return R.ok(monthlySettlementLog);
    }


    @GetMapping("/getLogMSExportRecord")
    public R<Page<LogMSExportRecord>> getLogMSExportRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                           @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                           @RequestParam(value = "beginDataDate") String beginDataDate,
                                                           @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogMSExportRecord> logBRImportFileRecordPageInfo = monthlySettlementLogService.getLogMSExportRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogMSExportRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRImportFileRecordPageInfo.getList()) ? logBRImportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRImportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRImportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogMSFileOptRecord")
    public R<Page<LogMSFileOptRecord>> getLogMSFileOptRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                             @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                             @RequestParam(value = "beginDataDate") String beginDataDate,
                                                             @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogMSFileOptRecord> logBRMarkDiffRecordPageInfo = monthlySettlementLogService.getLogMSFileOptRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogMSFileOptRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRMarkDiffRecordPageInfo.getList()) ? logBRMarkDiffRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRMarkDiffRecordPageInfo.getPageSize());
        page.setTotal(logBRMarkDiffRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogMSRPARecord")
    public R<Page<LogMSRPARecord>> getLogMSRPARecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                     @RequestParam(value = "beginDataDate") String beginDataDate,
                                                     @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogMSRPARecord> logBRExportFileRecordPageInfo = monthlySettlementLogService.getLogMSRPARecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogMSRPARecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logBRExportFileRecordPageInfo.getList()) ? logBRExportFileRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logBRExportFileRecordPageInfo.getPageSize());
        page.setTotal(logBRExportFileRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/getLogMSSendMailRecord")
    public R<Page<LogMSSendMailRecord>> getLogMSSendMailRecord(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                               @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                               @RequestParam(value = "beginDataDate") String beginDataDate,
                                                               @RequestParam(value = "endDataDate") String endDataDate) {
        if (null == pageNo) {
            pageNo = 1;
        }
        if (null == pageSize) {
            pageSize = Integer.MAX_VALUE;
        }
        PageInfo<LogMSSendMailRecord> logMSSendMailRecordPageInfo = monthlySettlementLogService.getLogMSSendMailRecord(pageNo, pageSize, beginDataDate, endDataDate);
        Page<LogMSSendMailRecord> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(logMSSendMailRecordPageInfo.getList()) ? logMSSendMailRecordPageInfo.getList() : new ArrayList<>());
        page.setSize(logMSSendMailRecordPageInfo.getPageSize());
        page.setTotal(logMSSendMailRecordPageInfo.getTotal());
        return R.ok(page);
    }

    @GetMapping("/reExecuteRpa")
    public R<Boolean> reExecuteRpa(@RequestParam("rpaLogId") String rpaLogId) {
        Boolean res = monthlySettlementLogService.reExecuteRpa(rpaLogId);
        return res ? R.ok() : R.failed();
    }
}
