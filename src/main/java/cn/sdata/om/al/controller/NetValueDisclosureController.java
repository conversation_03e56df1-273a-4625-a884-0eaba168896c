package cn.sdata.om.al.controller;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.JobConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.ValuationTime;
import cn.sdata.om.al.job.InitNetValueDisclosureJob;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.NetValueDisclosureT1BatchService;
import cn.sdata.om.al.service.ValuationTableDataService;
import cn.sdata.om.al.vo.NetValueDisclosureVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.quartz.JobDataMap;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@RequestMapping("disclosure")
public class NetValueDisclosureController {

    private final NetValueDisclosureService netValueDisclosureService;
    private final AccountInformationService accountInformationService;
    private final ValuationTableDataService valuationTableDataService;
    private final NetValueDisclosureT1BatchService netValueDisclosureT1BatchService;
    private final CronService cronService;

    @PostMapping("page")
    public R<?> page(@RequestBody CommonPageParam<NetValueDisclosureVO> commonPageParam) {
        Page<NetValueDisclosureVO> page = commonPageParam.getPage();
        Map<String, Object> param = commonPageParam.getParam();
        ObjectMapper objectMapper = new ObjectMapper();
        NetValueDisclosureParam netValueDisclosureParam = objectMapper.convertValue(param, NetValueDisclosureParam.class);
        Page<NetValueDisclosureVO> result = netValueDisclosureService.pageList(netValueDisclosureParam, page);
        return R.ok(result);
    }

    @GetMapping("batch/list")
    public R<?> getBatchList(){
        return R.ok(netValueDisclosureT1BatchService.list());
    }

    @PostMapping("batch/update")
    public R<?> updateBatchInfo(@RequestBody List<NetValueDisclosureT1Batch> netValueDisclosureT1Batches){
        for (NetValueDisclosureT1Batch netValueDisclosureT1Batch : netValueDisclosureT1Batches) {
            LambdaUpdateWrapper<NetValueDisclosureT1Batch> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(NetValueDisclosureT1Batch::getBatchTime, netValueDisclosureT1Batch.getBatchTime());
            updateWrapper.set(NetValueDisclosureT1Batch::getProductIds, netValueDisclosureT1Batch.getProductIds());
            updateWrapper.eq(NetValueDisclosureT1Batch::getId, netValueDisclosureT1Batch.getId());
            netValueDisclosureT1BatchService.update(updateWrapper);
        }
        return R.ok("修改完成");
    }

    @GetMapping("account/name/list")
    public R<?> getAccountNameList(String valuationTime) {
        valuationTime = Optional.of(valuationTime).orElse(ValuationTime.T0.name());
        LambdaQueryWrapper<AccountInformation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountInformation::getValuationTime, valuationTime);
        queryWrapper.eq(AccountInformation::getProductCategory, 3);
        List<CommonEntity> collect = accountInformationService.list(queryWrapper).stream()
                .map(accountInformation -> new CommonEntity(accountInformation.getId(), accountInformation.getFullProductName()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @GetMapping("account/batch/name/list")
    public R<?> getAccountBatchNameList() {
        Set<String> allBatchProductIds = netValueDisclosureT1BatchService.getAllBatchProductIds();
        LambdaQueryWrapper<AccountInformation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountInformation::getValuationTime, ValuationTime.T1.name());
        queryWrapper.eq(AccountInformation::getProductCategory, 3);
        List<CommonEntity> collect = accountInformationService.list(queryWrapper).stream()
                .filter(accountInformation -> !allBatchProductIds.contains(accountInformation.getId())).map(accountInformation -> new CommonEntity(accountInformation.getId(), accountInformation.getFullProductName()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @GetMapping("account/code/list")
    public R<?> getAccountList(String valuationTime) {
        valuationTime = Optional.of(valuationTime).orElse(ValuationTime.T0.name());
        LambdaQueryWrapper<AccountInformation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountInformation::getValuationTime, valuationTime);
        queryWrapper.eq(AccountInformation::getProductCategory, 3);
        List<CommonEntity> collect = accountInformationService.list(queryWrapper).stream()
                .map(accountInformation -> new CommonEntity(accountInformation.getProductCode(), accountInformation.getProductCode()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @GetMapping("show")
    public R<?> show(String productId, String dataDate){
        return R.ok(netValueDisclosureService.showRecords(productId, dataDate));
    }

    @PostMapping("sync")
    public R<?> sync(@RequestBody List<NetValueDisclosureExecuteParam> params) {
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            productIds = syncValuation(productIds, dataDate, param.getValuationTime());
            netValueDisclosureService.syncValuationData(productIds, dataDate, param.getValuationTime());
        }
        return R.ok("同步完成");
    }

    @PostMapping("valuation/download")
    public R<?> downloadValuation(@RequestBody List<NetValueDisclosureExecuteParam> params) {
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.startDownloadValuationJob(dataDate, productIds, true);
        }
        return R.ok("调度成功");
    }

    @PostMapping("balance/download")
    public R<?> downloadBalance(@RequestBody List<NetValueDisclosureExecuteParam> params) {
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.startDownloadBalanceJob(dataDate, productIds);
        }
        return R.ok("调度成功");
    }

    @PostMapping("recon/status/update")
    public R<?> modifyReconStatus(@RequestBody List<NetValueDisclosureExecuteParam> params) {
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.modifyReconStatus(productIds, dataDate, param.getReconStatus(), param.getReconReason());
        }
        return R.ok("修改成功");
    }

    @PostMapping("net-value/send")
    public R<?> sendNetValue(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendNetValueDisclosure(productIds, dataDate);
        }
        return R.ok("发送成功");
    }

    @PostMapping("custodian/send")
    public R<?> sendCustodianBankMail(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.startDownloadValuationJob(dataDate, productIds, false);
            netValueDisclosureService.startDownloadBalanceJob(dataDate, productIds);
        }
        return R.ok("发送成功");
    }

    @PostMapping("custodian/real/send")
    public R<?> realSendCustodianBankMail(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendCustodianBankMail(productIds, dataDate);
        }
        return R.ok("发送成功");
    }

    @PostMapping("investor/send")
    public R<?> sendInvestorMail(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendInvestorMail(productIds, dataDate);
        }
        return R.ok("发送成功");
    }

    @PostMapping("szt/send")
    public R<?> sendSZT(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendToSZT(productIds, dataDate);
        }
        return R.ok("发送深证通成功");
    }

    @PostMapping("third/send")
    public R<?> sendThirdMail(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendThirdMail(productIds, dataDate);
            netValueDisclosureService.sendToSZTThird(productIds, dataDate);
        }
        return R.ok("发送成功");
    }

    @PostMapping("statement/send")
    public R<?> sendInvestorStatementMail(@RequestBody List<NetValueDisclosureExecuteParam> params){
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            netValueDisclosureService.sendInvestorStatementMail(productIds, dataDate);
        }
        return R.ok("发送成功");
    }

    private String dealDataDate(String dataDate) {
        if (dataDate == null || dataDate.isEmpty()) {
            return new SimpleDateFormat(BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN).format(new Date());
        }
        return dataDate;
    }

    @PostMapping("value/sync")
    public R<?> saveNetValueFromValuation(@RequestBody List<NetValueDisclosureExecuteParam> params) {
        for (NetValueDisclosureExecuteParam param : params) {
            String dataDate = dealDataDate(param.getDataDate());
            List<String> productIds = param.getProductIds();
            String valuationTime = param.getValuationTime();
            syncValuation(productIds, dataDate, valuationTime);
        }
        return R.ok();
    }

    private List<String> syncValuation(List<String> productIds, String dataDate, String valuationTime) {
        Objects.requireNonNull(dataDate, "数据日期不能为空");
        if (productIds == null || productIds.isEmpty()) {
            LambdaQueryWrapper<AccountInformation> queryWrapper = new LambdaQueryWrapper<>();
            if (valuationTime != null) {
                queryWrapper.eq(AccountInformation::getValuationTime, valuationTime);
            }
            if (productIds == null) {
                productIds = new ArrayList<>();
            }
            productIds.addAll(accountInformationService.list(queryWrapper).stream().map(AccountInformation::getId).collect(Collectors.toList()));
        }
        List<ValuationTableData> netValueFromValuation = netValueDisclosureService.getNetValueFromValuation(dataDate, productIds);
        LambdaQueryWrapper<ValuationTableData> valuationTableDataQueryWrapper = new LambdaQueryWrapper<>();
        valuationTableDataQueryWrapper.eq(ValuationTableData::getValuationDate, dataDate);
        valuationTableDataQueryWrapper.in(ValuationTableData::getProductId, productIds);
        valuationTableDataService.remove(valuationTableDataQueryWrapper);
        valuationTableDataService.saveBatch(netValueFromValuation);
        return productIds;
    }

    @GetMapping("init")
    public R<?> initValuationTable(String dataDate){
        List<String> jobIds = cronService.getJobIdByClass(InitNetValueDisclosureJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(JobConstant.DATA_DATE, dataDate);
        cronService.startJobNow(jobIds, jobDataMap);
        return R.ok();
    }

}
