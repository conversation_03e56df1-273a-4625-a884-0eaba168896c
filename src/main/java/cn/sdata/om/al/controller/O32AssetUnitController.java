package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.O32AssetUnit;
import cn.sdata.om.al.entity.O32AssetUnitQuery;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.O32AssetUnitService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/assetUnit")
public class O32AssetUnitController {

    private O32AssetUnitService o32AssetUnitService;

    @Autowired
    public void setO32AssetUnitService(O32AssetUnitService o32AssetUnitService) {
        this.o32AssetUnitService = o32AssetUnitService;
    }

    @PostMapping("/page")
    public R<Page<O32AssetUnit>> page(@RequestBody O32AssetUnitQuery o32AssetUnitQuery) {
        PageInfo<O32AssetUnit> o32AssetUnitPageInfo = o32AssetUnitService.page(o32AssetUnitQuery);
        Page<O32AssetUnit> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(o32AssetUnitPageInfo.getList()) ? o32AssetUnitPageInfo.getList() : new ArrayList<>());
        page.setCurrent(o32AssetUnitQuery.getPageNo());
        page.setTotal(o32AssetUnitPageInfo.getTotal());
        page.setSize(o32AssetUnitQuery.getPageSize());
        return R.ok(page);
    }

    @PostMapping("/import")
    public R<Boolean> importO32AssetUnit(@RequestParam("file") MultipartFile file) {
        boolean res = o32AssetUnitService.importO32AssetUnit(file);
        return res ? R.ok() : R.failed();
    }

    @PostMapping("/getById")
    public R<O32AssetUnit> getById(@RequestParam("id") String id) {
        O32AssetUnit res = o32AssetUnitService.getById(id);
        return R.ok(res);
    }

    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody O32AssetUnit o32AssetUnit) {
        boolean res = o32AssetUnitService.saveOrUpdate(o32AssetUnit);
        return res ? R.ok() : R.failed();
    }

    @GetMapping("/codeList")
    public R<List<CommonEntity>> codeList() {
        List<CommonEntity> res = o32AssetUnitService.codeList();
        return R.ok(res);
    }

    @GetMapping("/nameList")
    public R<List<CommonEntity>> nameList() {
        List<CommonEntity> res = o32AssetUnitService.nameList();
        return R.ok(res);
    }
}
