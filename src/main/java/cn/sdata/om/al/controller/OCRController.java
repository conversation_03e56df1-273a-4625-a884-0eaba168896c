package cn.sdata.om.al.controller;

import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.result.R;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

@RestController
@RequestMapping("ocr")
@AllArgsConstructor
public class OCRController {

    private final OCRUtil ocrUtil;

    @PostMapping("parse")
    public R<?> parse(@RequestParam("file") MultipartFile file) {
        try {
            InputStream is = file.getInputStream();
            byte[] bytes = is.readAllBytes();
            return R.ok(ocrUtil.executeOCR(bytes));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @PostMapping("parseV2")
    public R<?> parseV2(@RequestParam("file") MultipartFile file, @RequestParam("page") int page) {
        try {
            InputStream is = file.getInputStream();
            byte[] bytes = is.readAllBytes();
            return R.ok(ocrUtil.executeOCRV2(bytes, page));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
