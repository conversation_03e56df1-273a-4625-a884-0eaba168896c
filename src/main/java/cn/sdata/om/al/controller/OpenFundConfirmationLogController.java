package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.LogOpenFundConfirmationService;
import cn.sdata.om.al.service.OpenFundConfirmationStatementService;
import cn.sdata.om.al.vo.OpenFundConfirmationLogVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开基确认单日志Controller
 */
@Slf4j
@RestController
@RequestMapping("open-confirmation-logs")
@AllArgsConstructor
public class OpenFundConfirmationLogController {

    private final LogOpenFundConfirmationService logOpenFundConfirmationService;
    private final OpenFundConfirmationStatementService openFundConfirmationStatementService;

    /**
     * 获取所有日志信息
     */
    @GetMapping("all")
    public R<?> getAllLogs(@RequestParam(defaultValue = "1") int current,
                          @RequestParam(defaultValue = "1000") int size,
                          @RequestParam(required = false) String startDate,
                          @RequestParam(required = false) String endDate) {

        Map<String, Object> result = new HashMap<>();
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);

        // 上传日志
        Page<LogOpenFundConfirmationUpload> uploadPage = new Page<>(current, size);
        Page<LogOpenFundConfirmationUpload> uploadLogs = logOpenFundConfirmationService.getUploadLogs(uploadPage, start, end);
        List<OpenFundConfirmationLogVO.UploadLogVO> uploadLogVOs = convertUploadLogs(uploadLogs.getRecords());
        result.put("uploadLogs", uploadLogVOs);

        // 下载日志
        Page<LogOpenFundConfirmationDownload> downloadPage = new Page<>(current, size);
        Page<LogOpenFundConfirmationDownload> downloadLogs = logOpenFundConfirmationService.getDownloadLogs(downloadPage, start, end);
        List<OpenFundConfirmationLogVO.DownloadLogVO> downloadLogVOs = convertDownloadLogs(downloadLogs.getRecords());
        result.put("downloadLogs", downloadLogVOs);

        // 邮件发送日志
        Page<LogOpenFundConfirmationMail> mailPage = new Page<>(current, size);
        Page<LogOpenFundConfirmationMail> mailLogs = logOpenFundConfirmationService.getMailLogs(mailPage, start, end);
        List<OpenFundConfirmationLogVO.MailLogVO> mailLogVOs = convertMailLogs(mailLogs.getRecords());
        result.put("mailLogs", mailLogVOs);

        // OCR确认日志
        Page<LogOpenFundConfirmationOcr> ocrPage = new Page<>(current, size);
        Page<LogOpenFundConfirmationOcr> ocrLogs = logOpenFundConfirmationService.getOcrLogs(ocrPage, start, end);
        List<OpenFundConfirmationLogVO.OcrLogVO> ocrLogVOs = convertOcrLogs(ocrLogs.getRecords());
        result.put("ocrLogs", ocrLogVOs);

        // 删除记录日志
        Page<LogOpenFundConfirmationDelete> deletePage = new Page<>(current, size);
        Page<LogOpenFundConfirmationDelete> deleteLogs = logOpenFundConfirmationService.getDeleteLogs(deletePage, start, end);
        List<OpenFundConfirmationLogVO.DeleteLogVO> deleteLogVOs = convertDeleteLogs(deleteLogs.getRecords());
        result.put("deleteLogs", deleteLogVOs);
        
        return R.ok(result);
    }

    /**
     * 获取上传日志
     */
    @GetMapping("upload")
    public R<?> getUploadLogs(@RequestParam(defaultValue = "1") int current,
                             @RequestParam(defaultValue = "10") int size,
                             @RequestParam(required = false) String startDate,
                             @RequestParam(required = false) String endDate) {
        Page<LogOpenFundConfirmationUpload> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundConfirmationUpload> result = logOpenFundConfirmationService.getUploadLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取下载日志
     */
    @GetMapping("download")
    public R<?> getDownloadLogs(@RequestParam(defaultValue = "1") int current,
                               @RequestParam(defaultValue = "10") int size,
                               @RequestParam(required = false) String startDate,
                               @RequestParam(required = false) String endDate) {
        Page<LogOpenFundConfirmationDownload> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundConfirmationDownload> result = logOpenFundConfirmationService.getDownloadLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取邮件发送日志
     */
    @GetMapping("mail")
    public R<?> getMailLogs(@RequestParam(defaultValue = "1") int current,
                           @RequestParam(defaultValue = "10") int size,
                           @RequestParam(required = false) String startDate,
                           @RequestParam(required = false) String endDate) {
        Page<LogOpenFundConfirmationMail> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundConfirmationMail> result = logOpenFundConfirmationService.getMailLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取OCR确认日志
     */
    @GetMapping("ocr")
    public R<?> getOcrLogs(@RequestParam(defaultValue = "1") int current,
                          @RequestParam(defaultValue = "10") int size,
                          @RequestParam(required = false) String startDate,
                          @RequestParam(required = false) String endDate) {
        Page<LogOpenFundConfirmationOcr> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundConfirmationOcr> result = logOpenFundConfirmationService.getOcrLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取删除记录日志
     */
    @GetMapping("delete")
    public R<?> getDeleteLogs(@RequestParam(defaultValue = "1") int current,
                             @RequestParam(defaultValue = "10") int size,
                             @RequestParam(required = false) String startDate,
                             @RequestParam(required = false) String endDate) {
        Page<LogOpenFundConfirmationDelete> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundConfirmationDelete> result = logOpenFundConfirmationService.getDeleteLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 通过确认单ID下载文件
     */
    @GetMapping("download-by-id")
    public void downloadFileById(@RequestParam String id, HttpServletResponse response) {
        try {
            // 根据id查找对应的确认单
            OpenFundConfirmationStatement statement = openFundConfirmationStatementService.getById(id);

            if (statement == null) {
                throw new RuntimeException("确认单不存在");
            }

            FileInfo fileInfo = openFundConfirmationStatementService.download(List.of(id), false);
            if (fileInfo == null) {
                throw new RuntimeException("文件不存在");
            }

            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileInfo.getFileName(), StandardCharsets.UTF_8));
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileInfo.getFileData());
            outputStream.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 转换上传日志为VO
     */
    private List<OpenFundConfirmationLogVO.UploadLogVO> convertUploadLogs(List<LogOpenFundConfirmationUpload> logs) {
        return logs.stream().map(log -> {
            OpenFundConfirmationLogVO.UploadLogVO vo = new OpenFundConfirmationLogVO.UploadLogVO();
            vo.setFileName(log.getFileName());
            vo.setFileId(log.getFileId());
            vo.setConfirmationId(log.getConfirmationId());
            vo.setUploadTime(log.getUploadTime());
            vo.setOperator(log.getOperator());
            vo.setOperationStatus(log.getOperationStatus());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换下载日志为VO
     */
    private List<OpenFundConfirmationLogVO.DownloadLogVO> convertDownloadLogs(List<LogOpenFundConfirmationDownload> logs) {
        return logs.stream().map(log -> {
            OpenFundConfirmationLogVO.DownloadLogVO vo = new OpenFundConfirmationLogVO.DownloadLogVO();
            vo.setDownloadTime(log.getDownloadTime());
            vo.setOperator(log.getOperator());
            vo.setOperationStatus(log.getOperationStatus());
            vo.setFileName(log.getFileName());
            vo.setFileId(log.getFileId());
            vo.setConfirmationId(log.getConfirmationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换邮件日志为VO
     */
    private List<OpenFundConfirmationLogVO.MailLogVO> convertMailLogs(List<LogOpenFundConfirmationMail> logs) {
        return logs.stream().map(log -> {
            OpenFundConfirmationLogVO.MailLogVO vo = new OpenFundConfirmationLogVO.MailLogVO();
            vo.setTaskName(log.getTaskName());
            vo.setSendMethod(log.getSendMethod().getDisplayName());
            vo.setMailStatus(log.getMailStatus().getDisplayName());
            vo.setSendTime(log.getSendTime());
            vo.setOperator(log.getOperator());
            vo.setMailLogId(log.getMailLogId());
            vo.setConfirmationId(log.getConfirmationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换OCR确认日志为VO
     */
    private List<OpenFundConfirmationLogVO.OcrLogVO> convertOcrLogs(List<LogOpenFundConfirmationOcr> logs) {
        return logs.stream().map(log -> {
            OpenFundConfirmationLogVO.OcrLogVO vo = new OpenFundConfirmationLogVO.OcrLogVO();
            vo.setConfirmTime(log.getConfirmTime());
            vo.setOperator(log.getOperator());
            vo.setFileName(log.getFileName());
            vo.setAccountSetName(log.getAccountSetName());
            vo.setTransactionChannel(log.getTransactionChannel());
            vo.setConfirmStatus(log.getConfirmStatus());
            vo.setConfirmationId(log.getConfirmationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换删除日志为VO
     */
    private List<OpenFundConfirmationLogVO.DeleteLogVO> convertDeleteLogs(List<LogOpenFundConfirmationDelete> logs) {
        return logs.stream().map(log -> {
            OpenFundConfirmationLogVO.DeleteLogVO vo = new OpenFundConfirmationLogVO.DeleteLogVO();
            vo.setDeleteTime(log.getDeleteTime());
            vo.setOperator(log.getOperator());
            vo.setFileName(log.getFileName());
            vo.setAccountSetName(log.getAccountSetName());
            vo.setTransactionChannel(log.getTransactionChannel());
            vo.setDeleteStatus(log.getDeleteStatus());
            vo.setConfirmationId(log.getConfirmationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                return sdf.parse(dateStr);
            } catch (ParseException ex) {
                log.warn("日期格式解析失败: {}", dateStr);
                return null;
            }
        }
    }
}
