package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.entity.OpenFundFieldMapping;
import cn.sdata.om.al.enums.OpenFundFields;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.OpenFundFieldMappingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("field-mapping")
@AllArgsConstructor
public class OpenFundFieldMappingController {

    private final OpenFundFieldMappingService openFundFieldMappingService;

    @PostMapping("key/page")
    public R<?> keyPage(@RequestBody CommonPageParam<OpenFundFieldMapping> commonPageParam) {
        Map<String, Object> param = commonPageParam.getParam();
        String value = (String) param.get("value");
        return R.ok(openFundFieldMappingService.keyPageQuery(commonPageParam.getPage(), value));
    }

    @PostMapping("value/page")
    public R<?> valuePage(@RequestBody CommonPageParam<OpenFundFieldMapping> commonPageParam) {
        Map<String, Object> param = commonPageParam.getParam();
        String name = (String) param.get("name");
        String value = (String) param.get("value");
        return R.ok(openFundFieldMappingService.valuePageQuery(commonPageParam.getPage(), name, value));
    }

    @PostMapping("key/add")
    public R<?> keyAdd(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.keyAdd(openFundFieldMappingList);
        return R.ok("新增完成");
    }

    @PostMapping("value/add")
    public R<?> valueAdd(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.valueAdd(openFundFieldMappingList);
        return R.ok("新增完成");
    }

    @PostMapping("key/update")
    public R<?> keyUpdate(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.keyUpdate(openFundFieldMappingList);
        return R.ok("更新完成");
    }

    @PostMapping("value/update")
    public R<?> valueUpdate(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.valueUpdate(openFundFieldMappingList);
        return R.ok("更新完成");
    }

    @PostMapping("key/delete")
    public R<?> keyDelete(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.keyDelete(openFundFieldMappingList);
        return R.ok("删除完成");
    }

    @PostMapping("value/delete")
    public R<?> valueDelete(@RequestBody List<OpenFundFieldMapping> openFundFieldMappingList) {
        openFundFieldMappingService.valueDelete(openFundFieldMappingList);
        return R.ok("删除完成");
    }

    @GetMapping("key/get")
    public R<?> keyGetOne(String type) {
        return R.ok(openFundFieldMappingService.keyGetOne(type));
    }

    @GetMapping("value/get")
    public R<?> valueGetOne(String type, String name) {
        return R.ok(openFundFieldMappingService.valueGetOne(type, name));
    }

    @GetMapping("key-map")
    public R<?> getKeyMapping() {
        return R.ok(Arrays.stream(OpenFundFields.values()).collect(Collectors.toMap(OpenFundFields::getName, OpenFundFields::getValue)));
    }

    @GetMapping("value-map")
    public R<?> getValueMapping() {
        return R.ok(Map.of(OpenFundFields.BUSINESS_TYPE.getName(), OpenFundFields.BUSINESS_TYPE.getValue()));
    }

}
