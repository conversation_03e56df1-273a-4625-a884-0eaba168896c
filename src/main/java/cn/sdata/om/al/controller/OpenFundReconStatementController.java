package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.OpenFundReconciliationStatementService;
import cn.sdata.om.al.service.mail.MailPickService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("open-recon")
@AllArgsConstructor
public class OpenFundReconStatementController {

    private final OpenFundReconciliationStatementService openFundReconciliationStatementService;
    private final MailContentMapper mailContentMapper;
    private final MailPickService mailPickService;

    @PostMapping("pick")
    public R<?> parse(@RequestBody List<String> mailIds) {
        List<MailContent> mailContents = mailContentMapper.listAllContent(mailIds);
        mailContents.forEach(mailContent -> mailContent.setBoxId("1"));
        mailPickService.pickMail(mailContents);
        return R.ok("重新分拣成功");
    }

    @PostMapping("re-pick")
    public R<?> rePick() {
        openFundReconciliationStatementService.retryPickMail();
        return R.ok("重新分拣");
    }

    @PostMapping("page")
    public R<?> page(@RequestBody CommonPageParam<OpenFundReconciliationParam> commonPageParam) {
        Page<OpenFundReconciliationParam> page = commonPageParam.getPage();
        Map<String, Object> param = commonPageParam.getParam();
        ObjectMapper objectMapper = new ObjectMapper();
        OpenFundReconciliationParam openFundReconciliationParam = objectMapper.convertValue(param, OpenFundReconciliationParam.class);
        return R.ok(openFundReconciliationStatementService.pageQuery(openFundReconciliationParam, page));
    }

    @PostMapping("upload")
    public R<?> page(@RequestParam("files") List<MultipartFile> files) {
        openFundReconciliationStatementService.upload(files);
        return R.ok("上传完成");
    }

    @PostMapping("download")
    public void download(@RequestBody List<String> ids, HttpServletResponse response){
        FileInfo fileInfo = openFundReconciliationStatementService.download(ids);
        if (fileInfo == null) {
            throw new RuntimeException("文件不存在");
        }
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileInfo.getFileName(), StandardCharsets.UTF_8));
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileInfo.getFileData());
            outputStream.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("show")
    public R<?> show(String id) {
        OpenFundReconciliationStatement openFundReconciliationStatement = openFundReconciliationStatementService.getOpenFundReconciliationStatement(id);
        return R.ok(openFundReconciliationStatement);
    }

    @PostMapping("confirm")
    public R<?> confirm(@RequestBody OpenFundReconciliationStatement openFundReconciliationStatement) {
        openFundReconciliationStatementService.confirm(openFundReconciliationStatement);
        return R.ok(openFundReconciliationStatement);
    }

    @PostMapping("mail/send")
    public R<?> sendMail(@RequestBody List<String> ids){
        //TODO 存在漏洞待处理
        openFundReconciliationStatementService.sendMail(ids);
        return R.ok("发送完成");
    }

    @GetMapping("remove")
    public R<?> remove(String id) {
        openFundReconciliationStatementService.removeAllInfo(id);
        return R.ok("删除成功");
    }

    @GetMapping("channel/list")
    public R<?> getChannelList() {
        List<String> channelList = openFundReconciliationStatementService.getChannelList();
        return R.ok(channelList);
    }

    @GetMapping("business/list")
    public R<?> getBusinessList() {
        List<String> businessList = openFundReconciliationStatementService.getBusinessList();
        return R.ok(businessList);
    }



}
