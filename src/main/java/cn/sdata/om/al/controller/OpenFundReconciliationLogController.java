package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.LogOpenFundReconciliationService;
import cn.sdata.om.al.service.OpenFundReconciliationStatementService;
import cn.sdata.om.al.vo.OpenFundReconciliationLogVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开基对账单日志Controller
 */
@Slf4j
@RestController
@RequestMapping("open-reconciliation-logs")
@AllArgsConstructor
public class OpenFundReconciliationLogController {

    private final LogOpenFundReconciliationService logOpenFundReconciliationService;
    private final OpenFundReconciliationStatementService openFundReconciliationStatementService;

    /**
     * 获取所有日志信息
     */
    @GetMapping("all")
    public R<?> getAllLogs(@RequestParam(defaultValue = "1") int current,
                          @RequestParam(defaultValue = "1000") int size,
                          @RequestParam(required = false) String startDate,
                          @RequestParam(required = false) String endDate) {

        Map<String, Object> result = new HashMap<>();
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);

        // 上传日志
        Page<LogOpenFundReconciliationUpload> uploadPage = new Page<>(current, size);
        Page<LogOpenFundReconciliationUpload> uploadLogs = logOpenFundReconciliationService.getUploadLogs(uploadPage, start, end);
        List<OpenFundReconciliationLogVO.UploadLogVO> uploadLogVOs = convertUploadLogs(uploadLogs.getRecords());
        result.put("uploadLogs", uploadLogVOs);

        // 下载日志
        Page<LogOpenFundReconciliationDownload> downloadPage = new Page<>(current, size);
        Page<LogOpenFundReconciliationDownload> downloadLogs = logOpenFundReconciliationService.getDownloadLogs(downloadPage, start, end);
        List<OpenFundReconciliationLogVO.DownloadLogVO> downloadLogVOs = convertDownloadLogs(downloadLogs.getRecords());
        result.put("downloadLogs", downloadLogVOs);

        // 邮件发送日志
        Page<LogOpenFundReconciliationMail> mailPage = new Page<>(current, size);
        Page<LogOpenFundReconciliationMail> mailLogs = logOpenFundReconciliationService.getMailLogs(mailPage, start, end);
        List<OpenFundReconciliationLogVO.MailLogVO> mailLogVOs = convertMailLogs(mailLogs.getRecords());
        result.put("mailLogs", mailLogVOs);

        // OCR确认日志
        Page<LogOpenFundReconciliationOcr> ocrPage = new Page<>(current, size);
        Page<LogOpenFundReconciliationOcr> ocrLogs = logOpenFundReconciliationService.getOcrLogs(ocrPage, start, end);
        List<OpenFundReconciliationLogVO.OcrLogVO> ocrLogVOs = convertOcrLogs(ocrLogs.getRecords());
        result.put("ocrLogs", ocrLogVOs);

        // 删除记录日志
        Page<LogOpenFundReconciliationDelete> deletePage = new Page<>(current, size);
        Page<LogOpenFundReconciliationDelete> deleteLogs = logOpenFundReconciliationService.getDeleteLogs(deletePage, start, end);
        List<OpenFundReconciliationLogVO.DeleteLogVO> deleteLogVOs = convertDeleteLogs(deleteLogs.getRecords());
        result.put("deleteLogs", deleteLogVOs);
        
        return R.ok(result);
    }

    /**
     * 获取上传日志
     */
    @GetMapping("upload")
    public R<?> getUploadLogs(@RequestParam(defaultValue = "1") int current,
                             @RequestParam(defaultValue = "10") int size,
                             @RequestParam(required = false) String startDate,
                             @RequestParam(required = false) String endDate) {
        Page<LogOpenFundReconciliationUpload> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundReconciliationUpload> result = logOpenFundReconciliationService.getUploadLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取下载日志
     */
    @GetMapping("download")
    public R<?> getDownloadLogs(@RequestParam(defaultValue = "1") int current,
                               @RequestParam(defaultValue = "10") int size,
                               @RequestParam(required = false) String startDate,
                               @RequestParam(required = false) String endDate) {
        Page<LogOpenFundReconciliationDownload> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundReconciliationDownload> result = logOpenFundReconciliationService.getDownloadLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取邮件发送日志
     */
    @GetMapping("mail")
    public R<?> getMailLogs(@RequestParam(defaultValue = "1") int current,
                           @RequestParam(defaultValue = "10") int size,
                           @RequestParam(required = false) String startDate,
                           @RequestParam(required = false) String endDate) {
        Page<LogOpenFundReconciliationMail> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundReconciliationMail> result = logOpenFundReconciliationService.getMailLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取OCR确认日志
     */
    @GetMapping("ocr")
    public R<?> getOcrLogs(@RequestParam(defaultValue = "1") int current,
                          @RequestParam(defaultValue = "10") int size,
                          @RequestParam(required = false) String startDate,
                          @RequestParam(required = false) String endDate) {
        Page<LogOpenFundReconciliationOcr> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundReconciliationOcr> result = logOpenFundReconciliationService.getOcrLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 获取删除记录日志
     */
    @GetMapping("delete")
    public R<?> getDeleteLogs(@RequestParam(defaultValue = "1") int current,
                             @RequestParam(defaultValue = "10") int size,
                             @RequestParam(required = false) String startDate,
                             @RequestParam(required = false) String endDate) {
        Page<LogOpenFundReconciliationDelete> page = new Page<>(current, size);
        Date start = parseDate(startDate);
        Date end = parseDate(endDate);
        Page<LogOpenFundReconciliationDelete> result = logOpenFundReconciliationService.getDeleteLogs(page, start, end);
        return R.ok(result);
    }

    /**
     * 通过对账单ID下载文件
     */
    @GetMapping("download-by-id")
    public void downloadFileById(@RequestParam String id, HttpServletResponse response) {
        try {
            // 根据id查找对应的对账单
            OpenFundReconciliationStatement statement = openFundReconciliationStatementService.getById(id);

            if (statement == null) {
                throw new RuntimeException("对账单不存在");
            }

            FileInfo fileInfo = openFundReconciliationStatementService.download(List.of(id));
            if (fileInfo == null) {
                throw new RuntimeException("文件不存在");
            }

            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileInfo.getFileName(), StandardCharsets.UTF_8));
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileInfo.getFileData());
            outputStream.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                return sdf.parse(dateStr);
            } catch (ParseException ex) {
                log.warn("日期格式解析失败: {}", dateStr);
                return null;
            }
        }
    }

    /**
     * 转换上传日志
     */
    private List<OpenFundReconciliationLogVO.UploadLogVO> convertUploadLogs(List<LogOpenFundReconciliationUpload> logs) {
        return logs.stream().map(log -> {
            OpenFundReconciliationLogVO.UploadLogVO vo = new OpenFundReconciliationLogVO.UploadLogVO();
            vo.setFileName(log.getFileName());
            vo.setFileId(log.getFileId());
            vo.setReconciliationId(log.getReconciliationId());
            vo.setUploadTime(log.getUploadTime());
            vo.setOperator(log.getOperator());
            vo.setOperationStatus(log.getOperationStatus());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换下载日志
     */
    private List<OpenFundReconciliationLogVO.DownloadLogVO> convertDownloadLogs(List<LogOpenFundReconciliationDownload> logs) {
        return logs.stream().map(log -> {
            OpenFundReconciliationLogVO.DownloadLogVO vo = new OpenFundReconciliationLogVO.DownloadLogVO();
            vo.setDownloadTime(log.getDownloadTime());
            vo.setOperator(log.getOperator());
            vo.setOperationStatus(log.getOperationStatus().name());
            vo.setFileName(log.getFileName());
            vo.setFileId(log.getFileId());
            vo.setReconciliationId(log.getReconciliationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换邮件日志
     */
    private List<OpenFundReconciliationLogVO.MailLogVO> convertMailLogs(List<LogOpenFundReconciliationMail> logs) {
        return logs.stream().map(log -> {
            OpenFundReconciliationLogVO.MailLogVO vo = new OpenFundReconciliationLogVO.MailLogVO();
            vo.setTaskName(log.getTaskName());
            vo.setSendMethod(log.getSendMethod().name());
            vo.setMailStatus(log.getMailStatus().name());
            vo.setSendTime(log.getSendTime());
            vo.setOperator(log.getOperator());
            vo.setMailLogId(log.getMailLogId());
            vo.setReconciliationId(log.getReconciliationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换OCR日志
     */
    private List<OpenFundReconciliationLogVO.OcrLogVO> convertOcrLogs(List<LogOpenFundReconciliationOcr> logs) {
        return logs.stream().map(log -> {
            OpenFundReconciliationLogVO.OcrLogVO vo = new OpenFundReconciliationLogVO.OcrLogVO();
            vo.setConfirmTime(log.getConfirmTime());
            vo.setOperator(log.getOperator());
            vo.setFileName(log.getFileName());
            vo.setAccountSetName(log.getAccountSetName());
            vo.setTransactionChannel(log.getTransactionChannel());
            vo.setConfirmStatus(log.getConfirmStatus());
            vo.setReconciliationId(log.getReconciliationId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换删除日志
     */
    private List<OpenFundReconciliationLogVO.DeleteLogVO> convertDeleteLogs(List<LogOpenFundReconciliationDelete> logs) {
        return logs.stream().map(log -> {
            OpenFundReconciliationLogVO.DeleteLogVO vo = new OpenFundReconciliationLogVO.DeleteLogVO();
            vo.setDeleteTime(log.getDeleteTime());
            vo.setOperator(log.getOperator());
            vo.setFileName(log.getFileName());
            vo.setAccountSetName(log.getAccountSetName());
            vo.setTransactionChannel(log.getTransactionChannel());
            vo.setDeleteStatus(log.getDeleteStatus());
            vo.setReconciliationId(log.getReconciliationId());
            return vo;
        }).collect(Collectors.toList());
    }
}
