package cn.sdata.om.al.controller;

import cn.hutool.core.collection.CollUtil;
import cn.sdata.om.al.dto.OtherContactsPageParamDto;
import cn.sdata.om.al.entity.OtherContacts;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.OtherContactsService;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 委托人管理
 */
@Slf4j
@RestController
@RequestMapping("/other")
@AllArgsConstructor
public class OtherContactsController {

    private final OtherContactsService otherContactsService;

    @RequestMapping("/list")
    public R<?> list() {
        return R.ok(otherContactsService.list());
    }

    /**
     * 新建/修改 接口
     *
     * @param oc
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public R<?> saveOrUpdate(@RequestBody OtherContacts oc) {
        try {
            if (oc == null || StringUtils.isBlank(oc.getName()) || StringUtils.isBlank(oc.getRecipient())) {
                return R.restResult(null, 400, "参数异常");
            }
            if (StringUtils.isBlank(oc.getId())) {
                oc.setId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20))
                        .setCreateTime(new Date());
            } else if (StringUtils.isNotBlank(oc.getId())) {
                oc.setUpdateTime(new Date());
            }
            return otherContactsService.saveOrUpdate(oc) ? R.ok() : R.failed();
        } catch (Exception e) {
            log.error("OtherContacts_saveOrUpdate_error:{}", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    public R<?> delete(@RequestBody List<String> ids) {
        try {
            if (CollUtil.isEmpty(ids)) {
                return R.restResult(null, 400, "参数异常");
            }
            return otherContactsService.removeByIds(ids) ? R.ok("删除成功") : R.failed("删除失败");
        } catch (Exception e) {
            log.error("OtherContacts_delete_error:{}", e);
            return R.failed(e.getMessage());
        }
    }


    /**
     * 分页接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/page")
    public R<?> page(@RequestBody OtherContactsPageParamDto dto) {
        try {
            Assert.notNull(dto, "参数为空");
            if (null == dto.getPageNo()) {
                dto.setPageNo(1);
            }
            if (null == dto.getPageSize()) {
                dto.setPageSize(10);
            }
            return R.ok(otherContactsService.page(new Page<>(dto.getPageNo(), dto.getPageSize()),
                    Wrappers.lambdaQuery(OtherContacts.class)
                            .in(CollUtil.isNotEmpty(dto.getNames()), OtherContacts::getName, dto.getNames())
                            .orderByDesc(OtherContacts::getCreateTime))
            );
        } catch (Exception e) {
            log.error("OtherContacts_page_error:{}", e);
            return R.failed(e.getMessage());
        }
    }
}
