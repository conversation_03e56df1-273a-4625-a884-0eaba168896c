package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.FlowListService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Controller
@RestController
@AllArgsConstructor
@RequestMapping("rpa")
public class RPAController {

    private FlowListService flowListService;

    @GetMapping("flow-list")
    public R<?> getFlowList() {
        List<FlowList> list = flowListService.list();
        return R.ok(list);
    }

}
