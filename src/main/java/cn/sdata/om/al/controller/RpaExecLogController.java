package cn.sdata.om.al.controller;

import cn.hutool.core.lang.Assert;
import cn.sdata.om.al.entity.RpaExecLogQuery;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.RpaExecLogService;
import cn.sdata.om.al.vo.RpaExecLogVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpa/exec/log/")
public class RpaExecLogController {

    @Resource
    private RpaExecLogService rpaExecLogService;

    /**
     * rpa执行日日志-业务类型列表
     *
     * @return 业务类型列表
     */
    @GetMapping("/type-list")
    public R<List<String>> bizTypeList() {
        return R.ok(rpaExecLogService.bizTypeList());
    }

    /**
     * rpa执行日日志-分页查询
     *
     * @param query 查询条件
     * @return 执行日志
     */
    @PostMapping("/queryPage")
    public R<IPage<RpaExecLogVO>> queryPage(@RequestBody RpaExecLogQuery query) {
        Assert.notNull(query, "查询参数不能为空");
        return R.ok(rpaExecLogService.queryPage(query));
    }
}
