package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.FileInfo;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.SMBService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RequestMapping("smb")
@RestController
@AllArgsConstructor
public class SMBController {


    private final SMBService smbService;


    @GetMapping("list")
    public R<?> list(String path) {
        return R.ok(smbService.listDir(path));
    }

    @GetMapping("download")
    public void download(String path, HttpServletResponse response) {
        try {
            setDownloadHeader(response, getFileName(path));
            byte[] bytes = smbService.download(path);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("下载失败", e);
        }
    }

    @PostMapping("batch/download")
    public void downloadBatch(@RequestBody List<RemoteFileInfo> files, HttpServletResponse response) {
        try {
            FileInfo fileInfo = smbService.downloadBatch(files);
            setDownloadHeader(response, fileInfo.getFileName());
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileInfo.getFileData());
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("下载失败", e);
        }
    }

    private void setDownloadHeader(HttpServletResponse response, String filename) {
        response.setContentType("application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
    }

    @PostMapping("upload")
    public R<?> upload(@RequestParam("file") MultipartFile file, @RequestParam("path")String path) {
        try {
            String name = file.getOriginalFilename();
            byte[] bytes = file.getBytes();
            smbService.upload(path, name, bytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.ok("上传成功");
    }

    private String getFileName(String path) {
        if (path != null && path.split("\\\\").length > 0) {
            String[] split = path.split("\\\\");
            return split[split.length - 1];
        }
        return null;
    }

}
