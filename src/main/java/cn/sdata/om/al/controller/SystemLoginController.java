package cn.sdata.om.al.controller;

import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import cn.sdata.om.al.dto.SystemLoginParamDto;
import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.entity.Role;
import cn.sdata.om.al.entity.User;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MenuService;
import cn.sdata.om.al.service.UserService;
import cn.sdata.om.al.utils.CaptchaUtil;
import cn.sdata.om.al.utils.EncryptionUtil;
import cn.sdata.om.al.utils.LdapAuthUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/10 10:33
 * @Version 1.0
 */
@Slf4j
@Controller
@RequestMapping("/loginController")
public class SystemLoginController {

    @Resource
    private UserService userService;

    private final static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private EncryptionUtil encryptionUtil;

    @Value("${login.lock-time}")
    private int loginLockTime;

    @Value("${login.ldap.url}")
    private String ldapUrl;

    @Value("${login.ou}")
    private String aou;

    /**
     * 用户登录接口
     *
     * @param userDto
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/login")
    public String systemLogin(@RequestBody SystemLoginParamDto userDto, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 登录前查询登录失败次数，并且判断锁定时间，如果超过配置次数则直接返回用户锁定
            boolean isAllowLogin = userService.getLoginFailedCount(userDto.getAccount());
            if (!isAllowLogin) {
                Map<String, Object> res = new HashMap<>();
                res.put("status", "LOCKED");
                writeJsonResponse(response, R.ok(res));
                return null;
            }
            Assert.notNull(userDto.getAccount(), "登录账号不能为空!");
            Assert.notNull(userDto.getPassword(), "登录密码不能为空!");
            //Assert.notNull(userDto.getCaptcha(), "验证码不能为空!");
            // 从Session中获取验证码文本
            //String captchaValue = String.valueOf(request.getSession().getAttribute("captcha"));
            // 验证用户输入
            /*if (StringUtils.isBlank(captchaValue) || !captchaValue.equalsIgnoreCase(userDto.getCaptcha())) {
                writeJsonResponse(response, R.restResult(null, 400, "验证码错误"));
                loginFailedRecord(userDto, "验证码错误");
                return null;
            }*/
            String oriPassword = encryptionUtil.decrypt(userDto.getPassword()),
                    password = MD5.create().digestHex(oriPassword);
            User user = userService.getOne(Wrappers.lambdaQuery(User.class).eq(User::getAccount, userDto.getAccount()).eq(User::getPassword, password).eq(User::getStatus, 1));
            if (ObjectUtil.isNull(user)) {
                //本地用户校验失败后，进行域控校验
                user = userService.getOne(Wrappers.lambdaQuery(User.class).eq(User::getAccount, userDto.getAccount()).eq(User::getStatus, 1));
                Pair<Boolean, Integer> ldapLogin = LdapAuthUtils.authenticate(userDto.getAccount().concat("@").concat(aou), oriPassword, ldapUrl);
                if (ldapLogin.getKey()) {
                    //域控验证通过
                    if (ObjectUtil.isNull(user)) {
                        //账号不存在,新增用户
                        String id = String.valueOf(System.nanoTime());
                        user = new User().setId(id)
                                .setAccount(userDto.getAccount())
                                .setName(userDto.getAccount())
                                .setPassword(password)
                                .setCreateTime(new Date())
                                .setCreateUser(id)
                                .setAdFlag(1);
                        userService.save(user);
                    } else {
                        if (!user.getPassword().equals(password)) {
                            //账号存在，进行密码同步
                            userService.update(Wrappers.lambdaUpdate(User.class)
                                    .eq(User::getId, user.getId())
                                    .eq(User::getAccount, userDto.getAccount())
                                    .set(User::getPassword, password)
                                    .set(User::getUpdateTime, new Date())
                                    .set(User::getUpdateUser, user.getId())
                                    .set(User::getAdFlag, 1));
                        }
                    }
                } else {
                    //域控验证失败
                    if (ldapLogin.getRight() == 404) {
                        //域账号不存在-查询本地库
                        if (ObjectUtil.isNull(user)) {
                            //本地库也不存在
                            writeJsonResponse(response, R.restResult(null, 400, "用户不存在"));
                            loginFailedRecord(userDto, "用户不存在");
                            return null;
                        } else {
                            //本地账号存在-判断密码
                            if (!password.equals(user.getPassword())) {
                                //本地用户-密码错误情况
                                writeJsonResponse(response, R.restResult(null, 400, "非域账号-密码错误"));
                                loginFailedRecord(userDto, "非域账号-密码错误");
                                return null;
                            }
                        }
                    } else if (ldapLogin.getRight() == 501) {
                        writeJsonResponse(response, R.restResult(null, 400, "域账号-密码错误"));
                        loginFailedRecord(userDto, "域账号-密码错误");
                        return null;
                    } else if (ldapLogin.getRight() == 49) {
                        writeJsonResponse(response, R.restResult(null, 400, "域账号-无效凭证"));
                        loginFailedRecord(userDto, "域账号-无效凭证");
                        return null;
                    } else {
                        writeJsonResponse(response, R.restResult(null, 500, "域控校验异常"));
                        loginFailedRecord(userDto, "域控校验异常");
                        return null;
                    }
                }
            }
            List<Role> roles = userService.getUser2roleList(user.getId());
            if (null != roles && CollUtil.isNotEmpty(roles)) {
                user.setRoles(roles).setRoleIds(roles.stream().map(Role::getId).distinct().collect(Collectors.toList()));
            }
            List<Menu> menus = userService.getUser2menuList(user.getId());
            if (null != menus && CollUtil.isNotEmpty(menus)) {
                user.setMenus(MenuService.buildTree(menus));
            }
            StpUtil.login(user.getId(), SaLoginConfig.setExtraData(Map.of("userId", user.getId(), "userName", user.getName())));
            StpUtil.getSession().set("user", user);
            writeJsonResponse(response, R.ok(genericRes(StpUtil.getTokenValue(), user)));
            // 如果期间登录成功了则重置失败次数
            userService.resetLoginFailedCount(userDto.getAccount());
            return null;
        } catch (Exception e) {
            log.error("login_error0:{}", e);
            writeJsonResponse(response, R.restResult(null, 500, e.getMessage()));
            // 统一将本地登录失败写入到表中
            loginFailedRecord(userDto, e.getMessage());
            return null;
        }
    }

    /**
     * 记录用户登录失败
     *
     * @param userDto 登录对象
     * @param msg     失败原因
     */
    private void loginFailedRecord(SystemLoginParamDto userDto, String msg) {
        String account = userDto.getAccount();
        Calendar calendar = DateUtil.calendar();
        calendar.add(Calendar.SECOND, loginLockTime);
        Date time = calendar.getTime();
        log.info("*****记录登录失败: account = {}, time = {}, msg = {}", account, time, msg);
        userService.recordLoginFailed(IdUtil.getSnowflakeNextIdStr(), account, msg, time);
    }

    /**
     * 退出接口
     *
     * @return
     */
    @PostMapping("/logout")
    public void systemLogout(HttpServletResponse response) {
        StpUtil.getSession().clear();
        StpUtil.logout();
        writeJsonResponse(response, R.ok("退出成功"));
    }

    /**
     * 获取图形验证码接口
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping("/captcha/image")
    public void getCaptchaImage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 生成验证码
        CaptchaUtil.Captcha captcha = CaptchaUtil.generateCaptcha();
        // 将验证码文本存储到Session中
        HttpSession session = request.getSession();
        session.setAttribute("captcha", captcha.getText());
        // 设置响应内容类型为图片
        response.setContentType("image/jpeg");
        response.getOutputStream().write(captcha.getImageBytes());
        response.getOutputStream().flush();
    }

    /**
     * 生成响应报文
     *
     * @param tokens
     * @param user
     * @return
     */
    private Object genericRes(String tokens, User user) {
        Map<String, Object> res = new HashMap<>();
        user.setPassword(null);
        res.put("user", user);
        res.put("accessToken", tokens);
        return res;
    }

    public static void writeJsonResponse(HttpServletResponse response, Object data) {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        if (data instanceof R) {
            R<Object> r = (R<Object>) data;
            if (r.getCode() == 401) {
                response.setStatus(401);
            }
        }
        try {
            String json = objectMapper.writeValueAsString(data);
            response.getWriter().write(json);
        } catch (IOException e) {
            // 处理异常
            log.error("登录接口响应异常", e);
        }
    }
}
