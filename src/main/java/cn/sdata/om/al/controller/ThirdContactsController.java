package cn.sdata.om.al.controller;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.entity.InvestorContacts;
import cn.sdata.om.al.entity.InvestorContactsParam;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.InvestorContactsService;
import cn.sdata.om.al.vo.InvestorContactVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.sdata.om.al.constant.BaseConstant.INVESTOR_THIRD;

@RestController
@RequestMapping("third")
@AllArgsConstructor
public class ThirdContactsController {

    private final InvestorContactsService investorContactsService;

    @PostMapping("page")
    public R<?> page(@RequestBody CommonPageParam<InvestorContacts> commonPageParam){
        Page<InvestorContactVO> pageResult = investorContactsService.getPageResult(commonPageParam, INVESTOR_THIRD);
        return R.ok(pageResult);
    }

    @GetMapping("list")
    public R<?> list(){
        List<InvestorContactVO> listResult = investorContactsService.getListResult(INVESTOR_THIRD);
        return R.ok(listResult);
    }

    @PostMapping("add")
    public R<?> saveBatch(@RequestBody InvestorContactsParam investorContactsParam){
        String id = investorContactsParam.getId();
        if (id == null) {
            investorContactsService.saveFromParam(investorContactsParam, INVESTOR_THIRD);
        }else{
            investorContactsService.updateFromParam(investorContactsParam, INVESTOR_THIRD);
        }
        return R.ok("新增完成");
    }

    @GetMapping("get")
    public R<?> get(String id){
        InvestorContactsParam investorContactsParam = investorContactsService.getParamById(id);
        return R.ok(investorContactsParam);
    }

    @GetMapping("remove")
    public R<?> remove(String id){
        investorContactsService.removeAll(id, INVESTOR_THIRD);
        return R.ok("删除完成");
    }





}
