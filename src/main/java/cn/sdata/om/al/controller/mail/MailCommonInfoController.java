package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSummarizeListQuery;
import cn.sdata.om.al.entity.mail.AccountFundInformation;
import cn.sdata.om.al.entity.mail.MailCommonInfo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailCommonInfoService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/commonInfo")
public class MailCommonInfoController {

    private MailCommonInfoService mailCommonInfoService;

    @Autowired
    public void setMailCommonInfoService(MailCommonInfoService mailCommonInfoService) {
        this.mailCommonInfoService = mailCommonInfoService;
    }

    /**
     * 分页查询公共信息
     *
     * @param accountSummarizeListQuery 查询参数
     * @return 分页列表
     */
    @PostMapping("/accountSummarize/page")
    public R<Page<MailCommonInfo>> page(@RequestBody AccountSummarizeListQuery accountSummarizeListQuery) {
        PageInfo<MailCommonInfo> mailCommonInfos = mailCommonInfoService.page(accountSummarizeListQuery);
        Page<MailCommonInfo> page = new Page<>();
        page.setCurrent(accountSummarizeListQuery.getPageNo());
        page.setRecords(CollectionUtil.isNotEmpty(mailCommonInfos.getList()) ? mailCommonInfos.getList() : new ArrayList<>());
        page.setSize(accountSummarizeListQuery.getPageSize());
        page.setTotal(mailCommonInfos.getTotal());
        return R.ok(page);
    }

    /**
     * 新增或修改账户基本信息
     *
     * @param mailCommonInfo 参数
     * @return 是否成功
     */
    @PostMapping("/accountSummarize/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody MailCommonInfo mailCommonInfo) {
        boolean res = mailCommonInfoService.saveOrUpdate(mailCommonInfo);
        return res ? R.ok() : R.failed("新增或更新失败");
    }

    /**
     * 根据id查询公共信息
     *
     * @param id 主键
     * @return 分页列表
     */
    @GetMapping("/accountSummarize")
    public R<MailCommonInfo> getById(@RequestParam("id") String id) {
        MailCommonInfo mailCommonInfos = mailCommonInfoService.getById(id);
        return R.ok(mailCommonInfos);
    }


    /**
     * 查询所有账套名称
     *
     * @return 分页列表
     */
    @GetMapping("/accountSummarize/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> commonEntities = mailCommonInfoService.list();
        return R.ok(commonEntities);
    }

    /**
     * 分页查询基金基本信息
     *
     * @return 分页列表
     */
    @PostMapping("/fundInfo/page")
    public R<Page<AccountFundInformation>> page(@RequestBody JSONObject jsonObject) {
        Integer pageNo = jsonObject.getInteger("pageNo");
        Integer pageSize = jsonObject.getInteger("pageSize");
        // 渠道管理员
        List<String> admins = jsonObject.getList("admins", String.class);
        // 渠道类型
        String type = jsonObject.getString("type");
        List<String> productIds = jsonObject.getList("productIds", String.class);
        PageInfo<AccountFundInformation> accountFundInformationPageInfo = mailCommonInfoService.fundInfoPage(pageNo, pageSize, productIds, admins, type);
        Page<AccountFundInformation> page = new Page<>();
        page.setCurrent(pageNo);
        page.setRecords(CollectionUtil.isNotEmpty(accountFundInformationPageInfo.getList()) ? accountFundInformationPageInfo.getList() : new ArrayList<>());
        page.setSize(pageSize);
        page.setTotal(accountFundInformationPageInfo.getTotal());
        return R.ok(page);
    }

    /**
     * 新增或修改基金账户基本信息
     *
     * @param accountFundInformation 参数
     * @return 是否成功
     */
    @PostMapping("/fundInfo/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody AccountFundInformation accountFundInformation) {
        boolean res = mailCommonInfoService.fundInfoSaveOrUpdate(accountFundInformation);
        return res ? R.ok() : R.failed("新增或更新失败");
    }


    /**
     * 新增或修改基金账户基本信息
     *
     * @param ids 参数
     * @return 是否成功
     */
    @PostMapping("/fundInfo/delete")
    public R<Boolean> fundInfoDelete(@RequestBody List<String> ids) {
        boolean res = mailCommonInfoService.fundInfoBatchDelete(ids);
        return res ? R.ok() : R.failed("删除失败");
    }


    /**
     * 基金管理员列表
     * @return 管理员列表
     */
    @GetMapping("/fundInfo/fundAdminList")
    public R<List<CommonEntity>> fundAdminList() {
        List<CommonEntity> res = mailCommonInfoService.fundAdminList();
        return R.ok(res);
    }


    /**
     * 根据id查询基金账户基本信息
     *
     * @param fundInfoId 参数
     * @return 是否成功
     */
    @GetMapping("/fundInfo")
    public R<AccountFundInformation> fundInfoGetById(@RequestParam("id") String fundInfoId) {
        AccountFundInformation accountFundInformation = mailCommonInfoService.fundInfoGetById(fundInfoId);
        return R.ok(accountFundInformation);
    }

    /**
     * 导入账户基本信息
     *
     * @param file 文件
     * @return 是否成功
     */
    @PostMapping("/importAccountSetInfo")
    public R<Boolean> importAccountSetInfo(@RequestParam("file") MultipartFile file) {
        Boolean res = mailCommonInfoService.importAccountSetInfo(file);
        return res ? R.ok() : R.failed();
    }

    /**
     * 导入账户基本信息
     *
     * @param file 文件
     * @return 是否成功
     */
    @PostMapping("/importFundInfo")
    public R<Boolean> importFundInfo(@RequestParam("file") MultipartFile file) {
        Boolean res = mailCommonInfoService.importFundInfo(file);
        return res ? R.ok() : R.failed();
    }
}
