package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailContactsListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailContacts;
import cn.sdata.om.al.entity.mail.vo.MailContactsVo;
import cn.sdata.om.al.entity.mail.vo.MailContactsListVo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailContactsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/contacts")
public class MailContactsController {

    private MailContactsService mailContactsService;

    @Autowired
    public void setMailContactsService(MailContactsService mailContactsService) {
        this.mailContactsService = mailContactsService;
    }

    /**
     * 分页查询通讯录
     *
     * @param mailContactsListQuery 查询参数
     * @return 分页信息
     */
    @PostMapping("/page")
    public R<Page<MailContactsListVo>> page(@RequestBody MailContactsListQuery mailContactsListQuery) {
        PageInfo<MailContactsListVo> contactsListVoPageInfo = mailContactsService.page(mailContactsListQuery);
        Page<MailContactsListVo> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(contactsListVoPageInfo.getList()) ? contactsListVoPageInfo.getList() : new ArrayList<>());
        page.setCurrent(mailContactsListQuery.getPageNo());
        page.setTotal(contactsListVoPageInfo.getTotal());
        page.setSize(mailContactsListQuery.getPageSize());
        return R.ok(page);
    }


    /**
     * 下拉框查询通讯录
     *
     * @return 分页信息
     */
    @PostMapping("/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> mailTemplateListVos = mailContactsService.list();
        return R.ok(mailTemplateListVos);
    }

    /**
     * 批量删除
     *
     * @param ids 主键id列表
     * @return 是否成功
     */
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody List<String> ids) {
        boolean res = mailContactsService.delete(ids);
        return res ? R.ok() : R.failed("删除失败");
    }

    /**
     * 新增或删除通讯录
     *
     * @param saveOrUpdateMailContacts 对象参数
     * @return 是否成功
     */
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateMailContacts saveOrUpdateMailContacts) {
        boolean res = mailContactsService.saveOrUpdate(saveOrUpdateMailContacts);
        return res ? R.ok() : R.failed("新增或更新失败");
    }

    /**
     * 根据id查询详情
     *
     * @param id 主键id
     * @return 模板详情
     */
    @GetMapping("")
    public R<MailContactsVo> getById(@RequestParam("id") String id) {
        MailContactsVo mailContactsVo = mailContactsService.getById(id);
        return R.ok(mailContactsVo);
    }

    /**
     * 根据模板id获取账套信息
     *
     * @param templateId 模板id
     * @return 账套信息
     */
    @GetMapping("/getAccountSetByTemplateId")
    public R<List<CommonEntity>> getAccountSetByTemplateId(@RequestParam("templateId") String templateId) {
        List<CommonEntity> list = mailContactsService.getAccountSetByTemplateId(templateId);
        return R.ok(list);
    }


    /**
     * 获取所有收件人
     *
     * @return 收件人列表
     */
    @GetMapping("/getAllRecipient")
    public R<List<CommonEntity>> getAllRecipient() {
        List<CommonEntity> list = mailContactsService.getAllRecipient();
        return R.ok(list);
    }

    /**
     * 获取通讯录分类
     *
     * @return 分类列表
     */
    @GetMapping("/getMailContactsType")
    public R<List<CommonEntity>> getMailContactsType() {
        List<CommonEntity> list = mailContactsService.getMailContactsType();
        return R.ok(list);
    }

    /**
     * 获取所有三方机构
     *
     * @return 三方机构列表
     */
    @GetMapping("/getAllThreePartyOrganization")
    public R<List<CommonEntity>> getAllThreePartyOrganization() {
        List<CommonEntity> list = mailContactsService.getAllThreePartyOrganization();
        return R.ok(list);
    }


    /**
     * 获取所有投资人
     *
     * @return 投资人列表
     */
    @GetMapping("/getAllInvestor")
    public R<List<CommonEntity>> getAllInvestor() {
        List<CommonEntity> list = mailContactsService.getAllInvestor();
        return R.ok(list);
    }


    /**
     * 获取通讯录分类
     *
     * @return 分类列表
     */
    @GetMapping("/getMailContactsCategoryByType")
    public R<List<CommonEntity>> getMailContactsCategoryByType(@RequestParam("typeId") String typeId) {
        List<CommonEntity> list = mailContactsService.getMailContactsCategoryByType(typeId);
        return R.ok(list);
    }

    @GetMapping("/type/get")
    public R<?> getTypeByTaskId(String taskId){
        List<String> types = mailContactsService.getTypeByTaskId(taskId);
        return R.ok(types);
    }
}
