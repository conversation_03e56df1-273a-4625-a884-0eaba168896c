package cn.sdata.om.al.controller.mail;

import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.File;

@RestController
@RequestMapping("/mailInfo")
public class MailInfoController {

    private MailInfoService mailInfoService;

    @Autowired
    public void setMailInfoService(MailInfoService mailInfoService) {
        this.mailInfoService = mailInfoService;
    }

    @GetMapping("/downloadAttachment")
    public void download(@RequestParam(value = "mailId", required = false) String mailId,
                         @RequestParam(value = "fileId", required = false) String fileId,
                         HttpServletResponse response) {
        if (StringUtils.isBlank(mailId) && StringUtils.isBlank(fileId)) {
            BusinessException.throwException("至少传一个参数");
        }
        File file = mailInfoService.downloadAttachments(mailId, fileId);
        if (ObjectUtil.isNull(file)) {
            BusinessException.throwException("生成文件失败");
        }
        try {
            CommonUtil.downloadFile(response, file);
        } catch (Exception e) {
            BusinessException.throwException(e.getMessage());
        }
    }
}
