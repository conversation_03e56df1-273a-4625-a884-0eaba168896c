package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.mail.params.MailLogListQuery;
import cn.sdata.om.al.entity.mail.vo.MailDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailLogListVo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailLogService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

@RestController
@RequestMapping("/mailLog")
public class MailLogController {

    private MailLogService mailLogService;

    @Autowired
    public void setMailLogService(MailLogService mailLogService) {
        this.mailLogService = mailLogService;
    }

    /**
     * 分页查询发送记录
     *
     * @param mailLogListQuery 查询参数
     * @return 分页信息
     */
    @PostMapping("/page")
    public R<Page<MailLogListVo>> page(@RequestBody MailLogListQuery mailLogListQuery) {
        PageInfo<MailLogListVo> mailLogListVos = mailLogService.page(mailLogListQuery);
        Page<MailLogListVo> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(mailLogListVos.getList()) ? mailLogListVos.getList() : new ArrayList<>());
        page.setCurrent(mailLogListQuery.getPageNo());
        page.setSize(mailLogListQuery.getPageSize());
        page.setTotal(mailLogListVos.getTotal());
        return R.ok(page, "查询成功");
    }

    /**
     * 查看邮件详情
     *
     * @param mailId 邮件id
     * @return 邮件详情
     */
    @GetMapping("/detail")
    public R<MailDetailVo> detail(@RequestParam("mailId") String mailId) {
        MailDetailVo mailDetailVo = mailLogService.detail(mailId);
        return R.ok(mailDetailVo);
    }
}
