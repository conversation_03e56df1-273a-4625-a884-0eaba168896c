package cn.sdata.om.al.controller.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailMacroService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/mailMacro")
public class MailMacroController {

    private MailMacroService mailMacroService;

    @Autowired
    public void setMailMacroService(MailMacroService mailMacroService) {
        this.mailMacroService = mailMacroService;
    }

    /**
     * 获取宏定义列表
     *
     * @param moduleName 模块名
     * @return 宏列表
     */
    @GetMapping("/list")
    public R<List<CommonEntity>> list(@RequestParam(required = false, value = "moduleName") String moduleName) {
        List<CommonEntity> mailMacroMaps = mailMacroService.list(moduleName);
        return R.ok(mailMacroMaps);
    }
}
