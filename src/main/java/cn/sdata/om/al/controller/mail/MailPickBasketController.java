package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickBasket;
import cn.sdata.om.al.entity.mail.vo.MailPickBasketVo;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailPickBasketService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/mailPickBasket")
public class MailPickBasketController {

    private MailPickBasketService mailPickBasketService;

    @Autowired
    public void setMailPickBasketService(MailPickBasketService mailPickBasketService) {
        this.mailPickBasketService = mailPickBasketService;
    }

    /**
     * 新增或修改邮件筐
     *
     * @param saveOrUpdateMailPickBasket 参数
     * @return 是否成功
     */
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateMailPickBasket saveOrUpdateMailPickBasket) {
        Boolean res = mailPickBasketService.saveOrUpdate(saveOrUpdateMailPickBasket);
        return res ? R.ok() : R.failed("新增或修改失败");
    }


    /**
     * 查询所有邮件筐
     *
     * @return 邮件筐列表
     */
    @GetMapping("/list")
    public R<List<MailPickBasketVo>> list() {
        List<MailPickBasketVo> commonEntities = mailPickBasketService.list();
        if (CollectionUtil.isNotEmpty(commonEntities)) {
            for (MailPickBasketVo pickBasketVo : commonEntities) {
                String id = pickBasketVo.getId();
                if (StringUtils.isNotBlank(id)) {
                    int count = mailPickBasketService.selectPickBasketCount(id);
                    pickBasketVo.setMailCount(count);
                }
            }
        }
        return R.ok(commonEntities);
    }


    @PostMapping("/listQuery")
    public R<List<MailPickBasketVo>> listQuery(@RequestBody MailContentListQuery mailContentListQuery) {
        List<MailPickBasketVo> commonEntities = mailPickBasketService.list();
        if (CollectionUtil.isNotEmpty(commonEntities)) {
            for (MailPickBasketVo pickBasketVo : commonEntities) {
                String id = pickBasketVo.getId();
                if (StringUtils.isNotBlank(id)) {
                    int count = mailPickBasketService.selectPickBasketCountByConditions(id, mailContentListQuery);
                    pickBasketVo.setMailCount(count);
                }
            }
        }
        return R.ok(commonEntities);
    }

    /**
     * 删除邮件筐
     *
     * @param id 邮件筐id
     * @return 是否成功
     */
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") String id) {
        // 判断此分拣筐是否关联了规则 如果关联了规则 则不能删除
        MailPickBasketVo mailPickBasketVo = mailPickBasketService.getById(id);
        if (null != mailPickBasketVo) {
            String ruleId = mailPickBasketVo.getRuleId();
            if (StringUtils.isNotBlank(ruleId)) {
                BusinessException.throwException("该邮件筐已经绑定规则不能删除");
            }
        }
        Boolean res = mailPickBasketService.delete(id);
        return res ? R.ok() : R.failed("删除失败");
    }
}
