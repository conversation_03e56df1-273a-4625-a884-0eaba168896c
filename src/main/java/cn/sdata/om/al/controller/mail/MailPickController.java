package cn.sdata.om.al.controller.mail;

import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.ManualPickParam;
import cn.sdata.om.al.entity.mail.vo.MailContentListVo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailPickService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/mailPick")
public class MailPickController {

    private MailPickService mailPickService;

    @Autowired
    public void setMailPickService(MailPickService mailPickService) {
        this.mailPickService = mailPickService;
    }

    /**
     * 邮件列表
     *
     * @param mailContentListQuery 查询参数
     * @return 列表
     */
    @PostMapping("/list")
    public R<List<MailContentListVo>> list(@RequestBody MailContentListQuery mailContentListQuery) {
        List<MailContentListVo> mailContentListVos = mailPickService.list(mailContentListQuery);
        return R.ok(mailContentListVos);
    }

    /**
     * 获取接收邮件详情
     *
     * @param id 邮件接收id
     * @return 邮件详情
     */
    @GetMapping("")
    public R<MailContent> getById(@RequestParam("id") String id) {
        MailContent mailContent = mailPickService.getById(id);
        return R.ok(mailContent);
    }

    /**
     * 移动
     *
     * @param boxId     邮件筐id
     * @param contentId 接收邮件id
     * @return 是否成功
     */
    @GetMapping("/move")
    public R<Boolean> move(@RequestParam("boxId") String boxId, @RequestParam("contentId") String contentId) {
        Boolean res = mailPickService.move(boxId, contentId);
        return res ? R.ok() : R.failed("移动失败");
    }


    /**
     * 手动分拣
     *
     * @param manualPickParam 分拣参数
     * @return 是否成功
     */
    @PostMapping("/pick")
    public R<Boolean> pick(@RequestBody ManualPickParam manualPickParam) {
        Boolean res = mailPickService.doPick(manualPickParam);
        return res ? R.ok() : R.failed("手动分拣失败");
    }

    /**
     * 同步读取邮件
     *
     * @param messageNumber 邮箱中最新邮件编号
     * @return 操作结果
     */
    @GetMapping("/readFromMail")
    public R<Void> readFromMail(@RequestParam("messageNumber") Integer messageNumber) {
        mailPickService.readSingleMail(messageNumber);
        return R.ok();
    }
}
