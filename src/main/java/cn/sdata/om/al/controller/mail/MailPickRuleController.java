package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailPickRuleListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickRule;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleListVo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailPickRuleService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件分拣规则
 */
@RestController
@RequestMapping("/mailPickRule")
public class MailPickRuleController {

    private MailPickRuleService mailPickRuleService;

    @Autowired
    public void setMailPickRuleService(MailPickRuleService mailPickRuleService) {
        this.mailPickRuleService = mailPickRuleService;
    }

    /**
     * 分页查询
     *
     * @param mailPickRuleListQuery 参数条件
     * @return 分页列表
     */
    @PostMapping("/page")
    public R<Page<MailPickRuleListVo>> page(@RequestBody MailPickRuleListQuery mailPickRuleListQuery) {
        PageInfo<MailPickRuleListVo> mailPickRuleListVoPageInfo = mailPickRuleService.page(mailPickRuleListQuery);
        Page<MailPickRuleListVo> page = new Page<>();
        page.setCurrent(mailPickRuleListQuery.getPageNo());
        page.setSize(mailPickRuleListQuery.getPageSize());
        page.setTotal(mailPickRuleListVoPageInfo.getTotal());
        page.setRecords(CollectionUtil.isNotEmpty(mailPickRuleListVoPageInfo.getList()) ? mailPickRuleListVoPageInfo.getList() : new ArrayList<>());
        return R.ok(page, "查询成功");
    }

    /**
     * 分页查询
     *
     * @param saveOrUpdateMailPickRule 参数
     * @return 分页列表
     */
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateMailPickRule saveOrUpdateMailPickRule) {
        boolean res = mailPickRuleService.saveOrUpdate(saveOrUpdateMailPickRule);
        return res ? R.ok() : R.failed("新增或更新失败");
    }

    /**
     * 根据id查询详情
     *
     * @param id 主键id
     * @return 模板详情
     */
    @GetMapping("")
    public R<MailPickRuleDetail> getById(@RequestParam("id") String id) {
        MailPickRuleDetail mailPickRuleDetail = mailPickRuleService.getById(id);
        return R.ok(mailPickRuleDetail);
    }

    /**
     * 下拉框查询邮件发送规则
     *
     * @return 分页信息
     */
    @GetMapping("/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> commonEntities = mailPickRuleService.list();
        return R.ok(commonEntities);
    }


    /**
     * 下拉框查询邮件发送规则
     *
     * @return 分页信息
     */
    @GetMapping("/unbindRuleList")
    public R<List<CommonEntity>> unbindRuleList() {
        List<CommonEntity> commonEntities = mailPickRuleService.unbindRuleList();
        return R.ok(commonEntities);
    }


    /**
     * 批量删除
     *
     * @param ids 主键id列表
     * @return 是否成功
     */
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody List<String> ids) {
        boolean res = mailPickRuleService.delete(ids);
        return res ? R.ok() : R.failed("删除失败");
    }
}
