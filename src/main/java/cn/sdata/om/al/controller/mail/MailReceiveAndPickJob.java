package cn.sdata.om.al.controller.mail;

import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.service.mail.MailPickService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MailReceiveAndPickJob {

    private MailPickService mailPickService;

    @Value("${receive-email.init-max-number}")
    private Integer maxNumber;

    @Value("${system.open-auto-pick}")
    private boolean closeAutoPick;

    @Autowired
    public void setMailPickService(MailPickService mailPickService) {
        this.mailPickService = mailPickService;
    }

    /**
     * 每2分钟执行抽取邮件
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void extractMail() {
        if (!closeAutoPick) {
            // 不同步历史邮件 20250320查询客户提供的邮件数谁 32715 为了测试功能故 只同步最近的5封
            mailPickService.readFromMail(maxNumber);
            List<MailContent> mailContents = SpringUtil.getBean(MailContentMapper.class).selectUnPickMail();
            mailPickService.pickMail(mailContents);
        }
    }

}
