package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailTemplateListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailTemplate;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateListVo;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.mail.MailTemplateService;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/template")
public class MailTemplateController {

    private MailTemplateService mailTemplateService;

    @Autowired
    public void setMailService(MailTemplateService mailTemplateService) {
        this.mailTemplateService = mailTemplateService;
    }

    /**
     * 分页查询邮件发送规则
     *
     * @param mailTemplateListQuery 查询参数
     * @return 分页信息
     */
    @PostMapping("/page")
    public R<Page<MailTemplateListVo>> page(@RequestBody MailTemplateListQuery mailTemplateListQuery) {
        PageInfo<MailTemplateListVo> mailTemplateListVos = mailTemplateService.page(mailTemplateListQuery);
        Page<MailTemplateListVo> page = new Page<>();
        page.setRecords(CollectionUtil.isNotEmpty(mailTemplateListVos.getList()) ? mailTemplateListVos.getList() : new ArrayList<>());
        page.setCurrent(mailTemplateListQuery.getPageNo());
        page.setSize(mailTemplateListQuery.getPageSize());
        page.setTotal(mailTemplateListVos.getTotal());
        return R.ok(page);
    }


    /**
     * 下拉框查询邮件发送规则
     *
     * @return 分页信息
     */
    @GetMapping("/list")
    public R<List<CommonEntity>> list() {
        List<CommonEntity> mailTemplateListVos = mailTemplateService.list();
        return R.ok(mailTemplateListVos);
    }

    /**
     * 批量删除
     *
     * @param ids 主键id列表
     * @return 是否成功
     */
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody List<String> ids) {
        boolean res = mailTemplateService.delete(ids);
        return res ? R.ok() : R.failed("删除失败");
    }

    /**
     * 新增或修改邮件模板
     *
     * @param saveOrUpdateMailTemplate 对象参数
     * @return 是否成功
     */
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody SaveOrUpdateMailTemplate saveOrUpdateMailTemplate) {
        boolean res = mailTemplateService.saveOrUpdate(saveOrUpdateMailTemplate);
        return res ? R.ok() : R.failed("新增或更新失败");
    }

    /**
     * 根据id查询详情
     *
     * @param id 主键id
     * @return 模板详情
     */
    @GetMapping("")
    public R<MailTemplateDetailVo> getById(@RequestParam("id") String id) {
        MailTemplateDetailVo mailTemplateDetailVo = mailTemplateService.getById(id);
        return R.ok(mailTemplateDetailVo);
    }

    /**
     * 获取全部账套字段名称
     *
     * @return 账套名称列表
     */
    @GetMapping("/getCategoryList")
    public R<List<CommonEntity>> getAccountSetListByFieldTypeId(@RequestParam("fieldTypeId") String fieldTypeId) {
        List<CommonEntity> commonEntities = mailTemplateService.getAccountSetList(fieldTypeId);
        return R.ok(commonEntities);
    }


    /**
     * 获取全部绑定任务
     *
     * @return 绑定任务列表
     */
    @GetMapping("/getFieldTypeList")
    public R<List<CommonEntity>> getFieldTypeList() {
        List<CommonEntity> commonEntities = mailTemplateService.getFieldTypeList();
        return R.ok(commonEntities);
    }

    /**
     * 根据模板id获取分类
     *
     * @return 分类信息
     */
    @GetMapping("/getCategoryByTemplateId")
    public R<JSONObject> getCategoryByTemplateId(@RequestParam("id") String templateId) {
        return R.ok(mailTemplateService.getCategoryByTemplateId(templateId));
    }
}
