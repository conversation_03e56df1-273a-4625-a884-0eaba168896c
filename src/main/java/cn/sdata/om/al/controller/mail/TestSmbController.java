package cn.sdata.om.al.controller.mail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.controller.OCRController;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.entity.mail.ShareDownloadFile;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.BankReconciliationSQRpaJob;
import cn.sdata.om.al.job.BankReconciliationZZRpaJob;
import cn.sdata.om.al.job.SQPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.job.ZZPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.FlowListMapper;
import cn.sdata.om.al.mapper.InterbankFeesMapper;
import cn.sdata.om.al.ocr.OCRResult;
import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.service.RpaService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.service.mail.MailPickService;
import cn.sdata.om.al.utils.MailUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static cn.sdata.om.al.constant.BaseConstant.RPA_END_DATE_NAME;
import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

@RestController
@RequestMapping("/testSmb")
@Slf4j
public class TestSmbController {

    private SMBService smbService;

    private MailUtil mailUtil;

    private MailInfoService mailInfoService;

    private RpaExecuteService rpaExecuteService;

    @Autowired
    public void setSmbService(SMBService smbService) {
        this.smbService = smbService;
    }

    @Autowired
    public void setRpaExecuteService(RpaExecuteService rpaExecuteService) {
        this.rpaExecuteService = rpaExecuteService;
    }

    @Autowired
    public void setMailUtil(MailUtil mailUtil) {
        this.mailUtil = mailUtil;
    }

    @GetMapping("/listDir")
    public R<List<String>> listDir() {
        List<String> strings = smbService.listDir("");
        return R.ok(strings);
    }

    public static void main(String[] args) {
        String encode = URLEncoder.encode("/多账套科目发生及余额表/20250307");
        System.out.println(encode);
    }

    @GetMapping("/listDirByPath")
    public R<List<String>> listDirByPath(@RequestParam("path") String path) {
        List<String> strings = smbService.listDir(path);
        return R.ok(strings);
    }

    @Autowired
    public void setMailInfoService(MailInfoService mailInfoService) {
        this.mailInfoService = mailInfoService;
    }

    @GetMapping("/download")
    public void download(HttpServletResponse response) throws Exception {
        ShareDownloadFile shareDownloadFile = smbService.downloadReturnFile("/常用软件/ASHR_status.txt");
        String filePath = shareDownloadFile.getFilePath();
        log.info("文件路径为:{}", filePath);
        InputStream inputStream = shareDownloadFile.getInputStream();
        response.reset();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(shareDownloadFile.getFileName(), StandardCharsets.UTF_8));
        ServletOutputStream outputStream = response.getOutputStream();
        byte[] b = new byte[1024];
        int len;
        //从输入流中读取一定数量的字节，并将其存储在缓冲区字节数组中，读到末尾返回-1
        while ((len = inputStream.read(b)) > 0) {
            outputStream.write(b, 0, len);
        }
        inputStream.close();
    }

    @GetMapping("/sendEmail")
    public R<Boolean> sendEmail(@RequestParam("url") String url) {
        String errorMsg;
        try {
            mailUtil.sendEmailFileFromShareFolder(CollectionUtil.newArrayList(url), CollectionUtil.newArrayList(url), null, "测试邮件", "测试发送邮件");
            return R.ok();
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        }
        return R.failed(errorMsg);
    }

    @GetMapping("/sendEmailAndFile")
    public R<Boolean> sendEmailAndFile(@RequestParam("url") String url,
                                       @RequestParam("filePath") String filePath) {
        boolean res = mailInfoService.testSendMail(url, filePath);
        return res ? R.ok() : R.failed();
    }

    @GetMapping("/testExecuteRpaAndDownloadFromShare")
    public R<Boolean> testExecuteRpaAndDownloadFromShare(@RequestParam(value = "beginDate", required = false) String beginDate,
                                                         @RequestParam(value = "endDate", required = false) String endDate) {
        beginDate = StringUtils.isBlank(beginDate) ? "2025-02-01" : beginDate;
        endDate = StringUtils.isBlank(endDate) ? "2025-02-28" : beginDate;
        // 获取流程配置
        FlowList flowList = SpringUtil.getBean(FlowListMapper.class).selectById(4);
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        String logId = IdUtil.getSnowflakeNextIdStr();
        // 预写日志
        BaseCronLog baseCronLog = new BaseCronLog();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setDataDate(DateUtil.today());
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId("12345");
        baseCronLog.setExecutor(BaseConstant.DEFAULT_USERNAME);
        baseCronLog.setExecuteMethod("testExecuteRpa");
        baseCronLogMapper.insert(baseCronLog);
        if (null == flowList) {
            BusinessException.throwException("id = 4的流程配置为null");
        }
        String name = flowList.getName();
        log.info("流程全路径为:{}", name);
        // 组装参数
        Map<String, Object> flowExtendParams = new HashMap<>();
        flowExtendParams.put(RPA_START_DATE_NAME, beginDate);
        flowExtendParams.put(RPA_END_DATE_NAME, endDate);
        // 开启流程
        RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flowList, flowExtendParams, logId);
        log.info("rpa执行日志为:{}", JSON.toJSONString(rpaExecLog));
        rpaExecuteService.startTimer(rpaExecLog, flowList, flowExtendParams, logId);
        return R.ok();
    }


    @PostMapping("/testExecuteRpaAndDownloadFromSharePost")
    public R<Boolean> testExecuteRpaAndDownloadFromSharePost(@RequestBody JSONObject jsonObject) {
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        return testExecuteRpaAndDownloadFromShare(beginDate, endDate);
    }

    @GetMapping("/testDays")
    public R<Object> testDays() {
        MarketTradeDayService marketTradeDayService = SpringUtil.getBean(MarketTradeDayService.class);
        Set<Date> firstMonthDay = marketTradeDayService.getMonthLastDay("00");
        return R.ok(firstMonthDay);
    }

    @PostMapping("/testOCR")
    public R<?> testOCR(@RequestParam("file") MultipartFile file) {
        return SpringUtil.getBean(OCRController.class).parse(file);
    }

    @PostMapping("/testOCRV2")
    public R<?> testOCRV2(@RequestParam("file") MultipartFile file, @RequestParam(value = "page", defaultValue = "2") int page) {
        return SpringUtil.getBean(OCRController.class).parseV2(file, page);
    }


    @GetMapping("/testQueryCop")
    public R<?> testQueryCop(@RequestParam(value = "nameOfPayee", required = false) String nameOfPayee,
                             @RequestParam(value = "beneficiaryAccount", required = false) String beneficiaryAccount,
                             @RequestParam(value = "bankAccount", required = false) String bankAccount) throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectAllCop(nameOfPayee, beneficiaryAccount, bankAccount);
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }


    @GetMapping("/testQueryCopV2")
    public R<?> testQueryCopV2(@RequestParam(value = "nameOfPayee", required = false) String nameOfPayee,
                               @RequestParam(value = "beneficiaryAccount", required = false) String beneficiaryAccount,
                               @RequestParam(value = "bankAccount", required = false) String bankAccount) throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectAllCopV2(nameOfPayee, beneficiaryAccount, bankAccount);
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

    @GetMapping("/testQueryCopAcc")
    public R<?> testQueryCopAcc() throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testQueryCopAcc();
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

    @GetMapping("/testQueryCopFund")
    public R<?> testQueryCopFund() throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testQueryCopFund();
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

    @GetMapping("/testJoinQueryCop")
    public R<?> testJoinQueryCop(@RequestParam(value = "nameOfPayee", required = false) String nameOfPayee,
                                 @RequestParam(value = "beneficiaryAccount", required = false) String beneficiaryAccount,
                                 @RequestParam(value = "bankAccount", required = false) String bankAccount,
                                 @RequestParam(value = "beginDate", required = false) String beginDate,
                                 @RequestParam(value = "endDate", required = false) String endDate) throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class)
                    .testJoinQueryCop(nameOfPayee, beneficiaryAccount, bankAccount, beginDate, endDate);
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }


    @GetMapping("/testQueryO32")
    public R<?> testQueryO32() throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectO32Date();
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }


    @GetMapping("/testHisQueryCop")
    public R<?> testHisQueryCop(@RequestParam(value = "nameOfPayee", required = false) String nameOfPayee,
                                @RequestParam(value = "beneficiaryAccount", required = false) String beneficiaryAccount,
                                @RequestParam(value = "bankAccount", required = false) String bankAccount) throws Exception {
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testHisQueryCop(nameOfPayee, beneficiaryAccount, bankAccount);
            return R.ok(jsonObjects);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.failed();
    }

    @GetMapping("/getMaxNumber")
    public R<Integer> getMaxNumber() {
        Integer maxNumber = SpringUtil.getBean(MailPickService.class).getMaxNumber();
        return R.ok(maxNumber);
    }

    @GetMapping("/testBRZZRpa")
    public R<Boolean> testBRZZRpa(@RequestParam("dataDate") String dataDate, @RequestParam("accountNumber") String accountNumber) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> brZZRpaJobIds = cronService.getJobIdByClass(BankReconciliationZZRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", SecureUtil.currentUser().getAccount());
        jobDataMap.put("accountNumber", accountNumber);
        jobDataMap.put("dataDate", dataDate);
        cronService.startJobNow(brZZRpaJobIds, jobDataMap);
        return R.ok();
    }

    @GetMapping("/testBRSQRpa")
    public R<Boolean> testBRSQRpa(@RequestParam("dataDate") String dataDate) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> brSQRpaJobIds = cronService.getJobIdByClass(BankReconciliationSQRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", SecureUtil.currentUser().getAccount());
        jobDataMap.put("dataDate", dataDate);
        cronService.startJobNow(brSQRpaJobIds, jobDataMap);
        return R.ok();
    }

    @GetMapping("/testFYZZRpa")
    public R<Boolean> testFYZZRpa(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> fyZZRpaJobIds = cronService.getJobIdByClass(ZZPaymentReceiptDownloadRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", SecureUtil.currentUser().getAccount());
        jobDataMap.put("beginDate", beginDate);
        jobDataMap.put("endDate", endDate);
        cronService.startJobNow(fyZZRpaJobIds, jobDataMap);
        return R.ok();
    }

    @GetMapping("/testFYSQRpa")
    public R<Boolean> testFYSQRpa(@RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate) throws Exception {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> fySQRpaJobIds = cronService.getJobIdByClass(SQPaymentReceiptDownloadRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", SecureUtil.currentUser().getAccount());
        jobDataMap.put("beginDate", beginDate);
        jobDataMap.put("endDate", endDate);
        cronService.startJobNow(fySQRpaJobIds, jobDataMap);
        return R.ok();
    }

    @GetMapping("/getTradeDay")
    public R<String> getTradeDay(@RequestParam("date") String date) {
        String tradeDay = SpringUtil.getBean(MarketTradeDayService.class).getTradeDay(date, "00", "1", 2);
        return R.ok(tradeDay);
    }


    @GetMapping("/getRPAInfo")
    public R<Map<String, String>> getRPAInfo(@RequestParam("flowId") String flowId,
                                             @RequestParam("execId") String execId,
                                             @RequestParam("execTime") String execTime) {
        Map<String, String> flowExecState = SpringUtil.getBean(RpaService.class).getFlowExecState(flowId,
                execId, execTime, true);
        return R.ok(flowExecState);
    }

    @PostMapping("/getTestApiOCR")
    public R<OCRResult> getTestApiOCR(@RequestParam("file") MultipartFile file) throws Exception {
        if (file != null && !file.isEmpty()) {
            OCRResult ocrResult = SpringUtil.getBean(OCRUtil.class).useApiExecuteOCR(file.getBytes());
            return R.ok(ocrResult);
        }
        return R.failed();
    }
}
