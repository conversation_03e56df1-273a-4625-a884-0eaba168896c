package cn.sdata.om.al.disclosure;

import cn.hutool.core.io.FileUtil;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/***
 * 逻辑暂时与估值表一致
 */
@Component
@AllArgsConstructor
public class CustodianBankValuationHandler implements MailHandler {

    private final ExcelValuationTableHandler excelValuationTableHandler;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        if (files != null && !files.isEmpty()) {
            List<RemoteFileInfo> remoteFileInfos = new ArrayList<>(files.values());
            String location = remoteFileInfos.get(0).getLocation();
            if ("remote".equals(location)) {
                return excelValuationTableHandler.execute(sendMailInfo, dataDate, files, mailTemplateDetailVo);
            }else{
                return dealLocal(sendMailInfo, files, mailTemplateDetailVo);
            }
        }
        return excelValuationTableHandler.execute(sendMailInfo, dataDate, files, mailTemplateDetailVo);
    }

    private SendMailInfo dealLocal(SendMailInfo sendMailInfo, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();
        for (String productId : productIds) {
            RemoteFileInfo remoteFileInfo = files.get(productId);
            if (remoteFileInfo != null) {
                Map<String, byte[]> attachment = sendMailInfo.getAttachment();
                Set<File> openFiles = remoteFileInfo.getOpenFile();
                for (File openFile : openFiles) {
                    attachment.put(openFile.getName(), FileUtil.readBytes(openFile));
                }
            }
        }
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }
}
