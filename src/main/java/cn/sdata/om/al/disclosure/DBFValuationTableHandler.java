package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DBFValuationTableHandler implements MailHandler {

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        return null;
    }
}
