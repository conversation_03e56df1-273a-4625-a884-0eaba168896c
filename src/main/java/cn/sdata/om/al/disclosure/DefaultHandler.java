package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.utils.SMBManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_MAP_KEY;
import static cn.sdata.om.al.utils.StringUtil.concatSeparator;

@Component
@AllArgsConstructor
@Slf4j
public class DefaultHandler implements MailHandler {

    private final SMBManager smbManager;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        Boolean flag = true;
        if (!files.isEmpty() && !files.values().isEmpty()) {
            List<RemoteFileInfo> list = new ArrayList<>(files.values());
            if (null != list.get(0) && "local".equals(list.get(0).getLocation())) {
                flag = false;
            }
        }
        if (flag) {
            //这里不存在账套,约定为default
            int size = files.size();
            for (int i = 0; i < size; i++) {
                RemoteFileInfo remoteFileInfo = files.get(DEFAULT_MAP_KEY + i);
                Objects.requireNonNull(remoteFileInfo, "文件信息为空");
                Map<String, byte[]> attachment = sendMailInfo.getAttachment();
                byte[] bytes = smbManager.downloadFile(concatSeparator(remoteFileInfo.getRelativePath(), remoteFileInfo.getFileName()));
                attachment.put(remoteFileInfo.getFileName(), bytes);
            }
        } else {
            for (RemoteFileInfo remoteFile : new ArrayList<>(files.values())) {
                try {
                    File file = new File(remoteFile.getFilePath());
                    Objects.requireNonNull(remoteFile, "本地文件信息为空");
                    Map<String, byte[]> attachment = sendMailInfo.getAttachment();
                    attachment.put(remoteFile.getFileName(), FileUtils.readFileToByteArray(file));
                } catch (Exception e) {
                    log.error("DefaultHandler_execute_localFile_error:{},{}", e, e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }
}

