package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.utils.SMBManager;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.sdata.om.al.utils.StringUtil.concatSeparator;

/**
 * 发送excel类型估值表以及余额表
 * 把RPA下载的文件放入附件即可
 */
@Component
@AllArgsConstructor
public class ExcelValuationTableHandler implements MailHandler {

    private final SMBManager smbManager;
    private final AccountInformationService accountInformationService;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        if (files == null || files.isEmpty()) {
            throw new RuntimeException("RPA下载文件为空");
        }
        Set<String> renameSet = Set.of("6154", "6160", "6171", "6173", "6188", "6201");
        List<String> productIds = sendMailInfo.getProductIds();
        Map<String, String> idNames = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId, AccountInformation::getFullProductName, (oldOne, newOne) -> newOne));
        for (String productId : productIds) {
            RemoteFileInfo remoteFileInfo = files.get(productId);
            if (remoteFileInfo != null) {
                Map<String, byte[]> attachment = sendMailInfo.getAttachment();
                byte[] bytes = smbManager.downloadFile(concatSeparator(remoteFileInfo.getRelativePath(), remoteFileInfo.getFileName()));
                String fileName = remoteFileInfo.getFileName();
                if (renameSet.contains(productId)) {
                    String productName = idNames.get(productId);
                    fileName = "估值表-" + productName + "-" + dataDate + ".xlsx";
                }
                attachment.put(fileName, bytes);
            }
        }
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }
}
