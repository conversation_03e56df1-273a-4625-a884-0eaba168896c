package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;

import java.util.Map;

public interface MailHandler {

    /**
     * 邮件逻辑处理器
     *
     * @param sendMailInfo         通过邮件发送配置处理好的邮件数据
     * @param dataDate             数据日期
     * @param files                RPA下载的文件列表 在净值场景中为账套编号 - 文件对象
     * @param mailTemplateDetailVo 当前邮件模板信息
     * @return 需要发送的邮件全部信息
     */
    SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo);

}
