package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.service.NetValueDisclosureService;
import lombok.AllArgsConstructor;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.*;

/**
 * 净值处理, 将净值写入邮件正文
 */
@Component
@AllArgsConstructor
public class NetValueHandler implements MailHandler {

    private final NetValueDisclosureService netValueDisclosureService;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();
        String content = mailTemplateDetailVo.getContent();
        content = fillMailContent(content, dataDate, productIds);
        sendMailInfo.setContent(content);
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        return sendMailInfo;
    }


    private String fillMailContent(String content,String dataDate, List<String> productIds) {
        Document contentDoc = Jsoup.parse(content);
        List<ValuationTableData> netValueFromValuation = netValueDisclosureService.getNetValueFromValuation(dataDate, productIds);
        Elements tables = contentDoc.getElementsByTag("table");
        if (!tables.isEmpty()) {
            Element table = tables.get(0);
            Element element = Jsoup.parse(buildTable(netValueFromValuation)).selectFirst("table");
            if (element != null) {
                table.replaceWith(element);
            }
        }
        return contentDoc.html();
    }

    private String buildTable(List<ValuationTableData> netValueFromValuation) {
        if (netValueFromValuation == null || netValueFromValuation.isEmpty()) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        result.append("<table style=\"text-align: center; width: 800px;\">");
        ValuationTableData test = netValueFromValuation.get(0);
        String productType = test.getProductType();
        if (PRODUCT_TYPE_FLAT.equals(productType)) {
            result.append("<thead style=\"background-color: rgb(155, 194, 230)\">");
            result.append("<tr>")
                    .append("<th>净值日期</th>")
                    .append("<th>产品名称</th>")
                    .append("<th>产品代码</th>")
                    .append("<th>单位净值</th>")
                    .append("<th>累计单位净值</th>")
                    .append("</tr>");
            result.append("</thead>");
            result.append("<tbody>");
            for (ValuationTableData valuationTableData : netValueFromValuation) {
                result.append("<tr>")
                        .append("<td>").append(valuationTableData.getValuationDate()).append("</td>")
                        .append("<td>").append(valuationTableData.getProductName()).append("</td>")
                        .append("<td>").append(valuationTableData.getProductCode()).append("</td>")
                        .append("<td>").append(valuationTableData.getNetValue()).append("</td>")
                        .append("<td>").append(valuationTableData.getSumNetValue()).append("</td>")
                        .append("</tr>");
            }
            result.append("</tbody>");
            result.append("</table>");
        } else if (PRODUCT_TYPE_STRUCTURED.equals(productType)) {
            result.append("<thead style=\"background-color: rgb(155, 194, 230)\">");
            result.append("<tr>")
                    .append("<th>净值日期</th>")
                    .append("<th>产品名称</th>")
                    .append("<th>产品代码</th>")
                    .append("<th>虚拟份额净值</th>")
                    .append("</tr>");
            result.append("</thead>");
            result.append("<tbody>");
            for (ValuationTableData valuationTableData : netValueFromValuation) {
                result.append("<tr>")
                        .append("<td rowspan=\"3\">").append(valuationTableData.getValuationDate()).append("</td>")
                        .append("<td>").append(valuationTableData.getProductName()).append("</td>")
                        .append("<td>").append(valuationTableData.getProductCode()).append("</td>")
                        .append("<td>").append(valuationTableData.getNetValue()).append("</td>")
                        .append("</tr>");
                result.append("<tr>")
                        .append("<td>").append(valuationTableData.getSuperiorProductName()).append("</td>")
                        .append("<td>").append(valuationTableData.getSuperiorProductCode()).append("</td>")
                        .append("<td>").append(valuationTableData.getSuperiorNetValue()).append("</td>")
                        .append("</tr>");
                result.append("<tr>")
                        .append("<td>").append(valuationTableData.getInferiorProductName()).append("</td>")
                        .append("<td>").append(valuationTableData.getInferiorProductCode()).append("</td>")
                        .append("<td>").append(valuationTableData.getInferiorNetValue()).append("</td>")
                        .append("</tr>");
            }
            result.append("</tbody>");
            result.append("</table>");
        }
        return result.toString();
    }
}
