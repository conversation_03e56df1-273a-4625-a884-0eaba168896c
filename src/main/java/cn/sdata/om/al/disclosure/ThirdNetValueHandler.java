package cn.sdata.om.al.disclosure;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.utils.ThirdFileUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
@AllArgsConstructor
public class ThirdNetValueHandler implements MailHandler {

    private final NetValueDisclosureService netValueDisclosureService;
    private final ThirdFileUtil thirdFileUtil;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();
        List<ValuationTableData> netValueFromValuation = netValueDisclosureService.getNetValueFromValuation(dataDate, productIds);
        String contactName = sendMailInfo.getContactName();
        byte[] attachment = thirdFileUtil.getAttachment(netValueFromValuation, contactName);
        Map<String, byte[]> attachments = sendMailInfo.getAttachment();
        attachments.put(contactName + "净值对接--" + dataDate + ".xlsx", attachment);
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        sendMailInfo.setContent(mailTemplateDetailVo.getContent());
        return sendMailInfo;
    }

}
