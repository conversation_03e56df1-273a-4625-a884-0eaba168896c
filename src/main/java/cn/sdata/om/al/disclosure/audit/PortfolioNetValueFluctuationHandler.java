package cn.sdata.om.al.disclosure.audit;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.disclosure.MailHandler;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@AllArgsConstructor
@Slf4j
public class PortfolioNetValueFluctuationHandler implements MailHandler {

    private final PortfolioNetValueFluctuationService fluctuationService;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();
        String content = mailTemplateDetailVo.getContent();
        Map<String, Object> extraData = sendMailInfo.getExtraData();
        List<String> dataDateList = (List<String>) extraData.get("dataDateList");
        // 通过邮件模板title判断邮件类型（如标题包含"回撤"则为retracement，否则为fluctuation）
        String type = "fluctuation";
        if (mailTemplateDetailVo.getTitle() != null && mailTemplateDetailVo.getTitle().contains("回撤")) {
            type = "retracement";
        }
        log.info("发送的模板类型为:{}", type);
        if (CollectionUtil.isNotEmpty(dataDateList)) {
            if (dataDateList.size() == 1) {
                dataDate = dataDateList.get(0);
            } else {
                dataDate = dataDateList.get(0) + "-" + dataDateList.get(dataDateList.size() - 1);
            }
        }
        sendMailInfo.setDataDate(dataDate);
        content = fillMailContent(content, dataDate, productIds, type, dataDateList);
        if (StringUtils.isBlank(content)) {
            return null;
        }
        sendMailInfo.setContent(content);
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        return sendMailInfo;
    }

    private String fillMailContent(String content, String dataDate, List<String> productIds, String type, List<String> dataDateList) {
        log.info("进入到fillMailContent() 的参数为:{},{},{},{},{}", content, dataDate, JSON.toJSONString(productIds), type, JSON.toJSONString(dataDateList));
        Document contentDoc = Jsoup.parse(content);
        LambdaQueryWrapper<PortfolioNetValueFluctuation> queryWrapper = new LambdaQueryWrapper<>();
        if ("retracement".equals(type)) {
            queryWrapper.eq(PortfolioNetValueFluctuation::getRetracementFlag, 1);
        } else {
            queryWrapper.eq(PortfolioNetValueFluctuation::getAbnormalFluctuation, 1);
        }
        if (CollectionUtil.isNotEmpty(dataDateList)) {
            queryWrapper.in(PortfolioNetValueFluctuation::getDataDate, dataDateList);
        } else {
            queryWrapper.eq(PortfolioNetValueFluctuation::getDataDate, dataDate);
        }
        queryWrapper.in(PortfolioNetValueFluctuation::getProductId, productIds);
        List<PortfolioNetValueFluctuation> fluctuationList = new ArrayList<>();
        if (!productIds.isEmpty()) {
            fluctuationList = fluctuationService.list(queryWrapper);
            log.info("查询出的波动数据为:{}", JSON.toJSONString(fluctuationList));
        }
        Elements tables = contentDoc.getElementsByTag("table");
        if (!tables.isEmpty()) {
            Element table = tables.get(0);
            Element element = Jsoup.parse(buildTable(fluctuationList, dataDate, type)).selectFirst("table");
            if (element != null) {
                table.replaceWith(element);
            }
        }
        return contentDoc.html();
    }

    /**
     * 构建净值波动/回撤表格HTML
     * @param list 数据列表
     * @param dataDate 数据日期
     * @param type 邮件类型：fluctuation-波动 retracement-回撤
     */
    private @NonNull String buildTable(@NonNull List<PortfolioNetValueFluctuation> list, String dataDate, String type) {
        if (list.isEmpty()) {
            return "<table><tr><td>暂无数据</td></tr></table>";
        }
        StringBuilder tableBuilder = new StringBuilder();
        tableBuilder.append("<table style='border-collapse: collapse; width: 100%; max-width: 1200px; margin: 0 auto; font-family: Arial, sans-serif; font-size: 12px; border: 1px solid #ddd;'>");
        tableBuilder.append("<caption style='font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; text-align: center;'>");
        tableBuilder.append("组合产品净值波动/回撤汇总（").append(dataDate).append("）");
        tableBuilder.append("</caption>");
        tableBuilder.append("<thead>");
        tableBuilder.append("<tr style='background-color: #f2f2f2;'>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>产品编号</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>产品名称</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>数据日期</th>");
        if ("fluctuation".equals(type)) {
            tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>净值异常波动</th>");
            tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>净值波动率</th>");
        } else if ("retracement".equals(type)) {
            tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>净值回撤</th>");
            tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>回撤原因</th>");
        }
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>当日单位净值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>上一日单位净值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>单位净值差值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>万份收益</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>上一日万份收益</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>万份收益差值</th>");
        tableBuilder.append("</tr>");
        tableBuilder.append("</thead>");
        tableBuilder.append("<tbody>");
        list.stream().sorted(Comparator.comparing(PortfolioNetValueFluctuation::getDataDate).thenComparing(PortfolioNetValueFluctuation::getProductCode)).forEach(item -> {
            String rowStyle = "padding: 10px 8px; text-align: center; border: 1px solid #ddd; background-color: #f9f9f9;";
            tableBuilder.append("<tr>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getProductCode() != null ? item.getProductCode() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append(" text-align: left;'>").append(item.getProductName() != null ? item.getProductName() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getDataDate() != null ? item.getDataDate() : "").append("</td>");
            if ("fluctuation".equals(type)) {
                tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getAbnormalFluctuation() != null && item.getAbnormalFluctuation() == 1 ? "是" : "否").append("</td>");
                tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getFluctuationRate() != null ? item.getFluctuationRate().toString() : "").append("</td>");
            } else if ("retracement".equals(type)) {
                tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getRetracementFlag() != null && item.getRetracementFlag() == 1 ? "是" : "否").append("</td>");
                tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getRetracementReason() != null ? item.getRetracementReason() : "").append("</td>");
            }
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getCurrentNetValue() != null ? item.getCurrentNetValue().toString() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getPreviousNetValue() != null ? item.getPreviousNetValue().toString() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getNetValueDifference() != null ? item.getNetValueDifference().toPlainString() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getCurrentTenThousandIncome() != null ? item.getCurrentTenThousandIncome().toString() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getPreviousTenThousandIncome() != null ? item.getPreviousTenThousandIncome().toString() : "").append("</td>");
            tableBuilder.append("<td style='").append(rowStyle).append("'>").append(item.getTenThousandIncomeDifference() != null ? item.getTenThousandIncomeDifference().toString() : "").append("</td>");
            tableBuilder.append("</tr>");
        });
        tableBuilder.append("</tbody>");
        tableBuilder.append("</table>");
        return tableBuilder.toString();
    }
}
