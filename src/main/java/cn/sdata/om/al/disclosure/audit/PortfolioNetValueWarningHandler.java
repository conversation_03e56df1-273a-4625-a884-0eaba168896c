package cn.sdata.om.al.disclosure.audit;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.enums.WarningStatus;
import cn.sdata.om.al.audit.service.PortfolioNetValueWarningService;
import cn.sdata.om.al.disclosure.MailHandler;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@AllArgsConstructor
@Slf4j
public class PortfolioNetValueWarningHandler implements MailHandler {

    private final PortfolioNetValueWarningService portfolioNetValueWarningService;

    @Override
    public SendMailInfo execute(SendMailInfo sendMailInfo, String dataDate, Map<String, RemoteFileInfo> files, MailTemplateDetailVo mailTemplateDetailVo) {
        Objects.requireNonNull(sendMailInfo, "邮件信息不得为空");
        Objects.requireNonNull(mailTemplateDetailVo, "邮件模板不得为空");
        List<String> productIds = sendMailInfo.getProductIds();

        Map<String, Object> extraData = sendMailInfo.getExtraData();
        List<String> dataDateList = (List<String>) extraData.get("dataDateList");
        String valuationTime = (String) extraData.get("valuationTime");
        String content = mailTemplateDetailVo.getContent();

        if (CollectionUtil.isNotEmpty(dataDateList)) {
            if (dataDateList.size() == 1) {
                dataDate = dataDateList.get(0);
            } else {
                dataDate = dataDateList.get(0) + "-" + dataDateList.get(dataDateList.size() - 1);
            }
        }
        content = fillMailContent(content, dataDate, productIds, dataDateList, valuationTime);
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        sendMailInfo.setDataDate(dataDate);
        sendMailInfo.setContent(content);
        sendMailInfo.setSubject(mailTemplateDetailVo.getTitle());
        return sendMailInfo;
    }


    private String fillMailContent(String content, String dataDate, List<String> productIds, List<String> dataDateList, String valuationTime) {
        log.info("进入到fillMailContent() 的参数为:{},{},{},{},{}", content, dataDate, JSON.toJSONString(productIds), JSON.toJSONString(dataDateList), valuationTime);
        Document contentDoc = Jsoup.parse(content);
        LambdaQueryWrapper<PortfolioNetValueWarning> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(PortfolioNetValueWarning::getStatusMessage, WarningStatus.NONE);
        queryWrapper.eq(StringUtils.isNotBlank(valuationTime), PortfolioNetValueWarning::getValuationTime, valuationTime);
        // 查询指定日期的数据
        if (CollectionUtil.isNotEmpty(dataDateList)) {
            queryWrapper.in(PortfolioNetValueWarning::getDataDate, dataDateList);
        } else {
            queryWrapper.eq(PortfolioNetValueWarning::getDataDate, dataDate);
        }

        queryWrapper.in(PortfolioNetValueWarning::getProductId, productIds);
        List<PortfolioNetValueWarning> portfolioNetValueWarnings = new ArrayList<>();
        if (!productIds.isEmpty()) {
            portfolioNetValueWarnings = portfolioNetValueWarningService.list(queryWrapper);
            log.info("查询出的预警信息为:{}", JSON.toJSONString(portfolioNetValueWarnings));
        }
        if (CollectionUtil.isEmpty(portfolioNetValueWarnings)) {
            return "";
        }
        Elements tables = contentDoc.getElementsByTag("table");
        if (!tables.isEmpty()) {
            Element table = tables.get(0);
            Element element = Jsoup.parse(buildTable(portfolioNetValueWarnings, dataDate)).selectFirst("table");
            if (element != null) {
                table.replaceWith(element);
            }
        }
        return contentDoc.html();
    }

    /**
     * 构建组合净值预警表格HTML
     * @param warnings 预警数据列表
     * @param dataDate 数据日期
     * @return HTML表格字符串
     */
    private @NonNull String buildTable(@NonNull List<PortfolioNetValueWarning> warnings, String dataDate) {
        if (warnings.isEmpty()) {
            return "<table><tr><td>暂无预警数据</td></tr></table>";
        }

        StringBuilder tableBuilder = new StringBuilder();
        tableBuilder.append("<table style='")
                .append("border-collapse: collapse; ")
                .append("width: 100%; ")
                .append("max-width: 1200px; ")
                .append("margin: 0 auto; ")
                .append("font-family: Arial, sans-serif; ")
                .append("font-size: 12px; ")
                .append("border: 1px solid #ddd;")
                .append("'>");

        // 添加标题行显示日期
        tableBuilder.append("<caption style='")
                .append("font-size: 18px; ")
                .append("font-weight: bold; ")
                .append("margin-bottom: 15px; ")
                .append("color: #333; ")
                .append("text-align: center;")
                .append("'>");
        tableBuilder.append("组合产品净值预警汇总（").append(dataDate).append("）");
        tableBuilder.append("</caption>");

        // 表头
        tableBuilder.append("<thead>");
        tableBuilder.append("<tr style='background-color: #f2f2f2;'>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>产品编号</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>产品名称</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>数据日期</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>当前净值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>预警值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>平仓值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>罚没值</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>份额</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>触发预警状态</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>应补仓资金</th>");
        tableBuilder.append("<th style='padding: 12px 8px; text-align: center; border: 1px solid #ddd; font-weight: bold;'>状态信息</th>");
        tableBuilder.append("</tr>");
        tableBuilder.append("</thead>");

        // 表体 - 按日期、状态信息、产品分组排序
        tableBuilder.append("<tbody>");
        warnings.stream()
                .sorted(Comparator.comparing(PortfolioNetValueWarning::getDataDate)
                        .thenComparing((warning -> warning.getStatusMessage() != null ? warning.getStatusMessage().ordinal() : Integer.MIN_VALUE), Comparator.reverseOrder())
                        .thenComparing(PortfolioNetValueWarning::getProductCode))
                .forEach(warning -> {
                    // 交替行背景色
                    String rowStyle = "padding: 10px 8px; text-align: center; border: 1px solid #ddd; background-color: #f9f9f9;";

                    tableBuilder.append("<tr>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getProductCode() != null ? warning.getProductCode() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append(" text-align: left;'>").append(warning.getProductName() != null ? warning.getProductName() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getDataDate() != null ? warning.getDataDate() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getNetValue() != null ? warning.getNetValue().toString() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getWarningValue() != null ? warning.getWarningValue().toString() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getLiquidationValue() != null ? warning.getLiquidationValue().toString() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getForfeitureValue() != null ? warning.getForfeitureValue().toString() : "").append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getFundShare() != null ? warning.getFundShare().toString() : "").append("</td>");

                    // 预警状态显示
                    String statusText;
                    if (warning.getWarningTriggered() != null && warning.getWarningTriggered() == 1) {
                        statusText = "是";
                    } else {
                        statusText = "否";
                    }
                    tableBuilder.append("<td style='").append(rowStyle).append(" ").append("'>").append(statusText).append("</td>");
                    tableBuilder.append("<td style='").append(rowStyle).append("'>").append(warning.getRequiredFunding() != null ? warning.getRequiredFunding().toString() : "").append("</td>");

                    // 状态信息显示
                    String statusMessageText = "";
                    String statusMessageColor = "";
                    if (warning.getStatusMessage() != null) {
                        statusMessageText = warning.getStatusMessage().getDisplayName();
                        statusMessageColor = warning.getStatusMessage().getColor();
                    }
                    tableBuilder.append("<td style='")
                            .append(rowStyle)
                            .append(" background-color: ").append(statusMessageColor).append("; ")
                            .append("font-weight: bold; color: white;")
                            .append("'>")
                            .append(statusMessageText).append("</td>");

                    tableBuilder.append("</tr>");
                });
        tableBuilder.append("</tbody>");
        tableBuilder.append("</table>");

        return tableBuilder.toString();
    }
}
