package cn.sdata.om.al.dto;

import cn.sdata.om.al.entity.dividendDetail.LogCallRpaDividendDetailEntity;
import cn.sdata.om.al.entity.dividendDetail.LogMailSendDividendDetailEntity;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/3 15:43
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class DividendDetailLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<LogCallRpaDividendDetailEntity> logCallRpas = Lists.newArrayList();

    private List<LogMailSendDividendDetailEntity> logMailSend = Lists.newArrayList();

}
