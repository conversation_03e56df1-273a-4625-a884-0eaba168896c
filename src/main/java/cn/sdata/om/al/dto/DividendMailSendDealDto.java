package cn.sdata.om.al.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/26 16:14
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class DividendMailSendDealDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String detailId;

    private String detailFileId;

    private String detailFileMailId;

    private String dataDate;

    private String productId;

    private String fileName;

    private String localFilePath;
}
