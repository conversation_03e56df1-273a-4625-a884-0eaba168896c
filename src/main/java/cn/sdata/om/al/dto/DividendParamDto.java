package cn.sdata.om.al.dto;

import cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/7 15:26
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class DividendParamDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String dataDate;

    private List<String> productIds;

    private List<DividendDetailEntity> details;

    private String startDate;

    private String endDate;

    private List<String> productNames;

    private String mailSendStatus;

    private Integer pageNo;

    private Integer pageSize;

    private String mailBankSendStatus;

    private String mailInvestorSendStatus;

}
