package cn.sdata.om.al.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/4/30 15:53
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@Accessors(chain = true)
public class DividendRemoteFileDealDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private String productId;
    private String productCode;
    private String productName;
    private String fileName;
    private String localFilePath;
}
