package cn.sdata.om.al.dto;

import cn.sdata.om.al.entity.investNetReport.LogCallRpaNetReportEntity;
import cn.sdata.om.al.entity.investNetReport.LogFileGenNetReportEntity;
import cn.sdata.om.al.entity.investNetReport.LogFileOperNetReportEntity;
import cn.sdata.om.al.entity.investNetReport.LogMailSendNetReportEntity;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/29 20:33
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class InvestNetReportLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<LogFileOperNetReportEntity> logFileOpers = Lists.newArrayList();

    private List<LogCallRpaNetReportEntity> logCallRpas = Lists.newArrayList();

    private List<LogFileGenNetReportEntity> logFileGens = Lists.newArrayList();

    private List<LogMailSendNetReportEntity> logMailSend = Lists.newArrayList();

}
