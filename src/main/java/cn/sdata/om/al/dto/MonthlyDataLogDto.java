package cn.sdata.om.al.dto;

import cn.sdata.om.al.entity.investNetReport.LogMailSendNetReportEntity;
import cn.sdata.om.al.entity.monthlyData.LogCallRpaMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.LogFileOperMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.LogMailSendMonthlyDataEntity;
import cn.sdata.om.al.mapper.monthlyData.LogMailSendMonthlyDataMapper;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 月度日志记录-返回对象
 *
 * <AUTHOR>
 * @Date 2025/6/3 10:02
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class MonthlyDataLogDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 下载记录
     */
    private List<LogFileOperMonthlyDataEntity> logFileDownloads = Lists.newArrayList();

    /**
     * 上传记录
     */
    private List<LogFileOperMonthlyDataEntity> logFileUploads = Lists.newArrayList();

    /**
     * 调度rpa记录
     */
    private List<LogCallRpaMonthlyDataEntity> logCallRpas = Lists.newArrayList();

    /**
     * 邮件发送记录
     */
    private List<LogMailSendMonthlyDataEntity> logMailSend = Lists.newArrayList();

}
