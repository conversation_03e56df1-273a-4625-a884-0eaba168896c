package cn.sdata.om.al.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 月度数据-入参dto
 *
 * <AUTHOR>
 * @Date 2025/4/11 10:09
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class MonthlyDataParamDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String dataDate;

    private List<String> taskTypes;
}
