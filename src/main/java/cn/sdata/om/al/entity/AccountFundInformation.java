package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "account_fund_information")
public class AccountFundInformation {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 账套id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 渠道类型
     */
    @TableField(value = "channel_type")
    private Integer channelType;

    /**
     * 管理者
     */
    @TableField(value = "administrator")
    private String administrator;

    /**
     * 户名
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 账号
     */
    @TableField(value = "account_number")
    private String accountNumber;

    /**
     * 邮件后缀
     */
    @TableField(value = "email_suffix")
    private String emailSuffix;
}