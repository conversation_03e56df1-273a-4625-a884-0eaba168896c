package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "account_information")
public class AccountInformation {
    /**
     * 账套编号
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 产品全称
     */
    @TableField(value = "full_product_name")
    private String fullProductName;

    /**
     * 产品代码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 估值时间
     */
    @TableField(value = "valuation_time")
    private String valuationTime;

    /**
     * 是否结构化，1表示是，0表示否
     */
    @TableField(value = "is_structured")
    private Integer isStructured;

    /**
     * 是否电子直连，1表示是，0表示否
     */
    @TableField(value = "is_electronic_direct_connection")
    private Integer isElectronicDirectConnection;

    /**
     * 托管行
     */
    @TableField(value = "custodian_bank")
    private String custodianBank;

    /**
     * 二次估值的证券类型
     */
    @TableField(value = "secondary_valuation_security_type")
    private String secondaryValuationSecurityType;

    /**
     * 账户名称
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 账号
     */
    @TableField(value = "account_number_detail")
    private String accountNumberDetail;

    /**
     * 开户行
     */
    @TableField(value = "opening_bank")
    private String openingBank;

    /**
     * 大额支付号
     */
    @TableField(value = "large_payment_number")
    private String largePaymentNumber;

    /**
     * 是否自动扣收，1自动扣收，0表示手工支付
     */
    @TableField(value = "is_auto_deduction")
    private Integer isAutoDeduction;

    /**
     * 中债（托管账号）
     */
    @TableField(value = "central_debt_account_number")
    private String centralDebtAccountNumber;

    /**
     * 外汇交易中心（会员代码）
     */
    @TableField(value = "foreign_exchange_center_member_code")
    private String foreignExchangeCenterMemberCode;

    /**
     * 上清（持有人账号）
     */
    @TableField(value = "clearing_house_holder_account")
    private String clearingHouseHolderAccount;

    /**
     * 持有人账号
     */
    @TableField(value = "holder_account_number")
    private String holderAccountNumber;

    /**
     * 持有人账户全称
     */
    @TableField(value = "holder_account_full_name")
    private String holderAccountFullName;

    @TableField(value = "wind_code")
    private String windCode;

    @TableField(value = "establishment_date")
    private String establishmentDate;

    @TableField(value = "product_status")
    private Integer productStatus;

    @TableField(value = "product_category")
    private Integer productCategory;

    /**
     * 上清支付方式
     */
    @TableField(value = "payment_method_sq")
    private String paymentMethodSQ;

    /**
     * 中债支付方式
     */
    @TableField(value = "payment_method_zz")
    private String paymentMethodZZ;

    /**
     * 外汇支付方式
     */
    @TableField(value = "payment_method_wh")
    private String paymentMethodWH;

    /**
     * 预警值
     */
    @TableField(value = "warn_value")
    private String warnValue;

    /**
     * 平仓线/罚没线
     */
    @TableField(value = "close_value")
    private String closeValue;

    /**
     * 是否为货币 0:否, 1:是
     */
    @TableField(value = "is_currency")
    private Integer isCurrency;
}
