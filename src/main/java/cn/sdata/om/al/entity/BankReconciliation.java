package cn.sdata.om.al.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 银行对账
 */
@Getter
@Setter
public class BankReconciliation {

    private String id;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人id
     */
    private String updateBy;

    /**
     * 修改人姓名
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 删除标识
     */
    private int deleted;

    /**
     * 账号名称
     */
    private String accountSetName;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 账套组id
     */
    private String accountSetGroupId;

    /**
     * 账套组名称
     */
    private String accountSetGroupName;

    /**
     * 账套编号
     */
    private String accountSetCode;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 结算场所
     */
    private String settlementLocation;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 估值持仓数量
     */
    private String valuationPositionQuantity;

    /**
     * 结算公司持仓数量
     */
    private String settlementCompanyPositionQuantity;

    /**
     * 差异
     */
    private String difference;

    /**
     * 差异原因
     */
    private String differenceReasons;

    /**
     * 持有人账号
     */
    private String accountNumber;

    /**
     * 原始编码
     */
    private String sourceCode;

    /**
     * 账套分类
     */
    private Integer productCategory;

    /**
     * 估值时间
     */
    private String valuationTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BankReconciliation)) return false;
        BankReconciliation that = (BankReconciliation) o;
        return Objects.equals(dataDate, that.dataDate) && Objects.equals(accountSetCode, that.accountSetCode) && Objects.equals(settlementLocation, that.settlementLocation) && Objects.equals(securityName, that.securityName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataDate, accountSetCode, settlementLocation, securityName);
    }
}
