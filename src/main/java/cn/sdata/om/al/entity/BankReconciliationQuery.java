package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.List;

@Data
public class BankReconciliationQuery {

    /**
     * 第几页
     */
    private int pageNo;

    /**
     * 每页几条
     */
    private int pageSize;

    /**
     * 数据日期起始
     */
    private String beginDataDate;

    /**
     * 数据日期结束
     */
    private String endDataDate;

    /**
     * 账套组
     */
    private List<String> accountSetGroupIds;

    /**
     * 账套
     */
    private List<String> accountSetIds;

    /**
     * 差异
     */
    private String differenceStatus;

    /**
     * 产品代码
     */
    private List<String> productCodes;

    /**
     * 结算场所
     */
    private List<String> settlementLocations;

    /**
     * 证券代码
     */
    private List<String> securityCodes;

    /**
     * 证券名称
     */
    private List<String> securityNames;

    /**
     * 账套名称
     */
    private List<String> accountSetNames;

    /**
     * 选择的列
     */
    private List<String> columns;


    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 结算场所
     */
    private String settlementLocation;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 证券名称
     */
    private String securityName;

    /**
     * 账套名称
     */
    private String accountSetName;

    /**
     * 账套名称
     */
    private String accountSetCode;

    /**
     * 结算中心证券代码
     */
    private List<String> sourceCodes;

    /**
     * 账套分类
     */
    private Integer productCategory;

    /**
     * 估值时间
     */
    private String valuationTime;
}
