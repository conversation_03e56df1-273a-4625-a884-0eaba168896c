package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommonPageParam<T> extends PageParam {

    /**
     * 参数
     */
    private Map<String, Object> param;

    /**
     * 需要模糊查询的字段
     */
    private List<String> like;


    /**
     * 需要排序的字段
     *
     */
    private Map<String,String> orderColumn;

    /**
     * 排序字段内容为中文的字段
     */
    private Set<String> chinese;

    private List<BetweenParam> between;

    @Data
    public static class BetweenParam{
        private String field;
        private String start;
        private String end;
    }

    public Page<T> getPage(){
        Page<T> page = new Page<>();
        page.setCurrent(getCurrent());
        page.setSize(getSize());
        return page;
    }
}
