package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "custodian_bank_contacts")
public class CustodianBankContacts {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "custodian_bank")
    private String custodianBank;

    @TableField(value = "product_id")
    private String productId;

    @TableField(exist = false)
    private String productName;

    @TableField(value = "custodian_role")
    private String custodianRole;

    @TableField(value = "recipient")
    private String recipient;

    @TableField(value = "recipient_cc")
    private String recipientCc;

    @TableField(value = "phone")
    private String phone;
}