package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName(value = "custodian_bank_contacts_view")
@AllArgsConstructor
@NoArgsConstructor
public class CustodianBankContactsView {
    @TableField(value = "id")
    private String id;

    @TableField(value = "sub_id")
    private String subId;
}