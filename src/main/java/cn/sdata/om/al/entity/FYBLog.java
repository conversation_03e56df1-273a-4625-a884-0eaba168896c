package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

@Data
public class FYBLog {
    Page<LogFYBGenerateFileRecord> logFYBGenerateFileRecord;
    Page<LogFYBDownloadFileRecord> logFYBDownloadFileRecord;
    Page<LogFYBImportO32Record> logFYBImportO32Record;
    Page<LogFYBO32ConfirmRecord> logFYBO32ConfirmRecord;
    Page<LogFYBSendMailRecord> logFYBSendMailRecord;
    Page<LogFYBSyncPayStatusRecord> logFYBSyncPayStatusRecord;
    Page<LogFYBUpdatePayStatusRecord> logFYBUpdatePayStatusRecord;
    Page<LogFYBRPARecord> logFYBRPARecord;
}
