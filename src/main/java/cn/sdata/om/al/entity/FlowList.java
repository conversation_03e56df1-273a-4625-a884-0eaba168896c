package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "om_flow_list")
public class FlowList {
    /**
     * 流程编号
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 流程名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 展示名称
     */
    @TableField(value = "show_name")
    private String showName;

    /**
     * 内网或外网
     */
    @TableField(value = "net_env_flag")
    private int netEnvFlag;

    /**
     * 根路径
     */
    @TableField(value = "base_path")
    private String basePath;

    /**
     * 定时器延迟时间(s)
     */
    @TableField(value = "timer_delay")
    private Integer timerDelay;

    /**
     * Timer触发间隔时间(s)
     */
    @TableField(value = "timer_period")
    private Integer timerPeriod;

    /**
     * 处理器Bean名称
     */
    @TableField(value = "handler_name")
    private String handlerName;

}