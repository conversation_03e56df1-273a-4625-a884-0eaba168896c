package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能数据邮件关联
 *
 * <AUTHOR>
 * @Date 2025/4/13 21:13
 * @Version 1.0
 */
@Data
@TableName("func_data_mail")
@Accessors(chain = true)
public class FuncDataToMailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 邮件id
     */
    @TableField(value = "mail_id")
    private String mailId;

    /**
     * 邮件发送日志id
     */
    @TableField(value = "mail_send_log_id")
    private String mailSendLogId;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 发送状态
     */
    @TableField(value = "mail_status")
    private String mailStatus;

    /**
     * 功能类型
     */
    @TableField(value = "func_type")
    private String funcType;

    /**
     * 数据类型
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 套账ids
     */
    @TableField(value = "product_id")
    private String productId;
}
