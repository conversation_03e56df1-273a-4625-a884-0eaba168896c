package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.List;

@Data
public class InsuranceRegistrationFeesQuery {

    private int pageNo;

    /**
     * 每页几条
     */
    private int pageSize;

    /**
     * 开始费用日期
     */
    private String beginCostDate;

    /**
     * 结束费用日期
     */
    private String endCostDate;

    /**
     * 账套名称
     */
    private List<String> productIds;

    //导入 O32 状态
    private String importO32Status;

    /**
     * o32识别结果
     */
    private String ocrRecognizeStatus;

    private String mailSendStatus;

    private String paymentStatus;

    private String paymentDate;
}
