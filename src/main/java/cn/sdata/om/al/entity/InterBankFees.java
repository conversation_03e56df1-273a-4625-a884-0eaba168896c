package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class InterBankFees {

    private String id;
    //账套名称
    private String productName;
    //收费机构
    private String feeCollectionAgencies;
    // 费用日期
    private String costDate;
    //费用开始日期
    private String beginCostDate;
    // 费用结束日期
    private String endCostDate;
    //支付方式
    private String paymentMethod;
    //支付状态
    private String paymentStatus;
    //支付日期
    private String paymentDate;
    //支付状态更新时间
    private String paymentStatusUpdateTime;
    //导入 O32 状态
    private String importO32Status;
    //金额
    private String amount;
    //收款人名称
    private String nameOfPayee;
    //收款人账号
    private String beneficiaryAccount;
    //收款人开户行名称
    private String bankAccount;
    //收款人开户行号
    private String bankAccountNumber;
    //摘要
    private String remark;
    //通知日期
    private String noticeDate;
    //更新人
    private String updateByName;
    //更新时间
    private String updateTime;

    // 缴费通知单文件base64字符串
    private String base64FileStr;

    /**
     * 缴费单文件id
     */
    private String fileId;

    private String productId;

    /**
     * o32返回错误信息
     */
    private String o32Message;

    /**
     * ocr识别状态
     */
    private String ocrRecognizeStatus;

    /**
     * 邮件id
     */
    private String mailId;

    /**
     * 邮件发送状态
     */
    private String mailSendStatus;

    /**
     * 导入o32时间
     */
    private String importO32Time;

    /**
     * 邮件发送时间
     */
    private String mailSendTime;

    /**
     * 上清、中债、外汇
     * 表明了应该查询交费记录的月份
     * 1季度的 为4月
     * 2季度的 为7月
     * 3季度的 为10月
     * 4季度的 为第二年1月
     * <p>
     * 中保登：
     * 统一取结束日期所在季度的上一个季度的最后一个月
     *
     */
    private String payMonth;

    /**
     * 账号名
     */
    private String name;

    /**
     * 账号编码
     */
    private String code;

    /**
     * 处理结果
     */
    private String handleResult;

    @JsonIgnore
    private String paymentMethodZZ;

    @JsonIgnore
    private String paymentMethodSQ;

    @JsonIgnore
    private String paymentMethodWH;
}
