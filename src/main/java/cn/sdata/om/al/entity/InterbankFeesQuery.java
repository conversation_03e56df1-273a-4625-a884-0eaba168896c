package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.List;

@Data
public class InterbankFeesQuery {

    private int pageNo;

    /**
     * 每页几条
     */
    private int pageSize;

    /**
     * 开始费用日期
     */
    private String beginCostDate;

    /**
     * 结束费用日期
     */
    private String endCostDate;

    /**
     * 账套名称
     */
    private List<String> productIds;

    //收费机构
    private String feeCollectionAgencies;

    //支付方式
    private String paymentMethod;
    //支付状态
    private String paymentStatus;

    //导入 O32 状态
    private String importO32Status;

    //支付日期
    private String paymentDate;

    private String ocrRecognizeStatus;

    private String mailSendStatus;
}
