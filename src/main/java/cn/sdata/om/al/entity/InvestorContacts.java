package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "investor_contacts")
public class InvestorContacts {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 投资人名称
     */
    @TableField(value = "investor")
    private String investor;

    /**
     * 账套ID
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 发送方式 邮件/深证通
     */
    @TableField(value = "`method`")
    private String method;

    /**
     * 发送内容
     */
    @TableField(value = "`handler`")
    private String handler;

    /**
     * 收件人
     */
    @TableField(value = "recipient")
    private String recipient;

    /**
     * 抄送人
     */
    @TableField(value = "recipient_cc")
    private String recipientCc;

    @TableField(value = "szt_path")
    private String sztPath;
}