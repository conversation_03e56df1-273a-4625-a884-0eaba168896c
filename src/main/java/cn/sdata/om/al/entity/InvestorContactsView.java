package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName(value = "investor_contacts_view")
@NoArgsConstructor
@AllArgsConstructor
public class InvestorContactsView {
    /**
     * 主键ID
     */
    @TableField(value = "id")
    private String id;

    /**
     * 下层数据ID
     */
    @TableField(value = "sub_id")
    private String subId;

    /**
     * 区分三方投资人
     */
    @TableField(value = "type")
    private String type;

}