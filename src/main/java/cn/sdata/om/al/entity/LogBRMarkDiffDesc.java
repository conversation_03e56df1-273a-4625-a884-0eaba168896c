package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class LogBRMarkDiffDesc {

    private String id;
    /**
     * 账套id
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 动作
     */
    private String action;

    /**
     * 备注
     */
    private String remark;

    /**
     * 日志记录id
     */
    @JsonIgnore
    private String logId;

    /**
     * 证券代码
     */
    private String securityCode;

    /**
     * 证券名称
     */
    private String securityName;
}
