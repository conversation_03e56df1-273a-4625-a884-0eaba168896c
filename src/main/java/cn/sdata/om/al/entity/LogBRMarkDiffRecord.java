package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 银行对账日志 - 差异标记日志
 */
@Data
public class LogBRMarkDiffRecord {

    private String id;

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 标记详情
     */
    private List<LogBRMarkDiffDesc> markDesc = new ArrayList<>();

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 结果
     */
    @JsonIgnore
    private String result;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    /**
     * 状态
     */
    private String status;


    /**
     * 数据日期
     */
    private String dataDate;
}
