package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 银行对账日志 - 同步估值日志
 */
@Data
public class LogBRSyncValuationRecord {

    private String id;
    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 结果
     */
    @JsonIgnore
    private String result;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;


    /**
     * 数据日期
     */
    private String dataDate;
}
