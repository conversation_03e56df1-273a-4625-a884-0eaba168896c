package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class LogCCRSendMailRecord {

    private String id;

    private String taskId;

    private String taskName;

    private String type;

    private String status;

    private String beginTime;

    private String endTime;

    private String sendStatus;

    private String createByName;

    private String mailIdStr;

    @JsonIgnore
    private List<String> mailIds;

    @JsonIgnore
    private String params;

    @JsonIgnore
    private String result;

    @JsonIgnore
    private String errorMsg;

    @JsonIgnore
    private String rpaLogId;

    private String dataDate;
}
