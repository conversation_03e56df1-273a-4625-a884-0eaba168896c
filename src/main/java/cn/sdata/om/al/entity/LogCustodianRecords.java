package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "log_custodian_records")
//托管对账结果记录
public class LogCustodianRecords {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 托管行邮件发送状态
     */
    @TableField(value = "custodian_status")
    private String custodianStatus;

    /**
     * 邮件发送时间
     */
    @TableField(value = "mail_send_time")
    private Date mailSendTime;

    /**
     * 操作人
     */
    @TableField(value = "custodian_operator")
    private String custodianOperator;

    /**
     * 邮件id
     */
    @TableField(value = "mail_id")
    private String mailId;
}