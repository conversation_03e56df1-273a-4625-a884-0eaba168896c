package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 银行间费用 下载文件
 */
@Data
public class LogFYBDownloadFileRecord {

    private String id;
    /**
     * 执行时间
     */
    private String beginTime;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 状态
     */
    private String status;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 结果
     */
    @JsonIgnore
    private String result;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    /**
     * 数据日期
     */
    private String dataDate;

    private String endTime;

}
