package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LogFYBGenerateFileRecord {

    private String id;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 账套清单
     */
    @JsonIgnore
    private List<String> productIds = new ArrayList<>();

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 状态
     */
    private String status;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 结果
     */
    @JsonIgnore
    private String result;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    /**
     * 数据日期
     */
    private String dataDate;

    @JsonIgnore
    private String productIdsStr;

    private String productNames;
}
