package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LogFYBO32ConfirmRecord {

    private String id;

    private String beginTime;

    private String endTime;

    private String createByName;

    private String status;

    @JsonIgnore
    private List<String> productIds = new ArrayList<>();

    @JsonIgnore
    private String productIdsStr;

    @JsonIgnore
    private String params;

    @JsonIgnore
    private String errorMsg;

    private String dataDate;

    private String productNames;
}
