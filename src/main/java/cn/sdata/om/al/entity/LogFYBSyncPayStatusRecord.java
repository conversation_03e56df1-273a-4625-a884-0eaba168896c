package cn.sdata.om.al.entity;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LogFYBSyncPayStatusRecord {

    private String id;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建人
     */
    private String createByName;

    /**
     * 账套信息
     */
    private List<JSONObject> productInfos = new ArrayList<>();

    @JsonIgnore
    private String productInfosStr;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    private String dataDate;

    private String status;

}
