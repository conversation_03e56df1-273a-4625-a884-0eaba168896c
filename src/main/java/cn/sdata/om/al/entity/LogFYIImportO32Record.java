package cn.sdata.om.al.entity;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LogFYIImportO32Record {

    private String id;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 执行人
     */
    private String createByName;

    /**
     * 导入o32结果
     */
    @JsonIgnore
    private String importResult;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 账套id
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;


    private String dataDate;

    private String result;

    private String status;

    private List<JSONObject> importResultList = new ArrayList<>();
}
