package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LogFYIO32ConfirmRecord {

    private String id;

    private String beginTime;

    private String createByName;

    private String status;

    @JsonIgnore
    private List<String> productIds = new ArrayList<>();

    @JsonIgnore
    private String params;

    @JsonIgnore
    private String errorMsg;

    private String endTime;

    private String dataDate;

    @JsonIgnore
    private String productIdsStr;

    private String productNames;
}
