package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class LogFYIUpdatePayStatusRecord {

    private String id;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建人
     */
    private String createByName;

    /**
     * 账套信息
     */
    @JsonIgnore
    private String productId;

    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    private String dataDate;

    /**
     * 更新前状态
     */
    private String preUpdateStatus;

    /**
     * 更新后状态
     */
    private String postUpdateStatus;

    private String productName;
}
