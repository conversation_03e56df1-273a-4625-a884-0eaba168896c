package cn.sdata.om.al.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class LogFYIUploadRecord {

    private String id;

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 执行方式
     */
    private String executeType;

    /**
     * 文件路径
     */
    private String fileUrl;


    /**
     * 参数
     */
    @JsonIgnore
    private String params;

    /**
     * 结果
     */
    @JsonIgnore
    private String result;

    /**
     * 错误信息
     */
    @JsonIgnore
    private String errorMsg;

    /**
     * 状态
     */
    private String status;


    /**
     * 数据日期
     */
    private String dataDate;

    private String endTime;
}
