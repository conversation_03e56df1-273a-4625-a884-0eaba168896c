package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "log_mail_send_records")
//披露状态
public class LogMailSendRecords {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 发送方式
     */
    @TableField(value = "send_method")
    private String sendMethod;

    /**
     * 发送内容
     */
    @TableField(value = "send_content")
    private String sendContent;

    /**
     * 发送对象
     */
    @TableField(value = "send_to")
    private String sendTo;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    @TableField(value = "`status`")
    private String status;

    @TableField(value = "mail_id")
    private String mailId;

    /**
     * 通讯录类型
     */
    @TableField(value = "contact_type")
    private String contactType;
}