package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 开基确认单删除记录日志表
 */
@Data
@TableName(value = "log_open_fund_confirmation_delete")
public class LogOpenFundConfirmationDelete {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 确认单ID
     */
    @TableField(value = "confirmation_id")
    private String confirmationId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 账套名称
     */
    @TableField(value = "account_set_name")
    private String accountSetName;

    /**
     * 交易渠道
     */
    @TableField(value = "transaction_channel")
    private String transactionChannel;

    /**
     * 删除时间
     */
    @TableField(value = "delete_time")
    private Date deleteTime;

    /**
     * 操作人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 删除状态
     */
    @TableField(value = "delete_status")
    private String deleteStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
