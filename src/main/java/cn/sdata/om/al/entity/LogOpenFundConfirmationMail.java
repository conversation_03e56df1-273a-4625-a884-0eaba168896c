package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.SendMethod;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 开基确认单邮件发送日志表
 */
@Data
@TableName(value = "log_open_fund_confirmation_mail")
public class LogOpenFundConfirmationMail {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 确认单ID
     */
    @TableField(value = "confirmation_id")
    private String confirmationId;

    /**
     * 邮件发送任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 发送方式
     */
    @TableField(value = "send_method")
    private SendMethod sendMethod;

    /**
     * 邮件发送状态
     */
    @TableField(value = "mail_status")
    private MailStatus mailStatus;

    /**
     * 邮件发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * 操作人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 邮件日志ID
     */
    @TableField(value = "mail_log_id")
    private String mailLogId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
