package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.OperationStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 开基对账单上传日志表
 */
@Data
@TableName(value = "log_open_fund_reconciliation_upload")
public class LogOpenFundReconciliationUpload {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 文件唯一标识
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * 对账单ID
     */
    @TableField(value = "reconciliation_id")
    private String reconciliationId;

    /**
     * 上传时间
     */
    @TableField(value = "upload_time")
    private Date uploadTime;

    /**
     * 操作人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 操作状态
     */
    @TableField(value = "operation_status")
    private OperationStatus operationStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
