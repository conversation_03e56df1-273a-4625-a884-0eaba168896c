package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "log_recon_records")
//对账状态操作记录
public class LogReconRecords {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 对账时间
     */
    @TableField(value = "recon_date")
    private Date reconDate;

    /**
     * 对账状态
     */
    @TableField(value = "recon_status")
    private String reconStatus;

    /**
     * 操作人
     */
    @TableField(value = "recon_operator")
    private String reconOperator;

    /**
     * 操作原因
     */
    @TableField(value = "operate_reason")
    private String operateReason;
}