package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "log_valuation_confirm_records")
//估值表确认状态
public class LogValuationConfirmRecords {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 确认状态
     */
    @TableField(value = "confirm_status")
    private String confirmStatus;

    /**
     * 确认时间
     */
    @TableField(value = "confirm_time")
    private Date confirmTime;

    /**
     * 操作人
     */
    @TableField(value = "confirm_operator")
    private String confirmOperator;

    /**
     * 估值表路径
     */
    @TableField(value = "valuation_table_path")
    private String valuationTablePath;
}