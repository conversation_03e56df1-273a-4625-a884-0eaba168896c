package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "om_mail_contacts_detail")
public class MailContactsDetail {
    @TableField(value = "contacts_id")
    private String contactsId;

    @TableField(value = "split_type")
    private String splitType;

    @TableField(value = "value")
    private String value;
}