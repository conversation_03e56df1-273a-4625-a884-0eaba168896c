package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.TradeMarketEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 市场交易日表
 */
@TableName(value = "market_trade_day")
@Data
public class MarketTradeDay implements Serializable {

    /**
     * 交易日分套类型
     * 不同的市场可以有不同的工作日
     * 数据字典10084，在市场工作日中维护
     * <p>
     * 新增时，默认为00 沪深交易日
     * 01 银行间交易日 02 沪港通交易日 03 深港通交易日
     * {@link TradeMarketEnum}
     */
    @TableField(value = "vc_tradeday_type")
    private String tradeDayType;

    /**
     * 日期
     */
    @TableField(value = "l_date")
    private Integer date;

    /**
     * 是否交易日
     * 1：交易,交收日
     * 2：非交易,交收日
     * 3: 仅交易日
     * <p>
     * 对于境内使用的交易日,只能设置1,2
     * 对于境外使用的交易日,可以设置1,2,3,而且只有当日仅能做交易,不能做交收时可以设置为3.
     */
    @TableField(value = "c_trade_flag")
    private String tradeFlag;

    /**
     * 星期
     * 数据字典10059项
     */
    @TableField(value = "l_week")
    private Integer week;

    /**
     * 虚拟主键
     * 虚拟主键，用于前台内存表增量加载，使用触发器自增长字段，sequence为seq_bondproperty_virtualkey
     */
    @TableField(value = "l_virtual_key")
    private Long virtualKey;

    /**
     * 时间戳
     * 更新时间戳，用于前台内存表增量更新，该字段使用触发器维护
     */
    @TableField(value = "vc_timestamp")
    private String timestamp;

    /**
     * 如果为1, 是新增交易日
     */
    @TableField(value = "c_special")
    private Integer special;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}