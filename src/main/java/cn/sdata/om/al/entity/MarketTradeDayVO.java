package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.TradeMarketEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MarketTradeDayVO {

    /**
     * 交易市场type 00沪深交易日 01银行间交易日 02沪港通交易日 03深港通交易日
     * {@link TradeMarketEnum}
     */
    private String type;

    /**
     * 交易市场name 00沪深交易日 01银行间交易日 02沪港通交易日 03深港通交易日
     * {@link TradeMarketEnum}
     */
    private String name;

    /**
     * T-1交易日
     */
    private String t1;

    /**
     * T-2交易日
     */
    private String t2;

    /**
     * T-3交易日
     */
    private String t3;

    /**
     * 上周最后一个交易日
     */
    private String weekLastTradeDay;

    /**
     * 上个月最后一个交易日
     */
    private String monthLastTradeDay;
}
