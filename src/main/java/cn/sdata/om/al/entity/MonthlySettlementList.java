package cn.sdata.om.al.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class MonthlySettlementList implements Serializable {

    private String id;

    /**
     * 文件夹分类
     */
    private String folderCategory;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 更新日期 即下载时间
     */
    private String updateDate;

    /**
     * 下载状态 0 未下载 1 已下载 2 下载中 3 排队中
     */
    private int downloadStatus;

    /**
     * 文件夹排序
     */
    private int folderOrder;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 发送状态
     */
    private String sendStatus;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 替换的文件名
     */
    private String replaceFileName;

    /**
     * 类型 0 rpa下载 1 手动上传
     */
    private String type;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MonthlySettlementList)) return false;
        MonthlySettlementList that = (MonthlySettlementList) o;
        return folderOrder == that.folderOrder && Objects.equals(folderCategory, that.folderCategory) && Objects.equals(fileName, that.fileName) && Objects.equals(dataDate, that.dataDate) && Objects.equals(folderName, that.folderName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(folderCategory, fileName, dataDate, folderOrder, folderName);
    }
}
