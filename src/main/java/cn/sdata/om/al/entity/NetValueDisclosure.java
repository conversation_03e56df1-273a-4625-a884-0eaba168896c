package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * T0净值披露
 */
@Data
@TableName(value = "net_value_disclosure")
public class NetValueDisclosure implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "account_id")
    private String productId;

    /**
     * 是否二次估值（1表示是，0表示否）
     */
    @TableField(value = "is_revalued")
    private Integer isRevalued;

    /**
     * 二次估值的资产类型
     */
    @TableField(value = "asset_type")
    private String assetType;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 估值表生成状态（1表示已生成，0表示未生成）
     */
    @TableField(value = "valuation_table_generated")
    private Integer valuationTableGenerated;

    /**
     * 托管对账邮件发送状态（无需发送，未发送，发送中，发送成功，发送失败）
     */
    @TableField(value = "custody_reconciliation_email_sent")
    private String custodyReconciliationEmailSent;

    /**
     * 对账状态 (未完成, 已完成, 异常)
     */
    @TableField(value = "reconciliation_status")
    private String reconciliationStatus;

    /**
     * 估值表确认状态（1表示已确认，0表示未确认）
     */
    @TableField(value = "valuation_table_confirmed")
    private Integer valuationTableConfirmed;

    /**
     * 净值披露状态（1表示已披露，0表示未披露）
     */
    @TableField(value = "net_value_disclosed")
    private Integer netValueDisclosed;

    /**
     * 估值表下载状态（未下载，下载中，下载成功，下载失败）
     */
    @TableField(value = "valuation_table_downloaded")
    private String valuationTableDownloaded;

    /**
     * 估值表发送状态（未发送，发送成功，发送失败）
     */
    @TableField(value = "valuation_table_sent")
    private String valuationTableSent;

    /**
     * 投资人报表发送状态（无需发送，未发送，发送成功，发送失败）
     */
    @TableField(value = "investor_report_sent")
    private String investorReportSent;

    /**
     * 三方机构发送状态（无需发送，未发送，发送成功，发送失败）
     */
    @TableField(value = "third_party_sent")
    private String thirdPartySent;

    /**
     * 估值表文件保存路径
     */
    @TableField(value = "valuation_table_path")
    private String valuationTablePath;

    /**
     * 余额表文件保存路径
     */
    @TableField(value = "balance_table_path")
    private String balanceTablePath;

    /**
     * RPA运行状态
     */
    @TableField(value = "rpa_status")
    private String rpaStatus;
    /**
     * RPA二次运行状态
     */
    @TableField(value = "rpa_second_status")
    private String rpaSecondStatus;

    /**
     * 余额表RPA运行状态
     */
    @TableField(value = "balance_rpa_status")
    private String balanceRpaStatus;

    @TableField(value = "dbf_status")
    private String dbfStatus;

    @TableField(value = "dbf_path")
    private String dbfPath;

    @TableField(value = "szt_status")
    private String sztStatus;

    @TableField(value = "manual_recon_confirm")
    private int manualReconConfirm;

    @TableField(exist = false)
    private Date confirmTime;

}