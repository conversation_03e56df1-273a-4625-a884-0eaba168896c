package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.List;

@Data
public class NetValueDisclosureParam {

    private String startDate;
    private String endDate;
    private List<String> group;
    private List<String> fullProductName;
    private List<String> productCode;
    private Integer isElectronicDirectConnection;
    private Integer valuationTableGenerated;
    private String reconciliationStatus;
    private Integer netValueDisclosed;
    private String valuationTime;
    private Integer valuationTableConfirmed;
    private String custodyReconciliationEmailSent;
    private Integer isRevalued;
}
