package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 净值披露T1批次
 */
@Data
@TableName(value = "net_value_disclosure_t1_batch")
public class NetValueDisclosureT1Batch {
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @DateTimeFormat(pattern = "HH:mm:ss")
    @TableField(value = "batch_time")
    private Date batchTime;

    @TableField(value = "product_ids")
    private String productIds;
}