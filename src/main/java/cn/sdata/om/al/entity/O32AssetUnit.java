package cn.sdata.om.al.entity;

import lombok.Data;

@Data
public class O32AssetUnit {

    private String id;

    // 序号
    private int orderNo;
    // 账套名称
    private String productName;
    // 账套编号
    private String productId;
    // O32基金名称
    private String o32FundName;
    // O32 资产单元名称
    private String o32AssetUnitName;
    // O32 资产单元编号
    private String o32AssetUnitCode;

    private String updateByName;

    private String updateTime;
}
