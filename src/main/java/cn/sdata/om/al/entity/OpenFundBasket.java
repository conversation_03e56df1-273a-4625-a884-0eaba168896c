package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "open_fund_basket")
public class OpenFundBasket {
    /**
     * 分拣框ID
     */
    @TableId(value = "box_id", type = IdType.ASSIGN_ID)
    private String boxId;

    /**
     * 处理器
     */
    @TableField(value = "`handler`")
    private String handler;
}