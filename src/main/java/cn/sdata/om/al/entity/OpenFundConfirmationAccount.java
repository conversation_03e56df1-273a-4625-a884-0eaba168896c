package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 开基确认与账套关联表
 */
@Data
@TableName(value = "open_fund_confirmation_account")
public class OpenFundConfirmationAccount {
    /**
     * 文件ID
     */
    private String openFundId;

    /**
     * 识别的账户名称
     */
    private String accountName;

    /**
     * 映射的账套名称
     */
    private String productId;
}