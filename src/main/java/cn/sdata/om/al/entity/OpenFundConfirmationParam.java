package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.MailRuleMatchStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import lombok.Data;

import java.util.List;

@Data
public class OpenFundConfirmationParam {

    //邮件接收时间开始
    private String startReceiveDate;
    //邮件接收时间结束
    private String endReceiveDate;
    //数据日期开始
    private String startDataDate;
    //数据日期结束
    private String endDataDate;
    //交易渠道
    private List<String> transactionChannel;
    //产品ID
    private List<String> productId;
    //业务类型
    private List<String> businessType;
    //邮件匹配状态
    private List<MailRuleMatchStatus> emailRuleMatchStatus;
    //OCR确认状态
    private List<OcrConfirmationStatus> ocrConfirmationStatus;

    private List<MailStatus> emailSentStatus;

}
