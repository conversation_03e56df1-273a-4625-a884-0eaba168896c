package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.MailRuleMatchStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 开基确认单表
 */
@Data
@TableName(value = "open_fund_confirmation_statement")
public class OpenFundConfirmationStatement {
    /**
     * 主键，确认单ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 确认日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 交易渠道
     */
    @TableField(value = "transaction_channel")
    private String transactionChannel;

    /**
     * 账套Id
     */
    @TableField(exist = false)
    private List<String> productIds = new ArrayList<>();

    /**
     * 账户名称
     */
    @TableField(exist = false)
    private List<String> accountNames = new ArrayList<>();

    /**
     * 业务类型
     */
    @TableField(exist = false)
    private List<String> businessTypes = new ArrayList<>();

    /**
     * 邮件ID
     */
    @TableField(value = "email_id")
    private String emailId;

    /**
     * 邮件规则匹配状态，0表示未匹配，1表示已匹配
     */
    @TableField(value = "email_rule_match_status")
    private MailRuleMatchStatus emailRuleMatchStatus = MailRuleMatchStatus.MATCHED;

    /**
     * 邮件接收时间
     */
    @TableField(value = "email_received_time")
    private Date emailReceivedTime;

    /**
     * 附件名称
     */
    @TableField(value = "attachment_name")
    private String attachmentName;

    /**
     * OCR确认状态，0表示未确认，1表示已确认，2表示已修改
     */
    @TableField(value = "ocr_confirmation_status")
    private OcrConfirmationStatus ocrConfirmationStatus = OcrConfirmationStatus.UNCONFIRMED;

    /**
     * 邮件发送状态
     */
    @TableField(value = "email_sent_status")
    private MailStatus emailSentStatus = MailStatus.UNSENT;


    @TableField(value = "file_path")
    private String filePath;

    @TableField(value = "file_id")
    private String fileId;

    @TableField(exist = false)
    private String fileContent;
}