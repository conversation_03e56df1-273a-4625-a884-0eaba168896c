package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 开基字段映射
 */
@Data
@TableName(value = "open_fund_field_mapping")
public class OpenFundFieldMapping {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "`type`")
    private String type;

    @TableField(value = "method_name")
    private String methodName;

    @TableField(value = "`name`")
    private String name;

    @TableField(value = "`value`")
    private String value;

    @TableField(value = "value_class")
    private String valueClass;
}