package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 开基确认与账套关联表
 */
@Data
@TableName(value = "open_fund_reconciliation_account")
public class OpenFundReconciliationAccount {
    /**
     * 文件ID
     */
    @TableField(value = "open_fund_id")
    private String openFundId;

    /**
     * 识别的账户名称
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 映射的账套名称
     */
    @TableField(value = "product_id")
    private String productId;
}