package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.File;
import java.io.Serializable;
import java.util.*;

@Data
@TableName
@NoArgsConstructor
@AllArgsConstructor
public class RemoteFileInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;
    @TableField(value = "serial_number")
    private Integer serialNumber;
    @TableField(value = "file_name")
    private String fileName;
    @TableField(value = "file_path")
    private String filePath;
    @TableField(value = "download_time")
    private Date downloadTime;
    @TableField(value = "deal_time")
    private Date dealTime;
    @TableField(value = "file_size")
    private Long fileSize;
    @TableField(value = "log_Id")
    private String logId;
    @TableField(value = "relative_path")
    private String relativePath;

    /**
     * 表示文件是远程还是本地的 默认:remote:远程 local:本地
     */
    @TableField(exist = false)
    private String location = "remote";

    @TableField(exist = false)
    private Set<String> openFundId = new LinkedHashSet<>();
    @TableField(exist = false)
    private Set<File> openFile = new LinkedHashSet<>();

}
