package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 角色实体类
 *
 * <AUTHOR>
 * @Date 2025/2/8 16:00
 * @Version 1.0
 */
@Data
@TableName(value = "role")
@Accessors(chain = true)
public class Role {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 角色名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 角色所属的用户列表
     */
    @TableField(exist = false)
    private List<User> users = new ArrayList<>();

    /**
     * 角色所属的用户列表Ids
     */
    @TableField(exist = false)
    private List<String> userIds = new ArrayList<>();

    /**
     * 角色所有的菜单
     */
    @TableField(exist = false)
    private List<Menu> menus = new ArrayList<>();

    /**
     * 角色所有的菜单id
     */
    @TableField(exist = false)
    private List<String> menuIds = new ArrayList<>();
}
