package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.RpaExecLogBizSceneEnum;
import cn.sdata.om.al.enums.RpaExecLogBizTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * rpa执行日志表
 *
 */
@TableName(value = "rpa_exec_log")
@Data
public class RpaExecLog implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * rpa业务类型：{@link RpaExecLogBizTypeEnum}
     */
    @TableField(value = "RPA_BIZ_TYPE")
    private String rpaBizType;

    /**
     * 流程ID
     */
    @TableField(value = "FLOW_ID")
    private String flowId;

    /**
     * 执行ID
     */
    @TableField(value = "EXEC_ID")
    private String execId;

    /**
     * 执行时间
     */
    @TableField(value = "EXEC_TIME")
    private String execTime;

    /**
     * 文件路径
     */
    @TableField(value = "FILE_PATH")
    private String filePath;

    /**
     * 文件名称
     */
    @TableField(value = "FILE_NAME")
    private String fileName;

    /**
     * 执行状态（-1：正在执行；0：手动停止；1：执行成功；2：执行超时；3：节点异常）
     */
    @TableField(value = "EXEC_STATE")
    private String execState;

    /**
     * 执行结果
     */
    @TableField(value = "EXEC_RESULT")
    private String execResult;

    /**
     * O32节点状态（执行中/成功/失败）
     */
    @TableField(value = "O32_NODE_STATE")
    private String o32NodeState;

    /**
     * 导入结果
     */
    @TableField(value = "IMPORT_RESULT")
    private String importResult;

    /**
     * 处理结果
     */
    @TableField(value = "PROCESS_RESULT")
    private String processResult;

    /**
     * 执行人
     */
    @TableField(value = "EXECUTOR")
    private String executor;

    /**
     * 开始时间
     */
    @TableField(value = "START_TIME")
    private String startTime;

    /**
     * 完成时间
     */
    @TableField(value = "FINISH_TIME")
    private String finishTime;

    /**
     * 备注
     */
    @TableField(value = "MEMO")
    private String memo;

    /**
     * 扩展信息
     */
    @TableField(value = "EXTEND_INFO")
    private String extendInfo;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    private String createdTime;

    /**
     * 修改时间
     */
    @TableField(value = "UPDATED_TIME")
    private String updatedTime;

    /**
     * 账户类型(1:一般户; 2、买方托管户; 3、卖方托管户)
     */
    @TableField(value = "ACCOUNT_TYPE")
    private Integer accountType;

    /**
     * rpa业务场景：{@link RpaExecLogBizSceneEnum}
     */
    @TableField(value = "RPA_BIZ_SCENE")
    private String rpaBizScene;

    /**
     * 截图
     */
    @TableField(value = "ERROR_IMAGE")
    private String errorImage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}