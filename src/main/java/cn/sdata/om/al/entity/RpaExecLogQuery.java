package cn.sdata.om.al.entity;

import cn.sdata.om.al.enums.RpaExecLogBizSceneEnum;
import cn.sdata.om.al.enums.RpaExecLogBizTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RpaExecLogQuery extends PageParam {

    /**
     * 执行时间-开始时间
     */
    private String execTimeStart;

    /**
     * 执行时间-结束时间
     */
    private String execTimeEnd;

    /**
     * 场景code列表 {@link RpaExecLogBizSceneEnum}
     */
    private List<String> sceneList;

    /**
     * 业务code列表 {@link RpaExecLogBizTypeEnum}
     */
    private List<String> bizList;

    /**
     * 执行状态
     */
    private String execSate;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 1:RPA执行
     * 2:RPA导入O32
     */
    private String type;
}
