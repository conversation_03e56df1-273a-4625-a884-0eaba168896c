package cn.sdata.om.al.entity;

import cn.sdata.om.al.config.SMBConfig;
import com.hierynomus.smbj.SMBClient;
import com.hierynomus.smbj.auth.AuthenticationContext;
import com.hierynomus.smbj.connection.Connection;
import com.hierynomus.smbj.session.Session;
import com.hierynomus.smbj.share.DiskShare;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Data
@Component
@Slf4j
public class SMBClientInfo {

    private SMBClient client;
    private Connection connection;
    private AuthenticationContext ac;
    private Session session;
    private DiskShare share;

    private SMBConfig smbConfig;

    @Autowired
    public void setSmbConfig(SMBConfig smbConfig) {
        this.smbConfig = smbConfig;
    }

    public SMBClientInfo(SMBConfig smbConfig) {
//        init(smbConfig);
    }

    public void reconnect(){
        init(smbConfig);
    }

    private void init(SMBConfig smbConfig){
        log.info("初始化SMBClientInfo相关数据");
        if (smbConfig == null) {
            throw new RuntimeException("初始化异常");
        }
        AuthenticationContext ac = new AuthenticationContext(smbConfig.getUsername(), smbConfig.getPassword().toCharArray(), smbConfig.getDomain());
        try {
            this.client = new SMBClient();
            this.connection = client.connect(smbConfig.getHost());
            this.session = connection.authenticate(ac);
            this.share = (DiskShare) session.connectShare(smbConfig.getBase());
        } catch (Exception e) {
            throw new RuntimeException("远程文件夹初始化异常:", e);
        }
    }


    @SneakyThrows
    public void closeAll(){
        share.close();
        session.close();
        connection.close();
        client.close();
    }
}
