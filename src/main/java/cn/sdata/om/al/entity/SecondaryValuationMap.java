package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "secondary_valuation_map")
public class SecondaryValuationMap {
    @TableField(value = "subject_code")
    private String subjectCode;

    @TableField(value = "valuation_time")
    private String valuationTime;

    @TableField(value = "secondary_name")
    private String secondaryName;
}