package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.*;

/**
 * 每个实例代表一封邮件
 */
@Data
public class SendMailInfo {


    private String subject;
    private List<String> recipient;
    private List<String> cc;
    private String content;
    private Map<String, byte[]> attachment = new LinkedHashMap<>();
    private List<String> attachmentPaths = new ArrayList<>();

    //这封邮件所涉及的账套信息
    private List<String> productIds;
    //用于处理附件以及正文的处理器
    private String handler;
    //此邮件的涉及的数据日期
    private String dataDate;
    //使用的模板
    private String templateId;
    //发送后的日志Id
    private String logId;
    //发送邮件状态
    private String status;
    //通讯录名称
    private String contactName;
    //第三方或者投资人
    private String contactType;

    /**
     * 反向记录邮件id
     */
    private String mailId;

    /**
     * 反向记录邮件发送日志id,关联om_mail_send_log的id
     */
    private String mailSendLogId;

    private int compressAttachmentIs;

    private Map<String, Object> extraData = new HashMap<>();
}
