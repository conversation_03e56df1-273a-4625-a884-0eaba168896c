package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @Date 2025/2/8 10:24
 * @Version 1.0
 */
@Data
@TableName(value = "user")
@Accessors(chain = true)
public class User {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 账户
     */
    @TableField(value = "account")
    private String account;

    /**
     * 用户名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 用户状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 是否为域账号(1:是，0:不是)
     */
    @TableField(value = "ad_flag")
    private Integer adFlag;

    /**
     * 用户所有的角色列表
     */
    @TableField(exist = false)
    private List<Role> roles = new ArrayList<>();

    /**
     * 用户所有的角色列表
     */
    @TableField(exist = false)
    private List<String> roleIds = new ArrayList<>();

    /**
     * 用户所有的菜单列表
     */
    @TableField(exist = false)
    private List<Menu> menus = new ArrayList<>();

}
