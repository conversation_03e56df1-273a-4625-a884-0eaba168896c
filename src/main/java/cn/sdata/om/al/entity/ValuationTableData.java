package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("valuation_table_data")
public class ValuationTableData {

    /**
     * 产品编号
     */
    @TableField("product_id")
    private String productId;

    /**
     * 净值日期
     */
    @TableField("valuation_date")
    private String valuationDate;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 平层单位净值
     */
    @TableField("net_value")
    private String netValue;

    /**
     * 平层产品代码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 平层单位累计净值
     */
    @TableField("sum_net_value")
    private String sumNetValue;

    /**
     * 劣后级产品代码
     */
    @TableField("inferior_product_code")
    private String inferiorProductCode;

    /**
     * 劣后级产品名称
     */
    @TableField("inferior_product_name")
    private String inferiorProductName;

    /**
     * 劣后级净值
     */
    @TableField("inferior_net_value")
    private String inferiorNetValue;

    /**
     * 优先级产品代码
     */
    @TableField("superior_product_code")
    private String superiorProductCode;

    /**
     * 优先级产品名称
     */
    @TableField("superior_product_name")
    private String superiorProductName;

    /**
     * 优先级净值
     */
    @TableField("superior_net_value")
    private String superiorNetValue;

    @TableField("product_type")
    private String productType;




}
