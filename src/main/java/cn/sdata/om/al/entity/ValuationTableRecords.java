package cn.sdata.om.al.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 估值表生成记录表
 */
@Data
@TableName(value = "log_valuation_table_records")
//估值表生成结果记录
public class ValuationTableRecords {
    /**
     * 主键，估值记录ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 估值结果生成时间
     */
    @TableField(value = "result_time")
    private Date resultTime;

    /**
     * 下载估值表时间
     */
    @TableField(value = "download_time")
    private Date downloadTime;

    /**
     * 操作人
     */
    @TableField(value = "`operator`")
    private String operator;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 估值表路径
     */
    @TableField(value = "valuation_table_path")
    private String valuationTablePath;

    @TableField(value = "product_id")
    private String productId;

    @TableField(value = "valuation_date")
    private String valuationDate;

}