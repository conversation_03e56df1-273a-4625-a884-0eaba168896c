package cn.sdata.om.al.entity;

import lombok.Data;

import java.util.Date;

@Data
public class ValuationTableStatus {

    /**
     * 产品编号
     */
    private String productId;

    /**
     * 估值日期
     */
    private String valuationDate;

    /**
     * 生成状态
     */
    private Integer generateStatus;

    /**
     * 对账状态
     */
    private String reconciliationStatus;

    /**
     * 确认状态
     */
    private Integer confirmStatus;

    /**
     * 确认时间
     */
    private Date confirmTime;

}
