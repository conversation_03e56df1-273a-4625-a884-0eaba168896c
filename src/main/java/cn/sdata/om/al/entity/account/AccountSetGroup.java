package cn.sdata.om.al.entity.account;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class AccountSetGroup {

    /**
     * 主键
     */
    private String id;

    /**
     * 账套组名称
     */
    private String name;

    /**
     * 账套id 逗号分隔
     */
    private List<String> accountSetCodes;

    /**
     * 账套名称 逗号分隔
     */
    private String accountSetName;

    @JsonIgnore
    private String accountCodesStr;

    private String createBy;

    private String updateBy;

    private String createTime;

    private String updateTime;

    private String updateByName;

    private String createByName;
}
