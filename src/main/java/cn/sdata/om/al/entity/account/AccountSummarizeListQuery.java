package cn.sdata.om.al.entity.account;

import lombok.Data;

import java.util.List;

@Data
public class AccountSummarizeListQuery {

    /**
     * 产品全称
     */
    private String fullProductName;

    /**
     * 账套id列表
     */
    private List<String> productIds;

    /**
     * 托管行
     */
    private String custodianBank;


    /**
     * 托管行
     */
    private List<String> custodianBanks;

    /**
     * 是否结构化 1 是 0 不是
     */
    private Integer structuredIs;

    /**
     * 是否电子直连 1 是 0 不是
     */
    private Integer electronicDirectConnectionIs;

    /**
     * 三方机构
     */
    private String threePartyOrganization;

    /**
     * 投资人
     */
    private String investor;

    /**
     * 第几页
     */
    private int pageNo;

    /**
     * 每页多少条数据
     */
    private int pageSize;

    /**
     * 三方机构
     */
    private List<String> threePartyOrganizationIds;

    /**
     * 投资人
     */
    private List<String> investorIds;

    /**
     * 套账分类:1 委受托 2 债权投资计划 3 组合产品
     */
    private Integer productCategory;

    /**
     * 估值时间
     */
    private String valuationTime;

    /**
     * 账套状态
     */
    private String productStatus;

    /**
     * 账套组ids
     */
    private List<String> groupIds;

    /**
     * 账套编号
     */
    private List<String> productCodes;
}
