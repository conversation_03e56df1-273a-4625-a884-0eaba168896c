package cn.sdata.om.al.entity.cashClear;

import cn.sdata.om.al.entity.mail.MailInfo;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 资金清算报表实体类
 *
 * <AUTHOR>
 * @Date 2025/4/15 9:35
 * @Version 1.0
 */
@Data
@TableName("cash_clear_report")
@Accessors(chain = true)
public class CashClearReportEntity {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 账套编号
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 产品代码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 账套名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 数据日期 yyyy-mm-dd 表字段类型DATE
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 文件更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "file_update_time")
    private Date fileUpdateTime;

    /**
     * 邮件发送状态
     */
    @TableField(value = "mail_send_status")
    private String mailSendStatus;

    /**
     * 邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "mail_send_time")
    private Date mailSendTime;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 文件操作人
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 远程文件id
     */
    @TableField(value = "remote_file_id")
    private String remoteFileId;

    @TableField(exist = false)
    private List<MailInfo> mailInfos = new ArrayList<>();

    @TableField(exist = false)
    private List<JSONObject> historyMails = new ArrayList<>();

}
