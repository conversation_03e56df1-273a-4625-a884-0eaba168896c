package cn.sdata.om.al.entity.dividendDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 分红信息明细实体类
 *
 * <AUTHOR>
 * @Date 2025/4/27 14:09
 * @Version 1.0
 */
@Data
@TableName("dividend_info_detail")
@Accessors(chain = true)
public class DividendDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 账套id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 账套编号
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 账套名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 操作人
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 步骤
     */
    @TableField(value = "step")
    private String step;

    /**
     * 生成文件批次号
     */
    @TableField(value = "batch_id")
    private String batchId;

    @TableField(exist = false)
    private String fileId;

    @TableField(exist = false)
    private String fileName;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String fileUpdateTime;

    @TableField(exist = false)
    private String mailId;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailSendTime;

    @TableField(exist = false)
    private String mailSendStatus;

    @TableField(exist = false)
    private String mailType;

    @TableField(exist = false)
    private String mailBankId;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailBankSendTime;

    @TableField(exist = false)
    private String mailBankSendStatus;

    @TableField(exist = false)
    private String mailInvestorId;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mailInvestorSendTime;

    @TableField(exist = false)
    private String mailInvestorSendStatus;

}
