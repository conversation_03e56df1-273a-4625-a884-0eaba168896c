package cn.sdata.om.al.entity.dividendDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/29 11:24
 * @Version 1.0
 */
@Data
@TableName("dividend_info_detail_file")
@Accessors(chain = true)
public class DividendDetailFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 详情id
     */
    @TableField(value = "detail_id")
    private String detailId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 本地文件路径
     */
    @TableField(value = "local_file_path")
    private String localFilePath;

    /**
     * 远程文件id
     */
    @TableField(value = "remote_file_id")
    private String remoteFileId;

    /**
     * 文件更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "file_update_time")
    private Date fileUpdateTime;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 0:初始化、1:非初始化
     */
    @TableField(value = "init_status")
    private Integer initStatus = 0;

    /**
     * 0:未选中、1:选中
     */
    @TableField(value = "select_status")
    private Integer selectStatus = 0;

    /**
     * 步骤
     */
    @TableField(value = "step")
    private String step;

    /**
     * 生成文件批次号
     */
    @TableField(value = "batch_id")
    private String batchId;
}
