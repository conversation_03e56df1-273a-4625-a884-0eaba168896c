package cn.sdata.om.al.entity.dividendDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/29 11:26
 * @Version 1.0
 */
@Data
@TableName("dividend_info_detail_file_mail")
@Accessors(chain = true)
public class DividendDetailFileMailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 明细id
     */
    @TableField(value = "detail_id")
    private String detailId;

    /**
     * 明细文件id
     */
    @TableField(value = "detail_file_id")
    private String detailFileId;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 邮件id
     */
    @TableField(value = "mail_id")
    private String mailId;

    /**
     * 邮件发送日志id
     */
    @TableField(value = "mail_send_log_id")
    private String mailSendLogId;

    /**
     * 邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "mail_send_time")
    private Date mailSendTime;

    /**
     * 邮件发送状态
     */
    @TableField(value = "mail_send_status")
    private String mailSendStatus;

    /**
     * 步骤
     */
    @TableField(value = "step")
    private String step;

    /**
     * 发送邮件批次号
     */
    @TableField(value = "batch_id")
    private String batchId;

    /**
     * 邮件类型
     */
    @TableField(value = "mail_type")
    private String mailType;
}
