package cn.sdata.om.al.entity.dividendDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志-调度rap记录-分红明细
 *
 * <AUTHOR>
 * @Date 2025/6/3 13:42
 * @Version 1.0
 */
@Data
@TableName("log_call_rpa_dividend_detail")
@Accessors(chain = true)
public class LogCallRpaDividendDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 关联id
     */
    @TableField(value = "base_corn_log_id")
    private String baseCornLogId;

    /**
     * 关联id
     */
    @TableField(value = "rpa_exec_log_id")
    private String rpaExecLogId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 操作人id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 操作人名称
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 日志所属功能
     */
    @TableField(value = "log_type")
    private String logType = "callRpa";

    /**
     * 账套id拼接串
     */
    @TableField(value = "product_id_str")
    private String productIdStr;

    /**
     * 任务名称
     */
    @TableField(exist = false)
    private String taskName;

    /**
     * 任务开始时间
     */
    @TableField(exist = false)
    private String startTime;

    /**
     * 任务结束时间
     */
    @TableField(exist = false)
    private String finishTime;

    /**
     * 任务返回结果
     */
    @TableField(exist = false)
    private String execResult;
}
