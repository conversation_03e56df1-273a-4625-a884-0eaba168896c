package cn.sdata.om.al.entity.dividendDetail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志-邮件发送记录-分红明细
 *
 * <AUTHOR>
 * @Date 2025/6/3 13:49
 * @Version 1.0
 */
@Data
@TableName("log_mail_send_dividend_detail")
@Accessors(chain = true)
public class LogMailSendDividendDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 发送方式
     */
    @TableField(value = "send_type")
    private String sendType;

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * 数据日期
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 邮件id
     */
    @TableField(value = "mail_id")
    private String mailId;

    /**
     * 邮件发送日志id
     */
    @TableField(value = "mail_send_log_id")
    private String mailSendLogId;

    /**
     * 操作人id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 操作人名称
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 步骤
     */
    @TableField(value = "step")
    private String step;

    /**
     * 日志所属功能
     */
    @TableField(value = "log_type")
    private String logType = "mailSend";
}
