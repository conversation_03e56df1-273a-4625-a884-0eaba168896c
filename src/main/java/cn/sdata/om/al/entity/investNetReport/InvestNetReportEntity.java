package cn.sdata.om.al.entity.investNetReport;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 投连净值播报主表实体类
 *
 * <AUTHOR>
 * @Date 2025/3/19 14:54
 * @Version 1.0
 */
@Data
@TableName("invest_net_report")
@Accessors(chain = true)
public class InvestNetReportEntity {

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 数据日期 yyyy-mm-dd 表字段类型DATE
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 文件修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "file_update_time")
    private Date fileUpdateTime;

    /**
     * 下载状态
     * 0:未下载
     * 1:下载成功
     * 2:下载失败
     * 3:下载中
     */
    @TableField(value = "download_status")
    private Integer downloadStatus;

    /**
     * 净值播报邮件发送状态
     */
    @TableField(value = "net_report_send_status")
    private String netReportSendStatus;

    /**
     * 净值播报邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "net_report_send_time")
    private Date netReportSendTime;

    /**
     * 净值邮件发送状态
     */
    @TableField(value = "net_value_send_status")
    private String netValueSendStatus;

    /**
     * 净值邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "net_value_send_time")
    private Date netValueSendTime;

    /**
     * 文件来源
     * 导出 TA-YYYYMMDD: RPA
     * UL valuation_YYYYMMDD: 系统
     * index reportYYYYMMDD: 手工
     * 净值播报-YYYYMMDD: 系统
     */
    @TableField(value = "source")
    private String source;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 文件类型编号
     * 1:index reportYYYYMMDD
     * 2:导出 TA-YYYYMMDD
     * 3:UL valuation_YYYYMMDD
     * 4:净值播报-YYYYMMDD
     */
    @TableField(value = "file_type_no")
    private Integer fileTypeNo;

    /**
     * 文件本地路径
     */
    @TableField(value = "local_file_path")
    private String localFilePath;

    /**
     * 文件操作人
     */
    @TableField(value = "user_id")
    private String userId;
}
