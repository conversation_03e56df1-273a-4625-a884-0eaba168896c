package cn.sdata.om.al.entity.investNetReport;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 寿险投连估值表下载记录数据
 *
 * <AUTHOR>
 * @Date 2025/3/17 14:43
 * @Version 1.0
 */
@Data
@TableName(value = "life_insurance_valuation_table_records")
@Accessors(chain = true)
public class LifeInsuranceValuationTableRecords {
    /**
     * 主键，估值记录ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 下载估值表时间
     */
    @TableField(value = "download_time")
    private Date downloadTime;

    /**
     * 操作人
     */
    @TableField(value = "`operator`")
    private String operator;

    /**
     * 估值表远程路径
     */
    @TableField(value = "remote_file_path")
    private String remoteFilePath;

    /**
     * 估值表本地路径
     */
    @TableField(value = "local_file_path")
    private String localFilePath;

    /**
     * 套账编号
     */
    @TableField(value = "account_set_code")
    private String accountSetCode;

    /**
     * 估值日期
     */
    @TableField(value = "valuation_date")
    private String valuationDate;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;
}
