package cn.sdata.om.al.entity.investNetReport;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 净值播报映射表
 *
 * <AUTHOR>
 * @Date 2025/3/10 13:14
 * @Version 1.0
 */
@Data
@TableName("invest_net_report_mapping")
public class MappingEntity {

    /**
     * daily summary名称
     */
    @TableField(value = "daily_summary_name")
    public String dailySummaryName;

    /**
     * 险种代码
     */
    @TableField(value = "insurance_type_code")
    public String insuranceTypeCode;

    /**
     * 净值播报名称
     */
    @TableField(value = "net_report_name")
    public String netReportName;

    /**
     * 账套编号
     */
    @TableField(value = "account_set_code")
    public String accountSetCode;
}
