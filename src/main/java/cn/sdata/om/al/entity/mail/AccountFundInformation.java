package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AccountFundInformation extends BaseEntity {

    /**
     * 账套id
     */
    private String productId;

    /**
     * 账套名称
     */
    private String productFullName;

    /**
     * 渠道类型
     */
    private int channelType;

    /**
     * 管理者
     */
    private String administrator;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 邮件后缀
     */
    private String emailSuffix;
}
