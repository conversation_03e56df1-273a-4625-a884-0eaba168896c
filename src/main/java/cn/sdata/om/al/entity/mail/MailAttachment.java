package cn.sdata.om.al.entity.mail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.InputStream;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailAttachment extends Attachment {

    private String mailId;

    /**
     * 文件流
     */
    @JsonIgnore
    private InputStream inputStream;

    /**
     * 文件内容
     */
    @JsonIgnore
    private byte[] content;

    /**
     * 0 发送邮件附件 1 接收邮件附件
     */
    @JsonIgnore
    private int type;
}
