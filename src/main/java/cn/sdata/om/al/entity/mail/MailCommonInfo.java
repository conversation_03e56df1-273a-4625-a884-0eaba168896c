package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailCommonInfo extends BaseEntity {

    /**
     * 产品全称
     */
    private String fullProductName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 估值时间
     */
    private String valuationTime;

    /**
     * 是否结构化 1 是 0 不是
     */
    private int structuredIs;

    /**
     * 是否电子直连 1 是 0 不是
     */
    private int electronicDirectConnectionIs;

    /**
     * 是否为货币
     */
    private int currencyIs;

    /**
     * 托管行
     */
    private String custodianBank;

    /**
     * 二次估值证券类型
     */
    private String secondaryValuationSecurityType;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账号
     */
    private String accountNumberDetail;

    /**
     * 开户行
     */
    private String openingBank;

    /**
     * 大额支付号
     */
    private String largePaymentNumber;

    /**
     * 是否自动扣收 1 自动 2 手工
     */
    private int autoDeductionIs;

    /**
     * 中债
     */
    private String centralDebtAccountNumber;


    /**
     * 外汇交易中心
     */
    private String foreignExchangeCenterMemberCode;

    /**
     * 上清(持久人账号)
     */
    private String clearingHouseHolderAccount;

    /**
     * 持久人账号
     */
    private String holderAccountNumber;

    /**
     * 持久人账户全称
     */
    private String holderAccountFullName;

    /**
     * 净值预警阈值
     */
    private String netAssetValueWarningThreshold;

    /**
     * 三方机构
     */
    private String threePartyOrganization;

    /**
     * 投资人
     */
    private String investor;

    /**
     * 三方机构
     */
    private List<String> threePartyOrganizationIds;

    /**
     * 投资人
     */
    private List<String> investorIds;

    /**
     * WIND 代码
     */
    private String windCode;

    /**
     * 产品状态
     */
    private Integer productStatus;

    /**
     * 成立日期
     */
    private String establishmentDate;

    /**
     * 所属组id
     */
    private String accountSetGroupId;

    /**
     * 账套类型
     */
    private Integer productCategory;

    /**
     * 账套组名称
     */
    private String groupName;

    /**
     * 上清支付方式
     */
    private String paymentMethodSQ;

    /**
     * 中债支付方式
     */
    private String paymentMethodZZ;

    /**
     * 外汇支付方式
     */
    private String paymentMethodWH;

    private String warnValue;

    private String closeValue;

    /**
     * 新增标识 1 是新增 0 是修改
     */
    private int addFlag;
}
