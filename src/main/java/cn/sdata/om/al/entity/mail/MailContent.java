package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailContent extends BaseEntity {

    /**
     * 邮件在文件夹中的编号
     */
    private Integer messageNumber;
    /**
     * 主题
     */
    private String subject;

    /**
     * 发送人
     */
    private MailUser mailFrom;

    /**
     * 发送人字符串
     */
    @JsonIgnore
    private String mailFromStr;

    /**
     * 接收人
     */
    private List<MailUser> receive;

    /**
     * 接收人字符串
     */
    @JsonIgnore
    private String receiveStr;

    /**
     * 正文类型
     */
    private String contentType;

    /**
     * 正文
     */
    private String content;

    /**
     * 附件列表
     */
    private List<MailAttachment> attachments;

    /**
     * 附件列表字符串
     */
    @JsonIgnore
    private String attachmentStr;

    /**
     * 接收时间
     */
    private String receivedDate;


    /**
     * 接收时间
     */
    private Date receivedTime;

    /**
     * 发送时间
     */
    private String sentDate;

    /**
     * 分拣筐ID
     */
    private String boxId;

    /**
     * 邮件待分拣的状态
     * <p>已分拣:-1(主要是针对上线前的历史数据,目前来看好像没啥用)</p>
     * <p>新(0):没进行邮件匹配</p>
     * <p>未(1):进行了匹配 但未命中规则</p>
     * <p>多(2):进行了匹配 命中多条规则</p>
     * <p>异(3):邮件进入分拣筐,逻辑处理异常被退回到待分拣筐中</p>
     * <p>退(4):分拣错了 退回到待分拣筐中</p>
     */
    private int pickStatus;

}
