package cn.sdata.om.al.entity.mail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class MailFilterRuleDTO {

    private String id;
    /**
     * 目标（receiver 接收人/ mailFrom 发送人 / subject 主题 / content 内容 / attachmentName 附件名称）
     */
    private String target;

    /**
     * 类型（include / exclude）
     */
    private String type;

    /**
     * 条件（AND / OR）
     */
    private String condition;

    /**
     * 规则内容
     */
    private List<String> contentList;


    @JsonIgnore
    private String contentListStr;
}
