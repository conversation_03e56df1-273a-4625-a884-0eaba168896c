package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 邮件信息
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class MailInfo extends BaseEntity {

    /**
     * 标题
     */
    private String title;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 抄送人
     */
    private String ccTo;

    /**
     * 附件文件地址列表
     */
    private List<String> attachmentUrls;

    /**
     * 正文文字
     */
    private String content;

    /**
     * 关联的模板id
     */
    private String templateId;

    /**
     * 关联的通讯录id
     */
    private String contactsId;

    /**
     * 发送状态
     */
    private int sendStatus;

    /**
     * 通讯录类型
     */
    private String contactsType;

    /**
     * 通讯录分类
     */
    private String contactsCategory;
}
