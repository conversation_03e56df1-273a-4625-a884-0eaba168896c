package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailLog extends BaseEntity {

    /**
     * 邮件id
     */
    private String mailId;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 抄送人
     */
    private String ccTo;

    /**
     * 发送状态
     */
    private int sendStatus;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private int taskStatus;

    /**
     * 错误信息
     */
    private String errorMsg;

}
