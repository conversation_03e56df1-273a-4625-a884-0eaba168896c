package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MailPickRule extends BaseEntity {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 执行类型 0 满足所有条件 1 满足任意条件
     */
    private int executeType;

    /**
     * 执行条件
     */
    private List<MailPickRuleCondition> executeConditions;

    /**
     * 所属邮件筐
     */
    private String pickBasket;

    /**
     * 附件密码
     */
    private String attachmentPassword;

    /**
     * 规则表达式
     */
    private String ruleExpression;
}
