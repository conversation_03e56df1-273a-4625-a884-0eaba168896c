package cn.sdata.om.al.entity.mail;

import cn.sdata.om.al.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邮件模板
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MailTemplate extends BaseEntity {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 正文文字
     */
    private String content;

    /**
     * 模板扩展字段
     */
    private MailTemplateExt mailTemplateExt;


}
