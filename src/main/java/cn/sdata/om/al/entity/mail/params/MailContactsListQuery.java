package cn.sdata.om.al.entity.mail.params;

import lombok.Data;

import java.util.List;

@Data
public class MailContactsListQuery {

    /**
     * 选择的任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 账套名称
     */
    private String accountSetName;


    /**
     * 账套id 多选
     */
    private List<String> accountSetIds;

    /**
     * 邮件地址
     */
    private String address;

    /**
     * 第几页
     */
    private int pageNo;

    /**
     * 每页记录数
     */
    private int pageSize;
}
