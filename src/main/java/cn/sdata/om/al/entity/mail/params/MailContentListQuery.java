package cn.sdata.om.al.entity.mail.params;

import lombok.Data;

@Data
public class MailContentListQuery {

    /**
     * 起始发送时间
     */
    private String beginSendDate;

    /**
     * 结束发送时间
     */
    private String endSendDate;

    /**
     * 标题
     */
    private String title;

    /**
     * 收件人
     */
    private String receiver;

    /**
     * 发件人
     */
    private String sender;

    /**
     * 正文
     */
    private String content;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 分拣筐类型 为null或者"" 时代表待分拣
     */
    private String pickBasket;

    /**
     * 起始收件日期
     */
    private String beginReceiveDate;

    /**
     * 结束收件日期
     */
    private String endReceiveDate;

    /**
     * 类型
     */
    private Integer type;

}
