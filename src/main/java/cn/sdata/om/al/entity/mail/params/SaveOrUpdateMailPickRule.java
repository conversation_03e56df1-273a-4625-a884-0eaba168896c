package cn.sdata.om.al.entity.mail.params;

import cn.sdata.om.al.entity.mail.MailFilterRuleDTO;
import lombok.Data;

import java.util.List;

@Data
public class SaveOrUpdateMailPickRule {

    private String id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 执行类型 0 满足所有条件 1 满足任意条件
     */
    private String executeType;

    /**
     * 执行条件
     */
    private List<MailFilterRuleDTO> executeConditions;

    /**
     * 所属邮件筐
     */
    private String pickBasket;

    /**
     * 附件密码
     */
    private String attachmentPassword;

    /**
     * 表达式
     */
    private String ruleExpression;

    private String createBy;

    private String updateBy;
}
