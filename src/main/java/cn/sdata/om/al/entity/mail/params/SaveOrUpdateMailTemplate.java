package cn.sdata.om.al.entity.mail.params;

import cn.sdata.om.al.entity.mail.MailTemplateExt;
import lombok.Data;

@Data
public class SaveOrUpdateMailTemplate {

    private String id;

    /**
     * 手动模板id
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 正文文字
     */
    private String content;


    /**
     * 模板扩展字段
     */
    private MailTemplateExt mailTemplateExt;

    /**
     * 创建人
     */
    private String createByName;

    /**
     * 更新人
     */
    private String updateByName;

    /**
     * 模板扩展字段 json字符串
     */
    private String mailTemplateExtStr;

    /**
     * 模板类型
     */
    private String templateType;
}
