package cn.sdata.om.al.entity.mail.vo;

import lombok.Data;


@Data
public class MailContactsVo {

    private String id;

    /**
     * 绑定任务
     */
    private String taskId;

    /**
     * 关联模板id
     */
    private String templateId;

    /**
     * 分类字段 (托管行 - CUSTODIAN_BANK、账套名称 - ACCOUNT_SET、投资人 - INVESTOR、三方 - THREE_PARTY_ORGAN、寿险 - LIFE_INSURANCE)
     */
    private String type;

    /**
     * 收件人类型 托管清算 - CUSTODIAN_LIQUIDATION、托管核算 - CUSTODIAN_CALCULATE
     */
    private String category;

    /**
     * 分类字段下具体数据
     */
    private String typeValues;

    /**
     * 无附件时是否发送 0 不勾 1 勾
     */
    private int withoutAttachmentIsSend;

    /**
     * 更新人
     */
    private String updateByName;

    /**
     * 创建人
     */
    private String createByName;

    private String handler;

    private Integer immediateSend;
}
