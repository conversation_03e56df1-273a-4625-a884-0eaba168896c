package cn.sdata.om.al.entity.mail.vo;

import cn.sdata.om.al.entity.mail.MailAttachment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class MailDetailVo {

    /**
     * 文件标题
     */
    private String title;

    private String mailId;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 抄送人
     */
    private String ccTo;

    /**
     * 附件文件地址列表
     */
    private List<MailAttachment> mailAttachments;

    /**
     * 正文文字
     */
    private String content;

    /**
     * 附件字符串
     */
    @JsonIgnore
    private String mailAttachmentsStr;
}
