package cn.sdata.om.al.entity.mail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MailLogListVo {

    private String id;

    /**
     * 邮件id
     */
    private String mailId;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 发送人
     */
    private String sender;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 抄送人
     */
    private String ccTo;
}
