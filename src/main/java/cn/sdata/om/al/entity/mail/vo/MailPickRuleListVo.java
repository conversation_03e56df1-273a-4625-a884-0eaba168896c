package cn.sdata.om.al.entity.mail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MailPickRuleListVo {

    private String id;

    private String ruleName;

    private String pickBasket;

    private String pickBasketName;

    private String updateByName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
