package cn.sdata.om.al.entity.mail.vo;

import cn.sdata.om.al.entity.mail.MailTemplateExt;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class MailTemplateDetailVo {

    private String id;

    /**
     * 手动模板id
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 正文文字
     */
    private String content;


    /**
     * 模板扩展字段
     */
    private MailTemplateExt mailTemplateExt;

    /**
     * 模板扩展字段
     */
    @JsonIgnore
    private String mailTemplateExtStr;

    /**
     * 模板类型
     */
    private String templateType;
}
