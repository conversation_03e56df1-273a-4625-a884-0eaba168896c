package cn.sdata.om.al.entity.mail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

@Data
public class MailTemplateListVo {

    /**
     * 模板id
     */
    private String id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 更新人id
     */
    private String updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 关联任务数量
     */
    private int bindTaskCount;

    /**
     * 扩展字段
     */
    @JsonIgnore
    private String extInfoStr;


    /**
     * 模板类型
     */
    private String templateType;
}
