package cn.sdata.om.al.entity.monthlyData;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志-文件操作记录-月度
 *
 * <AUTHOR>
 * @Date 2025/5/29 19:15
 * @Version 1.0
 */
@Data
@TableName("log_file_oper_monthly_data")
@Accessors(chain = true)
public class LogFileOperMonthlyDataEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 来源方式：手工上传、下载到本地、下载单文件
     */
    @TableField(value = "source")
    private String source;

    /**
     * 文件类型编号
     */
    @TableField(value = "file_type_no")
    private Integer fileTypeNo;

    /**
     * 数据日期，表字段类型为Date
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 操作人id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 操作人名称
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 步骤
     */
    @TableField(value = "step")
    private String step;

    /**
     * 日志所属功能
     */
    @TableField(value = "log_type")
    private String logType = "fileOper";

    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;
}
