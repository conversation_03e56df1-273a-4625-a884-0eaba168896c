package cn.sdata.om.al.entity.monthlyData;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 月度数据实体类
 *
 * <AUTHOR>
 * @Date 2025/4/7 10:47
 * @Version 1.0
 */
@Data
@TableName("monthly_data")
@Accessors(chain = true)
public class MonthlyDataEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务类型
     * ADD_VALUE_LEDGER
     * CLAIM_INVEST_PLAN
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 数据日期 YYYY-MM
     */
    @TableField(value = "data_date")
    private String dataDate;

    /**
     * rpa-开始日期
     */
    @TableField(value = "start_date")
    private String startDate;

    /**
     * rpa-结束日期
     */
    @TableField(value = "end_date")
    private String endDate;

    /**
     * 数据状态
     * R
     * WR
     */
    @TableField(value = "data_status")
    private String dataStatus;

    /**
     * 下载状态
     * 0:未下载
     * 1:下载成功
     * 2:下载失败
     * 3:下载中
     */
    @TableField(value = "download_status")
    private Integer downloadStatus;

    /**
     * 邮件发送状态（无需发送，未发送，发送中，发送成功，发送失败）
     */
    @TableField(value = "mail_send_status")
    private String mailSendStatus;

    /**
     * 文件更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "file_update_time")
    private Date fileUpdateTime;

    /**
     * 邮件发送时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "mail_send_time")
    private Date mailSendTime;

    /**
     * 数据创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 数据修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 文件类型编号
     * 1:金融商品转让台账表-组合
     * 2:多账套科目发生及余额表-组合
     * 3:多账套科目发生及余额表-债权计划
     * 4:增值税相关报表模块-产品汇总
     * 5:债权投资计划净值-YYYYMMDD
     */
    @TableField(value = "file_type_no")
    private Integer fileTypeNo;

    /**
     * 文件本地路径
     */
    @TableField(value = "local_file_path")
    private String localFilePath;

    /**
     * 文件操作人
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 文件处理的步骤
     */
    @TableField(value = "step")
    private String step;

    @TableField(exist = false)
    private String mailId;
}
