package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum MailContactsType {

    CUSTODIAN_BANK(List.of(MailSplitType.CUSTODIAN_BANK, MailSplitType.ACCOUNT_SET)),
    INVESTOR(List.of(MailSplitType.INVESTOR, MailSplitType.ACCOUNT_SET)),
    THREE_PARTY_ORGAN(List.of(MailSplitType.THREE_PARTY_ORGAN)),
    OTHER(List.of(MailSplitType.OTHER));

    private final List<MailSplitType> mailSplitTypes;

}
