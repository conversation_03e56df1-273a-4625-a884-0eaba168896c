package cn.sdata.om.al.enums;

import lombok.Getter;

@Getter
public enum MailContentHandler {

    EXCEL_VALUATION_TABLE("excelValuationTableHandler", "Excel估值表发送"),
    DBF_VALUATION_TABLE("dBFValuationTableHandler", "DBF估值表发送"),
    MARKET_PRICE("marketPriceHandler", "行情表"),
    NET_VALUE("netValueHandler", "净值发送"),
    THIRD_NET_VALUE("thirdNetValueHandler", "第三方净值发送"),
    CUSTODIAN_BANK_VALUATION("custodianBankValuationHandler", "托管行估值表发送"),
    DEFAULT_HANDLER("defaultHandler", "默认处理器"),
    PORTFOLIO_NET_VALUE_WARNING("portfolioNetValueWarningHandler", "投资人报表发送"),
    PORTFOLIO_NET_VALUE_FLUCTUATION("portfolioNetValueFluctuationHandler", "净值波动邮件"),
    PORTFOLIO_NET_VALUE_RETRACEMENT("portfolioNetValueFluctuationHandler", "净值回撤邮件");

    private final String handler;
    private final String value;


    MailContentHandler(String handler, String value) {
        this.handler = handler;
        this.value = value;
    }
}
