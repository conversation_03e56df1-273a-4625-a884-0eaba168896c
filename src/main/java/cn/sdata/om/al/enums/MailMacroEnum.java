package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MailMacroEnum {

    PRODUCT_CODE("#产品代码#", "产品代码"),
    PRODUCT_NAME("#产品名称#", "产品名称"),
    DATE("#操作日期#", "操作日期"),
    DATA_DATE("#数据日期#", "数据日期"),
    WARN("#预警值#", "预警值"),
    CLOSE("#平仓线/罚没线#", "平仓线/罚没线"),
    NET_VALUE("#单位净值#", "单位净值"),
    PRODUCT_NET_VALUE("#产品代码+净值#", "产品代码+净值");

    private final String replace;
    private final String simpleReplace;

}
