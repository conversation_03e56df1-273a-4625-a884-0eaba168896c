package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MailStatus {
    /**
     * 无需发送
     */
    NONE("无需发送"),
    /**
     * 未发送
     */
    UNSENT("未发送"),
    /**
     * 发送中
     */
    SENDING("发送中"),
    /**
     * 发送成功
     */
    SUCCESS("发送成功"),
    /**
     * 发送失败
     */
    FAILED("发送失败");

    private final String displayName;
}
