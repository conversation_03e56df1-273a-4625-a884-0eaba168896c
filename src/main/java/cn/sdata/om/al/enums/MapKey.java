package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MapKey {
    INVESTOR_PARAM_INVESTORS("investor"),
    INVESTOR_PARAM_PRODUCTS("productId"),
    INVESTOR_PARAM_METHODS("method"),
    CUSTODIAN_PARAM_BANK_NAME("bankName"),
    CUSTODIAN_PARAM_PRODUCTS("productId"),
    CUSTODIAN_PARAM_ROLE("custodianRole");
    private final String frontValue;
}
