package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 月度数据文件枚举
 *
 * <AUTHOR>
 * @Date 2025/4/7 11:39
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum MonthlyDataFileEnum {

    JRZRTZ_ZH("增值税台账", "金融商品转让台账表-组合-YYYYMMDD", 1, "ADD_VALUE_TAX_LEDGER"),

    DZTKMYE_ZH("增值税台账", "多账套科目发生及余额表-组合-YYYYMMDD", 2, "ADD_VALUE_TAX_LEDGER"),

    DZTKMYE_ZQJH("增值税台账", "多账套科目发生及余额表-债权计划-YYYYMMDD", 3, "ADD_VALUE_TAX_LEDGER"),

    ZZSBB_CPHZ("增值税台账", "增值税相关报表-产品汇总-YYYYMMDD", 4, "ADD_VALUE_TAX_LEDGER"),

    ZQJHJZ("债权投资计划", "债权投资计划净值YYYYMMDD", 5, "CLAIM_INVEST_PLAN");

    private final String taskName;

    private final String fileName;

    private final Integer typeNo;

    private final String taskType;
}
