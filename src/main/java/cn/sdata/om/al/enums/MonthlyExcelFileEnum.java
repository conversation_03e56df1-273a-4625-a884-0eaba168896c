package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/3/6 10:37
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum MonthlyExcelFileEnum {

    /**
     * 基金理财产品
     */
    FUND("I39", 3, "证券基本信息", "基金理财产品"),

    /**
     * 信托理财产品
     */
    TRUST("I39", 3, "证券基本信息", "信托理财产品"),

    /**
     * 保险理财产品
     */
    INSURANCE("I39", 3, "证券基本信息", "保险理财产品"),

    /**
     * 银行理财产品
     */
    BANK("I39", 3, "证券基本信息", "银行理财产品"),

    /**
     * 债券-金融债
     */
    FINANCIALBONDS("I39", 3, "证券基本信息", "金融债"),

    /**
     * 现金流量预测
     */
    CASHFLOWFORECAST("I39", 9, "现金流量预测", "多账套科目发生及余额表-YYYY年MM月管理费"),

    /**
     * 现金流量预测2
     */
    CASHFLOWFORECAST2("I39", 9, "现金流量预测", "多账套科目发生及余额表-YYYY年MM月管理费(2)");

    private final String type;

    private final int order;

    private final String parentDirName;

    private final String fileName;

}
