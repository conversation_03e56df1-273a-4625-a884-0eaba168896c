package cn.sdata.om.al.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 净值播报-默认数据中文件名称，类型，来源 枚举
 *
 * <AUTHOR>
 * @Date 2025/3/20 11:13
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum NetReportFileEnum {

    INDEX("index reportYYYYMMDD", 1, "MANUAL"),

    TA("导出 TA-YYYYMMDD", 2, "SYSTEM"),

    UL("UL valuation_YYYYMMDD", 3, "SYSTEM"),

    REPORT("净值播报-YYYYMMDD", 4, "SYSTEM"),

    SHARECHANGE("份额变动表", 5, "SYSTEM");

    private final String name;

    private final Integer typeNo;

    private final String source;
}
