package cn.sdata.om.al.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * rpa执行日志业务场景枚举
 */
@Getter
@AllArgsConstructor
public enum RpaExecLogBizSceneEnum {

    ZJGL_GGT("1", "资金管理-港股通风控金计算"),

    JSGL_KJ("2", "结算管理-开基交易确认"),

    JCSJ_YHJ("3", "基础业务数据-银行间结算公司数据"),

    ADJUST_FILE("4", "买方托管户流水处理-O32系统资金调整记录"),

    ZJGL_YEJLS("5", "资金管理-余额及流水管理");

    /**
     * code
     */
    private final String code;

    /**
     * desc
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return 枚举
     */
    public static RpaExecLogBizSceneEnum getByCode(String code) {
        for (RpaExecLogBizSceneEnum value : values()) {
            if (StrUtil.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }
}
