package cn.sdata.om.al.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * rpa执行日志业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum RpaExecLogBizTypeEnum {

    KJ_HAVE_O32("1", "结算管理-开基交易确认-有O32匹配确认记录"),

    KJ_NO_O32("2", "结算管理-开基交易确认-无O32匹配确认记录"),

    KJ_IMF_HLZT("3", "结算管理-开基交易确认-货币基金红利转投"),

    RISK_FUND_COMPUTE("4", "资金管理-港股通风控金计算"),

    INTERBANK_SQ("5", "基础业务数据-银行间结算公司数据-同步上清数据"),

    INTERBANK_ZZ("6", "基础业务数据-银行间结算公司数据-同步中债数据"),

    ADJUST_FILE("7","买方托管户流水处理-O32系统资金调整记录-导入O32");

    /**
     * code
     */
    private final String code;

    /**
     * desc
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return 枚举
     */
    public static RpaExecLogBizTypeEnum getByCode(String code) {
        for (RpaExecLogBizTypeEnum value : values()) {
            if (StrUtil.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }
}
