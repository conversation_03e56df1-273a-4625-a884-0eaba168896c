package cn.sdata.om.al.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * <p>
 * rpa执行状态枚举
 */
@Getter
@AllArgsConstructor
public enum RpaExecStateEnum {

    EXECUTE("EXECUTE", "-1", "正在执行"),

    STOP("STOP", "0", "手动停止"),

    SUCCESS("SUCCESS", "1", "执行成功"),

    TIMEOUT("TIMEOUT", "2", "执行超时"),

    EXCEPTION("EXCEPTION", "3", "节点异常");

    private final String code;
    private final String value;
    private final String name;

    public static RpaExecStateEnum getByCode(String code) {
        for (RpaExecStateEnum value : values()) {
            if (StrUtil.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }

    public static RpaExecStateEnum getByValue(String value) {
        for (RpaExecStateEnum item : values()) {
            if (StrUtil.equals(value, item.value)) {
                return item;
            }
        }
        return null;
    }

}
