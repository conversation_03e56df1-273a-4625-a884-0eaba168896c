package cn.sdata.om.al.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * <p>
 * rpa接口枚举
 */
@Getter
@AllArgsConstructor
public enum RpaInterfaceEnum {

    RPA_01("RPA_01", "TFlowDM", "GetFlowIDByFullPath", "获取流程ID"),

    RPA_02("RPA_02", "TFlowDM", "StartFlow", "执行流程"),

    RPA_03("RPA_03", "TUserDM", "GetFlowExecedState", "流程执行状态"),

    RPA_04("RPA_04", "TFlowDM", "GetBatchRPAChatParam", "获取流程结果"),

    RPA_05("RPA_05", "TFlowDM", "GetFlowScreenshotInfo", "获取流程截图信息"),

    RPA_06("RPA_06", "TFlowDM", "GetFlowScreenshot", "获取流程截图");

    private final String code;
    /**
     * 模块名称
     */
    private final String model;
    /**
     * 方法名称
     */
    private final String method;
    /**
     * 描述
     */
    private final String desc;

    public static RpaInterfaceEnum getByCode(String code) {
        for (RpaInterfaceEnum value : values()) {
            if (StrUtil.equals(code, value.code)) {
                return value;
            }
        }
        throw new RuntimeException("没有找到枚举项或code为空,请检查...");
    }
}
