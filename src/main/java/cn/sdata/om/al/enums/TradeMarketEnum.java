package cn.sdata.om.al.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * <p>
 * 交易市场枚举
 */
@Getter
@AllArgsConstructor
public enum TradeMarketEnum {

    HS("00", "沪深交易日", ""),

    YHJ("01", "银行间交易日", ""),

    HG("02", "沪港通交易日", "港股通（沪）"),

    SG("03", "深港通交易日", "港股通（深）");

    final String code;
    final String name;
    final String alias;

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return 枚举
     */
    public static TradeMarketEnum getByCode(String code) {
        for (TradeMarketEnum value : values()) {
            if (StrUtil.equals(code, value.code)) {
                return value;
            }
        }
        return null;
    }
}
