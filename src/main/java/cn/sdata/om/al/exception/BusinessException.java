package cn.sdata.om.al.exception;

import cn.sdata.om.al.common.res.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException {

    private final int code;

    private final String msg;

    public BusinessException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    private BusinessException(String message) {
        this(ResultCode.FAILURE.getCode(), message);
    }

    private BusinessException(Throwable t) {
        super(t);
        this.code = ResultCode.FAILURE.getCode();
        this.msg = t.getMessage();
    }

    public static void throwException(String message) {
        throw new BusinessException(message);
    }

    public static void throwException(int code, String message) {
        throw new BusinessException(code, message);
    }

    public static BusinessException getException(String message) {
        throw new BusinessException(message);
    }

    public static void throwException(Throwable t) {
        throw new BusinessException(t);
    }
}
