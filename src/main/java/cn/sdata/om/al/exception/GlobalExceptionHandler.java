package cn.sdata.om.al.exception;


import cn.dev33.satoken.exception.NotLoginException;
import cn.sdata.om.al.result.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Optional;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ResponseEntity<R<String>> exceptionHandler(Exception e) {
        // 获取异常消息，避免空指针
        String message = Optional.ofNullable(e.getMessage())
                .orElseGet(() -> Optional.ofNullable(e.getCause()).map(Throwable::getMessage).orElse("未知错误"));
        log.error("全局异常捕获: {}", message, e);
        Throwable cause = e.getCause();
        if (cause == null) {
            cause = e;
        }
        return new ResponseEntity<>(R.failed(cause.toString(), message), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // 处理未登录异常
    @ExceptionHandler(NotLoginException.class)
    @ResponseBody
    public R<?> handleNotLogin(NotLoginException e) {
        return R.restResult(null, 401, "用户未登录");
    }
}
