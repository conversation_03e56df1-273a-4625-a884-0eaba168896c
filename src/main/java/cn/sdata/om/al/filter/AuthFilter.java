package cn.sdata.om.al.filter;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/2/10 11:03
 * @Version 1.0
 */
@Configuration
public class AuthFilter implements WebMvcConfigurer {

    @Resource
    private UserApiAuthInterceptor userApiAuthInterceptor;

    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /**
         * 登录拦截器
         * */
        registry.addInterceptor(new SaInterceptor(handle -> {
                    SaRouter.match("/**").check(StpUtil::checkLogin);
                }))
                .order(0)
                .addPathPatterns("/**")
                .excludePathPatterns("/loginController/**", "/error", "/testSmb/**");

        /**
         * 增加接口权限校验拦截器
         * */
        registry.addInterceptor(userApiAuthInterceptor)
                .order(1)
                .addPathPatterns("/**")
                .excludePathPatterns("/loginController/**", "/error", "/testSmb/**");
    }

    /**
     * 跨域处理
     *
     * @param registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("*")
                .allowedHeaders("*")
                .exposedHeaders("user-token"); // 允许客户端访问自定义 Header
    }
}
