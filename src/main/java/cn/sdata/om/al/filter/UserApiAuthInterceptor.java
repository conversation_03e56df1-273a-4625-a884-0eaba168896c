package cn.sdata.om.al.filter;

import cn.sdata.om.al.entity.MenuApi;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.MenuApiService;
import cn.sdata.om.al.service.UserRoleService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户api访问权限拦截器
 *
 * <AUTHOR>
 * @Date 2025/2/17 19:15
 * @Version 1.0
 */
@Component
public class UserApiAuthInterceptor implements HandlerInterceptor {

    private final static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private MenuApiService menuApiService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userId = SecureUtil.currentUserId(),
                requestUrl = request.getRequestURI();
        Integer m2Api = Math.toIntExact(menuApiService.count(Wrappers.lambdaQuery(MenuApi.class).eq(MenuApi::getApiUrl, requestUrl))),
                u2Api = userRoleService.user2ApiCount(userId, requestUrl);
        if (m2Api > 0 && (StringUtils.isBlank(userId) || u2Api.equals(0))) {
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(401);
            response.getWriter().write(objectMapper.writeValueAsString(R.restResult(null, 401, "无权限访问")));
            return false;
        }
        return true;
    }
}
