package cn.sdata.om.al.ftp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ftp.FtpException;
import cn.sdata.om.al.config.FtpProperties;
import cn.sdata.om.al.entity.mail.Attachment;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.utils.C;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ftp service
 */
@Service
@Slf4j
public class FtpService {

    @Resource
    private FtpProperties ftpProperties;

    public R<?> uploadMultipartFile(MultipartFile multipartFile) {
        if (multipartFile.isEmpty()) {
            return R.failed("文件为空，文件上传失败");
        }
        // 创建ftp连接
        FTPClient ftpClient = connectFtpServer();
        Attachment attachment = new Attachment();
        attachment.setFilePath(ftpProperties.getBasePath() + File.separator + multipartFile.getOriginalFilename());
        // 上传文件至ftp
        return Boolean.TRUE.equals(uploadFileToFtp(ftpClient, multipartFile, multipartFile.getOriginalFilename(), ftpProperties.getBasePath()))
                ? R.ok(attachment) : R.failed("文件上传失败！");
    }

    /**
     * 获取指定目录下的文件列表
     *
     * @param ftpClient   FTP客户端
     * @param ftpFilePath 文件存放路径
     * @return 文件名列表
     */
    public List<String> getFileNameList(FTPClient ftpClient, String ftpFilePath, String containStr) {
        List<String> fileNameList = new ArrayList<>();
        try {
            //切换工作目录到文件所在的目录
            ftpClient.changeWorkingDirectory(ftpFilePath);
            ftpClient.enterLocalPassiveMode();
            FTPFile[] ftpFiles = ftpClient.listFiles(ftpFilePath);
            for (FTPFile ftpFile : ftpFiles) {
                if (StringUtils.isBlank(containStr) || ftpFile.getName().contains(containStr)) {
                    fileNameList.add(ftpFile.getName());
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(StrUtil.format("获取文件列表失败,文件路径{}", ftpFilePath), e);
        }
        return fileNameList;
    }

    /**
     * 多文件下载
     */
    public void downloadFilesAsZip(List<Attachment> attachmentList, HttpServletResponse response, String fileName) {
        if (CollUtil.isEmpty(attachmentList)) {
            return;
        }
        Assert.notBlank(fileName, "文件名不能为空");
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            // 创建链接
            FTPClient ftpClient = connectFtpServer();
            response.setContentType("application/zip");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            // 多文件下载
            for (Attachment attachment : attachmentList) {
                downloadFile(ftpClient, attachment.getFilePath(), attachment.getFileName(), zipOutputStream);
            }
            // 关闭ftp连接
            if (ftpClient.isConnected()) {
                ftpClient.disconnect();
            }

        } catch (IOException e) {
            throw new FtpException(e);
        }
    }

    public void downloadFile(FTPClient ftpClient, String ftpFilePath, String fileName, ZipOutputStream zipOutputStream) {
        try {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 切换工作目录到文件所在的目录
            ftpClient.changeWorkingDirectory(ftpFilePath);
            ftpClient.enterLocalPassiveMode();
            zipOutputStream.putNextEntry(new ZipEntry(fileName));
            boolean success = ftpClient.retrieveFile(fileName, zipOutputStream);
            if (success) {
                log.info("{}{}下载成功", ftpFilePath, fileName);
            }
            zipOutputStream.closeEntry();
        } catch (IOException e) {
            throw new FtpException(e);
        }
    }

    public void downloadFileFromFtp(String fileName, String ftpFilePath, HttpServletResponse response) {
        // 创建链接
        FTPClient ftpClient = connectFtpServer();
        if (null == ftpClient) {
            return;
        }
        try (OutputStream outputStream = response.getOutputStream()) {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 切换工作目录到文件所在的目录
            ftpClient.changeWorkingDirectory(ftpFilePath);
            ftpClient.enterLocalPassiveMode();
            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            ftpClient.retrieveFile(fileName, outputStream);
        } catch (Exception e) {
            log.error("FTP文件下载失败:", e);
            throw new FtpException("FTP文件下载失败", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ioe) {
                    log.error(ioe.toString());
                }
            }
        }
    }


    /**
     * 从ftp下载文件
     */
    public InputStream getInputStream(FTPClient ftpClient, String filePath, String fileName) {
        InputStream inputStream;
        try {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            // 切换工作目录到文件所在的目录
            if (StringUtils.isNotEmpty(filePath)) {
                ftpClient.changeWorkingDirectory(filePath);
            }
            ftpClient.enterLocalPassiveMode();
            inputStream = ftpClient.retrieveFileStream(fileName);
        } catch (Exception e) {
            log.error("ftp下载文件错误，错误信息为", e);
            throw new FtpException("ftp下载文件错误", e);
        }
        return inputStream;
    }

    /**
     * 上传文件流至ftp
     *
     * @param inputStream 文件输入流
     * @param fileName    文件名称
     * @param filePath    上传路径
     * @return 是否上传成功
     */
    public Boolean uploadStreamToFtp(FTPClient ftpClient, InputStream inputStream, String fileName, String filePath) {
        log.info("调用文件上传接口,上传的文件名为{},上传的文件地址为{}", fileName, filePath);
        // 返回结果
        boolean isOk = false;
        if (null == ftpClient) {
            return false;
        }
        try {
            // 设置文件传输模式为二进制，可以保证传输的内容不会被改变
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();    //注：上传文件都为0字节，设置为被动模式即可
            // 指定上传文件路径
            if (!ftpClient.changeWorkingDirectory(filePath)) {
                // 目录不存在,创建目录
                ftpClient.makeDirectory(filePath);
                //切换到目录,不然第一次文件会写入失败
                ftpClient.changeWorkingDirectory(filePath);
            }
            // 上传文件 参数：上传后的文件名，输入流,,返回Boolean类型，上传成功返回true
            isOk = ftpClient.storeFile(fileName, inputStream);
            // 退出ftp
            ftpClient.logout();
        } catch (IOException e) {
            log.error(e.toString());
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    // 断开ftp的连接
                    ftpClient.disconnect();
                } catch (IOException ioe) {
                    log.error(ioe.toString());
                }
            }
        }
        return isOk;
    }

    /**
     * 上传文件至ftp
     *
     * @param multipartFile 文件
     * @param fileName      文件名称
     * @param filePath      上传路径
     */
    public Boolean uploadFileToFtp(FTPClient ftpClient, MultipartFile multipartFile, String fileName, String filePath) {
        log.info("调用文件上传接口,上传的文件名为{},上传的文件地址为{}", fileName, filePath);
        // 返回结果
        boolean isOk = false;
        // 初始化连接
        if (null == ftpClient) {
            return false;
        }
        try (InputStream inputStream = multipartFile.getInputStream()) {
            // 设置文件传输模式为二进制，可以保证传输的内容不会被改变
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();    //注：上传文件都为0字节，设置为被动模式即可
            // 指定上传文件路径
            if (!ftpClient.changeWorkingDirectory(filePath)) {
                // 目录不存在,创建目录
                ftpClient.makeDirectory(filePath);
                //切换到目录,不然第一次文件会写入失败
                ftpClient.changeWorkingDirectory(filePath);
            }
            // 上传文件 参数：上传后的文件名，输入流,,返回Boolean类型，上传成功返回true
            isOk = ftpClient.storeFile(fileName, inputStream);
            // 退出ftp
            ftpClient.logout();
        } catch (IOException e) {
            log.error(e.toString());
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    // 断开ftp的连接
                    ftpClient.disconnect();
                } catch (IOException ioe) {
                    log.error(ioe.toString());
                }
            }
        }
        return isOk;
    }

    /**
     * 递归创建ftp文件目录
     *
     * @param ftpClient  ftp客户端
     * @param folderList 目录列表
     */
    public void createFolders(FTPClient ftpClient, String[] folderList) throws IOException {
        ftpClient.changeWorkingDirectory("/");
        for (String folder : folderList) {
            if (StringUtils.isBlank(folder)) {
                continue;
            }
            // 检查文件是否存在
            if (!ftpClient.changeWorkingDirectory(folder)) {
                // 不存在则创建文件夹
                ftpClient.makeDirectory(folder);
                // 进入创建的文件夹
                ftpClient.changeWorkingDirectory(folder);
            }
        }

    }

    public Boolean uploadFileToFtpByStream(FTPClient ftpClient, InputStream inputStream, String fileName, String filePath) {
        log.info("调用文件上传接口,上传的文件名为{},上传的文件地址为{}", fileName, filePath);
        // 返回结果
        boolean isOk = false;
        if (null == ftpClient) {
            return false;
        }
        try (BufferedInputStream bis = new BufferedInputStream(inputStream)) {
            // 设置文件传输模式为二进制，可以保证传输的内容不会被改变
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();    //注：上传文件都为0字节，设置为被动模式即可
            String[] folderList = filePath.split("/");
            // 创建文件目录
            createFolders(ftpClient, folderList);
            ftpClient.setBufferSize(8192 * 1024);
            // 上传文件 参数：上传后的文件名，输入流,,返回Boolean类型，上传成功返回true
            isOk = ftpClient.storeFile(fileName, bis);
        } catch (IOException e) {
            log.error(e.toString());
        } finally {
            try {
                // 关闭流
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        log.info("文件上传结束！！！！");
        return isOk;
    }

    /**
     * ftp连接
     */
    public FTPClient connectFtpServer() {
        // 创建FTPClient对象（对于连接ftp服务器，以及上传和上传都必须要用到一个对象）
        log.info("创建FTP连接");
        FTPClient ftpClient = new FTPClient();
        // 设置连接超时时间
        ftpClient.setConnectTimeout(1000 * 60);
        // 设置ftp字符集
        ftpClient.setControlEncoding(ftpProperties.getEncoding());
        // 设置被动模式，文件传输端口设置,否则文件上传不成功，也不报错
        ftpClient.enterLocalPassiveMode();
        ftpClient.setBufferSize(1024 * 1024 * 8);
        try {
            // 定义返回的状态码
            int replyCode;
            // 连接ftp(当前项目所部署的服务器和ftp服务器之间可以相互通讯，表示连接成功)
            ftpClient.connect(ftpProperties.getHost(), ftpProperties.getPort());
            // 输入账号和密码进行登录
            ftpClient.login(ftpProperties.getFtpName(), ftpProperties.getPassword());
            // 接受状态码(如果成功，返回230，如果失败返回503)
            replyCode = ftpClient.getReplyCode();
            // 根据状态码检测ftp的连接，调用isPositiveCompletion(reply)-->如果连接成功返回true，否则返回false
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.info("connect ftp {} failed", ftpProperties.getHost());
                // 连接失败，断开连接
                ftpClient.disconnect();
                return null;
            }
            log.info("replyCode:" + replyCode);
        } catch (IOException e) {
            throw new FtpException("ftp连接失败:", e);
        }
        return ftpClient;
    }


    /**
     * ftp连接（自定义配置）
     *
     * @return FTP客户端
     */
    /**
     * ftp连接（自定义配置）
     *
     * @param properties 连接参数
     * @param ifActive   是否主动模式
     * @return FTP客户端
     */
    public FTPClient connectFtpServer(FtpProperties properties, boolean ifActive) {
        // 创建FTPClient对象（对于连接ftp服务器，以及上传和上传都必须要用到一个对象）
        log.info("创建FTP连接");
        FTPClient ftpClient = new FTPClient();
        // 设置连接超时时间
        ftpClient.setConnectTimeout(1000 * 60);
        // 设置ftp字符集
        ftpClient.setControlEncoding(properties.getEncoding());
        if (ifActive) {
            // 设置主动模式
            ftpClient.enterLocalActiveMode();
        } else {
            // 设置被动模式，文件传输端口设置,否则文件上传不成功，也不报错
            ftpClient.enterLocalPassiveMode();
        }
        try {
            // 定义返回的状态码
            int replyCode;
            // 连接ftp(当前项目所部署的服务器和ftp服务器之间可以相互通讯，表示连接成功)
            ftpClient.connect(properties.getHost(), properties.getPort());
            // 输入账号和密码进行登录
            ftpClient.login(properties.getFtpName(), properties.getPassword());
            // 接受状态码(如果成功，返回230，如果失败返回503)
            replyCode = ftpClient.getReplyCode();
            // 根据状态码检测ftp的连接，调用isPositiveCompletion(reply)-->如果连接成功返回true，否则返回false
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.info("connect ftp {} failed", properties.getHost());
                // 连接失败，断开连接
                ftpClient.disconnect();
                return null;
            }
            log.info("replyCode:" + replyCode);
        } catch (IOException e) {
            log.error("ftp连接失败:" + e);
            return null;
        }
        return ftpClient;
    }

    public void close(FTPClient ftpClient) {
        //关闭FTP连接
        try {
            ftpClient.disconnect();
        } catch (IOException e) {
            log.error("FTP连接关闭失败！", e);
        }
    }

    public void deleteFile(FTPClient ftpClient, String filePath, String fileName) {
        Assert.notBlank(filePath, "删除的文件路径不能为空");
        Assert.notBlank(fileName, "删除的文件名称不能为空");
        String del = filePath + fileName;
        try {
            log.info("删除ftp中的文件:{}", del);
            boolean flag = ftpClient.deleteFile(del);
            Assert.isTrue(flag, "文件删除失败:[{}]", del);
        } catch (IOException e) {
            throw new RuntimeException(StrUtil.format("文件删除失败:[{}],错误信息:{}",
                    del, e.getMessage()));
        }
    }

    /**
     * 获取ftp base路径
     *
     * @return base路径
     */
    public String getBasePath(String date) {
        String basePath = ftpProperties.getBasePath();
        Assert.notBlank(basePath, "basePath-ftp路径未配置");
        return basePath + File.separator + C.defaultValue(date, DateUtil.today()) + File.separator;
    }

}
