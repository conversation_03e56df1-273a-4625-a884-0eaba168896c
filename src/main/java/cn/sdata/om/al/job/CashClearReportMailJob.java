package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.cashClearReport.CashClearReportService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.LogCCRUtil;
import cn.sdata.om.al.utils.StringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.DATA_DATE;

/**
 * 资金清算报表
 *
 * <AUTHOR>
 * @Date 2025/4/16 19:44
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CashClearReportMailJob implements QuartzJobBean {

    private final CronService cronService;

    private final CashClearReportService cashClearReportService;

    private final RemoteFileInfoService remoteFileInfoService;

    private final AccountInformationService accountInformationService;

    private final MailInfoService mailInfoService;

    private final FuncDataToMailService funcDataToMailService;

    @Override
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "CashClearReportMailJob context is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                jobId = mergedJobDataMap.getString(CronConstant.JOB_ID),
                dataDate = mergedJobDataMap.getString(DATA_DATE),
                executorStr = mergedJobDataMap.getString(CronConstant.EXECUTOR);
        List<String> clearIds = (List<String>) mergedJobDataMap.get("clearIds");
        if (StringUtils.isBlank(dataDate)) {
            dataDate = mergedJobDataMap.getString(CronConstant.START_DATE);
            if (StringUtils.isBlank(dataDate)) {
                dataDate = mergedJobDataMap.getString(CronConstant.END_DATE);
            }
        }
        Boolean syncStr = mergedJobDataMap.getBoolean(CronConstant.SYNC);
        log.info("CashClearReportMailJob_mergedJobDataMap_logId:{},jobId:{},dataDate:{},executorStr:{},syncStr:{}", logId, jobId, dataDate, executorStr, syncStr);
        try {
            List<String> remoteFileIds = cashClearReportService.list(
                            Wrappers.lambdaQuery(CashClearReportEntity.class)
                                    .eq(CashClearReportEntity::getDataDate, dataDate)
                                    .in(CollectionUtil.isNotEmpty(clearIds), CashClearReportEntity::getId, clearIds)
                                    .like(CashClearReportEntity::getFileName, DateUtil.format(DateUtil.parseDate(dataDate), "yyyy年MM月dd日"))).stream()
                    .map(CashClearReportEntity::getRemoteFileId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(remoteFileIds)) {
                updateSendStatus(dataDate, MailStatus.FAILED.name(), clearIds);
                log.error("CashClearReportMailJob_{}_数据日期无对应账套文件_remoteFileIds为空", dataDate);
                throw new TaskException("无关联账套文件");
            }
            List<RemoteFileInfo> files = remoteFileInfoService.listByIds(remoteFileIds);
            if (CollUtil.isEmpty(files)) {
                updateSendStatus(dataDate, MailStatus.FAILED.name(), clearIds);
                log.error("CashClearReportMailJob_{}_数据日期无对应远程账套文件_files为空", dataDate);
                throw new TaskException("无对应远程账套文件");
            }
            files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
            log.info("CashClearReportMailJob_files:{}", files);
            Map<String, RemoteFileInfo> productId2FileMap = genProductId2FileMap(files);
            log.info("CashClearReportMailJob_productId2FileMap:{}", productId2FileMap);
            Cron cron = cronService.getById(jobId);
            log.info("CashClearReportMailJob_cron:{}", cron);
            List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, productId2FileMap.keySet(), productId2FileMap);
            log.info("CashClearReportMailJob_sendMailInfos1:{}", sendMailInfos);
            sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
            log.info("CashClearReportMailJob_sendMailInfos2:{}", sendMailInfos);
            if (CollUtil.isEmpty(sendMailInfos)) {
                updateSendStatus(dataDate, MailStatus.FAILED.name(), clearIds);
            }
            boolean flag = false;
            for (SendMailInfo mailInfo : sendMailInfos) {
                if (MailStatus.FAILED.name().equals(mailInfo.getStatus())) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                log.error("CashClearReportMailJob_updateSendStatus_failed");
                updateSendStatus(dataDate, MailStatus.FAILED.name(), clearIds);
                updateLogStatus(logId, JobStatus.FAILED);
            } else {
                log.info("CashClearReportMailJob_updateSendStatus_success");
                updateSendStatus(dataDate, MailStatus.SUCCESS.name(), clearIds);
                updateLogStatus(logId, JobStatus.COMPLETE);
            }
            //邮件扩展数据入库
            String finalDataDate = dataDate;
            List<FuncDataToMailEntity> funcDataToMailEntities = sendMailInfos.stream().map(sendMailInfo -> new FuncDataToMailEntity()
                            .setId(IdUtil.getSnowflakeNextIdStr())
                            .setMailId(sendMailInfo.getMailId())
                            .setMailSendLogId(sendMailInfo.getMailSendLogId())
                            .setDataDate(finalDataDate)
                            .setMailStatus(sendMailInfo.getStatus())
                            .setFuncType(cron.getJobName())
                            .setDataType(cron.getJobName())
                            .setCreateTime(new Date())
                            .setProductId(String.join(",", sendMailInfo.getProductIds())))
                    .collect(Collectors.toList());
            log.info("CashClearReportMailJob_doExecute_funcDataToMailEntities:{}", funcDataToMailEntities);
            if (CollUtil.isNotEmpty(funcDataToMailEntities)) {
                funcDataToMailService.saveBatch(funcDataToMailEntities);
            }
            for (SendMailInfo mailInfo : sendMailInfos) {
                JSONObject params = new JSONObject();
                params.put("rpaLogId", logId);
                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                params.put("beginTime", DateUtil.now());
                params.put("username", executorStr);
                params.put("dataDate", DateUtil.format(DateUtil.parseDate(dataDate), "yyyy-MM-dd"));
                params.put("type", "MANUAL");
                params.put("params", JSON.toJSONString(mergedJobDataMap));
                params.put("mailId", mailInfo.getMailId());
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.SUCCESS.name());
                params.put("sendStatus", mailInfo.getStatus());
                LogCCRUtil.preSendMailLog(params);
            }
        } catch (Exception e) {
            updateSendStatus(dataDate, MailStatus.FAILED.name(), clearIds);
            updateLogStatus(logId, JobStatus.FAILED);
            log.error("CashClearReportMailJob_doExecute_error:{},{}", e, e.getMessage());
        }
    }

    private void updateLogStatus(String logId, JobStatus jobStatus) {
        LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
        uw.eq(BaseCronLog::getId, logId);
        uw.set(BaseCronLog::getStatus, jobStatus).set(BaseCronLog::getEndDateTime, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        SpringUtil.getBean(BaseCronLogService.class).update(uw);
    }

    /**
     * 更新邮件发送状态
     *
     * @param dataDate
     * @param mailSendStatus
     */
    public void updateSendStatus(String dataDate, String mailSendStatus, List<String> clearIds) {
        cashClearReportService.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                .eq(CashClearReportEntity::getDataDate, dataDate)
                .in(CollectionUtil.isNotEmpty(clearIds), CashClearReportEntity::getId, clearIds)
                .set(CashClearReportEntity::getMailSendStatus, mailSendStatus)
                .set(CashClearReportEntity::getMailSendTime, new Date())
                .set(CashClearReportEntity::getUpdateTime, new Date()));
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("资金清算报表-开始执行邮件发送");
            CashClearReportMailJob mailSendJob = (CashClearReportMailJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        } else {
            log.error("资金清算报表-无context");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("CashClearReportMailJob", CashClearReportMailJob.class);
    }

    /**
     * @param files
     * @return
     */
    private Map<String, RemoteFileInfo> genProductId2FileMap(List<RemoteFileInfo> files) {
        Objects.requireNonNull(files, "文件信息不为空");
        List<AccountInformation> list = accountInformationService.list();
        Map<String, String> name2ProductIdMap = list.stream().collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId, (oldId, newId) -> newId));
        log.info("CashClearReportMailJob_genProductId2FileMap_name2ProductIdMap:{}", name2ProductIdMap);

        Map<String, RemoteFileInfo> fileName2FileMap = files.stream().collect(Collectors.toMap(RemoteFileInfo::getFileName, remoteFileInfo -> remoteFileInfo, (oldFile, newFile) -> newFile, LinkedHashMap::new));
        log.info("CashClearReportMailJob_genProductId2FileMap_fileName2FileMap:{}", fileName2FileMap);

        Map<String, RemoteFileInfo> productId2FileMap = new LinkedHashMap<>();
        log.info("CashClearReportMailJob_genProductId2FileMap_productId2FileMap:{}", productId2FileMap);

        for (Map.Entry<String, RemoteFileInfo> entry : fileName2FileMap.entrySet()) {
            String fileName = entry.getKey(),
                    fullProductName = StringUtil.extractTAAccountName(fileName);
            log.info("formatCustodianFiles_fileName:{},fullProductName:{}", fileName, fullProductName);
            if (StringUtils.isBlank(fullProductName)) {
                log.error("formatCustodianFiles_fullProductName:{},文件名格式不正确，无法提取账套名称。", fullProductName);
                continue;
            }
            String productId = name2ProductIdMap.get(fullProductName);
            if (StringUtils.isBlank(productId)) {
                log.info("通过账套编码去查找账套id...");
                String[] fileNameArr = fileName.split("_");
                String productCode = fileNameArr[1];
                log.info("切分出来的账套编码为 {}", productCode);
                Map<String, String> name2ProductCodeMap = list.stream().collect(Collectors.toMap(AccountInformation::getProductCode, AccountInformation::getId, (oldId, newId) -> newId));
                if (MapUtil.isNotEmpty(name2ProductCodeMap)) {
                    productId = name2ProductCodeMap.get(productCode);
                }
            }
            log.info("formatCustodianFiles_productId:{}", productId);
            if (StringUtils.isBlank(productId)) {
                log.error("formatCustodianFiles_id_未找到对应的账套名称:{}", productId);
                continue;
            }
            productId2FileMap.put(productId, entry.getValue());
        }
        return productId2FileMap;
    }
}
