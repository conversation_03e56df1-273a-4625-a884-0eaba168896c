package cn.sdata.om.al.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.FlowListMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.utils.LogCCRUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

/**
 * 资金清算报表rpa
 *
 * <AUTHOR>
 * @Date 2025/4/16 9:49
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class CashClearReportRpaJob implements QuartzJobBean {

    private final CronService cronService;

    private final RpaExecuteService rpaExecuteService;

    private final BaseCronLogService baseCronLogService;

    @Override
    public void doExecute(JobExecutionContext context) {
        /*JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        try {
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                    dataDate = mergedJobDataMap.getString(SYSTEM_DATE_NAME),
                    executor = mergedJobDataMap.getString(CronConstant.EXECUTOR);
            if (StringUtils.isBlank(dataDate)) {
                log.error("CashClearReportRpaJob_doExecute_error:dataDate为空");
                throw new Exception("dataDate为空");
            }

            try {
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("CashClearReportRpaJob_update_executeMethod:{},{}", e, e.getMessage());
            }

            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, dataDate);
            flowExtendParams.put(RPA_END_DATE_NAME, dataDate);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);

            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo.getCron());
            param.put(LOG_ID, logId);
            param.put(SYSTEM_DATE_NAME, dataDate);
            param.put(CronConstant.EXECUTOR, executor);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
            log.info("资金清算报表-手动下载-发起RPA调用结束");
        } catch (Exception e) {
            log.error("CashClearReportRpaJob_doExecute_error:{},{}", e, e.getMessage());
            throw new TaskException(e);
        }*/
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        BaseCronLog baseCronLog = new BaseCronLog();
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        Objects.requireNonNull(context, "InterbankFeesJob JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String dataDate = mergedJobDataMap.getString(SYSTEM_DATE_NAME);
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String executor = mergedJobDataMap.getString(CronConstant.EXECUTOR);
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId(jobId);
        baseCronLog.setExecutor(StringUtils.isNotBlank(executor) ? executor : DEFAULT_USERNAME);
        baseCronLog.setExecuteMethod("MANUAL");
        baseCronLog.setDataDate(DateUtil.format(DateUtil.parseDate(dataDate), "yyyy年MM月dd日") + "-" + DateUtil.format(DateUtil.parseDate(dataDate), "yyyy年MM月dd日"));
        baseCronLogMapper.insert(baseCronLog);
        /*if (context != null) {
            log.info("资金清算报表-手动下载-开始执行RPA任务");
            CashClearReportRpaJob rpaJob = (CashClearReportRpaJob) AopContext.currentProxy();
            rpaJob.doExecute(context);
        } else {
            log.error("资金清算报表-手动下载-无JobExecutionContext");
        }*/
        try {
            Cron cronInfo = cronService.getById(jobId);
            Integer flowId = cronInfo.getFlowId();
            FlowList flow = SpringUtil.getBean(FlowListMapper.class).selectById(flowId);
            if (flow == null) {
                log.error("CashClearReportRpaJob_doExecute_error:flowList对象为空");
                throw new Exception("flowList对象为空");
            }
            if (StringUtils.isBlank(dataDate)) {
                log.error("CashClearReportRpaJob_doExecute_error:dataDate为空");
                throw new Exception("dataDate为空");
            }
            try {
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("CashClearReportRpaJob_update_executeMethod:{},{}", e, e.getMessage());
            }
            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, dataDate);
            flowExtendParams.put(RPA_END_DATE_NAME, dataDate);
            JSONObject params = new JSONObject();
            try {
                params.put("rpaLogId", logId);
                params.put("username", executor);
                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                params.put("params", JSONObject.toJSONString(mergedJobDataMap));
                params.put("dataDate", dataDate);
                params.put("type", "MANUAL");
                JSONObject rpaParams = new JSONObject();
                rpaParams.put("flow", flow);
                rpaParams.put("flowExtendParams", flowExtendParams);
                params.put("rpaParam", JSON.toJSONString(rpaParams));
                LogCCRUtil.preRpaLog(params);

            } catch (Exception e){
                e.printStackTrace();
            }
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo);
            param.put(LOG_ID, logId);
            param.put(SYSTEM_DATE_NAME, dataDate);
            param.put(CronConstant.EXECUTOR, executor);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
            log.info("资金清算报表-手动下载-发起RPA调用结束");
        } catch (Exception e) {
            log.error("CashClearReportRpaJob_doExecute_error:{},{}", e, e.getMessage());
            LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
            uw.eq(BaseCronLog::getId, logId);
            uw.set(BaseCronLog::getStatus, JobStatus.FAILED).set(BaseCronLog::getEndDateTime, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            baseCronLogService.update(uw);
            throw new TaskException(e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("CashClearReportRpaJob", CashClearReportRpaJob.class);
    }
}
