package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity;
import cn.sdata.om.al.entity.dividendDetail.DividendDetailFileEntity;
import cn.sdata.om.al.entity.dividendDetail.DividendDetailFileMailEntity;
import cn.sdata.om.al.entity.dividendDetail.LogMailSendDividendDetailEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.mapper.dividendDetail.LogMailSendDividendDetailMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.service.dividendDetail.DividendDetailFileMailService;
import cn.sdata.om.al.service.dividendDetail.DividendDetailFileService;
import cn.sdata.om.al.service.dividendDetail.DividendDetailService;
import cn.sdata.om.al.service.mail.MailInfoService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分红信息明细账套-委托人邮件发送
 *
 * <AUTHOR>
 * @Date 2025/5/6 14:03
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DividendDetailInvestorMailJob implements QuartzJobBean {

    private final DividendDetailService detailService;

    private final DividendDetailFileService fileService;

    private final DividendDetailFileMailService detailMailService;

    private final CronService cronService;

    private final MailInfoService mailInfoService;

    private final FuncDataToMailService funcDataToMailService;

    private final LogMailSendDividendDetailMapper logMailSendDividendDetailMapper;

    @Override
    public void doExecute(JobExecutionContext context) {
        try {
            Objects.requireNonNull(context, "DividendDetailInvestorMailJob JobExecutionContext is null");
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                    jobId = mergedJobDataMap.getString(CronConstant.JOB_ID),
                    executorStr = mergedJobDataMap.getString(CronConstant.EXECUTOR);
            Boolean syncStr = mergedJobDataMap.getBoolean(CronConstant.SYNC);
            String detailFileMailIds = mergedJobDataMap.getString("detailFileMailIds");
            String userId = mergedJobDataMap.getString("userId"),
                    userName = mergedJobDataMap.getString("userName");
            log.info("DividendDetailInvestorMailJob_mergedJobDataMap_logId:{},jobId:{},executorStr:{},syncStr:{},detailFileMailIds:{},userId:{},userName:{}",
                    logId, jobId, executorStr, syncStr, detailFileMailIds, userId, userName);
            if (StringUtils.isEmpty(detailFileMailIds)) {
                log.error("DividendDetailInvestorMailJob_doExecute_error:detailFileMailIds为空");
                throw new TaskException("DividendDetailInvestorMailJob_doExecute_error:detailFileMailIds为空");
            }
            Cron cron = cronService.getById(jobId);
            log.info("DividendDetailInvestorMailJob_cron:{}", cron);
            List<String> detailFileMailIdList = Arrays.asList(detailFileMailIds.split(","));
            log.info("DividendDetailInvestorMailJob_detailFileMailIdList:{}", detailFileMailIdList);

            for (String id : detailFileMailIdList) {
                DividendDetailFileMailEntity mailEntity = detailMailService.getById(id);
                DividendDetailFileEntity fileEntity = fileService.getById(mailEntity.getDetailFileId());
                DividendDetailEntity detailEntity = detailService.getById(mailEntity.getDetailId());
                Map<String, RemoteFileInfo> files = Maps.newHashMap();
                RemoteFileInfo fileInfo = new RemoteFileInfo();
                fileInfo.setFileName(Paths.get(fileEntity.getLocalFilePath()).getFileName().toString());
                fileInfo.setFilePath(fileEntity.getLocalFilePath());
                fileInfo.setLocation("local");
                fileInfo.setLogId(logId);
                files.put("local0", fileInfo);
                log.info("DividendDetailInvestorMailJob_for_files:{},id:{}", files.toString(), id);
                String dataDate = detailEntity.getDataDate(),
                        productId = detailEntity.getProductId();
                List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(Lists.newArrayList(productId)), files);
                log.info("DividendDetailInvestorMailJob_sendMailInfos1:{}", sendMailInfos);
                sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
                log.info("DividendDetailInvestorMailJob_sendMailInfos2:{}", sendMailInfos);
                if (CollUtil.isEmpty(sendMailInfos)) {
                    log.error("DividendDetailInvestorMailJob_sendMailInfos3_发送邮件为空_dataDate:{},productId:{}", dataDate, productId);

                    detailMailService.update(Wrappers.lambdaUpdate(DividendDetailFileMailEntity.class)
                            .eq(DividendDetailFileMailEntity::getId, id)
                            .eq(DividendDetailFileMailEntity::getDetailId, detailEntity.getId())
                            .eq(DividendDetailFileMailEntity::getDetailFileId, fileEntity.getId())
                            .eq(DividendDetailFileMailEntity::getMailType, "investor")
                            .set(DividendDetailFileMailEntity::getMailSendStatus, MailStatus.FAILED.name())
                            .set(DividendDetailFileMailEntity::getMailSendTime, new Date())
                            .set(DividendDetailFileMailEntity::getUpdateTime, new Date())
                            .set(DividendDetailFileMailEntity::getStep, "sendMailInfos为空")
                    );

                    //邮件发送日志记录
                    logMailSendDividendDetailMapper.insert(new LogMailSendDividendDetailEntity()
                            .setId(IdWorker.getIdStr())
                            .setTaskName("发送投资人邮件")
                            .setSendType("手工")
                            .setStatus(MailStatus.FAILED.name())
                            .setDataDate(dataDate)
                            .setUserId(userId)
                            .setUserName(userName)
                            .setCreateTime(new Date())
                            .setStep("发送投资人邮件异常:sendMailInfos为空")
                    );

                    continue;
                }
                SendMailInfo sendMailInfo = sendMailInfos.get(0);
                detailMailService.update(Wrappers.lambdaUpdate(DividendDetailFileMailEntity.class)
                        .eq(DividendDetailFileMailEntity::getId, id)
                        .eq(DividendDetailFileMailEntity::getDetailId, detailEntity.getId())
                        .eq(DividendDetailFileMailEntity::getDetailFileId, fileEntity.getId())
                        .eq(DividendDetailFileMailEntity::getMailType, "investor")
                        .set(DividendDetailFileMailEntity::getMailId, sendMailInfo.getMailId())
                        .set(DividendDetailFileMailEntity::getMailSendLogId, sendMailInfo.getMailSendLogId())
                        .set(DividendDetailFileMailEntity::getMailSendStatus, sendMailInfo.getStatus())
                        .set(DividendDetailFileMailEntity::getMailSendTime, new Date())
                        .set(DividendDetailFileMailEntity::getUpdateTime, new Date())
                );

                List<FuncDataToMailEntity> funcDataToMailEntities = sendMailInfos.stream()
                        .map(sendMail -> new FuncDataToMailEntity()
                                .setId(IdUtil.getSnowflakeNextIdStr())
                                .setMailId(sendMail.getMailId())
                                .setMailSendLogId(sendMail.getMailSendLogId())
                                .setMailStatus(sendMail.getStatus())
                                .setDataDate(dataDate)
                                .setFuncType(detailEntity.getId() + "-" + "investor")
                                .setDataType(fileEntity.getId() + "-" + "investor")
                                .setCreateTime(new Date()))
                        .collect(Collectors.toList());
                log.info("DividendDetailInvestorMailJob_FuncDataToMail_funcDataToMailEntities:{}", funcDataToMailEntities);
                if (CollUtil.isNotEmpty(funcDataToMailEntities)) {
                    funcDataToMailService.saveBatch(funcDataToMailEntities);
                }

                for (SendMailInfo mailInfo : sendMailInfos) {
                    //邮件发送日志记录
                    logMailSendDividendDetailMapper.insert(new LogMailSendDividendDetailEntity()
                            .setId(IdWorker.getIdStr())
                            .setTaskName("发送投资人邮件")
                            .setSendType("手工")
                            .setStatus(mailInfo.getStatus())
                            .setSendTime(new Date())
                            .setMailId(mailInfo.getMailId())
                            .setMailSendLogId(mailInfo.getMailSendLogId())
                            .setDataDate(dataDate)
                            .setUserId(userId)
                            .setUserName(userName)
                            .setCreateTime(new Date())
                    );
                }

            }
        } catch (Exception e) {
            log.error("DividendDetailInvestorMailJob_doExecute_error:{},{}", e, e.getMessage());
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("分红信息明细账套-投资人邮件-开始执行邮件发送");
            DividendDetailInvestorMailJob mailSendJob = (DividendDetailInvestorMailJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        } else {
            log.error("分红信息明细账账套-投资人邮件-无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("DividendDetailInvestorMailJob", DividendDetailInvestorMailJob.class);
    }
}
