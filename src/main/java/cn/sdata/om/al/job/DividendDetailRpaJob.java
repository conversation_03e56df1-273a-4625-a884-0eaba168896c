package cn.sdata.om.al.job;

import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.RpaExecuteService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.BaseConstant.SYSTEM_DATE_NAME;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

/**
 * 分红信息明细rpa
 *
 * <AUTHOR>
 * @Date 2025/4/27 16:05
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class DividendDetailRpaJob implements QuartzJobBean {

    private final CronService cronService;

    private final RpaExecuteService rpaExecuteService;

    private final BaseCronLogService baseCronLogService;

    @Override
    public void doExecute(JobExecutionContext context) {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        try {
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                    startDate = mergedJobDataMap.getString(CronConstant.START_DATE),
                    endDate = mergedJobDataMap.getString(CronConstant.END_DATE),
                    dataDate = mergedJobDataMap.getString(SYSTEM_DATE_NAME),
                    executor = mergedJobDataMap.getString(CronConstant.EXECUTOR),
                    productIdStr = mergedJobDataMap.getString("productIdStr");

            if (StringUtils.isBlank(dataDate)) {
                //自动类型
                dataDate = cronService.getFormatDataDate(context);
                startDate = dataDate;
                endDate = dataDate;
                productIdStr = "";
                executor = DEFAULT_USERNAME;
                log.info("DividendDetailRpaJob_auto_dataDate:{},startDate:{},endDate:{},executor:{}", dataDate, startDate, endDate, executor);
            } else {
                //手动类型
                try {
                    LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                    uw.eq(BaseCronLog::getId, logId);
                    uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                    baseCronLogService.update(uw);
                } catch (Exception e) {
                    log.error("DividendDetailRpaJob_update_executeMethod:{},{}", e, e.getMessage());
                }
            }

            log.info("DividendDetailRpaJob_doExecute_cronInfo:{},flow:{},logId:{},dataDate:{},executor:{},productIdStr:{}", cronInfo, flow, logId, dataDate, executor, productIdStr);
            if (StringUtils.isBlank(dataDate)) {
                log.error("DividendDetailRpaJob_doExecute_error:dataDate为空");
                throw new Exception("dataDate为空");
            }
            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, dataDate);
            flowExtendParams.put(RPA_END_DATE_NAME, dataDate);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            log.info("DividendDetailRpaJob_doExecute_rpaExecLog:{}", rpaExecLog);

            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo.getCron());
            param.put(LOG_ID, logId);
            param.put(SYSTEM_DATE_NAME, dataDate);
            param.put(CronConstant.EXECUTOR, executor);
            param.put("productIdStr", productIdStr);
            param.put(RPA_START_DATE_NAME, startDate);
            param.put(RPA_END_DATE_NAME, endDate);
            param.put("userId", mergedJobDataMap.getString("userId"));
            param.put("userName", mergedJobDataMap.getString("userName"));
            param.put("rpaExecLogId", rpaExecLog.getId());
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
            log.info("分红信息明细-手动下载-发起RPA调用结束");
        } catch (Exception e) {
            log.error("DividendDetailRpaJob_doExecute_error:{},{}", e, e.getMessage());
            throw new TaskException(e);
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("分红信息明细-手动下载-开始执行RPA任务");
            DividendDetailRpaJob rpaJob = (DividendDetailRpaJob) AopContext.currentProxy();
            rpaJob.doExecute(context);
        } else {
            log.error("分红信息明细-手动下载-无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("DividendDetailRpaJob", DividendDetailRpaJob.class);
    }
}
