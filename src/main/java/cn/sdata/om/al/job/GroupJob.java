package cn.sdata.om.al.job;

import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class GroupJob implements QuartzJobBean {

    private final CronService cronService;

    @Override
    public void doExecute(JobExecutionContext context) {
        if (context == null) {
            log.error("调度信息为空");
            return;
        }
        log.info("执行组任务");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String groupId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        List<String> jobIds = cronService.queryJobIds(groupId);
        cronService.startJobNow(jobIds, mergedJobDataMap);
    }

    @Override
    public void execute(JobExecutionContext context) {
        if (context != null) {
            log.info("开始执行组任务");
            GroupJob groupJob = (GroupJob) AopContext.currentProxy();
            groupJob.doExecute(context);
        }else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("GroupJob", GroupJob.class);
    }
}
