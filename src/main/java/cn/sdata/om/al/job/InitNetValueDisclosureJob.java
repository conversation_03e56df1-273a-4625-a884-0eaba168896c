package cn.sdata.om.al.job;

import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.enums.*;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.utils.JobExecutionContextUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.DATE_FORMAT_PATTERN;
import static cn.sdata.om.al.constant.BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN;

@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class InitNetValueDisclosureJob implements QuartzJobBean {

    private final AccountInformationService accountInformationService;
    private final NetValueDisclosureService netValueDisclosureService;
    private final MarketTradeDayService marketTradeDayService;

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("InitNetValueDisclosureJob", InitNetValueDisclosureJob.class);
    }


    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) {

        LambdaQueryWrapper<AccountInformation> accountWrapper = new LambdaQueryWrapper<>();
        accountWrapper.in(AccountInformation::getValuationTime, List.of(ValuationTime.T0.name(), ValuationTime.T1.name()));
        List<AccountInformation> list = accountInformationService.list(accountWrapper);
        String dataDate = JobExecutionContextUtil.getDataDate(context);
        String tradeCalendar = JobExecutionContextUtil.getTradeCalendar(context);
        if (dataDate == null) {
            dataDate = new SimpleDateFormat(HORIZONTAL_DATE_FORMAT_PATTERN).format(Calendar.getInstance().getTime());
        }
        saveNetValue(list, dataDate);
        boolean isLastWorkDay = marketTradeDayService.judgeLastWorkDay(DateUtil.format(DateUtil.parse(dataDate, HORIZONTAL_DATE_FORMAT_PATTERN), DATE_FORMAT_PATTERN), tradeCalendar);
        if (isLastWorkDay) {
            log.info("检测到为最后一天工作日");
            List<String> resultList = marketTradeDayService.getAllHoliday(DateUtil.format(DateUtil.parse(dataDate, HORIZONTAL_DATE_FORMAT_PATTERN), DATE_FORMAT_PATTERN), tradeCalendar);
            for (String holidayDate : resultList) {
                holidayDate = DateUtil.format(DateUtil.parse(holidayDate, DATE_FORMAT_PATTERN), HORIZONTAL_DATE_FORMAT_PATTERN);
                log.info("初始化{}净值数据", holidayDate);
                saveNetValue(list, holidayDate);
            }
        }
    }

    private void saveNetValue(List<AccountInformation> list, String currentDate) {
        Objects.requireNonNull(list, "不得为空");
        List<NetValueDisclosure> netValueDisclosures = new ArrayList<>();
        for (AccountInformation accountInformation : list) {
            NetValueDisclosure netValueDisclosure = getNetValueDisclosure(accountInformation, currentDate);
            if (netValueDisclosure != null) {
                netValueDisclosures.add(netValueDisclosure);
            }
        }
        //根据账套和估值日期初始化
        LambdaQueryWrapper<NetValueDisclosure> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetValueDisclosure::getValuationDate, currentDate);
        netValueDisclosureService.remove(queryWrapper);
        netValueDisclosureService.saveBatch(netValueDisclosures);
    }

    private NetValueDisclosure getNetValueDisclosure(AccountInformation accountInformation, String dataDate) {
        if (accountInformation == null) {
            return null;
        }
        NetValueDisclosure netValueDisclosure = new NetValueDisclosure();
        netValueDisclosure.setProductId(accountInformation.getId());
        netValueDisclosure.setIsRevalued(0);
        netValueDisclosure.setAssetType(null);
        netValueDisclosure.setValuationDate(dataDate);
        netValueDisclosure.setValuationTableGenerated(0);
        Integer isElectronicDirectConnection = accountInformation.getIsElectronicDirectConnection();
        if (isElectronicDirectConnection == 1) {
            netValueDisclosure.setCustodyReconciliationEmailSent(MailStatus.NONE.name());
        }else{
            netValueDisclosure.setCustodyReconciliationEmailSent(MailStatus.UNSENT.name());
        }
        netValueDisclosure.setReconciliationStatus(ValuationStatus.UNCOMPLETED.name());
        netValueDisclosure.setValuationTableConfirmed(0);
        netValueDisclosure.setNetValueDisclosed(0);
        netValueDisclosure.setValuationTableDownloaded(ValuationDownloadStatus.UN_DOWNLOADED.name());
        //根据通讯录判断
        netValueDisclosure.setValuationTableSent(MailStatus.UNSENT.name());
        //根据通讯录判断
        netValueDisclosure.setInvestorReportSent(MailStatus.UNSENT.name());
        //根据通讯录判断
        netValueDisclosure.setThirdPartySent(MailStatus.UNSENT.name());
        netValueDisclosure.setRpaStatus(ValuationRPAStatus.NOT_STARTED.name());
        netValueDisclosure.setRpaSecondStatus(ValuationRPAStatus.NOT_STARTED.name());
        netValueDisclosure.setSztStatus(ValuationRPAStatus.NOT_STARTED.name());
        netValueDisclosure.setDbfPath(ValuationRPAStatus.NOT_STARTED.name());
        netValueDisclosure.setBalanceRpaStatus(ValuationRPAStatus.NOT_STARTED.name());
        return netValueDisclosure;
    }


}
