package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.InsuranceRegistrationFeesFile;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.InsuranceRegistrationFeesMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.LogFYIUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
@Component
@AllArgsConstructor
public class InsuranceRegistrationFeesJob implements QuartzJobBean {

    private BaseCronLogMapper baseCronLogMapper;

    private MailInfoService mailInfoService;

    private CronService cronService;

    @Autowired
    public void setBaseCronLogMapper(BaseCronLogMapper baseCronLogMapper) {
        this.baseCronLogMapper = baseCronLogMapper;
    }

    @Autowired
    public void setMailInfoService(MailInfoService mailInfoService) {
        this.mailInfoService = mailInfoService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Objects.requireNonNull(context, "InsuranceRegistrationFeesJob JobExecutionContext is null");
        InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class);
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String username = (String) mergedJobDataMap.get("username");
        Map<String, String> productIdAndFileIdMap = (Map<String, String>) mergedJobDataMap.get("productIdAndFileIdMap");
        List<String> ids = (List<String>) mergedJobDataMap.get("ids");
        BaseCronLog baseCronLog = new BaseCronLog();
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId(jobId);
        baseCronLog.setExecutor(StringUtils.isNotBlank(username) ? username : DEFAULT_USERNAME);
        baseCronLog.setExecuteMethod("MANUAL");
        baseCronLog.setDataDate(DateUtil.format(new Date(), "yyyy年MM月dd日"));
        baseCronLogMapper.insert(baseCronLog);
        log.info("开始异步执行中保登缴费通知单发送邮件...");
        CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
            try {
                Cron cron = cronService.getById(jobId);
                List<InsuranceRegistrationFeesFile> insuranceRegistrationFeesFiles = SpringUtil.getBean(InsuranceRegistrationFeesMapper.class).getAllFiles();
                Map<String, List<InsuranceRegistrationFeesFile>> collect = new HashMap<>();
                if (CollectionUtil.isNotEmpty(insuranceRegistrationFeesFiles)) {
                    collect = insuranceRegistrationFeesFiles.stream().collect(Collectors.groupingBy(InsuranceRegistrationFeesFile::getId));
                }
                Set<String> productIdSet = productIdAndFileIdMap.keySet();
                Map<String, RemoteFileInfo> infoMap = Maps.newHashMap();
                for (String productId : productIdSet) {
                    String fileId = productIdAndFileIdMap.get(productId);
                    if (StringUtils.isNotBlank(fileId)) {
                        List<InsuranceRegistrationFeesFile> fileList = collect.get(fileId);
                        if (CollectionUtil.isNotEmpty(fileList)) {
                            InsuranceRegistrationFeesFile feesFile = fileList.get(0);
                            RemoteFileInfo fileInfo = new RemoteFileInfo();
                            fileInfo.setFileName(feesFile.getFileName());
                            fileInfo.setLocation("local");
                            fileInfo.setFilePath(feesFile.getFilePath());
                            fileInfo.setOpenFile(Set.of(new File(feesFile.getFilePath())));
                            infoMap.put(productId, fileInfo);
                        }
                    }
                }
                log.info("中保登费用 - 需要发送的文件为:{}", JSON.toJSONString(infoMap));
                List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, DateUtil.today(), productIdSet, infoMap);
                List<SendMailInfo> res = mailInfoService.doSendMailInfo(sendMailInfos);
                baseCronLog.setEndDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                if (CollectionUtil.isNotEmpty(res)) {
                    insuranceRegistrationFeesMapper.updateSendStatus(ids, MailStatus.SUCCESS.name());
                    baseCronLog.setStatus(JobStatus.COMPLETE);
                } else {
                    // 修改状态 -> 邮件发送失败...
                    insuranceRegistrationFeesMapper.updateSendStatus(ids, MailStatus.FAILED.name());
                    baseCronLog.setStatus(JobStatus.FAILED);
                }
                for (SendMailInfo mailInfo : sendMailInfos) {
                    JSONObject params = new JSONObject();
                    params.put("rpaLogId", logId);
                    params.put("logId", IdUtil.getSnowflakeNextIdStr());
                    params.put("beginTime", DateUtil.now());
                    params.put("username", baseCronLog.getExecutor());
                    params.put("type", "MANUAL");
                    params.put("params", JSON.toJSONString(mergedJobDataMap));
                    params.put("endTime", DateUtil.now());
                    params.put("status", CommonStatus.SUCCESS.name());
                    params.put("sendStatus", mailInfo.getStatus());
                    params.put("mailId", mailInfo.getMailId());
                    LogFYIUtils.preSendMailLog(params);
                }
            } catch (Exception e) {
                e.printStackTrace();
                baseCronLog.setTaskInfo(e.getMessage());
            } finally {
                baseCronLogMapper.updateById(baseCronLog);
            }
        });
        insuranceRegistrationFeesMapper.updateSendStatus(ids, MailStatus.SENDING.name());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("InsuranceRegistrationFeesJob", InsuranceRegistrationFeesJob.class);
    }
}
