package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Month;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.FileAndIds;
import cn.sdata.om.al.entity.InterBankFees;
import cn.sdata.om.al.mapper.InterbankFeesMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.utils.DateUtils;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
@Component
@AllArgsConstructor
public class InterbankFeesImportO32Job implements QuartzJobBean {

    private InterbankFeesService interbankFeesService;

    @Autowired
    public void setInterbankFeesService(InterbankFeesService interbankFeesService) {
        this.interbankFeesService = interbankFeesService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("---------开始调度银行间导入o32自动任务");
        DateTime date = DateUtil.date();
        int field = date.getField(DateField.MONTH);
        // 每年的 1月 4月 7月 10月的第10个工作日 开始自动下载 且传的日期是上个月的开始和结束日期
        if (field == Month.APRIL.getValue() || field == Month.JULY.getValue()
                || field == Month.OCTOBER.getValue() || field == Month.JANUARY.getValue()) {
            LocalDate today = LocalDate.now();
            // 当天是否属于第10个工作日
            LocalDate firstDayOfMonth = today.withDayOfMonth(1);
            LocalDate tenthWorkday = DateUtils.findTenthWorkday(firstDayOfMonth);
            if (!today.equals(tenthWorkday)) {
                log.error("当天的日期不是本月的第10个工作日...");
                return;
            }
        } else {
            log.error("当前日期不能执行此任务");
            return;
        }
        // 查询业务日切时间
        String initDate = null;
        try {
            List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectO32Date();
            if (CollectionUtil.isNotEmpty(jsonObjects)) {
                JSONObject object = jsonObjects.get(0);
                initDate = object.getString("L_INIT_DATE");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(initDate)) {
            initDate = DateUtil.format(new Date(), "yyyyMMdd");
        }
        try {
            String[] quarterMonths = DateUtils.getQuarterMonths();
            List<InterBankFees> fees = interbankFeesService.selectLastQuarterData(quarterMonths[0], quarterMonths[1]);
            List<FileAndIds> fileList = interbankFeesService.generateO32File(fees, 0, initDate);
            // 生成后需要上传至共享文件夹
            interbankFeesService.uploadShareFolder(fileList, 0, true, DEFAULT_USERNAME, "AUTO");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("InterbankFeesImportO32Job", InterbankFeesImportO32Job.class);
    }
}
