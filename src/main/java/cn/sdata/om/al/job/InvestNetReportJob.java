package cn.sdata.om.al.job;

import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.entity.investNetReport.InvestNetReportEntity;
import cn.sdata.om.al.enums.NetReportFileEnum;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.InvestNetReportService;
import cn.sdata.om.al.service.RpaExecuteService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

/**
 * <AUTHOR>
 * @Date 2025/3/26 20:45
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class InvestNetReportJob implements QuartzJobBean {

    private final CronService cronService;

    private final RpaExecuteService rpaExecuteService;

    private final InvestNetReportService reportService;

    private final BaseCronLogService baseCronLogService;

    @Override
    public void doExecute(JobExecutionContext context) {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        try {
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = cronInfo.getLogId(),
                    startDate = mergedJobDataMap.getString(CronConstant.START_DATE),
                    endDate = mergedJobDataMap.getString(CronConstant.END_DATE),
                    accountSetCodes = mergedJobDataMap.getString(RPA_PRODUCT_IDS);

            try {
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("InvestNetReportJob_update_executeMethod:{},{}", e, e.getMessage());
            }

            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, startDate);
            flowExtendParams.put(RPA_END_DATE_NAME, endDate);
            flowExtendParams.put(RPA_PRODUCT_IDS, accountSetCodes);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);

            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo.getCron());
            param.put(LOG_ID, logId);
            param.put(RPA_START_DATE_NAME, startDate);
            param.put(RPA_END_DATE_NAME, endDate);
            param.put(RPA_PRODUCT_IDS, accountSetCodes);
            param.put(CronConstant.EXECUTOR, mergedJobDataMap.getString(CronConstant.EXECUTOR));
            param.put("userId", mergedJobDataMap.getString("userId"));
            param.put("userName", mergedJobDataMap.getString("userName"));
            param.put("rpaExecLogId", rpaExecLog.getId());
            param.put(SYSTEM_DATE_NAME, startDate);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
        } catch (Exception e) {
            log.error("InvestNetReportJob_doExecute_error:{},{}", e, e.getMessage());
            reportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, String.valueOf(mergedJobDataMap.get(CronConstant.START_DATE)))
                    .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "InvestNetReportJob_doExecute_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("投连净值播报-开始执行InvestNetReportJob任务");
            InvestNetReportJob job = (InvestNetReportJob) AopContext.currentProxy();
            job.doExecute(context);
        } else {
            log.error("投连净值播报-无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("InvestNetReportJob", InvestNetReportJob.class);
    }
}
