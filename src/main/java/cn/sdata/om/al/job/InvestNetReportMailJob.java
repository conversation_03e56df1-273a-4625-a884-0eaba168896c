package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.investNetReport.InvestNetReportEntity;
import cn.sdata.om.al.entity.investNetReport.LogMailSendNetReportEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.NetReportFileEnum;
import cn.sdata.om.al.mapper.investNetReport.LogMailSendNetReportMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.service.InvestNetReportService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;

/**
 * <AUTHOR>
 * @Date 2025/3/27 9:30
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class InvestNetReportMailJob implements QuartzJobBean {

    private final CronService cronService;

    private final MailInfoService mailInfoService;

    private final InvestNetReportService reportService;

    private final FuncDataToMailService funcDataToMailService;

    private final LogMailSendNetReportMapper logMailSendMapper;

    @Override
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "InvestNetReportMailJob JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                jobId = mergedJobDataMap.getString(CronConstant.JOB_ID),
                dataDate = mergedJobDataMap.getString(DATA_DATE),
                filePathStr = mergedJobDataMap.getString(FILE_PATHS),
                manualStr = mergedJobDataMap.getString(CronConstant.MANUAL),
                executorStr = mergedJobDataMap.getString(CronConstant.EXECUTOR),
                accountSetCodesStr = mergedJobDataMap.getString(ACCOUNT_SET_CODES),
                userId = mergedJobDataMap.getString("userId"),
                userName = mergedJobDataMap.getString("userName");
        Boolean syncStr = mergedJobDataMap.getBoolean(CronConstant.SYNC);
        log.info("InvestNetReportMailJob_mergedJobDataMap_param:{},{},{},{},{},{},{},{},{},{}",
                logId, jobId, dataDate, filePathStr, accountSetCodesStr, manualStr, executorStr, syncStr, userId, userName);
        try {
            List<String> filePaths = Arrays.asList(filePathStr.split(", ")),
                    accountSetCodes = Arrays.asList(accountSetCodesStr.split(", "));
            Map<String, RemoteFileInfo> infoMap = Maps.newHashMap();
            for (int i = 0; i < filePaths.size(); i++) {
                String k = "local" + i, filePath = filePaths.get(i);
                RemoteFileInfo fileInfo = new RemoteFileInfo();
                fileInfo.setFileName(Paths.get(filePath).getFileName().toString());
                fileInfo.setFilePath(filePath);
                fileInfo.setLocation("local");
                fileInfo.setLogId(logId);
                infoMap.put(k, fileInfo);
            }
            log.info("InvestNetReportMailJob_infoMap:{}", infoMap.toString());
            Cron cron = cronService.getById(jobId);
            log.info("InvestNetReportMailJob_cron:{}", cron);
            List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(accountSetCodes), infoMap);
            log.info("InvestNetReportMailJob_sendMailInfos1:{}", sendMailInfos);
            sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
            log.info("InvestNetReportMailJob_sendMailInfos2:{}", sendMailInfos);
            if (CollUtil.isEmpty(sendMailInfos)) {

                //邮件发送日志记录
                logMailSendMapper.insert(new LogMailSendNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setTaskName("净值播报邮件发送")
                        .setSendType("手工")
                        .setStatus(MailStatus.FAILED.name())
                        .setDataDate(dataDate)
                        .setUserId(SecureUtil.currentUserId())
                        .setUserName(SecureUtil.currentUserName())
                        .setCreateTime(new Date())
                        .setStep("sendNetReportMail发送邮件异常:sendMailInfos为空")
                );

                updateSendStatus(dataDate, MailStatus.FAILED.name());
                return;
            }
            boolean flag = false;
            for (SendMailInfo mailInfo : sendMailInfos) {
                if (MailStatus.FAILED.name().equals(mailInfo.getStatus())) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                updateSendStatus(dataDate, MailStatus.FAILED.name());
            } else {
                updateSendStatus(dataDate, MailStatus.SUCCESS.name());
            }
            List<FuncDataToMailEntity> entities = sendMailInfos.stream()
                    .map(sendMailInfo -> new FuncDataToMailEntity()
                            .setId(IdUtil.getSnowflakeNextIdStr())
                            .setMailId(sendMailInfo.getMailId())
                            .setMailSendLogId(sendMailInfo.getMailSendLogId())
                            .setDataDate(dataDate)
                            .setMailStatus(sendMailInfo.getStatus())
                            .setFuncType("investNetReport")
                            .setDataType("netReport")
                            .setCreateTime(new Date()))
                    .collect(Collectors.toList());
            log.info("InvestNetReportMailJob_FuncDataToMail_entities:{}", entities);
            if (CollUtil.isNotEmpty(entities)) {
                funcDataToMailService.saveBatch(entities);
            }

            for (SendMailInfo sendMailInfo : sendMailInfos) {
                //邮件发送日志记录
                logMailSendMapper.insert(new LogMailSendNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setTaskName("净值播报邮件发送")
                        .setSendType("手工")
                        .setStatus(sendMailInfo.getStatus())
                        .setSendTime(new Date())
                        .setMailId(sendMailInfo.getMailId())
                        .setMailSendLogId(sendMailInfo.getMailSendLogId())
                        .setDataDate(dataDate)
                        .setUserId(userId)
                        .setUserName(userName)
                        .setCreateTime(new Date())
                );
            }

        } catch (Exception e) {

            //邮件发送日志记录
            logMailSendMapper.insert(new LogMailSendNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setTaskName("净值播报邮件发送")
                    .setSendType("手工")
                    .setStatus(MailStatus.FAILED.name())
                    .setDataDate(dataDate)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setStep("执行异常:" + e.getMessage())
            );

            updateSendStatus(dataDate, MailStatus.FAILED.name());
            log.error("InvestNetReportMailJob_doExecute_error:{},{}", e, e.getMessage());
        }

    }

    /**
     * 更新邮件发送状态
     *
     * @param dataDate
     * @param mailSendStatus
     */
    public void updateSendStatus(String dataDate, String mailSendStatus) {
        reportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
                .set(InvestNetReportEntity::getNetReportSendStatus, mailSendStatus)
                .set(InvestNetReportEntity::getNetReportSendTime, new Date())
                .set(InvestNetReportEntity::getUpdateTime, new Date()));
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("投连净值播报-开始执行邮件发送");
            InvestNetReportMailJob mailSendJob = (InvestNetReportMailJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        } else {
            log.error("投连净值播报-无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("InvestNetReportMailJob", InvestNetReportMailJob.class);
    }
}
