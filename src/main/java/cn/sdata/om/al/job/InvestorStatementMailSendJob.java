package cn.sdata.om.al.job;

import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogCustodianRecordsService;
import cn.sdata.om.al.service.LogMailSendRecordsService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.mail.MailInfoService;
import org.springframework.stereotype.Component;

@Component
public class InvestorStatementMailSendJob extends MailSendJob{


    public InvestorStatementMailSendJob(MailInfoService mailInfoService, CronService cronService, NetValueDisclosureService netValueDisclosureService, LogMailSendRecordsService logMailSendRecordsService, LogCustodianRecordsService logCustodianRecordsService) {
        super(mailInfoService, cronService, netValueDisclosureService, logMailSendRecordsService, logCustodianRecordsService);
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("InvestorStatementMailSendJob", InvestorStatementMailSendJob.class);
    }
}
