package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.monthlyData.LogMailSendMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.MonthlyDataEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.mapper.monthlyData.LogMailSendMonthlyDataMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.service.monthlyData.MonthlyDataService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.nio.file.Paths;
import java.util.*;

import static cn.sdata.om.al.constant.JobConstant.*;

/**
 * 月度数据-邮件发送
 *
 * <AUTHOR>
 * @Date 2025/4/8 17:04
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MonthlyDataZQMailJob implements QuartzJobBean {

    private final CronService cronService;

    private final MailInfoService mailInfoService;

    private final MonthlyDataService monthlyDataService;

    private final FuncDataToMailService funcDataToMailService;

    private final LogMailSendMonthlyDataMapper logMailSendMonthlyDataMapper;

    @Override
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "MonthlyDataZQMailJob context is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                jobId = mergedJobDataMap.getString(CronConstant.JOB_ID),
                dataDate = mergedJobDataMap.getString(DATA_DATE),
                filePathStr = mergedJobDataMap.getString(FILE_PATHS),
                manualStr = mergedJobDataMap.getString(CronConstant.MANUAL),
                executorStr = mergedJobDataMap.getString(CronConstant.EXECUTOR),
                userId = mergedJobDataMap.getString("userId"),
                userName = mergedJobDataMap.getString("userName");
        Boolean syncStr = mergedJobDataMap.getBoolean(CronConstant.SYNC);
        log.info("MonthlyDataZQMailJob_mergedJobDataMap_param:{},{},{},{},{},{},{}",
                logId, jobId, dataDate, filePathStr, manualStr, executorStr, syncStr, userId, userName);
        try {
            List<String> filePaths = Arrays.asList(filePathStr.split(", "));
            Map<String, RemoteFileInfo> infoMap = Maps.newHashMap();
            for (int i = 0; i < filePaths.size(); i++) {
                String k = "local" + i, filePath = filePaths.get(i);
                RemoteFileInfo fileInfo = new RemoteFileInfo();
                fileInfo.setFileName(Paths.get(filePath).getFileName().toString());
                fileInfo.setFilePath(filePath);
                fileInfo.setLocation("local");
                fileInfo.setLogId(logId);
                infoMap.put(k, fileInfo);
            }
            log.info("MonthlyDataZQMailJob_infoMap:{}", infoMap.toString());
            Cron cron = cronService.getById(jobId);
            log.info("MonthlyDataZQMailJob_cron:{}", cron);
            List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(), infoMap);
            log.info("MonthlyDataZQMailJob_sendMailInfos1:{}", sendMailInfos);
            sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
            log.info("MonthlyDataZQMailJob_sendMailInfos2:{}", sendMailInfos);
            if (CollUtil.isEmpty(sendMailInfos)) {

                //邮件发送日志记录
                logMailSendMonthlyDataMapper.insert(new LogMailSendMonthlyDataEntity()
                        .setId(IdWorker.getIdStr())
                        .setTaskName("发送债权投资计划邮件")
                        .setSendType("手工")
                        .setStatus(MailStatus.FAILED.name())
                        .setDataDate(dataDate)
                        .setUserId(userId)
                        .setUserName(userName)
                        .setCreateTime(new Date())
                        .setStep("MonthlyDataZQMailJob发送邮件异常:sendMailInfos为空")
                );

                updateSendStatus(dataDate, MailStatus.FAILED.name());
                return;
            }
            boolean flag = false;
            for (SendMailInfo mailInfo : sendMailInfos) {
                if (MailStatus.FAILED.name().equals(mailInfo.getStatus())) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                updateSendStatus(dataDate, MailStatus.FAILED.name());
            } else {
                updateSendStatus(dataDate, MailStatus.SUCCESS.name());
            }
            List<FuncDataToMailEntity> entities = Lists.newArrayList();
            for (SendMailInfo mailInfo : sendMailInfos) {
                FuncDataToMailEntity entity = new FuncDataToMailEntity();
                entity.setId(IdUtil.getSnowflakeNextIdStr())
                        .setMailId(mailInfo.getMailId())
                        .setMailSendLogId(mailInfo.getMailSendLogId())
                        .setDataDate(dataDate)
                        .setMailStatus(mailInfo.getStatus())
                        .setFuncType("monthlyData")
                        .setDataType(MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                        .setCreateTime(new Date());
                entities.add(entity);
            }
            log.info("MonthlyDataZQMailJob_FuncDataToMail_entities:{}", entities);
            funcDataToMailService.saveBatch(entities);

            for (SendMailInfo sendMailInfo : sendMailInfos) {
                logMailSendMonthlyDataMapper.insert(new LogMailSendMonthlyDataEntity()
                        .setId(IdWorker.getIdStr())
                        .setTaskName("发送债权投资计划邮件")
                        .setDataDate(dataDate)
                        .setSendType("手工")
                        .setStatus(sendMailInfo.getStatus())
                        .setSendTime(new Date())
                        .setMailId(sendMailInfo.getMailId())
                        .setMailSendLogId(sendMailInfo.getMailSendLogId())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setCreateTime(new Date())
                );
            }

        } catch (Exception e) {
            updateSendStatus(dataDate, MailStatus.FAILED.name());
            log.error("MonthlyDataZQMailJob_doExecute_error:{},{}", e, e.getMessage());

            //邮件发送日志记录
            logMailSendMonthlyDataMapper.insert(new LogMailSendMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setTaskName("发送债权投资计划邮件")
                    .setSendType("手工")
                    .setStatus(MailStatus.FAILED.name())
                    .setDataDate(dataDate)
                    .setUserId(userId)
                    .setUserName(userName)
                    .setCreateTime(new Date())
                    .setStep("MonthlyDataZQMailJob执行异常:" + e.getMessage())
            );

        }
    }

    public void updateSendStatus(String dataDate, String mailSendStatus) {
        monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                .set(MonthlyDataEntity::getMailSendStatus, mailSendStatus)
                .set(MonthlyDataEntity::getUpdateTime, new Date())
                .set(MonthlyDataEntity::getMailSendTime, new Date()));
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("月度数据-债权-开始执行邮件发送");
            MonthlyDataZQMailJob mailSendJob = (MonthlyDataZQMailJob) AopContext.currentProxy();
            mailSendJob.doExecute(context);
        } else {
            log.error("月度数据-债权-无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("MonthlyDataZQMailJob", MonthlyDataZQMailJob.class);
    }
}
