package cn.sdata.om.al.job;

import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.entity.monthlyData.MonthlyDataEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.service.monthlyData.MonthlyDataService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

/**
 * 月度数据-债权投资计划净值rpa
 *
 * <AUTHOR>
 * @Date 2025/4/8 9:24
 * @Version 1.0
 */
@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class MonthlyDataZQTZJHJZRpaJob implements QuartzJobBean {

    private final CronService cronService;

    private final RpaExecuteService rpaExecuteService;

    private final MonthlyDataService monthlyDataService;

    private final BaseCronLogService baseCronLogService;

    @Override
    public void doExecute(JobExecutionContext context) {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        try {
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = mergedJobDataMap.getString(CronConstant.LOG_ID),
                    startDate = mergedJobDataMap.getString(CronConstant.START_DATE),
                    endDate = mergedJobDataMap.getString(CronConstant.END_DATE),
                    dataDate = mergedJobDataMap.getString(SYSTEM_DATE_NAME);

            try {
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("MonthlyDataZQTZJHJZRpaJob_update_executeMethod:{},{}", e, e.getMessage());
            }

            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, startDate);
            flowExtendParams.put(RPA_END_DATE_NAME, endDate);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);

            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo.getCron());
            param.put(LOG_ID, logId);
            param.put(RPA_START_DATE_NAME, startDate);
            param.put(RPA_END_DATE_NAME, endDate);
            param.put(CronConstant.EXECUTOR, mergedJobDataMap.getString(CronConstant.EXECUTOR));
            param.put(SYSTEM_DATE_NAME, dataDate);
            param.put("userId", mergedJobDataMap.getString("userId"));
            param.put("userName", mergedJobDataMap.getString("userName"));
            param.put("rpaExecLogId", rpaExecLog.getId());
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
        } catch (Exception e) {
            monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, mergedJobDataMap.getString(SYSTEM_DATE_NAME))
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                    .set(MonthlyDataEntity::getDownloadStatus, 2)
                    .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZRpaJob_doExecute_error")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, mergedJobDataMap.getString(CronConstant.EXECUTOR))
            );
            log.error("MonthlyDataZQTZJHJZRpaJob_doExecute_error:{},{}", e, e.getMessage());
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("月度数据-债权投资计划净值-开始执行MonthlyDataZQTZJHJZRpaJob任务");
            MonthlyDataZQTZJHJZRpaJob job = (MonthlyDataZQTZJHJZRpaJob) AopContext.currentProxy();
            job.doExecute(context);
        } else {
            log.error("月度数据-债权投资计划净值-无context");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("MonthlyDataZQTZJHJZRpaJob", MonthlyDataZQTZJHJZRpaJob.class);
    }
}
