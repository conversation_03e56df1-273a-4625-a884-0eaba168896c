package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.sdata.om.al.controller.MonthlySettlementController;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.impl.MonthlySettlementServiceImpl;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class MonthlySettlementJob implements QuartzJobBean {

    private final CronService cronService;

    private MonthlySettlementController monthlySettlementController;

    private MonthlySettlementServiceImpl.OrdersAndFlowConfig ordersAndFlowConfig;

    @Autowired
    public void setOrdersAndFlowConfig(MonthlySettlementServiceImpl.OrdersAndFlowConfig ordersAndFlowConfig) {
        this.ordersAndFlowConfig = ordersAndFlowConfig;
    }

    @Autowired
    public void setMonthlySettlementController(MonthlySettlementController monthlySettlementController) {
        this.monthlySettlementController = monthlySettlementController;
    }

    @Override
    public void doExecute(JobExecutionContext context) {
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("---------开始调度月结自动任务");
        String dataDate = cronService.getFormatDataDate(context);
        if (dataDate == null) {
            return;
        }
        log.info("--------月结调度 dataDate = {}", dataDate);
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        Cron cron = cronService.getById(jobId);
        if (cron == null) {
            BusinessException.throwException("任务没有找到");
        }
        Integer id = cron.getFlowId();
        log.info("--------月结调度 flowId = {}", id);
        Map<String, String> config = ordersAndFlowConfig.getConfig();
        Map<String, String> reverse = MapUtil.reverse(config);
        String order = reverse.get(id + "");
        log.info("--------月结调度 order = {}", order);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("username", DEFAULT_USERNAME);
        jsonObject.put("date", DateUtil.format(DateUtil.parseDate(dataDate), "yyyy-MM"));
        jsonObject.put("orders", CollectionUtil.newArrayList(order));
        jsonObject.put("type", "AUTO");
        log.info("--------月结调度最终传参为 = {}", jsonObject);
        monthlySettlementController.generateFiles(jsonObject);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("MonthlySettlementJob", MonthlySettlementJob.class);
    }
}
