package cn.sdata.om.al.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.MonthlySettlementList;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.MonthlySettlementMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.LogMSUtil;
import cn.sdata.om.al.utils.OmFileUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
@Component
@AllArgsConstructor
public class MonthlySettlementSendMailJob implements QuartzJobBean {

    private final CronService cronService;

    private final MailInfoService mailInfoService;

    private BaseCronLogMapper baseCronLogMapper;

    @Autowired
    public void setBaseCronLogMapper(BaseCronLogMapper baseCronLogMapper) {
        this.baseCronLogMapper = baseCronLogMapper;
    }

    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        Objects.requireNonNull(context, "MonthlySettlementSendMailJob JobExecutionContext is null");
        MonthlySettlementMapper monthlySettlementMapper = SpringUtil.getBean(MonthlySettlementMapper.class);
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String dataDate = (String) mergedJobDataMap.get("date");
        String username = (String) mergedJobDataMap.get("username");
        BaseCronLog baseCronLog = new BaseCronLog();
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId(jobId);
        baseCronLog.setExecutor(StringUtils.isNotBlank(username) ? username : DEFAULT_USERNAME);
        baseCronLog.setExecuteMethod("MANUAL");
        baseCronLog.setDataDate(dataDate.replaceAll("-", "年").concat("月"));
        baseCronLogMapper.insert(baseCronLog);
        List<String> orders = (List<String>) mergedJobDataMap.get("orders");
        List<MonthlySettlementList> settlementLists = (List<MonthlySettlementList>) mergedJobDataMap.get("settlementLists");
        String executorStr = username;
        CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {

            try {
                File file = FileUtil.createTempFile("月结数据", ".7z", true);
                file = FileUtil.rename(file, "I39月度结账数据-" + dataDate.replaceAll("-", ".") + "." + FileUtil.extName(file), true);
                OmFileUtil.getZipFile(settlementLists, file);
                log.info("月结邮件发送任务执行...");
                Cron cron = cronService.getById(jobId);
                Map<String, RemoteFileInfo> infoMap = Maps.newHashMap();
                RemoteFileInfo fileInfo = new RemoteFileInfo();
                fileInfo.setLocation("local");
                fileInfo.setFileName(file.getName());
                fileInfo.setFilePath(file.getAbsolutePath());
                infoMap.put("monthlySettlementParam", fileInfo);
                log.info("******月结邮件发送任务执行参数为:{}", fileInfo);
                log.info("开始异步执行发送邮件...");
                List<SendMailInfo> composeByConfig = mailInfoService.composeByConfig(cron, dataDate, Set.of(), infoMap);
                List<SendMailInfo> sendMailInfos = mailInfoService.doSendMailInfo(composeByConfig);
                // List<SendMailInfo> sendMailInfos = mailInfoService.doSendMailInfo(cron, dataDate, Set.of(), infoMap);
                baseCronLog.setEndDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                if (CollectionUtil.isNotEmpty(sendMailInfos)) {
                    monthlySettlementMapper.updateSendStatus(dataDate, orders, MailStatus.SUCCESS.name());
                    baseCronLog.setStatus(JobStatus.COMPLETE);
                } else {
                    // 修改状态 -> 邮件发送失败...
                    monthlySettlementMapper.updateSendStatus(dataDate, orders, MailStatus.FAILED.name());
                    baseCronLog.setStatus(JobStatus.FAILED);
                }
                for (SendMailInfo mailInfo : sendMailInfos) {
                    JSONObject params = new JSONObject();
                    params.put("rpaLogId", logId);
                    params.put("logId", IdUtil.getSnowflakeNextIdStr());
                    params.put("beginTime", DateUtil.now());
                    params.put("username", executorStr);
                    params.put("dataDate", dataDate);
                    params.put("type", "MANUAL");
                    params.put("params", JSON.toJSONString(mergedJobDataMap));
                    params.put("endTime", DateUtil.now());
                    params.put("status", CommonStatus.SUCCESS.name());
                    params.put("sendStatus", mailInfo.getStatus());
                    params.put("mailId", mailInfo.getMailId());
                    LogMSUtil.preSendMailLog(params);
                }
            } catch (Exception e) {
                e.printStackTrace();
                baseCronLog.setTaskInfo(e.getMessage());
            } finally {
                baseCronLogMapper.updateById(baseCronLog);
            }
        });
        // 修改状态 -> 邮件发送中...
        monthlySettlementMapper.updateSendStatus(dataDate, orders, MailStatus.SENDING.name());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("MonthlySettlementSendMailJob", MonthlySettlementSendMailJob.class);
    }
}
