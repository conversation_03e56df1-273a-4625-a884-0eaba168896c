package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogOpenFundConfirmationService;
import cn.sdata.om.al.service.LogOpenFundReconciliationService;
import cn.sdata.om.al.service.OpenFundConfirmationStatementService;
import cn.sdata.om.al.service.OpenFundReconciliationStatementService;
import cn.sdata.om.al.service.mail.MailInfoService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.qrtz.constant.CronConstant.OPERATOR;
import static cn.sdata.om.al.qrtz.constant.CronConstant.SEND_METHOD;

@Slf4j
@Component
public class OpenFundMailConfirmationSendJob extends OpenFundMailSendJob {

    private final MailInfoService mailInfoService;
    private final CronService cronService;

    public OpenFundMailConfirmationSendJob(MailInfoService mailInfoService, CronService cronService, OpenFundConfirmationStatementService openFundConfirmationStatementService, OpenFundReconciliationStatementService openFundReconciliationStatementService, LogOpenFundConfirmationService logOpenFundConfirmationService, LogOpenFundReconciliationService logOpenFundReconciliationService, MailInfoService mailInfoService1, CronService cronService1, OpenFundConfirmationStatementService openFundConfirmationStatementService1, OpenFundReconciliationStatementService openFundReconciliationStatementService1, LogOpenFundConfirmationService logOpenFundConfirmationService1, LogOpenFundReconciliationService logOpenFundReconciliationService1) {
        super(mailInfoService, cronService, openFundConfirmationStatementService, openFundReconciliationStatementService, logOpenFundConfirmationService, logOpenFundReconciliationService);
        this.mailInfoService = mailInfoService1;
        this.cronService = cronService1;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(@NonNull JobExecutionContext context) {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        /*if (dataDate != null) {
            dataDate = cronService.getFormatDataDate(context);
        }*/
        if (StringUtils.isBlank(dataDate)) {
            dataDate = cronService.getFormatDataDate(context);
        }
        String operator = (String) mergedJobDataMap.get(OPERATOR);
        SendMethod sendMethod = (SendMethod) mergedJobDataMap.get(SEND_METHOD);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
        String statementType = (String) mergedJobDataMap.get("STATEMENT_TYPE"); // 获取单据类型
        Cron cron = cronService.getById(jobId);
        List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);
        sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
        Set<String> openFundIds = infoMap.values().stream().map(RemoteFileInfo::getOpenFundId).reduce((set1, set2) -> {
            set1.addAll(set2);
            return set1;
        }).orElse(new HashSet<>());
        if (openFundIds.isEmpty()) {
            log.info("开放式基金-没有邮件需要发送");
            return;
        }

        // 根据单据类型处理不同的逻辑
        if ("CONFIRMATION".equals(statementType)) {
            log.info("处理确认单邮件发送，单据数量: {}", openFundIds.size());
            handleConfirmationStatements(sendMailInfos, infoMap, openFundIds, cron, operator, sendMethod);
        } else if ("RECONCILIATION".equals(statementType)) {
            log.info("处理对账单邮件发送，单据数量: {}", openFundIds.size());
            handleReconciliationStatements(sendMailInfos, infoMap, openFundIds, cron, operator, sendMethod);
        } else {
            log.error("未指定单据类型或类型错误: {}", statementType);
            throw new IllegalArgumentException("必须指定单据类型: CONFIRMATION 或 RECONCILIATION");
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("开始执行邮件发送");
            OpenFundMailConfirmationSendJob openFundMailSendJob = (OpenFundMailConfirmationSendJob) AopContext.currentProxy();
            openFundMailSendJob.doExecute(context);
        } else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("OpenFundMailConfirmationSendJob", OpenFundMailConfirmationSendJob.class);
    }
}
