package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.OpenFundConfirmationStatement;
import cn.sdata.om.al.entity.OpenFundReconciliationStatement;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogOpenFundConfirmationService;
import cn.sdata.om.al.service.LogOpenFundReconciliationService;
import cn.sdata.om.al.service.OpenFundConfirmationStatementService;
import cn.sdata.om.al.service.OpenFundReconciliationStatementService;
import cn.sdata.om.al.service.mail.MailInfoService;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;
import static cn.sdata.om.al.constant.JobConstant.*;
import static cn.sdata.om.al.qrtz.constant.CronConstant.OPERATOR;
import static cn.sdata.om.al.qrtz.constant.CronConstant.SEND_METHOD;

@Slf4j
@Component
@AllArgsConstructor
public class OpenFundMailSendJob implements QuartzJobBean {

    private final MailInfoService mailInfoService;
    private final CronService cronService;
    private final OpenFundConfirmationStatementService openFundConfirmationStatementService;
    private final OpenFundReconciliationStatementService openFundReconciliationStatementService;
    private final LogOpenFundConfirmationService logOpenFundConfirmationService;
    private final LogOpenFundReconciliationService logOpenFundReconciliationService;

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(@NonNull JobExecutionContext context) {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        /*if (dataDate != null) {
            dataDate = cronService.getFormatDataDate(context);
        }*/
        if (StringUtils.isBlank(dataDate)) {
            dataDate = cronService.getFormatDataDate(context);
        }
        String operator = (String) mergedJobDataMap.get(OPERATOR);
        SendMethod sendMethod = (SendMethod) mergedJobDataMap.get(SEND_METHOD);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
        String statementType = (String) mergedJobDataMap.get("STATEMENT_TYPE"); // 获取单据类型
        Cron cron = cronService.getById(jobId);
        List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);
        sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
        Set<String> openFundIds = infoMap.values().stream().map(RemoteFileInfo::getOpenFundId).reduce((set1, set2) -> {
            set1.addAll(set2);
            return set1;
        }).orElse(new HashSet<>());
        if (openFundIds.isEmpty()) {
            log.info("开放式基金-没有邮件需要发送");
            return;
        }

        // 根据单据类型处理不同的逻辑
        if ("CONFIRMATION".equals(statementType)) {
            log.info("处理确认单邮件发送，单据数量: {}", openFundIds.size());
            handleConfirmationStatements(sendMailInfos, infoMap, openFundIds, cron, operator, sendMethod);
        } else if ("RECONCILIATION".equals(statementType)) {
            log.info("处理对账单邮件发送，单据数量: {}", openFundIds.size());
            handleReconciliationStatements(sendMailInfos, infoMap, openFundIds, cron, operator, sendMethod);
        } else {
            log.error("未指定单据类型或类型错误: {}", statementType);
            throw new IllegalArgumentException("必须指定单据类型: CONFIRMATION 或 RECONCILIATION");
        }
    }

    /**
     * 处理确认单邮件发送
     */
    protected void handleConfirmationStatements(List<SendMailInfo> sendMailInfos, Map<String, RemoteFileInfo> infoMap,
                                              Set<String> openFundIds, Cron cron, String operator, SendMethod sendMethod) {
        Map<String, OpenFundConfirmationStatement> confirmationResult = openFundConfirmationStatementService.listByIds(openFundIds).stream()
                .collect(Collectors.toMap(OpenFundConfirmationStatement::getId,
                        statement -> statement, (oldOne, newOne) -> newOne));

        sendMailInfos.stream().filter(sendMailInfo -> MailStatus.SUCCESS.name().equals(sendMailInfo.getStatus())).forEach(sendMailInfo -> {
            dealConfirmationAndLog(sendMailInfo, infoMap, confirmationResult, MailStatus.SUCCESS, cron, operator, sendMethod);
        });
        sendMailInfos.stream().filter(sendMailInfo -> MailStatus.FAILED.name().equals(sendMailInfo.getStatus())).forEach(sendMailInfo -> {
            dealConfirmationAndLog(sendMailInfo, infoMap, confirmationResult, MailStatus.FAILED, cron, operator, sendMethod);
        });

        if (!confirmationResult.isEmpty()) {
            openFundConfirmationStatementService.saveOrUpdateBatch(confirmationResult.values());
        }
    }

    /**
     * 处理对账单邮件发送
     */
    protected void handleReconciliationStatements(List<SendMailInfo> sendMailInfos, Map<String, RemoteFileInfo> infoMap,
                                                Set<String> openFundIds, Cron cron, String operator, SendMethod sendMethod) {
        Map<String, OpenFundReconciliationStatement> reconciliationResult = openFundReconciliationStatementService.listByIds(openFundIds).stream()
                .collect(Collectors.toMap(OpenFundReconciliationStatement::getId,
                        statement -> statement, (oldOne, newOne) -> newOne));

        sendMailInfos.stream().filter(sendMailInfo -> MailStatus.SUCCESS.name().equals(sendMailInfo.getStatus())).forEach(sendMailInfo -> {
            dealReconciliationAndLog(sendMailInfo, infoMap, reconciliationResult, MailStatus.SUCCESS, cron, operator, sendMethod);
        });
        sendMailInfos.stream().filter(sendMailInfo -> MailStatus.FAILED.name().equals(sendMailInfo.getStatus())).forEach(sendMailInfo -> {
            dealReconciliationAndLog(sendMailInfo, infoMap, reconciliationResult, MailStatus.FAILED, cron, operator, sendMethod);
        });

        if (!reconciliationResult.isEmpty()) {
            openFundReconciliationStatementService.saveOrUpdateBatch(reconciliationResult.values());
        }
    }

    /**
     * 处理确认单邮件发送和日志记录
     */
    protected void dealConfirmationAndLog(SendMailInfo sendMailInfo, Map<String, RemoteFileInfo> infoMap,
                                        Map<String, OpenFundConfirmationStatement> confirmationResult,
                                        MailStatus status, Cron cron, String operator, SendMethod sendMethod) {
        List<String> sentProductIds = sendMailInfo.getProductIds();
        for (String sentProductId : sentProductIds) {
            RemoteFileInfo remoteFileInfo = infoMap.get(sentProductId);
            if (remoteFileInfo != null) {
                Set<String> openFundIdsInFile = remoteFileInfo.getOpenFundId();
                for (String openFundIdInFile : openFundIdsInFile) {
                    OpenFundConfirmationStatement confirmationStatement = confirmationResult.get(openFundIdInFile);
                    if (confirmationStatement != null) {
                        confirmationStatement.setEmailSentStatus(status);
                        // 为确认单记录邮件发送日志
                        logConfirmationMailSendResult(openFundIdInFile, sendMailInfo, status.name(), cron, operator, sendMethod);
                    }
                }
            }
        }
    }

    /**
     * 处理对账单邮件发送和日志记录
     */
    protected void dealReconciliationAndLog(SendMailInfo sendMailInfo, Map<String, RemoteFileInfo> infoMap,
                                          Map<String, OpenFundReconciliationStatement> reconciliationResult,
                                          MailStatus status, Cron cron, String operator, SendMethod sendMethod) {
        List<String> sentProductIds = sendMailInfo.getProductIds();
        for (String sentProductId : sentProductIds) {
            RemoteFileInfo remoteFileInfo = infoMap.get(sentProductId);
            if (remoteFileInfo != null) {
                Set<String> openFundIdsInFile = remoteFileInfo.getOpenFundId();
                for (String openFundIdInFile : openFundIdsInFile) {
                    OpenFundReconciliationStatement reconciliationStatement = reconciliationResult.get(openFundIdInFile);
                    if (reconciliationStatement != null) {
                        reconciliationStatement.setEmailSentStatus(status);
                        // 为对账单记录邮件发送日志
                        logReconciliationMailSendResult(openFundIdInFile, sendMailInfo, status.name(), cron, operator, sendMethod);
                    }
                }
            }
        }
    }

    /**
     * 记录确认单邮件发送结果日志
     */
    protected void logConfirmationMailSendResult(String confirmationId, SendMailInfo sendMailInfo, String status, Cron cron, String operator, SendMethod sendMethod) {
        try {
            if (operator == null) {
                operator = DEFAULT_USERNAME; // 如果无法获取当前用户，使用默认值
            }

            if (sendMethod == null) {
                sendMethod = SendMethod.AUTO;
            }
            String taskName = cron != null ? cron.getJobName() : "开基确认单邮件发送";

            logOpenFundConfirmationService.logMailSend(
                    confirmationId, // 确认单ID
                    taskName, // 使用Cron的jobName作为任务名称
                    sendMethod,
                    status,
                    operator,
                    sendMailInfo.getLogId() // logId就是邮件ID，这是正确的
            );
        } catch (Exception e) {
            log.error("记录确认单邮件发送日志失败", e);
        }
    }

    /**
     * 记录对账单邮件发送结果日志
     */
    protected void logReconciliationMailSendResult(String reconciliationId, SendMailInfo sendMailInfo, String status, Cron cron, String operator, SendMethod sendMethod) {
        try {
            if (operator == null) {
                operator = DEFAULT_USERNAME; // 如果无法获取当前用户，使用默认值
            }

            if (sendMethod == null) {
                sendMethod = SendMethod.AUTO;
            }
            String taskName = cron != null ? cron.getJobName() : "开基对账单邮件发送";

            logOpenFundReconciliationService.logMailSend(
                    reconciliationId, // 对账单ID
                    taskName, // 使用Cron的jobName作为任务名称
                    sendMethod,
                    status,
                    operator,
                    sendMailInfo.getLogId() // logId就是邮件ID，这是正确的
            );
        } catch (Exception e) {
            log.error("记录对账单邮件发送日志失败", e);
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("开始执行邮件发送");
            OpenFundMailSendJob openFundMailSendJob = (OpenFundMailSendJob) AopContext.currentProxy();
            openFundMailSendJob.doExecute(context);
        } else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("OpenFundMailSendJob", OpenFundMailSendJob.class);
    }
}
