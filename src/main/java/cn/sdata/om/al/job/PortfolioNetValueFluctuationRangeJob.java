package cn.sdata.om.al.job;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.service.CronService;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class PortfolioNetValueFluctuationRangeJob implements QuartzJobBean {

    private final CronService cronService;
    private final PortfolioNetValueFluctuationService fluctuationService;

    @Override
    public void doExecute(JobExecutionContext context) {
        // 预留，实际逻辑在execute
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String startDate = (String) mergedJobDataMap.get(CronConstant.START_DATE);
        String endDate = (String) mergedJobDataMap.get(CronConstant.END_DATE);
        /*List<String> betweenToStr = DateUtils.getDatesBetweenToStr(startDate, endDate);
        if (CollectionUtil.isNotEmpty(betweenToStr)) {
            for (String date : betweenToStr) {
                log.info("开始执行日期={}的同步任务", date);
                fluctuationService.processFluctuationForDate(date);
            }
        }*/
        PortfolioNetValueFluctuationService bean = SpringUtil.getBean(PortfolioNetValueFluctuationService.class);
        Map<String, String> map = bean.processFluctuationForDateRange(startDate, endDate);
        if (MapUtil.isNotEmpty(map)) {
            log.info("任务执行结果为:{}", JSONObject.toJSONString(map));
        }
        // String today = cronService.autoGetManualDataDate(context);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("PortfolioNetValueFluctuationRangeJob", PortfolioNetValueFluctuationRangeJob.class);
    }
}
