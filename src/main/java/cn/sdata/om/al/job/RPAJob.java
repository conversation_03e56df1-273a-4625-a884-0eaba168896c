package cn.sdata.om.al.job;

import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.*;
import cn.sdata.om.al.utils.LogCCRUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;
import static cn.sdata.om.al.utils.StringUtil.getNowDateStr;

@Slf4j
@Component
@AllArgsConstructor
@TradeDay
public class RPAJob implements QuartzJobBean {

    private final CronService cronService;

    private final RpaExecuteService rpaExecuteService;


    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("RPAJob", RPAJob.class);
    }

    @Override
    public void doExecute(JobExecutionContext context) {
        String dataDate = cronService.getFormatDataDate(context);
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
        FlowList flow = cronInfo.getFlow();
        String logId = cronInfo.getLogId();
        String startDate = (String) mergedJobDataMap.get(CronConstant.START_DATE);
        String endDate = (String) mergedJobDataMap.get(CronConstant.END_DATE);
        if (startDate == null || endDate == null) {
            startDate = dataDate;
            endDate = getNowDateStr(BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN);
        }
        Map<String, Object> flowExtendParams = new HashMap<>();
        flowExtendParams.put(RPA_START_DATE_NAME, startDate);
        flowExtendParams.put(RPA_END_DATE_NAME, endDate);
        Map<String, Object> param = new HashMap<>();
        param.put(RPAConstant.CRON, cronInfo.getCron());
        param.put(LOG_ID, logId);
        param.put(RPA_START_DATE_NAME, startDate);
        param.put(RPA_END_DATE_NAME, endDate);
        param.put(SYSTEM_DATE_NAME, dataDate);
        if (flow.getId() == 1) {
            // 如果任务是 资金清算的自动任务 则新增日志
            JSONObject logParams = new JSONObject();
            logParams.put("rpaLogId", logId);
            logParams.put("username", DEFAULT_USERNAME);
            logParams.put("dataDate", dataDate);
            JSONObject rpaParams = new JSONObject();
            rpaParams.put("flow", flow);
            rpaParams.put("flowExtendParams", flowExtendParams);
            logParams.put("rpaParam", JSONObject.toJSONString(rpaParams));
            logParams.put("type", "AUTO");
            logParams.put("logId", IdUtil.getSnowflakeNextIdStr());
            logParams.put("params", JSONObject.toJSONString(mergedJobDataMap));
            LogCCRUtil.preRpaLog(logParams);
        }
        RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
        rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
        if (dataDate == null) {
            return;
        }
        try {
            log.info("RPA任务完成");
        } catch (Exception e) {
            throw new TaskException(e);
        }
    }

    @Override
    public void execute(JobExecutionContext context) {
        if (context != null) {
            log.info("开始执行RPA任务");
            RPAJob rpaJob = (RPAJob) AopContext.currentProxy();
            rpaJob.doExecute(context);
        } else {
            log.error("无JobExecutionContext");
        }
    }
}
