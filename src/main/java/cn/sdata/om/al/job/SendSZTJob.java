package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.service.NetValueDisclosureService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.sdata.om.al.constant.JobConstant.*;

@Slf4j
@Component
@AllArgsConstructor
public class SendSZTJob implements QuartzJobBean {

    private NetValueDisclosureService netValueDisclosureService;

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        if (context == null) {
            throw new TaskException("context为空");
        }
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        String dataDate = (String) jobDataMap.get(DATA_DATE);
        List<NetValueDisclosure> products = (List<NetValueDisclosure>)jobDataMap.get(NET_VALUE_DISCLOSURE_LIST);
        if (products == null || products.isEmpty()) {
            throw new TaskException("需要下载的产品为空");
        }
        netValueDisclosureService.doSendSZT(dataDate, products, context);
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            SendSZTJob syncValuationTableJob = (SendSZTJob) AopContext.currentProxy();
            syncValuationTableJob.doExecute(context);
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("SendSZTJob", SendSZTJob.class);
    }
}
