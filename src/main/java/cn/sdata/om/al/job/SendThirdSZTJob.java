package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.entity.CronInfo;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.utils.ThirdFileUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.*;

/**
 * 第三方深证通发送Job
 * 使用ThirdFileUtil生成文件，上传到远程文件夹，然后将上传完成的路径和配置的STZPath传给RPA
 */
@Slf4j
@Component
@AllArgsConstructor
public class SendThirdSZTJob implements QuartzJobBean {

    private final NetValueDisclosureService netValueDisclosureService;
    private final ThirdFileUtil thirdFileUtil;
    private final SMBService smbService;
    private final RpaExecuteService rpaExecuteService;
    private final CronService cronService;

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        if (context == null) {
            throw new TaskException("context为空");
        }
        
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        String dataDate = (String) jobDataMap.get(DATA_DATE);
        Map<String, List<NetValueDisclosure>> netValueDisclosureThirdMap = 
            (Map<String, List<NetValueDisclosure>>) jobDataMap.get(NET_VALUE_DISCLOSURE_THIRD_MAP);
        Map<String, String> sztPathMap = (Map<String, String>) jobDataMap.get(SZT_PATH);
        
        if (netValueDisclosureThirdMap == null || netValueDisclosureThirdMap.isEmpty()) {
            throw new TaskException("需要处理的第三方净值披露数据为空");
        }
        
        if (sztPathMap == null || sztPathMap.isEmpty()) {
            throw new TaskException("深证通路径配置为空");
        }
        
        log.info("开始执行第三方深证通发送任务, 数据日期: {}, 投资人列表: {}, 深证通路径: {}",
                dataDate, netValueDisclosureThirdMap.keySet(), sztPathMap);
        
        doSendThirdSZT(dataDate, netValueDisclosureThirdMap, sztPathMap, context);
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            SendThirdSZTJob sendThirdSZTJob = (SendThirdSZTJob) AopContext.currentProxy();
            sendThirdSZTJob.doExecute(context);
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("SendThirdSZTJob", SendThirdSZTJob.class);
    }

    /**
     * 执行第三方深证通发送逻辑
     *
     * @param dataDate 数据日期
     * @param netValueDisclosureThirdMap 投资人净值披露数据映射 (投资人 -> 净值披露数据列表)
     * @param sztPathMap 深证通路径映射
     * @param context Job执行上下文
     */
    private void doSendThirdSZT(String dataDate, 
                               Map<String, List<NetValueDisclosure>> netValueDisclosureThirdMap,
                               Map<String, String> sztPathMap,
                               JobExecutionContext context) {
        try {
            // 遍历每个投资人
            for (Map.Entry<String, List<NetValueDisclosure>> entry : netValueDisclosureThirdMap.entrySet()) {
                String investorName = entry.getKey();
                List<NetValueDisclosure> netValueDisclosures = entry.getValue();

                if (netValueDisclosures == null || netValueDisclosures.isEmpty()) {
                    log.warn("投资人 {} 的净值披露数据为空，跳过处理", investorName);
                    continue;
                }

                log.info("开始处理投资人: {}, 净值披露数据数量: {}", investorName, netValueDisclosures.size());
                
                // 转换为ValuationTableData格式
                List<ValuationTableData> valuationTableDataList = convertToValuationTableData(netValueDisclosures);
                
                // 使用ThirdFileUtil生成文件
                byte[] fileBytes = thirdFileUtil.getAttachment(valuationTableDataList, investorName);

                // 生成文件名
                String fileName = generateFileName(investorName, dataDate);

                // 上传到远程文件夹
                String remotePath = getRemotePath(dataDate);
                smbService.upload(remotePath, fileName, fileBytes);

                log.info("文件上传成功: 投资人={}, 文件名={}, 远程路径={}", investorName, fileName, remotePath);

                // 获取完整的上传路径
                String fullUploadPath = remotePath + "\\" + fileName;

                // 获取对应的深证通路径
                String sztPath = getSztPathForInvestor(netValueDisclosures, sztPathMap);

                // 调用RPA处理
                callRpaForThirdSZT(dataDate, investorName, fullUploadPath, sztPath, context, netValueDisclosures);
            }
            
            log.info("第三方深证通发送任务执行完成");
            
        } catch (Exception e) {
            log.error("执行第三方深证通发送任务失败", e);
            throw new TaskException("执行第三方深证通发送任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将NetValueDisclosure转换为ValuationTableData
     */
    private List<ValuationTableData> convertToValuationTableData(List<NetValueDisclosure> netValueDisclosures) {
        // 提取产品ID列表
        List<String> productIds = netValueDisclosures.stream()
                .map(NetValueDisclosure::getProductId)
                .distinct()
                .collect(Collectors.toList());

        // 获取估值日期
        String valuationDate = netValueDisclosures.isEmpty() ? null : netValueDisclosures.get(0).getValuationDate();

        // 使用NetValueDisclosureService获取净值数据
        return netValueDisclosureService.getNetValueFromValuation(valuationDate, productIds);
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String investorName, String dataDate) {
        String dateStr = dataDate.replace("-", "");
        return String.format("%s_净值对接_%s.xlsx", investorName, dateStr);
    }

    /**
     * 获取远程上传路径
     */
    private String getRemotePath(String dataDate) {
        String dateStr = dataDate.replace("-", "");
        return String.format("third_szt\\%s", dateStr);
    }

    /**
     * 获取投资人对应的深证通路径
     */
    private String getSztPathForInvestor(List<NetValueDisclosure> netValueDisclosures,
                                        Map<String, String> sztPathMap) {
        // 从净值披露数据中获取产品ID，然后查找对应的深证通路径
        for (NetValueDisclosure disclosure : netValueDisclosures) {
            String productId = disclosure.getProductId();
            String sztPath = sztPathMap.get(productId);
            if (sztPath != null && !sztPath.isEmpty()) {
                return sztPath;
            }
        }
        
        // 如果没有找到特定的路径，返回默认路径或第一个可用路径
        return sztPathMap.values().stream().findFirst().orElse("");
    }

    /**
     * 调用RPA处理第三方深证通
     */
    private void callRpaForThirdSZT(String dataDate, String investorName,
                                   String uploadPath, String sztPath,
                                   JobExecutionContext context, List<NetValueDisclosure> netValueDisclosures) {
        try {
            log.info("调用RPA处理第三方深证通: 投资人={}, 上传路径={}, 深证通路径={}",
                    investorName, uploadPath, sztPath);

            // 获取Job上下文信息
            JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
            CronInfo cronInfo = cronService.getCronInfo(mergedJobDataMap);
            FlowList flow = cronInfo.getFlow();
            String logId = cronInfo.getLogId();

            // 构建RPA参数
            Map<String, Object> rpaParams = new HashMap<>();
            rpaParams.put("数据日期", dataDate.replace("-", ""));
            rpaParams.put("投资人", investorName);
            rpaParams.put("文件路径", uploadPath);
            rpaParams.put("深证通路径", sztPath);

            // 执行RPA
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, rpaParams, logId);

            // 构建回调参数
            Map<String, Object> callbackParams = new HashMap<>();
            callbackParams.put(NET_VALUE_DISCLOSURE_LIST, netValueDisclosures);
            callbackParams.put(DATA_DATE, dataDate);
            callbackParams.put("投资人", investorName);
            callbackParams.put("文件路径", uploadPath);
            callbackParams.put("深证通路径", sztPath);

            // 启动定时器监控RPA执行状态
            rpaExecuteService.startTimer(rpaExecLog, flow, callbackParams, logId);

            log.info("RPA任务已启动: 投资人={}, execId={}", investorName, rpaExecLog.getExecId());

        } catch (Exception e) {
            log.error("调用RPA处理第三方深证通失败: 投资人={}, 错误信息={}", investorName, e.getMessage(), e);
            throw new TaskException("调用RPA失败: " + e.getMessage(), e);
        }
    }
}
