package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.enums.ValuationTime;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class SyncT1NetValueDisclosureJob implements QuartzJobBean {

    private final NetValueDisclosureService netValueDisclosureService;
    private final AccountInformationService accountInformationService;


    @Override
    public void doExecute(JobExecutionContext context) {
    }

    @Override
    public void execute(JobExecutionContext context) {
        if (context != null) {
            log.info("开始执行T1净值披露任务");
            LambdaQueryWrapper<AccountInformation> accountInformationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            accountInformationLambdaQueryWrapper.eq(AccountInformation::getValuationTime, ValuationTime.T1.name());
            accountInformationLambdaQueryWrapper.eq(AccountInformation::getProductCategory, 3);
            List<String> productIds = accountInformationService.list(accountInformationLambdaQueryWrapper).stream().map(AccountInformation::getId).collect(Collectors.toList());
            if (!productIds.isEmpty()) {
                netValueDisclosureService.sync(context, productIds, ValuationTime.T1);
            }else{
                log.info("无T1净值披露数据");
            }
        }else {
            log.error("无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() {
        QuartzJobFactory.addJobClass("SyncT1NetValueDisclosureJob", SyncT1NetValueDisclosureJob.class);
    }
}
