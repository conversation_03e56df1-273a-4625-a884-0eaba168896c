package cn.sdata.om.al.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Month;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.FlowListMapper;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.utils.DateUtils;
import cn.sdata.om.al.utils.LogFYBUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Slf4j
@Component
@RequiredArgsConstructor
public class ZZPaymentReceiptDownloadRpaJob implements QuartzJobBean {

    private RpaExecuteService rpaExecuteService;

    private BaseCronLogService baseCronLogService;

    private CronService cronService;

    @Autowired
    public void setRpaExecuteService(RpaExecuteService rpaExecuteService) {
        this.rpaExecuteService = rpaExecuteService;
    }

    @Autowired
    public void setBaseCronLogService(BaseCronLogService baseCronLogService) {
        this.baseCronLogService = baseCronLogService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Override
    public void doExecute(JobExecutionContext context) {

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        /*
         假设2025年
            4月的第7个工作日rpa下载一次 下载的范围是 2025年1季度
            7月的第7个工作日rpa下载一次 下载的范围是 2025年2季度
            10月的第7个工作日rpa下载一次 下载的范围是 2025年3季度
            26年1月的第7个工作日rpa下载一次 下载的范围是 2025年4季度
         */
        log.info("---------开始调度中债缴费单下载自动任务");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String beginDate = mergedJobDataMap.getString("beginDate");
        String endDate = mergedJobDataMap.getString("endDate");
        if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            DateTime date = DateUtil.date();
            int field = date.getField(DateField.MONTH);
            // 每年的 1月 4月 7月 10月的第7个工作日 开始自动下载 且传的日期是上个月的开始和结束日期
            if (field == Month.APRIL.getValue() || field == Month.JULY.getValue()
                    || field == Month.OCTOBER.getValue() || field == Month.JANUARY.getValue()) {
                LocalDate today = LocalDate.now();
                // 当天是否属于第7个工作日
                LocalDate firstDayOfMonth = today.withDayOfMonth(1);
                LocalDate seventhWorkday = DateUtils.findSeventhWorkday(firstDayOfMonth);
                if (!today.equals(seventhWorkday)) {
                    log.error("当天的日期不是本月的第7个工作日...");
                    return;
                }
                // 1. 计算上一季度起始月份
                int prevQuarterStartMonth = ((today.getMonthValue() - 1) / 3) * 3 + 1 - 3;
                int year = today.getYear();
                // 2. 处理跨年（若1-3月计算后月份≤0）
                if (prevQuarterStartMonth <= 0) {
                    prevQuarterStartMonth += 12;
                    year--;
                }
                // 3. 构建首日和末日
                LocalDate firstDay = LocalDate.of(year, prevQuarterStartMonth, 1);
                LocalDate lastDay = firstDay.plusMonths(2)
                        .withDayOfMonth(firstDay.plusMonths(2).lengthOfMonth());
                // 4. 格式化为"yyyy-M"（无前导零）
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M");
                beginDate = firstDay.format(formatter);
                endDate = lastDay.format(formatter);
            } else {
                log.error("当前日期不能执行此任务");
                return;
            }
        }
        log.info("下载中债缴费单的时间参数为:beginDate = {}, endDate = {}", beginDate, endDate);
        BaseCronLog baseCronLog = new BaseCronLog();
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        Objects.requireNonNull(context, "ZZPaymentReceiptDownloadRpaJob JobExecutionContext is null");
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        String logId = IdUtil.getSnowflakeNextIdStr();
        baseCronLog.setId(logId);
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLog.setTaskId(jobId);
        String username = mergedJobDataMap.getString("username");
        baseCronLog.setExecutor(StringUtils.isNotBlank(username) ? username : DEFAULT_USERNAME);
        String type = mergedJobDataMap.getString("type");
        baseCronLog.setExecuteMethod(StringUtils.isNotBlank(type) ? type : "AUTO");
        baseCronLog.setDataDate(beginDate + "-" + endDate);
        baseCronLogMapper.insert(baseCronLog);
        try {
            Cron cronInfo = cronService.getById(jobId);
            Integer flowId = cronInfo.getFlowId();
            FlowList flow = SpringUtil.getBean(FlowListMapper.class).selectById(flowId);
            if (flow == null) {
                log.error("ZZPaymentReceiptDownloadRpaJob_doExecute_error:flowList对象为空");
                BusinessException.throwException("flowList对象为空");
            }
            if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
                log.error("ZZPaymentReceiptDownloadRpaJob_doExecute_error:beginDate或者endDate为空");
                BusinessException.throwException("beginDate或者endDate为空");
            }
            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, beginDate);
            flowExtendParams.put(RPA_END_DATE_NAME, endDate);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("username", DEFAULT_USERNAME);
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("params", JSONObject.toJSONString(mergedJobDataMap));
            params.put("dataDate", beginDate + "-" + endDate);
            params.put("type", "AUTO");
            JSONObject rpaParams = new JSONObject();
            rpaParams.put("flow", flow);
            rpaParams.put("flowExtendParams", flowExtendParams);
            params.put("rpaParam", JSON.toJSONString(rpaParams));
            LogFYBUtils.preRpaLog(params);
            RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flow, flowExtendParams, logId);
            Map<String, Object> param = new HashMap<>();
            param.put(RPAConstant.CRON, cronInfo);
            param.put(LOG_ID, logId);
            param.put(SYSTEM_DATE_NAME, beginDate + "-" + endDate);
            param.put(CronConstant.EXECUTOR, DEFAULT_USERNAME);
            rpaExecuteService.startTimer(rpaExecLog, flow, param, logId);
            log.info("中债缴费单单-手动下载-发起RPA调用结束");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("ZZPaymentReceiptDownloadRpaJob_doExecute_error:{},{}", e, e.getMessage());
            LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
            uw.eq(BaseCronLog::getId, logId);
            uw.set(BaseCronLog::getStatus, JobStatus.FAILED).set(BaseCronLog::getEndDateTime, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            baseCronLogService.update(uw);
            BusinessException.throwException(e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("ZZPaymentReceiptDownloadRpaJob", ZZPaymentReceiptDownloadRpaJob.class);
    }
}
