package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.DividendAnnouncementSyncService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

/**
 * 分红公告数据同步定时任务
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class DividendAnnouncementSyncJob implements QuartzJobBean {

    private final CronService cronService;
    private final DividendAnnouncementSyncService dividendAnnouncementSyncService;

    @Override
    public void doExecute(JobExecutionContext context) {
        // 预留，实际逻辑在execute
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        String syncDate = cronService.autoGetManualDataDate(context);
        log.info("开始执行分红公告数据同步定时任务，同步日期：{}", syncDate);

        try {
            // 执行数据同步
            R<String> syncResult = dividendAnnouncementSyncService.syncJuyuanDividendData(syncDate);
            if (syncResult.isSuccess()) {
                log.info("分红公告数据同步成功：{}", syncResult.getMessage());
            } else {
                log.error("分红公告数据同步失败：{}", syncResult.getMessage());
            }

        } catch (Exception e) {
            log.error("分红公告数据同步定时任务执行异常", e);
            throw new JobExecutionException(e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("DividendAnnouncementSyncJob", DividendAnnouncementSyncJob.class);
    }
}
