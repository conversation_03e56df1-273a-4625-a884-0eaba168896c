package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.MonthEndAndNonStandardService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;


/**
 * 月末存款及非标行情检查
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class MonthEndAndNonStandardEmailJob implements QuartzJobBean {

    private final CronService cronService;
    private final MonthEndAndNonStandardService monthEndAndNonStandardService;

    @Override
    public void doExecute(JobExecutionContext context) {
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        //月初第二个银行间工作日，汇总处标红，并发送邮件提醒
        monthEndAndNonStandardService.sendMonthEndAndNonStandardEmail();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("MonthEndAndNonStandardEmailJob", MonthEndAndNonStandardEmailJob.class);
    }
}