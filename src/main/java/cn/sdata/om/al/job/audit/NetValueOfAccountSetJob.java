package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.NetValueOfAccountSetService;
import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.audit.service.TjjjzService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class NetValueOfAccountSetJob implements QuartzJobBean {

    private final CronService cronService;

    private final TjjjzService tjjzService;

    private final NetValueOfAccountSetService netValueOfAccountSetService;

    @Override
    public void doExecute(JobExecutionContext context) {
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String today = cronService.autoGetManualDataDate(context);
        //同步净值信息到MySQL
        tjjzService.syncValuationInfo(mergedJobDataMap,today);
        //根据净值信息生成组合产品净值波动
        netValueOfAccountSetService.syncNetValueFluctuation(today);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("NetValueOfAccountSetJob", NetValueOfAccountSetJob.class);
    }
}