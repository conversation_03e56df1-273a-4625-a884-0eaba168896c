package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class PortfolioNetValueFluctuationJob implements QuartzJobBean {

    private final CronService cronService;
    private final PortfolioNetValueFluctuationService fluctuationService;

    @Override
    public void doExecute(JobExecutionContext context) {
        // 预留，实际逻辑在execute
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        String today = cronService.autoGetManualDataDate(context);
        fluctuationService.processFluctuationForDate(today);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("PortfolioNetValueFluctuationJob", PortfolioNetValueFluctuationJob.class);
    }
} 