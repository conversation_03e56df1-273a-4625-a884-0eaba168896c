package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.service.PortfolioNetValueFluctuationService;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailInfoService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.JobConstant.*;

@Component
@Slf4j
@AllArgsConstructor
public class PortfolioNetValueRetracementMailJob implements QuartzJobBean {

    private final MailInfoService mailInfoService;
    private final CronService cronService;
    private final PortfolioNetValueFluctuationService fluctuationService;

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
        String specifiedHandler = (String) mergedJobDataMap.get("specifiedHandler");
        Map<String, Object> extraData = (Map<String, Object>) mergedJobDataMap.get("extraData");
        Cron cron = cronService.getById(jobId);
        // 新增属性
        cron.setExtraData(extraData);
        cron.setSpecifiedHandler(specifiedHandler);
        List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);

        sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
        for (SendMailInfo sendMailInfo : sendMailInfos) {
            List<String> sentProductIds = sendMailInfo.getProductIds();
            String sentDataDate = sendMailInfo.getDataDate();

            // 更新指定日期的数据
            LambdaUpdateWrapper<PortfolioNetValueFluctuation> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PortfolioNetValueFluctuation::getDataDate, sentDataDate);
            updateWrapper.in(PortfolioNetValueFluctuation::getProductId, sentProductIds);
            String status = sendMailInfo.getStatus();
            MailStatus mailStatus = MailStatus.valueOf(status);
            updateWrapper.set(PortfolioNetValueFluctuation::getEmailSent, mailStatus);
            fluctuationService.update(updateWrapper);
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("组合产品净值波动/回撤邮件发送");
            PortfolioNetValueRetracementMailJob job = (PortfolioNetValueRetracementMailJob) AopContext.currentProxy();
            job.doExecute(context);
        } else {
            log.error("组合产品净值波动/回撤邮件发送，无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("PortfolioNetValueRetracementMailJob", PortfolioNetValueRetracementMailJob.class);
    }
}
