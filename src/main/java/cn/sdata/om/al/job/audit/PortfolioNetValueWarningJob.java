package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.PortfolioNetValueWarningService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class PortfolioNetValueWarningJob implements QuartzJobBean {

    private final CronService cronService;
    private final PortfolioNetValueWarningService portfolioNetValueWarningService;

    @Override
    public void doExecute(JobExecutionContext context) {
        String dataDate = cronService.autoGetManualDataDate(context);
        portfolioNetValueWarningService.processWarningForDate(dataDate);
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("组合产品净值预警同步");
            PortfolioNetValueWarningJob portfolioNetValueWarningJob = (PortfolioNetValueWarningJob) AopContext.currentProxy();
            portfolioNetValueWarningJob.doExecute(context);
        } else {
            log.error("组合产品净值预警邮件发送，无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("PortfolioNetValueWarningJob", PortfolioNetValueWarningJob.class);
    }
}
