package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.service.PortfolioNetValueWarningService;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailInfoService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.JobConstant.*;

@Component
@Slf4j
@AllArgsConstructor
public class PortfolioNetValueWarningMailJob implements QuartzJobBean {

    private final MailInfoService mailInfoService;
    private final CronService cronService;
    private final PortfolioNetValueWarningService portfolioNetValueWarningService;

    @Override
    @SuppressWarnings("unchecked")
    public void doExecute(JobExecutionContext context) {
        Objects.requireNonNull(context, "JobExecutionContext is null");
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String dataDate = (String) mergedJobDataMap.get(DATA_DATE);
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        Map<String, RemoteFileInfo> infoMap = (Map<String, RemoteFileInfo>) mergedJobDataMap.get(REMOTE_FILE);
        List<String> productIds = (List<String>) mergedJobDataMap.get(PRODUCT_ID);
        Map<String, Object> extraData = (Map<String, Object>) mergedJobDataMap.get("extraData");

        Cron cron = cronService.getById(jobId);
        cron.setExtraData(extraData);
        cron.setSpecifiedHandler("PORTFOLIO_NET_VALUE_WARNING");
        List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, dataDate, new LinkedHashSet<>(productIds), infoMap);

        sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
        for (SendMailInfo sendMailInfo : sendMailInfos) {
            List<String> sentProductIds = sendMailInfo.getProductIds();
            String sentDataDate = sendMailInfo.getDataDate();

            // 更新指定日期的数据
            LambdaUpdateWrapper<PortfolioNetValueWarning> portfolioNetValueWarningLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            portfolioNetValueWarningLambdaUpdateWrapper.eq(PortfolioNetValueWarning::getDataDate, sentDataDate);
            portfolioNetValueWarningLambdaUpdateWrapper.in(PortfolioNetValueWarning::getProductId, sentProductIds);
            String status = sendMailInfo.getStatus();
            MailStatus mailStatus = MailStatus.valueOf(status);
            portfolioNetValueWarningLambdaUpdateWrapper.set(PortfolioNetValueWarning::getEmailSent, mailStatus);
            portfolioNetValueWarningService.update(portfolioNetValueWarningLambdaUpdateWrapper);
        }
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        if (context != null) {
            log.info("组合产品净值预警邮件发送");
            PortfolioNetValueWarningMailJob portfolioNetValueWarningMailJob = (PortfolioNetValueWarningMailJob) AopContext.currentProxy();
            portfolioNetValueWarningMailJob.doExecute(context);
        } else {
            log.error("组合产品净值预警邮件发送，无JobExecutionContext");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("PortfolioNetValueWarningMailJob", PortfolioNetValueWarningMailJob.class);
    }
}
