package cn.sdata.om.al.job.audit;

import cn.sdata.om.al.audit.service.StockFundExRightsCheckService;
import cn.sdata.om.al.qrtz.QuartzJobBean;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.result.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

/**
 * 股票和开基除权价格检查定时任务
 * 
 * <AUTHOR>
 * @date 2025-08-09
 */
@Slf4j
@Component
@RequiredArgsConstructor
@TradeDay
public class StockFundExRightsCheckJob implements QuartzJobBean {

    private final CronService cronService;
    private final StockFundExRightsCheckService stockFundExRightsCheckService;

    @Override
    public void doExecute(JobExecutionContext context) {
        // 空实现，实际逻辑在execute方法中
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 获取数据日期
            String dataDate = cronService.autoGetManualDataDate(context);
            log.info("开始执行股票和开基除权价格检查定时任务，数据日期：{}", dataDate);

            // 执行除权价格检查
            R<String> result = stockFundExRightsCheckService.executeExRightsCheck(dataDate);
            
            if (result.isSuccess()) {
                log.info("股票和开基除权价格检查定时任务执行成功：{}", result.getMessage());
            } else {
                log.error("股票和开基除权价格检查定时任务执行失败：{}", result.getMessage());
                throw new JobExecutionException("股票和开基除权价格检查定时任务执行失败：" + result.getMessage());
            }

        } catch (Exception e) {
            log.error("股票和开基除权价格检查定时任务执行异常", e);
            throw new JobExecutionException("股票和开基除权价格检查定时任务执行异常", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuartzJobFactory.addJobClass("StockFundExRightsCheckJob", StockFundExRightsCheckJob.class);
    }
}
