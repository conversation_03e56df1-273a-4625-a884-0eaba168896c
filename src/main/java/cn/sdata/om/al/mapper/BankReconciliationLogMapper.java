package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankReconciliationLogMapper {

    List<LogBRImportFileRecord> pageLogImport(@Param("beginDate") String beginDataDate, @Param("endDate") String endDataDate);

    List<LogBRMarkDiffRecord> pageLogDiff(@Param("beginDate") String beginDataDate, @Param("endDate") String endDataDate);

    List<LogBRMarkDiffDesc> getDiffDesc(String id);

    List<LogBRExportFileRecord> pageLogExport(@Param("beginDate") String beginDataDate, @Param("endDate") String endDataDate);

    List<LogBRSyncValuationRecord> pageLogSync(@Param("beginDate") String beginDataDate, @Param("endDate") String endDataDate);

    void batchSaveMarkDiffDesc(List<LogBRMarkDiffDesc> logBRMarkDiffDescList);

    void saveMarkDiffLog(LogBRMarkDiffRecord logBRMarkDiffRecord);

    void updateMarkDiffLogById(@Param("id") String logId,
                               @Param("endTime") String endTime,
                               @Param("result") String result,
                               @Param("errorMsg") String errorMsg,
                               @Param("status") String status);

    void saveUploadLog(LogBRImportFileRecord logBRImportFileRecord);

    void updateUploadLogById(@Param("id") String logId,
                             @Param("endTime") String endTime,
                             @Param("errorMsg") String errorMsg,
                             @Param("status") String status,
                             @Param("fileUrl") String fileUrl,
                             @Param("dataDate") String dataDate);

    void saveSyncLog(LogBRSyncValuationRecord logBRSyncValuationRecord);

    void updateSyncLogById(@Param("id") String logId,
                           @Param("endTime") String endTime,
                           @Param("errorMsg") String errorMsg,
                           @Param("status") String status);

    void deleteSyncLog(String logId);

    void saveExportLog(LogBRExportFileRecord logBRSyncValuationRecord);

    void updateExportLogById(@Param("id") String logId,
                             @Param("endTime") String endTime,
                             @Param("errorMsg") String errorMsg,
                             @Param("status") String status,
                             @Param("fileUrl") String fileUrl);

    List<LogBRRPARecord> getLogBRRPARecordList(@Param("beginDate") String beginDataDate, @Param("endDate") String endDataDate);

    LogBRRPARecord getBRRpaLogByRpaLogId(String rpaLogId);

    void saveRpaLog(LogBRRPARecord brrpaRecord);

    void updateRpaLogById(LogBRRPARecord logBRRPARecord);

    LogBRRPARecord getBRRpaLogById(String id);

}
