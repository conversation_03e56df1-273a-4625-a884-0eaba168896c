package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.BankReconciliation;
import cn.sdata.om.al.entity.BankReconciliationFile;
import cn.sdata.om.al.entity.BankReconciliationQuery;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankReconciliationMapper {
    List<BankReconciliation> page(BankReconciliationQuery bankReconciliationQuery);

    List<String> settlementLocationList();

    List<String> securityNameList();

    List<String> securitiesCodeList();

    int markDifference(@Param("list") List<String> ids, @Param("differenceReasons") String differenceReasons, @Param("userId") String userId);

    @DS("valuation")
    List<JSONObject> queryValuationTableByZQDM(@Param("code") String code, @Param("date") String date, @Param("accountSetId") String accountSetId);

    void saveBatch(List<BankReconciliation> bankReconciliations);

    @DS("valuation")
    List<JSONObject> queryValuationTableByZQDMs(@Param("param") JSONObject jsonObject);

    void syncBankReconciliation(@Param("q") JSONObject jsonObject);

    int checkData(@Param("date") String date, @Param("securityName") String securityName, @Param("accountSetId") String accountSetId);

    @DS("valuation")
    List<String> checkCode(String code);

    List<BankReconciliation> selectBankReconciliationInfoByDate(@Param("beginDate") String beginDate,
                                                                @Param("endDate") String endDate);

    @DS("valuation")
    List<JSONObject> queryValuationTableByConditions(@Param("securityCode") String securityCode,
                                                     @Param("dataDate") String dataDate,
                                                     @Param("accountSetCode") String accountSetCode);

    List<String> sourceCodeList();

    void saveBankReconciliationFile(BankReconciliationFile bankReconciliationFile);

    List<BankReconciliationFile> getBankReconciliationFiles(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    @DS("valuation")
    List<JSONObject> queryValuationTableByDate(String testDate);

    @DS("valuation")
    List<JSONObject> selectValuationSettlementLocation(List<String> collect);

    List<BankReconciliation> selectAlreadyExistInfo(@Param("date") String testDate);

    int syncExistBankReconciliation(@Param("accountSetCode") String accountSetCode,
                                    @Param("securityName") String securityName,
                                    @Param("dataDate") String dataDate,
                                    @Param("settlementLocation") String settlementLocation,
                                    @Param("settlementCompanyPositionQuantity") String settlementCompanyPositionQuantity);

    @DS("valuation")
    List<JSONObject> selectValuationSettlementLocationBySourceCode(List<String> list);

    void deleteByDate(@Param("list") List<String> testDates, @Param("location") String location);

    void updateData(BankReconciliation reconciliation);

    void updateSettlementQuantity(BankReconciliation bankReconciliation);

    @DS("valuation")
    List<JSONObject> queryValuationTableByDateScope(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    JSONObject selectSyncTimeByUserId(String id);

    void updateSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void insertSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void updateSettlementSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void insertSettlementSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void updateAllSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void insertAllSyncTime(@Param("endDate") String endDate, @Param("syncTime") String now);

    void updateQuantityToZero(BankReconciliation bankReconciliation);

    List<BankReconciliation> selectAlreadyExistInfoByLocation(@Param("date") String testDate, @Param("location") String location);

    List<BankReconciliation> selectAlreadyExistInfoByLocationAndAccountNumber(@Param("date") String testDate, @Param("location") String location,
                                                                              @Param("accountNumber") String accountNumber);
    @DS("valuation")
    List<JSONObject> queryValuationTableByConditionsAndSourceCode(@Param("sourceCode") String sourceCode,
                                                                  @Param("date") String dataDate,
                                                                  @Param("accountSetCode") String accountSetCode);

    JSONObject selectSyncTimeByEndDate(String endDate);

    List<JSONObject> selectAllSyncTime();

    List<JSONObject> getProductByIds(List<String> ids);

    List<JSONObject> selectDataDateByIds(List<String> ids);

}
