package cn.sdata.om.al.mapper;

import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BaseCronLogMapper extends BaseMapper<BaseCronLog> {

    List<BaseCronLog> queryGroupByTask();

    String getLastExecute(@Param("taskId") String taskId);

    void liivSendMailUpdateStatus(@Param("id") String id,
                                  @Param("status") String status,
                                  @Param("endDateTime") String endDateTime,
                                  @Param("dataDate") String dataDate);

    BaseCronLog getLatestLog(@Param("taskId") String taskId, @Param("dataDate") String dataDate);
}