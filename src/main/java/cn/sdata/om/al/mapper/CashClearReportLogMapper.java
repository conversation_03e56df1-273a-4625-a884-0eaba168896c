package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.LogCCRExportRecord;
import cn.sdata.om.al.entity.LogCCRRPARecord;
import cn.sdata.om.al.entity.LogCCRSendMailRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CashClearReportLogMapper {
    List<LogCCRRPARecord> getLogCCRRPARecordList(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogCCRExportRecord> getLogCCRExportRecordList(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogCCRSendMailRecord> getLogCCRSendMailRecordList(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    void saveRpaLog(LogCCRRPARecord logCCRRPARecord);

    LogCCRRPARecord getCCRRpaLogByRpaLogId(String rpaLogId);

    void updateRpaLogById(LogCCRRPARecord logCCRRPARecord);

    void saveExportLog(LogCCRExportRecord logCCRExportRecord);

    void updateExportLogById(@Param("id") String logId,
                             @Param("endTime") String endTime,
                             @Param("errorMsg") String errorMsg,
                             @Param("status") String status,
                             @Param("fileUrl") String fileUrl);

    void saveSendMailLog(LogCCRSendMailRecord logCCRSendMailRecord);

    LogCCRSendMailRecord getCCRSendMailLogByLogId(String rpaLogId);

    void updateSendMailLogById(LogCCRSendMailRecord logCCRSendMailRecord);

    LogCCRRPARecord getCCRRpaLogById(String id);

}
