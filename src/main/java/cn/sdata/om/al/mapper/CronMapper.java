package cn.sdata.om.al.mapper;


import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.entity.CronGroupRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CronMapper extends BaseMapper<Cron> {

    List<CronGroupRelation> getRelation();

    List<Cron> listAll();

    boolean restoreForce(@Param("jobId") String jobId);

}




