package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.CustodianBankContacts;
import cn.sdata.om.al.vo.CustodianBankContactsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustodianBankContactsMapper extends BaseMapper<CustodianBankContacts> {
    Page<CustodianBankContactsVO> pageResult(@Param("bankNames") List<String> bankName,
                                             @Param("products") List<String> products,
                                             @Param("custodianRoles") List<String> custodianRole,
                                             Page<CustodianBankContactsVO> page);

    List<CustodianBankContactsVO> listResult(@Param("custodianRole") String custodianRole);

    List<CommonEntity> listProduct();

    List<CustodianBankContacts> getRecipientInfo(@Param("ids") List<String> values, @Param("role") String role);
}