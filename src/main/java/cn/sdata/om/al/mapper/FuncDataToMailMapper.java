package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.FuncDataToMailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/13 21:35
 * @Version 1.0
 */
@Mapper
public interface FuncDataToMailMapper extends BaseMapper<FuncDataToMailEntity> {

    /**
     * 根据数据日期和功能类型查询最新关联邮件信息
     *
     * @param dataDate 数据日期
     * @param funcType 功能类型
     * @return
     */
    List<FuncDataToMailEntity> getLatestMailByDataDateAndFuncType(@Param("dataDate") String dataDate, @Param("funcType") String funcType);

    /**
     * 获取资金清算报表功能对应的发送邮件数据
     *
     * @param dataDate 数据日期
     * @return
     */
    List<FuncDataToMailEntity> getCashClearReportMailList(@Param("beginDate") String beginDate,
                                                          @Param("endDate") String endDate);

}
