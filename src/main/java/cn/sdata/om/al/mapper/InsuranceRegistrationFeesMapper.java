package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.InsuranceRegistrationFees;
import cn.sdata.om.al.entity.InsuranceRegistrationFeesFile;
import cn.sdata.om.al.entity.InsuranceRegistrationFeesQuery;
import com.alibaba.fastjson2.JSONObject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InsuranceRegistrationFeesMapper {
    void batchSaveFile(List<InsuranceRegistrationFeesFile> insuranceRegistrationFeesFiles);

    void batchSave(List<InsuranceRegistrationFees> res);

    List<InsuranceRegistrationFees> page(InsuranceRegistrationFeesQuery insuranceRegistrationFeesQuery);

    List<InsuranceRegistrationFees> selectListByDate(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<InsuranceRegistrationFeesFile> selectByFileIds(List<String> fileIds);

    InsuranceRegistrationFeesFile selectByFileId(String fileId);

    void updateBaseInfo(@Param("data") JSONObject jsonObject, @Param("ids") List<String> ids);

    void updateTableData(@Param("data") JSONObject object);

    List<InsuranceRegistrationFees> selectList(List<String> ids);

    void updateImportStatus(@Param("id") String id, @Param("status") String name, @Param("msg") String error);

    void batchUpdateImportStatus(@Param("ids") List<String> ids, @Param("status") String name, @Param("importTime") String importTime, @Param("errMsg") String errMsg);

    List<InsuranceRegistrationFeesFile> getFileByIds(List<String> ids);

    int selectRepeatData(InsuranceRegistrationFees fees);

    void updatePayStatus(@Param("id") String id, @Param("tradeTime") String tradeTime, @Param("statusUpdateTime") String now);

    void updateSendStatus(@Param("ids") List<String> ids, @Param("sendStatus") String name);

    List<InsuranceRegistrationFeesFile> getAllFiles();

    List<InsuranceRegistrationFees> selectLastQuarterData(@Param("beginDate") String quarterMonth, @Param("endDate") String quarterMonth1);

    void updateHandleResult(@Param("id") String id, @Param("handleResult") String handleResult);

    int deleteById(String id);

}
