package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.InterBankFeesFile;
import cn.sdata.om.al.entity.InterBankFees;
import cn.sdata.om.al.entity.InterbankFeesQuery;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InterbankFeesMapper {
    List<InterBankFees> page(InterbankFeesQuery interbankFeesQuery);

    int batchSave(List<InterBankFees> res);

    void batchSaveFile(List<InterBankFeesFile> interBankFeesFiles);

    InterBankFees getById(String id);

    InterBankFeesFile getFileById(String fileId);

    int update(InterBankFees interbankFees);

    List<InterBankFeesFile> getFileByIds(List<String> ids);

    List<InterBankFees> selectList(List<String> ids);

    String getImportOrderNumber(@Param("date") String today, @Param("type") String type);

    void insertImportOrderNumber(@Param("date") String today, @Param("orderNum") String res, @Param("type") String type);

    void updateImportOrderNumber(@Param("date") String today, @Param("orderNum") String res, @Param("type") String type);

    void batchUpdateImportStatus(@Param("ids") List<String> ids, @Param("status") String name, @Param("importTime") String importTime, @Param("errMsg") String errMsg);

    void updateImportStatus(@Param("id") String id, @Param("status") String name, @Param("msg") String error);

    @DS("cop")
    List<JSONObject> selectCopPayStatus(@Param("nameOfPayee") String nameOfPayee,
                                        @Param("beneficiaryAccount") String beneficiaryAccount,
                                        @Param("amount") String amount,
                                        @Param("beginDate") String beginDate,
                                        @Param("endDate") String endDate,
                                        @Param("productId") String productId);

    @DS("cop")
    List<JSONObject> testSelectAllCop(@Param("nameOfPayee") String nameOfPayee,
                                      @Param("beneficiaryAccount") String beneficiaryAccount,
                                      @Param("bankAccount") String bankAccount);

    @DS("o32")
    List<JSONObject> testSelectO32Date();

    int selectRepeatData(InterBankFees interBankFees);

    @DS("cop")
    List<JSONObject> testHisQueryCop(@Param("nameOfPayee") String nameOfPayee,
                                     @Param("beneficiaryAccount") String beneficiaryAccount,
                                     @Param("bankAccount") String bankAccount);

    @DS("cop")
    List<JSONObject> selectCopHistoryPayStatus(@Param("nameOfPayee") String nameOfPayee,
                                               @Param("beneficiaryAccount") String beneficiaryAccount,
                                               @Param("amount") String amount,
                                               @Param("beginDate") String beginDate,
                                               @Param("endDate") String endDate,
                                               @Param("productId") String productId);

    void updatePayStatus(@Param("id") String id, @Param("tradeTime") String tradeTime, @Param("statusUpdateTime") String now);

    List<InterBankFeesFile> getAllFiles();

    void updateSendStatus(@Param("ids") List<String> ids, @Param("sendStatus") String name);

    @DS("cop")
    List<JSONObject> testSelectAllCopV2(@Param("nameOfPayee") String nameOfPayee,
                                        @Param("beneficiaryAccount") String beneficiaryAccount,
                                        @Param("bankAccount") String bankAccount);

    @DS("cop")
    List<JSONObject> testQueryCopAcc();

    @DS("cop")
    List<JSONObject> testQueryCopFund();

    @DS("cop")
    List<JSONObject> testJoinQueryCop(@Param("nameOfPayee") String nameOfPayee,
                                      @Param("beneficiaryAccount") String beneficiaryAccount,
                                      @Param("bankAccount") String bankAccount,
                                      @Param("beginDate") String beginDate,
                                      @Param("endDate") String endDate);

    List<InterBankFees> selectLastQuarterData(@Param("beginDate") String quarterMonth, @Param("endDate") String quarterMonth1);

    String selectProductCodeById(String productId);
    void updateHandleResult(@Param("id") String id, @Param("handleResult") String handleResult);

    int deleteById(String id);
}
