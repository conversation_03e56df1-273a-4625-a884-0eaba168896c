package cn.sdata.om.al.mapper;

import cn.sdata.om.al.dto.InvestNetReportShareChangeDto;
import cn.sdata.om.al.entity.investNetReport.InvestNetReportEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/19 19:25
 * @Version 1.0
 */
@Mapper
public interface InvestNetReportMapper extends BaseMapper<InvestNetReportEntity> {

    /**
     * 获取份额变动表文件路径
     *
     * @param bizDate
     * @return
     */
    List<String> getShareChangeExcelLocalPath(@Param("bizDate") String bizDate);

    /**
     * 获取份额变动表文件路径v1
     *
     * @param bizDate
     * @return
     */
    List<InvestNetReportShareChangeDto> getShareChangeExcelLocalPathV1(@Param("bizDate") String bizDate);
}
