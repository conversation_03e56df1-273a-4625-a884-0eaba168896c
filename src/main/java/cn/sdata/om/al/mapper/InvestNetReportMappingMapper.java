package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.investNetReport.MappingEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/11 14:41
 * @Version 1.0
 */
@Mapper
public interface InvestNetReportMappingMapper extends BaseMapper<MappingEntity> {

    /**
     * 获取映射配置信息
     *
     * @return
     */
    List<MappingEntity> mappingList();
}
