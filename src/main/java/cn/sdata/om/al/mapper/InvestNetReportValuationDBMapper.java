package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.investNetReport.ValuationNetValueEntity;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/11 19:01
 * @Version 1.0
 */
@Mapper
@DS("valuation")
public interface InvestNetReportValuationDBMapper {

    /**
     * 获取净值数据-多个日期
     *
     * @param accountSetCodes
     * @param dates
     * @return
     */
    List<ValuationNetValueEntity> netValueListByDates(@Param("accountSetCodes") List<String> accountSetCodes, @Param("dates") List<String> dates);
}
