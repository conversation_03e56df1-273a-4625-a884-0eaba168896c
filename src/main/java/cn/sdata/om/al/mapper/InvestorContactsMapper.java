package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.InvestorContacts;
import cn.sdata.om.al.vo.InvestorContactVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvestorContactsMapper extends BaseMapper<InvestorContacts> {

    Page<InvestorContactVO> pageResult(@Param("investors") List<String> investors,
                                       @Param("products") List<String> products,
                                       @Param("methods") List<String> methods,
                                       @Param("type") String type,
                                       IPage<InvestorContactVO> page);

    List<CommonEntity> listProduct();

    List<InvestorContactVO> listResult(String type);
}