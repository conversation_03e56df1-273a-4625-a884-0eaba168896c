package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.InvestorContacts;
import cn.sdata.om.al.entity.InvestorContactsView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface InvestorContactsViewMapper extends BaseMapper<InvestorContactsView> {

    List<InvestorContacts> getRecipientInfo(@Param("ids")List<String> ids, @Param("method") String method, @Param("handler") String handler, @Param("type")String type);

    List<InvestorContacts> getRecipientInfoByProduct(@Param("productIds")List<String> productIds, @Param("method") String method, @Param("handler") String handler, @Param("type")String type);

}