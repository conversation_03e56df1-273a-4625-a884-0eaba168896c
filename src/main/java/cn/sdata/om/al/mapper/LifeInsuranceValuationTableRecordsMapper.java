package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.investNetReport.LifeInsuranceValuationTableRecords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/17 15:34
 * @Version 1.0
 */
@Mapper
public interface LifeInsuranceValuationTableRecordsMapper extends BaseMapper<LifeInsuranceValuationTableRecords> {

    /**
     * 获取数据日期对应的最新的寿险-投连估值表下载记录数据
     *
     * @param valuationDate   数据日期
     * @param accountSetCodes 套账编号
     * @return
     */
    List<LifeInsuranceValuationTableRecords> getLatestList(@Param("valuationDate") String valuationDate, @Param("accountSetCodes") List<String> accountSetCodes);
}
