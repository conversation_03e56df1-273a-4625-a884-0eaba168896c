package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LogFYBMapper {
    List<LogFYBGenerateFileRecord> getLogFYBGenerateFileRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBDownloadFileRecord> getLogFYBDownloadFileRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBImportO32Record> getLogFYBImportO32Record(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBO32ConfirmRecord> getLogFYBO32ConfirmRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBSendMailRecord> getLogFYBSendMailRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBSyncPayStatusRecord> getLogFYBSyncPayStatusRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYBUpdatePayStatusRecord> getLogFYBUpdatePayStatusRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    void saveUpdatePayStatusLog(LogFYBUpdatePayStatusRecord logFYBUpdatePayStatusRecord);

    void modifyUpdatePayStatusLog(@Param("logId") String logId,
                                  @Param("endTime") String endTime,
                                  @Param("postUpdateStatus") String postUpdateStatus);

    void saveDownloadFileLog(LogFYBDownloadFileRecord fybDownloadFileRecord);

    void updateDownloadFileLog(@Param("logId") String logId,
                               @Param("fileUrl") String fileUrl,
                               @Param("status") String status,
                               @Param("errorMsg") String errorMsg,
                               @Param("endTime") String endTime);

    void saveGenerateO32File(LogFYBGenerateFileRecord logFYBGenerateFileRecord);

    void updateGenerateO32File(@Param("logId") String logId,
                               @Param("fileUrl") String fileUrl,
                               @Param("status") String status,
                               @Param("errorMsg") String errorMsg,
                               @Param("endTime") String endTime,
                               @Param("productIds") String productIds);

    void saveSendMailLog(LogFYBSendMailRecord logFYBSendMailRecord);

    void saveO32ConfirmLog(LogFYBO32ConfirmRecord logFYBO32ConfirmRecord);

    void updateO32ConfirmLog(@Param("logId") String logId,
                             @Param("endTime") String endTime,
                             @Param("status") String status);

    void saveSyncPayStatusLog(LogFYBSyncPayStatusRecord logFYBSyncPayStatusRecord);

    void updateSyncPayStatusLog(@Param("logId") String logId,
                                @Param("endTime") String endTime,
                                @Param("productInfos") String productInfos,
                                @Param("status") String status);

    void saveImportO32Log(LogFYBImportO32Record logFYBImportO32Record);

    void updateImportO32Log(@Param("logId") String logId,
                            @Param("endTime") String endTime,
                            @Param("status") String status,
                            @Param("o32Result") String o32Result,
                            @Param("fileUrl") String fileUrl);

    void saveRpaLog(LogFYBRPARecord logFYBRPARecord);

    LogFYBRPARecord getFYBRpaLogByRpaLogId(String rpaLogId);

    void updateRpaLogById(LogFYBRPARecord logFYBRPARecord);

    List<LogFYBRPARecord> getLogFYBRPARecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    LogFYBRPARecord getFYBRpaLogById(String rpaLogId);

}
