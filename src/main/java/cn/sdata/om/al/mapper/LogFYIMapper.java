package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LogFYIMapper {
    List<LogFYIGenerateFileRecord> getLogFYIGenerateFileRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYIDownloadFileRecord> getLogFYIDownloadFileRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYIImportO32Record> getLogFYIImportO32Record(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYIO32ConfirmRecord> getLogFYIO32ConfirmRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYISendMailRecord> getLogFYISendMailRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYISyncPayStatusRecord> getLogFYISyncPayStatusRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYIUpdatePayStatusRecord> getLogFYIUpdatePayStatusRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogFYIUploadRecord> getLogFYIUploadRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    void saveUpdatePayStatusLog(LogFYIUpdatePayStatusRecord logFYIUpdatePayStatusRecord);

    void modifyUpdatePayStatusLog(@Param("logId") String logId,
                                  @Param("endTime") String endTime,
                                  @Param("postUpdateStatus") String postUpdateStatus);

    void saveDownloadFileLog(LogFYIDownloadFileRecord fybDownloadFileRecord);

    void updateDownloadFileLog(@Param("logId") String logId,
                               @Param("fileUrl") String fileUrl,
                               @Param("status") String status,
                               @Param("errorMsg") String errorMsg,
                               @Param("endTime") String endTime);

    void saveGenerateO32File(LogFYIGenerateFileRecord logFYIGenerateFileRecord);

    void updateGenerateO32File(@Param("logId") String logId,
                               @Param("fileUrl") String fileUrl,
                               @Param("status") String status,
                               @Param("errorMsg") String errorMsg,
                               @Param("endTime") String endTime,
                               @Param("productIds") String productIds);

    void saveSendMailLog(LogFYISendMailRecord logFYISendMailRecord);

    void saveO32ConfirmLog(LogFYIO32ConfirmRecord logFYIO32ConfirmRecord);

    void updateO32ConfirmLog(@Param("logId") String logId,
                             @Param("endTime") String endTime,
                             @Param("status") String status);

    void saveSyncPayStatusLog(LogFYISyncPayStatusRecord logFYISyncPayStatusRecord);

    void updateSyncPayStatusLog(@Param("logId") String logId,
                                @Param("endTime") String endTime,
                                @Param("productInfos") String productInfos,
                                @Param("status") String status);

    void saveImportO32Log(LogFYIImportO32Record logFYIImportO32Record);

    void updateImportO32Log(@Param("logId") String logId,
                            @Param("endTime") String endTime,
                            @Param("status") String status,
                            @Param("o32Result") String o32Result,
                            @Param("fileUrl") String fileUrl);

    void saveUploadFile(LogFYIUploadRecord logFYIUploadRecord);

    void updateUploadFile(@Param("logId") String logId,
                          @Param("status") String status,
                          @Param("endTime") String endTime);

}
