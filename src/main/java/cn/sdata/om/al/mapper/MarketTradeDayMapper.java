package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.MarketTradeDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MarketTradeDayMapper extends BaseMapper<MarketTradeDay> {

    Integer ltTradeDay(@Param("numberDate") Integer numberDate,
                       @Param("tradeType") String tradeType,
                       @Param("tradeFlag") String tradeFlag);

    MarketTradeDay getOffsetTradeDayPositive(@Param("tradeDayType") String tradeDayType,
                                             @Param("baseDate") String baseDate,
                                             @Param("offset") Integer offset);

    List<String> getNextHolidayList(@Param("tradeDayType") String tradeDayType,
                                    @Param("baseDate") String baseDate);

    MarketTradeDay getOffsetTradeDayNegative(@Param("tradeDayType") String tradeDayType,
                                             @Param("baseDate") String baseDate,
                                             @Param("offset") Integer offset);

    MarketTradeDay getOffsetTradeDayZero(@Param("tradeDayType") String tradeDayType,
                                         @Param("baseDate") String baseDate);

    List<String> getAllTradeDay(@Param("tradeDayType") String tradeDayType);

    Integer eqTradeDay(@Param("numberDate") Integer numberDate,
                       @Param("tradeType") String tradeType,
                       @Param("tradeFlag") String tradeFlag);

    Integer geTradeDay(@Param("numberDate") Integer numberDate,
                       @Param("tradeType") String tradeType,
                       @Param("tradeFlag") String tradeFlag);

    Integer leTradeDay(@Param("numberDate") Integer numberDate,
                       @Param("tradeType") String tradeType,
                       @Param("tradeFlag") String tradeFlag);

    List<Integer> likeTradeDay(@Param("date") int date, @Param("tradeType") String tradeType);

    List<String> getThisMonthSpecialTradeDay(@Param("ym") String ym);

    List<String> getThisMonthNoTradeDay(@Param("ym") String ym);

    Integer gtTradeDay(@Param("numberDate") Integer numberDate,
                       @Param("tradeType") String tradeType,
                       @Param("tradeFlag") String tradeFlag);

    List<Integer> gtNTradeDay(@Param("numberDate") Integer numberDate,
                              @Param("tradeType") String tradeType,
                              @Param("tradeFlag") String tradeFlag,
                              @Param("n") int n);

    List<String> getNonTradingDays(@Param("begin") Integer begin, @Param("end") Integer end);

}




