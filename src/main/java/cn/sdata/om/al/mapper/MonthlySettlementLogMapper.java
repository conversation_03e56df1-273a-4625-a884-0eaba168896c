package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MonthlySettlementLogMapper {

    List<LogMSExportRecord> getLogMSExportRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogMSFileOptRecord> getLogMSFileOptRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogMSRPARecord> getLogMSRPARecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    List<LogMSSendMailRecord> getLogMSSendMailRecord(@Param("beginDataDate") String beginDataDate, @Param("endDataDate") String endDataDate);

    void saveExportLog(LogMSExportRecord logMSExportRecord);

    void updateExportLogById(@Param("id") String logId,
                             @Param("endTime") String endTime,
                             @Param("errorMsg") String errorMsg,
                             @Param("status") String status,
                             @Param("fileUrl") String fileUrl);

    void saveFileOptLog(LogMSFileOptRecord logMSFileOptRecord);

    void updateFileOptLogById(@Param("id") String logId,
                              @Param("endTime") String endTime,
                              @Param("errorMsg") String errorMsg,
                              @Param("status") String status,
                              @Param("fileUrl") String fileUrl);

    void saveRpaLog(LogMSRPARecord logMSRPARecord);

    LogMSRPARecord getRpaLogById(String logId);

    void updateRpaLog(LogMSRPARecord logMSRPARecord);

    void saveSendMailLog(LogCCRSendMailRecord logCCRSendMailRecord);

    LogMSSendMailRecord getMSSendMailLogByLogId(String rpaLogId);

    void updateSendMailLogById(LogMSSendMailRecord logMSSendMailRecord);

    LogMSRPARecord getById(String id);

}
