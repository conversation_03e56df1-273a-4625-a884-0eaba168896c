package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.MonthlySettlementList;
import cn.sdata.om.al.entity.MonthlySettlementListQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MonthlySettlementMapper {
    List<MonthlySettlementList> page(MonthlySettlementListQuery monthlySettlementListQuery);

    List<MonthlySettlementList> selectMonthlySettlementByIds(List<String> ids);

    void batchSave(List<MonthlySettlementList> monthlySettlementLists);

    List<MonthlySettlementList> selectAll();

    void deleteUploadData(@Param("fNames") List<String> collect, @Param("date") String dataDate);

    void deleteByDate(String preMonthDate);

    int selectCountByDate(String dataDate);

    void updateFileInfo(@Param("date") String date, @Param("name") String name, @Param("path") String path,
                        @Param("replaceFileName") String replaceFileName, @Param("type") String type);

    void updateFileDownloadStatus(@Param("date") String date, @Param("orders") List<String> orders, @Param("status") String status);

    List<String> selectFileNameByDate(@Param("date") String date, @Param("order") String order);

    List<MonthlySettlementList> selectMonthlySettlementByDateAndOrders(@Param("date") String date, @Param("orders") List<String> orders);

    void updateSendStatus(@Param("date") String date, @Param("orders") List<String> orders, @Param("status") String status);

    void updateNoMatchFileNameStatus(@Param("order") String folderOrder, @Param("date") String date, @Param("status") String number);

    int countMatchFileName(@Param("date") String date, @Param("fileName") String prefix);

    int selectDownloadStatus(@Param("date") String date, @Param("order") String order);

    void updateFileDownloadStatusMatchProductId(@Param("namePrefix") String pId, @Param("status") String number,
                                                @Param("date") String date, @Param("order") String order);
}
