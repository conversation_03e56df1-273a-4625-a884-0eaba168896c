package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.NetValueDisclosureParam;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.vo.NetValueDisclosureVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface NetValueDisclosureMapper extends BaseMapper<NetValueDisclosure> {

    Page<NetValueDisclosureVO> listPage(@Param("param") NetValueDisclosureParam netValueDisclosureParam, IPage<NetValueDisclosureVO> page);

    @Select("select dict_value from dict where dict_key = #{key}")
    String getDate(String key);

    @Update("update dict set dict_value = #{comparisonDay} where dict_key  = #{key} ")
    void setComparisonDay(String comparisonDay,String key);
}