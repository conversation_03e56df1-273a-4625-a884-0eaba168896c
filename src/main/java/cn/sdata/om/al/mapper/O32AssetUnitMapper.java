package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.O32AssetUnit;
import cn.sdata.om.al.entity.O32AssetUnitQuery;

import java.util.List;

public interface O32AssetUnitMapper {
    void saveBatch(List<O32AssetUnit> o32AssetUnits);

    void truncateTable();

    List<O32AssetUnit> page(O32AssetUnitQuery o32AssetUnitQuery);

    O32AssetUnit getById(String id);

    int update(O32AssetUnit o32AssetUnit);

    int insert(O32AssetUnit o32AssetUnit);

    int getMaxOrderNumber();

    List<O32AssetUnit> selectList();

    List<String> codeList();

    List<String> nameList();

}
