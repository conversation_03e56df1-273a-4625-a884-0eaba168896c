package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.OpenFundConfirmationParam;
import cn.sdata.om.al.entity.OpenFundConfirmationStatement;
import cn.sdata.om.al.vo.OpenFundConfirmationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface OpenFundConfirmationStatementMapper extends BaseMapper<OpenFundConfirmationStatement> {
    Page<OpenFundConfirmationVO> pageQuery(@Param("param") OpenFundConfirmationParam openFundConfirmationParam, Page<OpenFundConfirmationParam> page);


}