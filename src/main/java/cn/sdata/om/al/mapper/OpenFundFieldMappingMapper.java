package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.OpenFundFieldMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface OpenFundFieldMappingMapper extends BaseMapper<OpenFundFieldMapping> {
    IPage<OpenFundFieldMapping> keyPageQuery(@Param("value") String value, Page<OpenFundFieldMapping> page);
    OpenFundFieldMapping keyGetOne(@Param("type") String type);
    OpenFundFieldMapping valueGetOne(@Param("type") String type, @Param("name") String name);
    IPage<OpenFundFieldMapping> valuePageQuery(@Param("name") String name, @Param("value") String value, Page<OpenFundFieldMapping> page);
}