package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.OpenFundReconciliationParam;
import cn.sdata.om.al.entity.OpenFundReconciliationStatement;
import cn.sdata.om.al.vo.OpenFundReconciliationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface OpenFundReconciliationStatementMapper extends BaseMapper<OpenFundReconciliationStatement> {
    Page<OpenFundReconciliationVO> pageQuery(@Param("param") OpenFundReconciliationParam openFundReconciliationParam, Page<OpenFundReconciliationParam> page);
}