package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSetGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectGroupMapper {
    List<AccountSetGroup> page(@Param("list") List<String> accountSetGroupIds);

    int update(AccountSetGroup accountSetGroup);

    int save(AccountSetGroup accountSetGroup);

    AccountSetGroup getById(String id);

    int batchDelete(List<String> ids);

    List<CommonEntity> list();

    List<String> selectAccountCodesStrExcludeSelf(String id);
}
