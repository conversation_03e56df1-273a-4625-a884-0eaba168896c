package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.entity.Role;
import cn.sdata.om.al.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 16:02
 * @Version 1.0
 */
@Repository
public interface RoleMapper extends BaseMapper<Role> {

    @Select("SELECT u.* FROM user u, user_role ur, role r WHERE u.id = ur.user_id AND ur.role_id = r.id AND r.id = #{roleId}")
    List<User> getRole2userList(@Param("roleId") String roleId);

    @Select("SELECT m.* FROM menu m, role_menu rm, role r WHERE m.id = rm.menu_id AND rm.role_id = r.id AND r.id = #{roleId}")
    List<Menu> getRole2menuList(@Param("roleId") String roleId);
}
