package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.entity.Role;
import cn.sdata.om.al.entity.User;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 14:41
 * @Version 1.0
 */
@Repository
public interface UserMapper extends BaseMapper<User> {

    /**
     * 获取用户对应的角色列表
     *
     * @param userId
     * @return
     */
    @Select("SELECT distinct r.* FROM user u, user_role ur, role r WHERE u.id = ur.user_id AND ur.role_id = r.id AND u.id = #{userId} ")
    List<Role> getUser2roleList(@Param("userId") String userId);

    /**
     * 获取用户对应的菜单列表
     *
     * @param userId
     * @return
     */
    List<Menu> getUser2menuList(@Param("userId") String userId);

    void recordLoginFailed(@Param("id") String id,
                           @Param("account") String account,
                           @Param("msg") String msg,
                           @Param("expireTime") Date time);

    JSONObject getLoginFailedByAccount(String account);

    /**
     * 重置登录错误次数
     *
     * @param account 账号
     */
    void resetLoginFailedCount(String account);
}
