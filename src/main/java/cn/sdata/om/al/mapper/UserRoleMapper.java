package cn.sdata.om.al.mapper;

import cn.sdata.om.al.entity.UserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 16:02
 * @Version 1.0
 */
@Repository
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 批量插入用户角色数据
     *
     * @param list
     */
    void insertBatch(@Param("list") List<UserRole> list);

    /**
     * 用户对应的权限接口
     *
     * @param userId 用户id
     * @param url    请求接口
     * @return
     */
    Integer user2ApiCount(@Param("userId") String userId, @Param("url") String url);

}
