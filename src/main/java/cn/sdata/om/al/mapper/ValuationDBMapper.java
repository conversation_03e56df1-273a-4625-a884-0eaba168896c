package cn.sdata.om.al.mapper;

import cn.sdata.om.al.audit.dto.SecurityHoldingDto;
import cn.sdata.om.al.audit.entity.MonthEndAndNonStandard;
import cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation;
import cn.sdata.om.al.audit.entity.PortfolioNetValueWarning;
import cn.sdata.om.al.audit.entity.Tjjjz;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.SecondValuationData;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.entity.ValuationTableStatus;
import cn.sdata.om.al.vo.MonthEndAndNonStandardVO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper
@DS("valuation")
public interface ValuationDBMapper {

    List<ValuationTableStatus> getGenerateStatus(@Param("date") String date);

    List<ValuationTableStatus> getReconciliationStatus(@Param("date") String date);

    List<ValuationTableStatus> getConfirmStatus(@Param("date") String date);

    List<ValuationTableData> getStructuralTableData(@Param("date") String date, @Param("productIds") List<String> productIds);

    List<ValuationTableData> getFlatTableData(@Param("date") String date, @Param("productIds") List<String> productIds);

    List<SecondValuationData> getSecondValuationData(@Param("subjectCodes") Collection<String> subjectCodes, @Param("date") String date, @Param("valuationTime") String valuationTime);

    List<PortfolioNetValueWarning> getPortfolioNetValueWarning(@Param("date") String date, @Param("productCodes") List<String> productCodes, @Param("productIds") List<String> productIds);

    List<PortfolioNetValueFluctuation> getPortfolioNetValueFluctuation(@Param("date") String date, @Param("productCodes") List<String> productCodes, @Param("productIds") List<String> productIds);


    List<Tjjjz> syncTjjjzInfo(@Param("startDate") String startDate, @Param("today") String today);

    List<CommonEntity> getProductCodeMapping();

    @MapKey("vcJjmc")
    Map<String, Tjjjz> selectStructuredTableData();

    List<MonthEndAndNonStandard> selectMonthEndAndNonStandardInfo(@Param("vo") MonthEndAndNonStandardVO vo);

    List<MonthEndAndNonStandard> selectProdSecurityList(@Param("securityType")String securityType,
                                                        @Param("securityCode")String securityCode);

    /**
     * 查询估值系统证券持仓数据（用于除权价格检查）
     *
     * @param dataDate 数据日期
     * @return 证券持仓数据
     */
    List<SecurityHoldingDto> selectSecurityHoldingForExRightsCheck(@Param("dataDate") String dataDate);

    /**
     * 查询估值系统除权流水
     *
     * @param securityCode 证券代码
     * @param productId 账套编号
     * @param dataDate 数据日期
     * @return 除权流水数据
     */
    List<Map<String, Object>> selectExRightsFlow(@Param("securityCode") String securityCode,
                                                  @Param("productId") String productId,
                                                  @Param("dataDate") String dataDate);

    /**
     * 查询估值系统红股流水
     *
     * @param securityCode 证券代码
     * @param productId 账套编号
     * @param dataDate 数据日期
     * @return 红股流水数据
     */
    List<Map<String, Object>> selectBonusShareFlow(@Param("securityCode") String securityCode,
                                                    @Param("productId") String productId,
                                                    @Param("dataDate") String dataDate);

    /**
     * 查询估值系统红利发放流水
     *
     * @param securityCode 证券代码
     * @param productId 账套编号
     * @param dataDate 数据日期
     * @return 红利发放流水数据
     */
    List<Map<String, Object>> selectDividendFlow(@Param("securityCode") String securityCode,
                                                  @Param("productId") String productId,
                                                  @Param("dataDate") String dataDate);

    String seleDataDate();

    String seleDataDateInfo();

    List<MonthEndAndNonStandard> selectSecInfo(@Param("vo") MonthEndAndNonStandardVO vo);
}
