package cn.sdata.om.al.mapper.cashClearReport;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15 10:08
 * @Version 1.0
 */
@Mapper
public interface CashClearReportMapper extends BaseMapper<CashClearReportEntity> {


    /**
     * 通过数据日期和jobName获取远程文件信息列表
     *
     * @param dataDateStr yyyy年MM月dd日-yyyy年MM月dd日
     * @param jobName
     * @return
     */
    List<RemoteFileInfo> getRemoteFilesByDateAndJobName(@Param("dataDateStr") String dataDateStr, @Param("jobName") String jobName);
}
