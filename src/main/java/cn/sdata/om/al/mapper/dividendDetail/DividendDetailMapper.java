package cn.sdata.om.al.mapper.dividendDetail;

import cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分红信息明细mapper
 *
 * <AUTHOR>
 * @Date 2025/4/27 15:12
 * @Version 1.0
 */
@Mapper
public interface DividendDetailMapper extends BaseMapper<DividendDetailEntity> {

    List<DividendDetailEntity> getD2F2MList(@Param("startDate") String startDate,
                                            @Param("endDate") String endDate,
                                            @Param("productNames") List<String> productNames);

    List<DividendDetailEntity> getD2F2MListV1(@Param("startDate") String startDate,
                                              @Param("endDate") String endDate,
                                              @Param("productNames") List<String> productNames);

    List<DividendDetailEntity> getSendMails(@Param("detailId") String detailId, @Param("dataDate") String dataDate);
}
