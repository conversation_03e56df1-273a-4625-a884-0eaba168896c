package cn.sdata.om.al.mapper.dividendDetail;

import cn.sdata.om.al.entity.dividendDetail.LogCallRpaDividendDetailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/3 13:55
 * @Version 1.0
 */
@Mapper
public interface LogCallRpaDividendDetailMapper extends BaseMapper<LogCallRpaDividendDetailEntity> {

    @Select("select lcrdd.*, rel.rpa_biz_scene as task_name, rel.start_time , rel.finish_time, rel.exec_result \n" +
            "from log_call_rpa_dividend_detail lcrdd \n" +
            "left join rpa_exec_log rel \n" +
            "on lcrdd.rpa_exec_log_id = rel.id \n" +
            "where lcrdd.data_date between #{startDate} and #{endDate} \n" +
            "order by lcrdd.create_time desc")
    List<LogCallRpaDividendDetailEntity> getCallRpaLogsByDataDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select("select lcrdd.*, rel.rpa_biz_scene as task_name, rel.start_time , rel.finish_time, rel.exec_result \n" +
            "from log_call_rpa_dividend_detail lcrdd \n" +
            "left join rpa_exec_log rel \n" +
            "on lcrdd.rpa_exec_log_id = rel.id \n" +
            "where lcrdd.id = #{id} ")
    LogCallRpaDividendDetailEntity getCallRpaLogById(@Param("id") String id);
}
