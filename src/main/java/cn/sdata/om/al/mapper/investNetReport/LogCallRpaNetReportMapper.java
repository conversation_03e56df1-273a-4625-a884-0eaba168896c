package cn.sdata.om.al.mapper.investNetReport;

import cn.sdata.om.al.entity.investNetReport.LogCallRpaNetReportEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/26 10:53
 * @Version 1.0
 */
@Mapper
public interface LogCallRpaNetReportMapper extends BaseMapper<LogCallRpaNetReportEntity> {

    @Select("select lcrnr.*, rel.rpa_biz_scene as task_name, rel.start_time , rel.finish_time, rel.exec_result from log_call_rpa_net_report lcrnr left join rpa_exec_log rel on lcrnr.rpa_exec_log_id = rel.id where lcrnr.data_date  = #{dataDate} order by lcrnr.create_time desc")
    List<LogCallRpaNetReportEntity> getCallRpaLogsByDataDate(@Param("dataDate") String dataDate);
}
