package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailCommonInfo;

import java.util.List;

public interface AccountSetMapper {
    List<CommonEntity> list();

    List<String> productCodeList();

    List<String> accountSetCodeList();

    List<String> accountSetNameList();

    List<MailCommonInfo> queryAccountSetByIds(List<String> zqdmIds);

    List<String> getIdZZByAccount(String accCode);

    List<String> getIdSQByAccount(String accCode);

    List<MailCommonInfo> queryAll();

}
