package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSummarizeListQuery;
import cn.sdata.om.al.entity.mail.AccountFundInformation;
import cn.sdata.om.al.entity.mail.MailCommonInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailCommonInfoMapper {
    List<MailCommonInfo> page(AccountSummarizeListQuery accountSummarizeListQuery);

    int update(MailCommonInfo mailCommonInfo);

    int save(MailCommonInfo mailCommonInfo);

    MailCommonInfo getById(String id);

    List<CommonEntity> list();

    String selectContactsByTypeAndCategory(@Param("type") String type, @Param("category") String category);

    int funInfoUpdate(AccountFundInformation accountFundInformation);

    int fundInfoSave(AccountFundInformation accountFundInformation);

    int fundInfoBatchDelete(List<String> ids);

    List<AccountFundInformation> fundInfoPage(@Param("list") List<String> productIds, @Param("admins") List<String> admins, @Param("type") String type);

    AccountFundInformation fundInfoGetById(String fundInfoId);

    List<String> selectPartyOrganizationByProductId(String id);

    List<String> selectInvestorByProductId(String id);

    List<CommonEntity> accountSetListNoGroup(List<String> res);

    void deleteProductAndInvestorIdsRelation(String id);

    void deleteProductAndThreePartyOrganizationIdsRelation(String id);

    void saveProductAndInvestorIdsRelation(List<CommonEntity> commonEntities);

    void saveThreePartyOrganizationIdsRelation(List<CommonEntity> commonEntities);

    void saveBatch(List<MailCommonInfo> mailCommonInfos);

    void saveBatchFundInfo(List<AccountFundInformation> accountFundInformations);

    List<String> fundAdminList();

}
