package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.ContactsAndAccountSetRelation;
import cn.sdata.om.al.entity.mail.params.ContactsAndTaskRelation;
import cn.sdata.om.al.entity.mail.params.MailContactsListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailContacts;
import cn.sdata.om.al.entity.mail.vo.MailContactsVo;
import cn.sdata.om.al.entity.mail.vo.MailContactsListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailContactsMapper {
    List<MailContactsListVo> page(MailContactsListQuery mailContactsListQuery);

    List<CommonEntity> list();

    int delete(@Param("list") List<String> ids);

    MailContactsVo getById(String id);

    int save(SaveOrUpdateMailContacts saveOrUpdateMailContacts);

    int update(SaveOrUpdateMailContacts saveOrUpdateMailContacts);

    void deleteContactsAndTaskRelation(String id);

    void deleteContactsAndAccountSetRelation(String id);

    void saveContactsAndTaskRelation(List<ContactsAndTaskRelation> contactsAndTaskRelations);

    void saveContactsAndAccountSetRelation(List<ContactsAndAccountSetRelation> contactsAndAccountSetRelations);

    List<String> getAccountSetByTemplateId(String templateId);

    List<CommonEntity> accountSetList();

    List<String> getAllRecipient();

    List<CommonEntity> getMailContactsType();

    List<CommonEntity> getMailContactsCategoryByType(String typeId);

    void batchDeleteContactsAndTaskRelation(List<String> ids);

    void batchDeleteContactsAndAccountSetRelation(List<String> ids);

    List<CommonEntity> getAccountSetByIds(List<String> accountSetIds);

    List<String> getMailContactsByAccountSetIdsAndTemplateId(@Param("list") List<String> accountSetIds, @Param("templateId") String templateId);

    List<CommonEntity> getAllContactsAccountId(@Param("templateId") String templateId);

    String getTemplateIdByName(@Param("name")String name);

    List<CommonEntity> getAccountSetByContactsId(String contactsId);

    List<MailContactsVo> getInfoByTaskId(@Param("taskId") String taskId);
}
