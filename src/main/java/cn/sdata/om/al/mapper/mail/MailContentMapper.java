package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.ManualPickParam;
import cn.sdata.om.al.entity.mail.vo.MailContentListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailContentMapper {
    void save(MailContent mailContent);

    int getMaxNumber();

    MailContent getById(String id);

    List<MailContent> listAllContent(@Param("ids") List<String> ids);

    List<MailContentListVo> list(MailContentListQuery mailContentListQuery);

    int move(@Param("boxId") String boxId, @Param("contentId") String contentId);

    int doPick(ManualPickParam manualPickParam);

    List<MailContent> selectUnPickMail();

    List<MailContent> selectAllPickMail();

    String selectRuleNameByBoxId(@Param("boxId") String boxId);
}
