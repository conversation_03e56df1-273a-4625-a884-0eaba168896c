package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.mail.MailInfo;
import cn.sdata.om.al.entity.mail.vo.MailDetailVo;

import java.util.List;

public interface MailInfoMapper {

    MailDetailVo getById(String mailId);

    void save(MailInfo mailInfo);

    /**
     * 通过邮件ids获取邮件列表
     *
     * @param mailIds
     * @return
     */
    List<MailInfo> getMailInfoByMailIds(List<String> mailIds);
}
