package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailMacroMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailMacroMapper {
    List<CommonEntity> list(@Param("moduleName") String moduleName);

    List<MailMacroMap> page(@Param("moduleName") String moduleName);

    List<CommonEntity> all(@Param("moduleName") String moduleName);
}
