package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickBasket;
import cn.sdata.om.al.entity.mail.vo.MailPickBasketVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailPickBasketMapper {
    List<MailPickBasketVo> list();

    int save(SaveOrUpdateMailPickBasket saveOrUpdateMailPickBasket);

    int update(SaveOrUpdateMailPickBasket saveOrUpdateMailPickBasket);

    int delete(String id);

    int getMaxOrderBy();

    int selectPickBasketCount(String id);

    MailPickBasketVo getById(String id);

    void updateMailToUnPick(String id);

    void updateRuleIdToNull(String id);

    void bindRule(@Param("ruleId") String id, @Param("pickBasketId") String pickBasket);

    int selectPickBasketCountByConditions(@Param("id") String id, @Param("p") MailContentListQuery contentListQuery);
}
