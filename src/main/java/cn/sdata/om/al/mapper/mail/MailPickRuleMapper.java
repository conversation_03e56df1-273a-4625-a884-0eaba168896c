package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailPickRuleCondition;
import cn.sdata.om.al.entity.mail.params.MailPickRuleListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickRule;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleListVo;

import java.util.List;

public interface MailPickRuleMapper {
    List<MailPickRuleListVo> page(MailPickRuleListQuery mailPickRuleListQuery);

    void deleteConditionsByRuleId(String id);

    int update(SaveOrUpdateMailPickRule saveOrUpdateMailPickRule);

    int save(SaveOrUpdateMailPickRule saveOrUpdateMailPickRule);

    void batchSaveConditions(List<MailPickRuleCondition> executeConditions);

    MailPickRuleDetail getById(String id);

    List<CommonEntity> list();

    boolean delete(List<String> ids);

    void batchDeleteConditionsByRuleId(List<String> ids);

    List<MailPickRuleDetail> selectAllRule();

    List<CommonEntity> unbindRuleList();

    void updateBasketRuleToNull(List<String> ids);

}
