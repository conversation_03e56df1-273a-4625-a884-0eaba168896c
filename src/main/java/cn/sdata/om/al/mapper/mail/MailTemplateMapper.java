package cn.sdata.om.al.mapper.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailTemplateListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailTemplate;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MailTemplateMapper {
    int batchDeleteByIds(List<String> ids);

    int update(SaveOrUpdateMailTemplate saveOrUpdateMailTemplate);

    int save(SaveOrUpdateMailTemplate saveOrUpdateMailTemplate);

    List<MailTemplateListVo> page(MailTemplateListQuery mailTemplateListQuery);

    List<CommonEntity> list();

    MailTemplateDetailVo getById(String id);

    List<CommonEntity> getAccountSetList(@Param("fieldTypeId") String fieldTypeId);

    List<CommonEntity> getFieldTypeList();

}
