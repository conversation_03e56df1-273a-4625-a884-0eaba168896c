package cn.sdata.om.al.mapper.monthlyData;

import cn.sdata.om.al.entity.monthlyData.LogCallRpaMonthlyDataEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/30 13:40
 * @Version 1.0
 */
@Mapper
public interface LogCallRpaMonthlyDataMapper extends BaseMapper<LogCallRpaMonthlyDataEntity> {

    @Select("select lcrmd.*, rel.rpa_biz_scene as task_name, rel.start_time , rel.finish_time, rel.exec_result \n" +
            "from log_call_rpa_monthly_data lcrmd \n" +
            "left join rpa_exec_log rel \n" +
            "on lcrmd.rpa_exec_log_id = rel.id \n" +
            "where lcrmd.data_date  = #{dataDate} \n" +
            "order by lcrmd.create_time desc")
    List<LogCallRpaMonthlyDataEntity> getCallRpaLogsByDataDate(@Param("dataDate") String dataDate);


    /**
     * @param id
     * @return
     */
    @Select("select lcrmd.*, rel.rpa_biz_scene as task_name, rel.start_time , rel.finish_time, rel.exec_result \n" +
            "from log_call_rpa_monthly_data lcrmd \n" +
            "left join rpa_exec_log rel \n" +
            "on lcrmd.rpa_exec_log_id = rel.id \n" +
            "where lcrmd.id = #{id} ")
    LogCallRpaMonthlyDataEntity getCallRpaLogById(@Param("id") String id);
}
