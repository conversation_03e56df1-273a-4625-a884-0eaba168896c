package cn.sdata.om.al.ocr;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.sdata.om.al.utils.StringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralProRequest;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralProResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class OCRUtil {

    @Value("${ocr.secretId:}")
    private String secretId;

    @Value("${ocr.secretKey:}")
    private String secretKey;

    @Value("${ocr.endpoint:}")
    private String endpoint;

    @Value("${ocr.base-url}")
    private String baseUrl;

    @Value("${ocr.interface-uri}")
    private String interfaceURI;

    @Value("${ocr.use-api}")
    private boolean useApi;

    @Value("${ocr.token}")
    private String token;

    @NotNull
    private OcrClient getOcrClient() {
        Credential cred = new Credential(secretId, secretKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(endpoint);
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        return new OcrClient(cred, "", clientProfile);
    }

    public OCRResult executeOCR(byte[] bytes) {
        try {
            if (useApi) {
                return useApiExecuteOCR(bytes);
            }
            log.info("开始调用OCR,使用直连模式...");
            OcrClient client = getOcrClient();
            // SmartStructuralOCRV2Request req = new SmartStructuralOCRV2Request();
            SmartStructuralProRequest req = new SmartStructuralProRequest();
            req.setImageBase64(StringUtil.transFileToBase64(bytes));
            SmartStructuralProResponse resp = client.SmartStructuralPro(req);
            // SmartStructuralOCRV2Response resp = client.SmartStructuralOCRV2(req);
            return this.parseToMaps(AbstractModel.toJsonString(resp));
        } catch (Exception e) {
//            throw new RuntimeException("OCR接口执行异常", e);
            log.error("OCR接口执行异常", e);
            e.printStackTrace();
        }
        return new OCRResult();
    }

    public OCRResult useApiExecuteOCR(byte[] bytes) throws Exception {
        log.info("开始调用OCR,使用封装API模式...");
        int numberOfPages = 1;
        try (PDDocument pdf = PDDocument.load(bytes)) {
            numberOfPages = pdf.getNumberOfPages();
        } catch (Exception e) {
            log.error("加载pdf失败...无法获取页数默认设置为1");
            e.printStackTrace();
        }
        log.info("此pdf一共有{}页", numberOfPages);
        String fileToBase64 = StringUtil.transFileToBase64(bytes);
        JSONObject param = new JSONObject();
        param.put("ImageBase64", fileToBase64);
        param.put("PdfPageNumber", numberOfPages);
        param.put("ReturnFullText", true);
        JSONObject printParam = new JSONObject(param);
        printParam.put("ImageBase64", fileToBase64.substring(0, 10));
        log.info("组装请求的body参数为:{}", JSON.toJSONString(printParam));
        try (HttpResponse response = HttpUtil.createPost(baseUrl + interfaceURI).header(" X-CLOUD-TOKEN", token).body(JSON.toJSONBytes(param)).contentType(ContentType.JSON.getValue()).execute()) {
            log.info("请求api的响应码为:{}", response.getStatus());
            if (response.isOk()) {
                String body = response.body();
                log.info("返回的body的完整信息为:{}", body);
                if (StringUtils.isNotBlank(body)) {
                    JSONObject jsonObject = JSONObject.parseObject(body);
                    log.info("接口返回结构所包含的keys为:{}", jsonObject.keySet());
                    String code = jsonObject.getString("code");
                    String msg = jsonObject.getString("msg");
                    log.info("接口返回的状态码为:{}, 返回信息为:{}", code, msg);
                    if (StringUtils.isNotBlank(code) && "200".equals(code)) {
                        String data = jsonObject.getString("data");
                        if (StringUtils.isNotBlank(data)) {
                            log.info("接口的结果中是否包含StructuralList:{}", JSONObject.parseObject(data).containsKey("StructuralList"));
                            return this.parseToMaps(data);
                        }
                    }
                }
            }
        }
        return new OCRResult();
    }


    public OCRResult executeOCRv2(byte[] bytes) {
        if (useApi) {
            try {
                return useApiExecuteOCR(bytes);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return executeOCR(bytes);
    }


    /**
     * 此方法目前只用于中保登缴费单(2页)ocr识别
     * @param bytes 字节数据
     * @param pageSize 页数
     * @return 结果对象
     */
    public OCRResult executeOCRV2(byte[] bytes, int pageSize) {
        try {
            if (useApi) {
                return useApiExecuteOCR(bytes);
            }
            return executeOCR(bytes);
        } catch (Exception e) {
            throw new RuntimeException("OCR接口执行异常", e);
        }
    }

    public OCRResult parseToMaps(String json) {
        try {
            log.info("开始处理StructuralList...");
            Objects.requireNonNull(json, "json不得为空");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(json);
            // 第一层：提取StructuralList
            JsonNode structuralList = rootNode.path("StructuralList");
            OCRResult ocrResult = new OCRResult();
            for (JsonNode groupNode : structuralList) {
                // 第二层：处理Groups数组
                List<Map<String, String>> tableData = ocrResult.getTableData();
                Map<String, String> commonData = ocrResult.getCommonData();
                for (JsonNode group : groupNode.path("Groups")) {
                    // 第三层：处理Lines数组
                    Map<String, String> record = new LinkedHashMap<>();
                    JsonNode lines = group.path("Lines");
                    if (lines.size() > 1) {
                        for (JsonNode line : lines) {
                            String key = line.path("Key").path("AutoName").asText();
                            String value = line.path("Value").path("AutoContent").asText();
                            // 清理OCR识别结果中的多余空格
                            value = cleanOcrText(value);
                            record.put(key, value);
                        }
                    } else if (lines.size() == 1) {
                        JsonNode line = lines.get(0);
                        String key = line.path("Key").path("AutoName").asText();
                        String value = line.path("Value").path("AutoContent").asText();
                        // 清理OCR识别结果中的多余空格
                        value = cleanOcrText(value);
                        String alreadyValue = commonData.get(key);
                        if (alreadyValue != null) {
                            commonData.put(autoKey(key), value);
                        } else {
                            commonData.put(key, value);
                        }
                    }
                    // 只有当记录不为空时才加入结果
                    if (!record.isEmpty()) {
                        tableData.add(record);
                    }
                }
            }
            return ocrResult;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public OCRResult parseToMaps(OCRResult ocrResult, String json) {
        try {
            Objects.requireNonNull(json, "json不得为空");
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(json);
            // 第一层：提取StructuralList
            JsonNode structuralList = rootNode.path("StructuralList");
            for (JsonNode groupNode : structuralList) {
                // 第二层：处理Groups数组
                List<Map<String, String>> tableData = ocrResult.getTableData();
                Map<String, String> commonData = ocrResult.getCommonData();
                for (JsonNode group : groupNode.path("Groups")) {
                    // 第三层：处理Lines数组
                    Map<String, String> record = new LinkedHashMap<>();
                    JsonNode lines = group.path("Lines");
                    if (lines.size() > 1) {
                        for (JsonNode line : lines) {
                            String key = line.path("Key").path("AutoName").asText();
                            String value = line.path("Value").path("AutoContent").asText();
                            // 清理OCR识别结果中的多余空格
                            value = cleanOcrText(value);
                            record.put(key, value);
                        }
                    } else if (lines.size() == 1) {
                        JsonNode line = lines.get(0);
                        String key = line.path("Key").path("AutoName").asText();
                        String value = line.path("Value").path("AutoContent").asText();
                        // 清理OCR识别结果中的多余空格
                        value = cleanOcrText(value);
                        String alreadyValue = commonData.get(key);
                        if (alreadyValue != null) {
                            commonData.put(autoKey(key), value);
                        } else {
                            commonData.put(key, value);
                        }
                    }
                    // 只有当记录不为空时才加入结果
                    if (!record.isEmpty()) {
                        tableData.add(record);
                    }
                }
            }
            return ocrResult;
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private String autoKey(String key) {
        if (key.contains("-")) {
            String[] split = key.split("-");
            int number = Integer.parseInt(split[1]);
            return key + "-" + (number + 1);
        } else {
            return key + "-" + 1;
        }
    }

    /**
     * 清理OCR识别结果中的多余空格
     *
     * @param text OCR识别的原始文本
     * @return 清理后的文本
     */
    private String cleanOcrText(String text) {
        if (text == null) {
            return null;
        }

        // 1. 去除首尾空格
        text = text.trim();

        // 2. 将多个连续的空格（包括全角空格）替换为单个空格
        text = text.replaceAll("[\\s\\u00A0\\u3000]+", " ");

        // 3. 去除特定位置的多余空格（如"中德安联人寿保险有限公司-分红 PARIII"中的空格）
        // 针对客户名称中英文字符前的多余空格进行特殊处理
        text = text.replaceAll("\\s+([A-Z]+)", "$1");

        return text;
    }

}
