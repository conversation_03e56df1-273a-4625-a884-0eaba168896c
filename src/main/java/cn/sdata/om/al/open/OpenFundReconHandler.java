package cn.sdata.om.al.open;

import cn.hutool.core.io.FileUtil;
import cn.sdata.om.al.entity.OpenFundReconciliationStatement;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import cn.sdata.om.al.service.OpenFundReconciliationStatementService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OpenFundReconHandler implements BaseBoxHandler {

    private OpenFundReconciliationStatementService openFundReconfirmationStatementService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${open.fund.reconciliation.path:}")
    private String reconciliationPath;

    @Autowired
    public void setOpenFundReconfirmationStatementService(OpenFundReconciliationStatementService openFundReconfirmationStatementService) {
        this.openFundReconfirmationStatementService = openFundReconfirmationStatementService;
    }

    @Override
    public void execute(MailContent mailContent) {
        Objects.requireNonNull(mailContent, "缺失mailContent");
        String mailId = mailContent.getId();
        //判断已确认不会重新识别
        LambdaQueryWrapper<OpenFundReconciliationStatement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpenFundReconciliationStatement::getEmailId, mailId);
        List<OpenFundReconciliationStatement> openFundReconciliationStatements = openFundReconfirmationStatementService.list(queryWrapper);
        if (!openFundReconciliationStatements.isEmpty()) {
            OpenFundReconciliationStatement openFundReconciliationStatement = openFundReconciliationStatements.get(0);
            if (OcrConfirmationStatus.CONFIRMED.equals(openFundReconciliationStatement.getOcrConfirmationStatus())) {
                return;
            }
        }
        String attachmentStr = mailContent.getAttachmentStr();
        try {
            ParseParam parseParam = openFundReconfirmationStatementService.initMap();
            parseParam.setMailContent(mailContent);
            List<MailAttachment> attachments = objectMapper.readValue(attachmentStr, new TypeReference<>() {
            });
            attachments = filterPdf(attachments);
            for (MailAttachment attachment : attachments) {
                parseParam.setMailAttachment(attachment);
                byte[] bytes = FileUtil.readBytes(attachment.getFilePath());
                String filePath = getNewFilePath(mailContent.getId(), attachment.getFileName());
                openFundReconfirmationStatementService.deal(bytes, filePath, parseParam);
            }
        } catch (Exception e) {
            log.error("附件解析异常", e);
        }
    }

    private List<MailAttachment> filterPdf(List<MailAttachment> mailAttachments) {
        Objects.requireNonNull(mailAttachments, "附件信息为空");
        return mailAttachments.stream()
                .filter(mailAttachment -> mailAttachment.getFileName().contains(".pdf") || mailAttachment.getFileName().contains(".PDF")).collect(Collectors.toList());
    }

    private @NonNull String getNewFilePath(String mailId, String fileName) {
        return reconciliationPath + File.separator + mailId + File.separator + fileName;
    }

}
