package cn.sdata.om.al.open;

import cn.sdata.om.al.entity.OpenFundFieldMapping;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.MailContent;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class ParseParam {

    private Map<String, String> suffixAdministrator = new HashMap<>();
    private Map<String, String> accountNameProductId = new HashMap<>();
    private Map<String, OpenFundFieldMapping> keyFieldMapping = new HashMap<>();
    private Map<String, OpenFundFieldMapping> valueFieldMapping = new HashMap<>();
    private Map<String, String> productIdName = new HashMap<>();

    private MailContent mailContent;
    private MailAttachment mailAttachment;

}
