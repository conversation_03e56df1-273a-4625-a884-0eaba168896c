package cn.sdata.om.al.qrtz;



import cn.hutool.core.util.ObjectUtil;
import org.quartz.Job;

import java.util.HashMap;
import java.util.Map;

public class QuartzJobFactory {

	public static Map<String, Class<? extends Job>> jobClassMap = new HashMap<>();

	public static Class<? extends Job> getJobClass(String jobClassName){
		return jobClassMap.get(jobClassName);
	}

	public static void addJobClass(String jobClassName, Class<? extends Job> jobClass) {
		if (ObjectUtil.isNotEmpty(jobClassName) && jobClass != null) {
			jobClassMap.put(jobClassName, jobClass);
		}
	}

	public static boolean containKey(String jobClassName) {
		return jobClassMap.containsKey(jobClassName);
	}

}
