package cn.sdata.om.al.qrtz.aspect;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.qrtz.annotation.TradeDay;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class JobAspect {

    private final CronService cronService;

    private final BaseCronLogService baseCronLogService;

    @Pointcut("execution (* cn.sdata.om.al.job..*.execute(..))")
    public void executeMethod() {
    }

    @Pointcut("execution (* cn.sdata.om.al.job..*.doExecute(..))")
    public void doExecuteMethod() {
    }

    @Around("executeMethod()")
    public Object executeAround(ProceedingJoinPoint joinPoint) throws Throwable {
        //如果类包含交易日注解.说明希望这个任务只有交易日执行,则判断当前日期是否是交易日
        Class<?> aClass = joinPoint.getTarget().getClass();
        TradeDay tradeDayAnno = aClass.getAnnotation(TradeDay.class);
        if (tradeDayAnno == null) {
            //没有tradeDay注解,直接执行.
            return joinPoint.proceed();
        }
        String tradeCalendar = (String) getJobDetailValue(joinPoint, CronConstant.TRADE_CALENDAR);
        if (tradeCalendar == null) {
            log.error("未找到交易日历, 停止执行调度任务");
            return null;
        }
        String tradeDay = cronService.getTradeDay(tradeCalendar, DateUtil.format(new Date(), BaseConstant.DATE_FORMAT_PATTERN), 0, BaseConstant.DATE_FORMAT_PATTERN);
        if (tradeDay != null) {
            return joinPoint.proceed();
        } else {
            boolean isTest = false;
            String property = SpringUtil.getProperty("system.is-test");
            if (StringUtils.isNotBlank(property)) {
                isTest = Boolean.parseBoolean(property);
            }
            if (isTest) {
                return joinPoint.proceed();
            }
            log.info("当前日期不是交易日, {}不执行", aClass.getName());
        }
        return null;
    }

    @Around("doExecuteMethod()")
    public Object doExecuteAround(ProceedingJoinPoint joinPoint) {
        //真正执行了业务代码, 需要在这里执行一些日志信息
        BaseCronLog baseCronLog = new BaseCronLog();
        baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String dataDateStr = cronService.getManualDataDate(getJobContext(joinPoint));
        baseCronLog.setDataDate(dataDateStr);
        String jobID = (String) getJobDetailValue(joinPoint, CronConstant.JOB_ID);
        baseCronLog.setTaskId(jobID);
        String executor = (String) getJobDetailValue(joinPoint, CronConstant.EXECUTOR);
        String executeMethod = (String) getJobDetailValue(joinPoint, CronConstant.MANUAL);
        Boolean sync = (Boolean) getJobDetailValue(joinPoint, CronConstant.SYNC);
        if (executor == null) {
            executor = BaseConstant.DEFAULT_USERNAME;
        }
        if (executeMethod != null) {
            baseCronLog.setExecuteMethod(executeMethod);
        }
        baseCronLog.setExecutor(executor);
        baseCronLog.setStatus(JobStatus.RUNNING);
        baseCronLogService.saveOrUpdate(baseCronLog);
        String id = baseCronLog.getId();
        baseCronLog.setId(id);
        log.trace("执行doExecute方法");
        Object proceed = null;
        try {
            putArgs(joinPoint, id);
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            baseCronLog.setStatus(JobStatus.FAILED);
            baseCronLog.setTaskInfo(e.getMessage());
            baseCronLog.setEndDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (sync != null && sync) {
            baseCronLog.setStatus(JobStatus.COMPLETE);
            baseCronLog.setEndDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        baseCronLogService.saveOrUpdate(baseCronLog);
        return proceed;
    }

    private void putArgs(ProceedingJoinPoint joinPoint, String logId) {
        Object[] args = joinPoint.getArgs();
        JobExecutionContext context = (JobExecutionContext) args[0];
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        mergedJobDataMap.put(CronConstant.LOG_ID, logId);
    }

    private Object getJobDetailValue(ProceedingJoinPoint joinPoint, String key) {
        if (joinPoint == null) {
            return null;
        }
        JobExecutionContext context = getJobContext(joinPoint);
        if (context == null) {
            return null;
        }
        JobDataMap jobDataMap = context.getMergedJobDataMap();
        return jobDataMap.get(key);
    }

    private JobExecutionContext getJobContext(ProceedingJoinPoint joinPoint) {
        if (joinPoint == null) {
            return null;
        }
        Class<?> aClass = joinPoint.getTarget().getClass();
        Object[] args = joinPoint.getArgs();
        if (args.length != 0) {
            return (JobExecutionContext) args[0];
        }
        log.error("{}的execute方法参数错误", aClass.getName());
        return null;
    }

}
