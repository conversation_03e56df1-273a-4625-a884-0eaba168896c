package cn.sdata.om.al.qrtz.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.*;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.qrtz.vo.CronGroupRelationVO;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.CommonQueryService;
import cn.sdata.om.al.service.RpaExecLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/cron")
@AllArgsConstructor
public class CronController {

    private final CronService cronService;
    private final CommonQueryService<BaseCronLog> commonQueryService;
    private final RpaExecLogService rpaExecLogService;

    /**
     * 可执行任务类
     *
     * @return 定时任务集合
     */
    @GetMapping("/query-job-class")
    public R<List<KeyValueVO>> queryJobClass() {
        return cronService.queryJobClass();
    }

    /**
     * 查询任务
     *
     * @param cron cron类
     * @return 定时任务集合
     */
    @GetMapping("/query-job")
    public R<List<Cron>> queryJob(Cron cron) {
        return cronService.queryJob(Condition.getQueryWrapper(cron));
    }

    /**
     * 查询任务组任务树
     * @return 任务组树
     */
    @GetMapping("tree")
    public R<List<CronGroupRelationVO>> getTree() {
        return R.ok(cronService.getTree());
    }

    @PostMapping("update")
    public R<List<CronGroupRelationVO>> update(@RequestBody Cron cron) {
        if (cron == null) {
            throw new RuntimeException("cron不得为空");
        }
        boolean updated = cronService.updateJob(cron);
        return updated ? R.ok("更新成功") : R.failed("更新失败");
    }

    /**
     * 新增定时任务
     *
     * @param cron cron类
     * @return 操作结果
     */
    @PostMapping("/add-job")
    public R<?> addJob(@RequestBody Cron cron) {
        return cronService.addJob(cron);
    }

    /**
     * 启用任务
     *
     * @param param 任务ID
     * @return 操作结果
     */
    @PostMapping("/enable-job")
    public R<?> enableJob(@RequestBody Map<String, List<String>> param) {
        List<String> jobIds = param.get("jobIds");
        if (jobIds == null) {
            throw new TaskException("jobIds不得为空");
        }
        return cronService.enableJob(jobIds);
    }

    /**
     * 立即启动
     *
     * @param param 任务ID
     * @return 操作结果
     */
    @PostMapping("/start-job-now")
    @SuppressWarnings("unchecked")
    public R<?> startJobNow(@RequestBody Map<String, Object> param) {
        List<String> jobIds = (List<String>) param.get("jobIds");
        if (jobIds == null) {
            throw new TaskException("jobIds不得为空");
        }

        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(CronConstant.MANUAL, "MANUAL");
        jobDataMap.put(CronConstant.DATA_DATE_OFFSET, param.get("dataDateOffset"));
        jobDataMap.put(CronConstant.START_DATE, (String) param.get("startDate"));
        jobDataMap.put(CronConstant.END_DATE, (String) param.get("endDate"));
        boolean result = cronService.startJobNow(jobIds, jobDataMap);
        return result ? R.ok("启动成功") : R.failed("启动失败");
    }

    @PostMapping("/start-group-job")
    public R<?> startGroupJobNow(@RequestBody Map<String, String> param) {
        String groupId = param.get("groupId");
        if (groupId == null) {
            throw new TaskException("groupId不得为空");
        }
        boolean result = cronService.startGroupJobNow(groupId);
        return result ? R.ok("启动成功") : R.failed("启动失败");
    }

    /**
     * 停用任务
     *
     * @param param 任务ID
     * @return 操作结果
     */
    @PostMapping("/stop-job")
    public R<?> stopJob(@RequestBody Map<String, List<String>> param) {
        List<String> jobIds = param.get("jobIds");
        if (jobIds == null) {
            throw new TaskException("jobIds不得为空");
        }
        return cronService.stopJob(jobIds);
    }

    /**
     * 删除任务
     * @param param 任务ID
     * @return 操作结果
     */
    @PostMapping("/delete-job")
    public R<?> deleteJob(@RequestBody Map<String, String> param) {
        String jobId = param.get("jobId");
        if (jobId == null) {
            return R.failed("jobId不得为空");
        }
        return cronService.deleteJob(jobId);
    }

    @PostMapping("/restore-job")
    public R<?> restoreJob(@RequestBody Map<String, String> param) {
        try {
            String jobId = param.get("jobId");
            if (jobId == null) {
                return R.failed("jobId不得为空");
            }
            boolean restored = cronService.restoreJob(jobId);
            if (restored) {
                LambdaQueryWrapper<Cron> cronWrapper = new LambdaQueryWrapper<>();
                cronWrapper.eq(Cron::getJobId, jobId);
                Cron cron = cronService.getOne(cronWrapper);
                cronService.doAddJob(cron);
            }
        } catch (Exception e) {
            return R.failed("恢复失败");
        }
        return R.ok("恢复成功");
    }

    @PostMapping("/log/page")
    public R<?> jobLogPage(@RequestBody CommonPageParam<BaseCronLog> commonPageParam) {
        Map<String, String> map = new HashMap<>();
        map.put("START_DATE_TIME", "desc");
        commonPageParam.setOrderColumn(map);
        Page<BaseCronLog> pages = commonQueryService.commonPage(commonPageParam, BaseCronLog.class);
        List<CronGroupRelation> relation = cronService.getBaseMapper().getRelation();
        Map<String, Cron> taskMap = cronService.list().stream().collect(Collectors.toMap(Cron::getJobId, cron -> cron, (cron1, cron2) -> cron2));
        Map<String, List<CronGroupRelation>> groupMap = relation.stream().collect(Collectors.groupingBy(CronGroupRelation::getJobId));
        for (BaseCronLog record : pages.getRecords()) {
            String taskId = record.getTaskId();
            List<CronGroupRelation> cronGroupRelations = groupMap.get(taskId);
            Cron job = taskMap.get(taskId);
            if (cronGroupRelations != null) {
                List<String> jobGroup = cronGroupRelations.stream().map(CronGroupRelation::getGroupName).collect(Collectors.toList());
                record.setJobGroup(jobGroup);
            }
            if (job != null) {
                record.setTaskName(job.getJobName());
            }
        }
        return R.ok(pages);
    }

    private byte[] hexStringToByteArray(String hex) {
        hex = hex.replaceAll("\\s+", ""); // 去除所有空白字符
        if (hex.length() % 2 != 0) {
            hex = "0" + hex; // 补前导零
        }
        byte[] result = new byte[hex.length() / 2];
        for (int i = 0; i < result.length; i++) {
            int index = i * 2;
            int val = Integer.parseInt(hex.substring(index, index + 2), 16);
            result[i] = (byte) (val & 0xFF); // 处理有符号字节溢出
        }
        return result;
    }


    /**
     * 根据日志id获取rpa错误截图
     * @param logId 日志id
     * @return 错误截图base64字符串
     */
    @GetMapping("/log/getErrorImage")
    public R<String> getErrorImage(@RequestParam("logId") String logId) {
        List<String> errorImgs = rpaExecLogService.getErrorImageByLogId(logId);
        if (CollectionUtil.isEmpty(errorImgs)) {
            return R.restResult("", 200, "没有错误图片");
        }
        String errorImg = errorImgs.get(0);
        try {
            if (StringUtils.isNotBlank(errorImg)) {
                byte[] images = hexStringToByteArray(errorImg);
                String str = Base64.getEncoder().encodeToString(images);
                return R.restResult("data:image/jpeg;base64," + str, 200, "ok");
            }
            return R.restResult("", 200, "ok");
        } catch (Exception e) {
            e.printStackTrace();
            return R.failed(e.getMessage());
        }
    }

    @GetMapping("test-cal")
    public R<?> test() {
        cronService.testCal();
        return R.ok();
    }

    @GetMapping("group-job")
    public R<?> showJob(String groupId) {
        Map<String, List<Cron>> result = cronService.queryNoGroupJob(groupId);
        return R.ok(result);
    }

    @PostMapping("add-group-job")
    public R<?> addGroupJob(@RequestBody GroupCronParam param) {
        return cronService.saveGroupJob(param);
    }

    @GetMapping("list")
    public R<?> listAllCron() {
        List<Map<String, String>> result = cronService.list().stream()
                .map(cron -> Map.of("jobId", cron.getJobId(), "jobName", cron.getJobName()))
                .collect(Collectors.toList());
        return R.ok(result);
    }

    @GetMapping("file")
    public R<?> getFileInfo(String jobId) {
        return R.ok(cronService.getRemoteFileInfo(jobId));
    }
}
