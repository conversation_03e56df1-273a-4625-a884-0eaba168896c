package cn.sdata.om.al.qrtz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 调度任务组表
 */
@Data
@TableName(value = "base_cron_group")
public class BaseCronGroup {
    /**
     * 任务组ID
     */
    @TableId(value = "group_id")
    private String groupId;

    /**
     * 任务组NAME
     */
    @TableField(value = "group_name")
    private String groupName;
}