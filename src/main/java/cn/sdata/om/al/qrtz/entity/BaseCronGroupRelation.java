package cn.sdata.om.al.qrtz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 调度任务组关系表
 */
@Data
@TableName(value = "base_cron_group_relation")
public class BaseCronGroupRelation {
    /**
     * 任务组ID
     */
    @TableField(value = "group_id")
    private String groupId;

    /**
     * 任务ID
     */
    @TableField(value = "job_id")
    private String jobId;

    @TableField(value = "seq")
    private Integer seq;
}