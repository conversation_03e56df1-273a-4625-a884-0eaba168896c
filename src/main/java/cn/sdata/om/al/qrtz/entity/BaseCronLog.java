package cn.sdata.om.al.qrtz.entity;


import cn.sdata.om.al.qrtz.enums.JobStatus;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 调度执行日志
 */
@Data
@TableName(value = "base_cron_log")
public class BaseCronLog {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务ID
     */
    @TableField(value = "TASK_ID")
    private String taskId;

    /**
     * 任务组
     */
    @TableField(exist = false)
    private List<String> jobGroup;

    /**
     * 任务名称
     */
    @TableField(exist = false)
    private String taskName;

    /**
     * 调度开始时间
     */
    @TableField(value = "START_DATE_TIME")
    private String startDateTime;

    /**
     * 调度结束时间
     */
    @TableField(value = "END_DATE_TIME")
    private String endDateTime;

    /**
     * 数据日期
     */
    @TableField(value = "DATA_DATE")
    private String dataDate;

    /**
     * 执行人
     */
    @TableField(value = "EXECUTOR")
    private String executor;


    /**
     * 状态 运行中 RUNNING 完成 COMPLETE 异常 FAILED
     */
    @TableField(value = "`STATUS`")
    private JobStatus status;

    /**
     * 执行方式 AUTO 自动 MANUAL 手动
     */
    @TableField(value = "execute_method")
    private String executeMethod = "AUTO";

    @TableField(value = "rpa_status")
    private JobStatus rpaStatus;

    /**
     * 执行信息
     */
    @TableField(value = "TASK_INFO")
    private String taskInfo;

    /**
     * rpa执行id
     */
    @TableField(value = "EXEC_ID")
    private String execId;
}
