package cn.sdata.om.al.qrtz.entity;


import cn.sdata.om.al.qrtz.enums.JobPlan;
import cn.sdata.om.al.qrtz.vo.CronVO;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.quartz.JobKey;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 定时任务表
 */
@TableName(value = "base_cron")
@Data
public class Cron implements Serializable {

    /**
     * 定时任务ID
     */
    @TableId(value = "job_id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private String jobId;

    /**
     * 定时任务名称
     */
    @TableField(value = "job_name", jdbcType = JdbcType.VARCHAR)
    private String jobName;

    /**
     * 定时任务分组
     */
    @TableField(value = "job_group", jdbcType = JdbcType.VARCHAR)
    private String jobGroup;

    /**
     * 触发器名称
     */
    @TableField(value = "trigger_name", jdbcType = JdbcType.VARCHAR)
    private String triggerName;

    /**
     * 触发器分组
     */
    @TableField(value = "trigger_group", jdbcType = JdbcType.VARCHAR)
    private String triggerGroup;

    /**
     * 执行类名称
     */
    @TableField(value = "class_name", jdbcType = JdbcType.VARCHAR)
    private String className;

    /**
     * 执行类路径
     */
    @TableField(value = "class_path", jdbcType = JdbcType.VARCHAR)
    private String classPath;

    /**
     * 执行周期
     */
    @TableField(value = "cron", jdbcType = JdbcType.VARCHAR)
    private String cron;

    /**
     * 0-手动，1-自动
     */
    @TableField(value = "status", jdbcType = JdbcType.BIGINT)
    private Long status;

    /**
     * 备注
     */
    @TableField(value = "remark", jdbcType = JdbcType.VARCHAR)
    private String remark;

    @TableField(value = "trade_calendar")
    private String tradeCalendar;

    @TableField(value = "data_date_offset")
    private Integer dataDateOffset;

    @TableField(value = "execute_calendar")
    private String executeCalendar;

    @TableField(value = "execute_plan")
    private JobPlan executePlan;

    @JsonIgnore
    @TableField(exist = false)
    private JobKey nextJobKey;

    /**
     * 创建人
     */
    @JsonIgnore
    @TableField(value = "create_by", fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String createBy;

    /**
     * 创建时间
     */
    @JsonIgnore
    @TableField(value = "create_time", fill = FieldFill.INSERT, jdbcType = JdbcType.DATE)
    private Date createTime;

    /**
     * 修改人
     */
    @JsonIgnore
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.VARCHAR)
    private String updatedBy;

    /**
     * 修改时间
     */
    @JsonIgnore
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.DATE)
    private Date updatedTime;

    /**
     * 是否启用
     */
    @TableField(value = "is_deleted", jdbcType = JdbcType.BIGINT)
    @TableLogic(value = "0", delval = "1")
    private Long isDeleted;

    @TableField(value = "flow_id", jdbcType = JdbcType.BIGINT)
    private Integer flowId;

    @TableField(value = "mail_id", jdbcType = JdbcType.VARCHAR)
    private String mailId;

    @TableField(value = "auto_mail", jdbcType = JdbcType.BIGINT)
    private Integer autoMail;

    @TableField(value = "postpone", jdbcType = JdbcType.BIGINT)
    private Integer postpone;

    @TableField(value = "postpone_replay", jdbcType = JdbcType.BIGINT)
    private Integer postponeReplay;

    @TableField(value = "contact_type", jdbcType = JdbcType.VARCHAR)
    private String contactType;

    @TableField(value = "disclosure_handler", jdbcType = JdbcType.VARCHAR)
    private String disclosureHandler;

    @TableField(exist = false)
    private String specifiedHandler;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> extraData = new HashMap<>();

    public CronVO transToVO(Map<String, BaseCronLog> taskLog) {
        CronVO vo = new CronVO();
        vo.setId(jobId);
        BaseCronLog baseCronLog = taskLog.get(jobId);
        vo.setCronName(jobName);
        vo.setCreateBy(createBy);
        vo.setCreateTime(createTime);
        vo.setTradeCalendar(tradeCalendar);
        vo.setDataDateOffset(dataDateOffset);
        vo.setExecuteCalendar(executeCalendar);
        vo.setTaskStatus(status);
        vo.setCron(cron);
        vo.setIsDeleted(isDeleted);
        if (baseCronLog != null) {
            vo.setLastStatus(baseCronLog.getStatus());
            vo.setLastDataDate(baseCronLog.getDataDate());
            vo.setLastExecutor(baseCronLog.getExecutor());
            vo.setLastExecuteTime(baseCronLog.getStartDateTime());
        }
        return vo;
    }
}
