package cn.sdata.om.al.qrtz.enums;

import lombok.Getter;

import java.util.List;

@Getter
public enum JobCalendar {

    /**
     * 无日历
     */
    DEFAULT("", null, null),

    /**
     * 周第一个工作日
     */
    WEEK1("getWeekDay", List.of(String.class, Integer.class), List.of("00", 1)),
    /**
     * 周第二个工作日
     */
    WEEK2("getWeekDay", List.of(String.class, Integer.class), List.of("00", 2)),
    /**
     * 周第三个工作日
     */
    WEEK3("getWeekDay", List.of(String.class, Integer.class), List.of("00", 3)),
    /**
     * 周第四个工作日
     */
    WEEK4("getWeekDay", List.of(String.class, Integer.class), List.of("00", 4)),
    /**
     * 周第五个工作日
     */
    WEEK5("getWeekDay", List.of(String.class, Integer.class), List.of("00", 5)),

    /**
     * 每月第一个工作日执行
     */
    MONTH_FIRST("getFirstMonthDay", List.of(String.class), List.of("00")),

    /**
     * 每月第二个工作日执行
     */
    MONTH_SECOND("getSecondMonthDay", List.of(String.class), List.of("00")),

    /**
     * 每月第15日执行
     */
    MONTH_MIDDLE("getDays", List.of(String.class, List.class), List.of("00", List.of(15))),

    /**
     * 每月倒数第一个工作日执行
     */
    MONTH_LAST_FIRST("getMonthLastDay", List.of(String.class), List.of("00")),

    /**
     * 每月倒数第二个工作日执行
     */
    MONTH_LAST_SECOND("getMonthSecondLastDay", List.of(String.class), List.of("00"));

    private final String methodName;

    private final List<Class<?>> paramTypes;

    private final List<Object> params;

    JobCalendar(String methodName, List<Class<?>> paramTypes, List<Object> params) {
        this.methodName = methodName;
        this.paramTypes = paramTypes;
        this.params = params;
    }

}
