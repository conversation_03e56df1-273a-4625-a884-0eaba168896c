package cn.sdata.om.al.qrtz.service;


import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseCronLogService extends ServiceImpl<BaseCronLogMapper, BaseCronLog> {

    List<BaseCronLog> queryGroupByTask() {
        return this.getBaseMapper().queryGroupByTask();
    }

    String getLastExecute(String taskId) {
        return this.getBaseMapper().getLastExecute(taskId);
    }


    public BaseCronLog getLatestLog(String taskId, String dataDate) {
        return this.getBaseMapper().getLatestLog(taskId, dataDate);
    }
}
