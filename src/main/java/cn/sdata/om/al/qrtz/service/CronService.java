package cn.sdata.om.al.qrtz.service;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.MarketTradeDay;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.job.GroupJob;
import cn.sdata.om.al.job.SyncValuationTableJob;
import cn.sdata.om.al.mapper.CronMapper;
import cn.sdata.om.al.qrtz.QuartzJobFactory;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.*;
import cn.sdata.om.al.qrtz.enums.JobCalendar;
import cn.sdata.om.al.qrtz.enums.JobPlan;
import cn.sdata.om.al.qrtz.util.ExecuteCalendar;
import cn.sdata.om.al.qrtz.util.GroupTriggerListener;
import cn.sdata.om.al.qrtz.util.JobDataMapList;
import cn.sdata.om.al.qrtz.vo.CronGroupRelationVO;
import cn.sdata.om.al.qrtz.vo.CronVO;
import cn.sdata.om.al.qrtz.vo.KeyValueVO;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.FlowListService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.utils.ReflexUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.*;
import static cn.sdata.om.al.utils.StringUtil.getNowDateStr;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CronService extends ServiceImpl<CronMapper, Cron>
        implements InitializingBean {

    private final Scheduler scheduler;

    private final BaseCronLogService baseCronLogService;

    private final MarketTradeDayService marketTradeDayService;

    private final GroupTriggerListener groupTriggerListener;

    private final BaseCronGroupService baseCronGroupService;

    private final BaseCronGroupRelationService baseCronGroupRelationService;

    private final RemoteFileInfoService remoteFileInfoService;

    private final FlowListService flowListService;

    public R<List<KeyValueVO>> queryJobClass() {
        List<String> jobClass = new ArrayList<>(QuartzJobFactory.jobClassMap.keySet());
        if (ObjectUtil.isNotEmpty(jobClass)) {
            jobClass.sort(Comparator.comparing(Function.identity()));
        }
        List<KeyValueVO> result = jobClass.stream().map(j -> new KeyValueVO(j, j)).collect(Collectors.toList());
        return R.ok(result);
    }

    public R<List<Cron>> queryJob(QueryWrapper<Cron> cronQw) {
        return R.ok(this.list(cronQw));
    }

    @Transactional
    public R<?> addJob(Cron cron) {
        if (!QuartzJobFactory.containKey(cron.getClassName())) {
            return R.failed("保存失败，找不到该定时任务类！");
        }
        boolean isNew = ObjectUtil.isEmpty(cron.getJobId());
        // 判断新增还是修改，如果是修改，判断是否已存在该任务
        LambdaQueryWrapper<Cron> cronLqw = new LambdaQueryWrapper<>();
        cronLqw.eq(Cron::getJobName, cron.getJobName());
        long count = baseMapper.selectCount(isNew ? cronLqw : cronLqw.notIn(Cron::getJobId, cron.getJobId()));
        if (count > 0) {
            return R.failed("保存失败，该任务名已存在！");
        }
        // 将任务分组及执行器分组设为默认值
        if (ObjectUtil.isEmpty(cron.getStatus())) {
            cron.setStatus(0L);
        }
        if (ObjectUtil.isEmpty(cron.getJobGroup())) {
            cron.setJobGroup("运管系统任务");
        }
        if (ObjectUtil.isEmpty(cron.getTriggerName())) {
            cron.setTriggerName(cron.getJobName() + "触发器");
        }
        if (ObjectUtil.isEmpty(cron.getTriggerGroup())) {
            cron.setTriggerGroup("运管系统触发器");
        }
        // 保存执行类路径
        cron.setClassPath(QuartzJobFactory.getJobClass(cron.getClassName()).getName());
        // 保存任务
        this.saveOrUpdate(cron);
        // 如果这是已启用的任务，则启动or重置它
        if (cron.getStatus() == 1) {
            // 删除旧任务
            if (!isNew) {
                deleteJob(cron);
            }
            // 部署新任务
            boolean success = doAddJob(cron);
            if (success) {
                return R.ok("成功保存定时任务，已启动");
            } else {
                return R.ok("成功保存定时任务，启动失败");
            }
        } else {
            return R.ok("成功保存定时任务，未启动");
        }
    }

    @Transactional
    public R<?> enableJob(List<String> jobIds) {
        List<Cron> jobList = getJobs(jobIds);
        // 部署新任务
        for (Cron cron : jobList) {
            if (resumeJob(cron)) {
                cron.setStatus(1L);
            }
        }
        this.saveOrUpdateBatch(jobList);
        return R.ok("成功启动定时任务");
    }

    public boolean startJobNow(List<String> jobIds, JobDataMap jobDataMap) {
        List<Cron> jobs = getJobs(jobIds);
        for (Cron job : jobs) {
            try {
                JobDetail jobDetail = scheduler.getJobDetail(getCronJobKey(job));
                if (jobDataMap == null) {
                    jobDataMap = new JobDataMap();
                }
                if (jobDetail != null) {
                    jobDataMap.putAll(jobDetail.getJobDataMap());
                }
                Integer flowId = job.getFlowId();
                if (ObjectUtil.isNull(flowId) || 0 == flowId) {
                    // 非RPA任务
                    jobDataMap.put(CronConstant.SYNC, true);
                }
                jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
                scheduler.triggerJob(getCronJobKey(job), jobDataMap);
            } catch (SchedulerException e) {
                log.error("启动调度异常", e);
                return false;
            }
        }
        return true;

    }

    public boolean startGroupJobNow(String groupId) {
        JobDataMapList jobDataMapList = JobDataMapList.getInstance();
        List<CronGroupRelation> relation = this.getBaseMapper().getRelation();
        Map<String, List<CronGroupRelation>> groupMap = relation.stream().collect(Collectors.groupingBy(CronGroupRelation::getGroupId));
        List<CronGroupRelation> cronList = groupMap.get(groupId);
        if (cronList != null) {
            List<String> jobIds = cronList.stream().map(CronGroupRelation::getJobId).collect(Collectors.toList());
            Map<Integer, CronGroupRelation> seqMap = cronList.stream()
                    .collect(Collectors.toMap(CronGroupRelation::getSeq, cronGroupRelation -> cronGroupRelation, (cron, cron2) -> cron2, TreeMap::new));
            List<Cron> jobs = getJobs(jobIds);
            Map<String, Cron> jobMap = jobs.stream().collect(Collectors.toMap(Cron::getJobId, cron -> cron, (cron, cron2) -> cron2));
            for (Map.Entry<Integer, CronGroupRelation> entry : seqMap.entrySet()) {
                Integer seq = entry.getKey();
                CronGroupRelation value = entry.getValue();
                String jobId = value.getJobId();
                Cron thisCron = jobMap.get(jobId);
                if (thisCron == null) {
                    continue;
                }
                Integer next = seq + 1;
                CronGroupRelation nextRelation = seqMap.get(next);
                if (nextRelation != null) {
                    String nextJobId = nextRelation.getJobId();
                    Cron nextCron = jobMap.get(nextJobId);
                    if (nextCron == null) {
                        int size = seqMap.size();
                        for (int i = next; i <= size; i++) {
                            nextRelation = seqMap.get(i);
                            if (nextRelation == null)
                                continue;
                            nextJobId = nextRelation.getJobId();
                            nextCron = jobMap.get(nextJobId);
                            if (nextCron != null) {
                                break;
                            }
                        }
                    }
                    if (nextCron == null) {
                        continue;
                    }
                    thisCron.setNextJobKey(getCronJobKey(nextCron));
                    try {
                        JobKey cronJobKey = getCronJobKey(thisCron);
                        JobDetail jobDetail = scheduler.getJobDetail(cronJobKey);
                        if (jobDetail == null) {
                            continue;
                        }
                        JobDataMap jobDataMap = jobDetail.getJobDataMap();
                        jobDataMap.put(CronConstant.EXECUTOR, DEFAULT_USERNAME);
                        jobDataMap.put(CronConstant.NEXT_JOB, thisCron.getNextJobKey());
                        jobDataMapList.put(cronJobKey, thisCron.getNextJobKey(), jobDataMap);
                    } catch (SchedulerException e) {
                        log.error("获取jobDetail异常", e);
                    }
                }
            }
            List<CronGroupRelation> cronGroupRelations = new ArrayList<>(seqMap.values());
            if (!cronGroupRelations.isEmpty()) {
                CronGroupRelation cronGroupRelation = cronGroupRelations.get(0);
                String jobId = cronGroupRelation.getJobId();
                Cron firstJob = jobMap.get(jobId);
                JobKey jobKey = getCronJobKey(firstJob);
                JobDataMap jobDataMap = jobDataMapList.getJobData(jobKey);
                try {
                    scheduler.getListenerManager().addTriggerListener(groupTriggerListener);
                    scheduler.triggerJob(jobKey, jobDataMap);
                } catch (SchedulerException e) {
                    throw new TaskException("启动调度异常", e);
                }
            }
        }
        return true;
    }


    @Transactional
    public R<?> stopJob(List<String> jobIds) {
        List<Cron> jobList = getJobs(jobIds);
        // 反部署，其实是删除quartz表中数据
        for (Cron cron : jobList) {
            boolean result = disableJob(cron);
            if (result) {
                cron.setStatus(0L);
            }
        }
        this.saveOrUpdateBatch(jobList);
        return R.ok("成功停止定时任务");
    }

    @Transactional
    public R<?> deleteJob(String jobIds) {
        // 反部署，其实是删除quartz表中数据
        List<Cron> jobList = getJobs(jobIds);
        // 存放成功反部署的jobId，没有成功反部署的任务，不删除
        List<String> delJobId = new ArrayList<>();
        for (Cron cron : jobList) {
            if (deleteJob(cron)) {
                delJobId.add(cron.getJobId());
            }
        }
        // 删除cron表的数据（逻辑删除）
        this.removeBatchByIds(delJobId);
        return R.ok("成功删除定时任务");
    }

    private Set<JobCalendar> transToCalendar(String executeCalendarStr) {
        if (!executeCalendarStr.contains(",")) {
            return Set.of(JobCalendar.valueOf(executeCalendarStr));
        } else {
            Set<JobCalendar> jobCalendars = new HashSet<>();
            for (String str : executeCalendarStr.split(",")) {
                jobCalendars.add(JobCalendar.valueOf(str));
            }
            return jobCalendars;
        }
    }


    public Boolean doAddJob(Cron cron) {
        try {
            JobDetail jobDetail = createJobDetail(cron);
            String cronStr = cron.getCron();
            String executeCalendarStr = cron.getExecuteCalendar();
            JobPlan executePlan = cron.getExecutePlan();
            Set<JobCalendar> jobCalendars = transToCalendar(executeCalendarStr);
            TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger();
            if (!jobCalendars.contains(JobCalendar.DEFAULT) && (JobPlan.WEEK == executePlan || JobPlan.MONTH == executePlan)) {
                triggerBuilder = getCalendarTriggerBuilder(cron, jobCalendars, triggerBuilder);
            }
            // 按新的cronExpression表达式构建一个新的trigger
            CronTrigger trigger = triggerBuilder
                    .withIdentity(getCronTriggerKey(cron))
                    .withSchedule(CronScheduleBuilder.cronSchedule(cronStr))
                    .startNow()
                    .build();
            scheduler.scheduleJob(jobDetail, trigger);
            Long status = cron.getStatus();
            if (status == 0L) {
                scheduler.pauseTrigger(getCronTriggerKey(cron));
                scheduler.unscheduleJob(getCronTriggerKey(cron));
                log.info("{}处于手动状态", cron.getJobName());
            }
        } catch (Exception e) {
            log.error("初始化定时任务失败！", e);
            return false;
        }
        return true;
    }

    private TriggerBuilder<Trigger> getCalendarTriggerBuilder(Cron cron, Set<JobCalendar> executeCalendars, TriggerBuilder<Trigger> triggerBuilder) throws Exception {
        //不用默认日历 增加
        ReflexUtil<Object> objectReflexUtil = new ReflexUtil<>();
        String tradeCalendar = cron.getTradeCalendar();
        Set<Date> allCalendarDate = new HashSet<>();
        for (JobCalendar executeCalendar : executeCalendars) {
            List<Class<?>> paramTypes = executeCalendar.getParamTypes();
            List<Object> params = new ArrayList<>(executeCalendar.getParams());
            params.set(0, tradeCalendar);
            Set<Date> calendarDate = objectReflexUtil.invokeTradeDayQuery(marketTradeDayService, executeCalendar.getMethodName(), paramTypes, params);
            allCalendarDate.addAll(calendarDate);
        }
        ExecuteCalendar calendar = new ExecuteCalendar();
        calendar.addAllExecuteDate(allCalendarDate);
        scheduler.addCalendar(cron.getJobId(), calendar, true, true);
        return triggerBuilder.modifiedByCalendar(cron.getJobId());
    }

    @SuppressWarnings("unchecked")
    private JobDetail createJobDetail(Cron cron) {
        try {
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(CronConstant.TRADE_CALENDAR, cron.getTradeCalendar());
            jobDataMap.put(CronConstant.DATA_DATE_OFFSET, cron.getDataDateOffset());
            jobDataMap.put(CronConstant.JOB_ID, cron.getJobId());
            Class<? extends Job> jobClass = (Class<? extends Job>) Class.forName(cron.getClassPath());
            // 构建JobDetail
            return JobBuilder.newJob(jobClass)
                    .withIdentity(getCronJobKey(cron))
                    .storeDurably()
                    .setJobData(jobDataMap)
                    .build();
        } catch (ClassNotFoundException e) {
            throw new TaskException(e);
        }
    }

    private TriggerKey getCronTriggerKey(Cron cron) {
        if (cron == null) {
            throw new TaskException("cron为空, 无法获取trigger名称");
        }
        return TriggerKey.triggerKey(cron.getTriggerName() + cron.getJobId(), cron.getTriggerGroup());
    }

    private JobKey getCronJobKey(Cron cron) {
        if (cron == null) {
            throw new TaskException("cron为空, 无法获取job名称");
        }
        return JobKey.jobKey(cron.getJobName() + cron.getJobId(), cron.getJobGroup());
    }

    public Boolean deleteJob(Cron cron) {
        try {
            // 暂停
            scheduler.pauseTrigger(getCronTriggerKey(cron));
            // 解除部署
            scheduler.unscheduleJob(getCronTriggerKey(cron));
            // 删除
            scheduler.deleteJob(getCronJobKey(cron));
        } catch (Exception e) {
            log.error("删除定时任务失败！", e);
            return false;
        }
        return true;
    }

    private boolean disableJob(Cron cron) {
        try {
            //暂停
            scheduler.pauseTrigger(getCronTriggerKey(cron));
            //停止
            scheduler.unscheduleJob(getCronTriggerKey(cron));
            return true;
        } catch (Exception e) {
            log.error("暂停任务失败！", e);
        }
        return false;
    }

    private boolean resumeJob(Cron cron) {
        try {
            String executeCalendarStr = cron.getExecuteCalendar();
            Set<JobCalendar> jobCalendars = transToCalendar(executeCalendarStr);
            TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger();
            if (!jobCalendars.contains(JobCalendar.DEFAULT)) {
                triggerBuilder = getCalendarTriggerBuilder(cron, jobCalendars, triggerBuilder);
            }
            CronTrigger trigger = triggerBuilder
                    .withIdentity(getCronTriggerKey(cron))
                    .forJob(getCronJobKey(cron))
                    .withSchedule(CronScheduleBuilder.cronSchedule(cron.getCron()))
                    .build();
            scheduler.scheduleJob(trigger);
            return true;
        } catch (Exception e) {
            log.error("启用任务失败！", e);
        }
        return false;
    }

    private List<Cron> getJobs(String jobIds) {
        List<String> jobIdList = getJobIds(jobIds);
        if (ObjectUtil.isNotEmpty(jobIdList)) {
            LambdaQueryWrapper<Cron> cronLqw = new LambdaQueryWrapper<>();
            cronLqw.in(Cron::getJobId, jobIdList);
            return this.list(cronLqw);
        }
        return new ArrayList<>();
    }


    public List<Cron> getJobs(List<String> jobIdList) {
        if (ObjectUtil.isNotEmpty(jobIdList)) {
            LambdaQueryWrapper<Cron> cronLqw = new LambdaQueryWrapper<>();
            cronLqw.in(Cron::getJobId, jobIdList);
            return this.list(cronLqw);
        }
        return new ArrayList<>();
    }

    private List<String> getJobIds(String jobIds) {
        List<String> jobIdList = new ArrayList<>();
        if (ObjectUtil.isEmpty(jobIds)) {
            return jobIdList;
        }
        for (String jobId : toStrList(jobIds)) {
            if (ObjectUtil.isNotEmpty(jobId)) {
                jobIdList.add(jobId);
            }
        }
        return jobIdList;
    }

    private List<String> toStrList(String str) {
        if (ObjectUtil.isEmpty(str)) {
            return List.of();
        }
        return Arrays.asList(str.split(","));
    }

    public List<CronGroupRelationVO> getTree() {
        List<CronGroupRelation> relation = this.getBaseMapper().getRelation();
        List<BaseCronLog> baseCronLogs = baseCronLogService.queryGroupByTask();
        Map<String, BaseCronLog> taskLogInfo = baseCronLogs.stream()
                .collect(Collectors
                        .toMap(BaseCronLog::getTaskId,
                                baseCronLog -> baseCronLog,
                                (baseCronLog, baseCronLog2) -> baseCronLog2));

        List<Cron> list = getBaseMapper().listAll();
        Map<String, Cron> collectMap = list.stream().collect(Collectors.toMap(Cron::getJobId,
                cron -> cron,
                (cron, cron2) -> cron2));
        Map<String, CronGroupRelation> groupRelationMap = relation.stream().collect(Collectors.toMap(CronGroupRelation::getGroupId,
                cronGroupRelation -> cronGroupRelation,
                (groupRelation, groupRelation2) -> groupRelation2));
        List<CronGroupRelationVO> result = new ArrayList<>();
        Map<String, List<Cron>> groupCornMap = new TreeMap<>(Comparator.comparing(Long::valueOf));
        for (CronGroupRelation cronGroupRelation : relation) {
            String jobId = cronGroupRelation.getJobId();
            String groupId = cronGroupRelation.getGroupId();
            List<Cron> cronList = groupCornMap.get(groupId);
            Cron cron = collectMap.get(jobId);
            if (cronList == null) {
                cronList = new ArrayList<>();
            }
            cronList.add(cron);
            groupCornMap.put(groupId, cronList);
        }
        for (Map.Entry<String, List<Cron>> entry : groupCornMap.entrySet()) {
            String groupId = entry.getKey();
            List<Cron> cronList = entry.getValue();
            CronGroupRelation groupRelation = groupRelationMap.get(groupId);
            if (groupRelation != null) {
                CronGroupRelationVO cronGroupRelationVO = new CronGroupRelationVO();
                cronGroupRelationVO.setGroupId(groupRelation.getGroupId());
                cronGroupRelationVO.setGroupName(groupRelation.getGroupName());
                List<CronVO> collect = cronList.stream()
                        .map(cron -> cron.transToVO(taskLogInfo))
                        .collect(Collectors.toList());
                cronGroupRelationVO.setChildren(collect);
                result.add(cronGroupRelationVO);
            }
        }
        return result;
    }

    @Transactional
    public boolean updateJob(Cron cronParam) {
        String jobId = cronParam.getJobId();
        if (jobId == null) {
            throw new RuntimeException("更新操作需要传JobId");
        }
//        Cron cronInDB = this.getById(jobId);
//        String tradeCalendar = cronParam.getTradeCalendar();
//        if(tradeCalendar != null){
//            cronInDB.setTradeCalendar(tradeCalendar);
//        }
//        Integer dataDateOffset = cronParam.getDataDateOffset();
//        if (dataDateOffset != null) {
//            cronInDB.setDataDateOffset(dataDateOffset);
//        }
//        String cron = cronParam.getCron();
//        if (cron != null) {
//            cronInDB.setCron(cron);
//        }
//        String executeCalendarStr = cronParam.getExecuteCalendar();
//        if (executeCalendarStr != null) {
//            cronInDB.setExecuteCalendar(executeCalendarStr);
//        }
        //先删除再新增
        this.deleteJob(cronParam);
        //新增任务
        this.doAddJob(cronParam);
        return this.saveOrUpdate(cronParam);
    }

    public boolean restoreJob(String jobId) {
        return getBaseMapper().restoreForce(jobId);
    }

    public String getTradeDay(String tradeCalendar, String baseDate, Integer offset, String pattern) {
        if (CronConstant.NATURAL_DAY_CALENDAR.equals(tradeCalendar)) {
            return new SimpleDateFormat(pattern).format(new Date());
        }
        MarketTradeDay offsetTradeDay = marketTradeDayService.getOffsetTradeDay(tradeCalendar, baseDate, offset);
        if (offsetTradeDay == null) {
            return null;
        }
        Integer date = offsetTradeDay.getDate();
        if (date == null) {
            //当天不是交易日
            return null;
        }
        return DateUtil.format(DateUtil.parse(date.toString(), DATE_FORMAT_PATTERN), pattern);
    }

    public String getDataDate(JobExecutionContext context) {
        if (context == null) {
            return null;
        }
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String tradeCalendar = (String) mergedJobDataMap.get(CronConstant.TRADE_CALENDAR);
        String baseDate = DateUtil.format(new Date(), DATE_FORMAT_PATTERN);
        Integer offset = (Integer) mergedJobDataMap.get(CronConstant.DATA_DATE_OFFSET);
        return this.getTradeDay(tradeCalendar, baseDate, offset, DATE_FORMAT_PATTERN);
    }

    public String getManualDataDate(JobExecutionContext context) {
        if (context == null) {
            return null;
        }
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String startDate = (String) mergedJobDataMap.get(CronConstant.START_DATE);
        String endDate = (String) mergedJobDataMap.get(CronConstant.END_DATE);
        if (startDate == null || endDate == null) {
            String tradeCalendar = (String) mergedJobDataMap.get(CronConstant.TRADE_CALENDAR);
            String baseDate = DateUtil.format(new Date(), DATE_FORMAT_PATTERN);
            Integer offset = (Integer) mergedJobDataMap.get(CronConstant.DATA_DATE_OFFSET);
            startDate = this.getTradeDay(tradeCalendar, baseDate, offset, CHINESE_DATE_FORMAT_PATTERN);
            endDate = getNowDateStr(CHINESE_DATE_FORMAT_PATTERN);
            return startDate + "-" + endDate;
        }
        return DateUtil.format(DateUtil.parse(startDate, HORIZONTAL_DATE_FORMAT_PATTERN), CHINESE_DATE_FORMAT_PATTERN) + "-" + DateUtil.format(DateUtil.parse(endDate, HORIZONTAL_DATE_FORMAT_PATTERN), CHINESE_DATE_FORMAT_PATTERN);
    }

    public String autoGetManualDataDate(JobExecutionContext context){
        if (context == null) {
            return null;
        }
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        String startDate = (String) mergedJobDataMap.get(CronConstant.START_DATE);
        String endDate = (String) mergedJobDataMap.get(CronConstant.END_DATE);
        if (startDate == null || endDate == null) {
            String tradeCalendar = (String) mergedJobDataMap.get(CronConstant.TRADE_CALENDAR);
            String baseDate = DateUtil.format(new Date(), DATE_FORMAT_PATTERN);
            Integer offset = (Integer) mergedJobDataMap.get(CronConstant.DATA_DATE_OFFSET);
            startDate = this.getTradeDay(tradeCalendar, baseDate, offset, HORIZONTAL_DATE_FORMAT_PATTERN);
            return startDate;
        }
        return startDate;
    }

    public String getFormatDataDate(JobExecutionContext context) {
        String formatDate;
        try {
            String dataDate = getDataDate(context);
            SimpleDateFormat srcFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
            SimpleDateFormat destFormat = new SimpleDateFormat(HORIZONTAL_DATE_FORMAT_PATTERN);
            Date parsed = srcFormat.parse(dataDate);
            formatDate = destFormat.format(parsed);
        } catch (ParseException e) {
            throw new TaskException("日期转换失败", e);
        }
        return formatDate;
    }


    /**
     * 项目启动时，初始化所有已启动的任务
     */
    @Override
    public void afterPropertiesSet() {
        log.info("--------CronService：初始化定时任务-开始--------");
        LambdaQueryWrapper<Cron> cronLqw = new LambdaQueryWrapper<>();
        cronLqw.in(Cron::getStatus, 0L, 1L);
        List<Cron> runJobList = this.list(cronLqw);
        // 清空之前的任务
        try {
            scheduler.clear();
            log.info("--------CronService：清空Quartz调度器成功。");
        } catch (Exception e) {
            log.error("--------CronService：清空Quartz调度器失败！", e);
        }
        for (Cron cron : runJobList) {
            // 重新部署任务
            try {
                Class<?> jobClass = Class.forName(cron.getClassPath());
                if (Job.class.isAssignableFrom(jobClass)) {
                    if (doAddJob(cron)) {
                        log.info("--------CronService：{}==》初始化成功。", cron.getJobName());
                    }
                } else {
                    String log1 = "--------CronService：" + cron.getJobName() + "==》初始化失败！";
                    String log2 = "配置的类没有实现" + Job.class.getName() + "接口";
                    log.error(log1, log2);
                }
            } catch (ClassNotFoundException e) {
                String logMessage = "--------CronService：" + cron.getJobName() + "==》初始化失败！";
                log.error(logMessage, e);
            }
        }
        // 启动调度器
        try {
            scheduler.start();
            log.info("--------CronService：启动Quartz调度器成功。");
        } catch (Exception e) {
            log.error("--------CronService：启动Quartz调度器失败！", e);
        }
        log.info("--------CronService：初始化定时任务-结束--------");
    }

    public Map<String, List<Cron>> queryNoGroupJob(String groupId) {
        Map<String, List<Cron>> result = new HashMap<>();
        List<String> jobIds = queryJobIds(groupId);
        LambdaQueryWrapper<Cron> cronWrapper = new LambdaQueryWrapper<>();
        List<Cron> inGroupJob = new ArrayList<>();
        if (!jobIds.isEmpty()) {
            cronWrapper.in(Cron::getJobId, jobIds);
            inGroupJob = this.list(cronWrapper);
        }
        cronWrapper = new LambdaQueryWrapper<>();
        cronWrapper.ne(Cron::getClassName, GroupJob.class.getSimpleName());
        if (!jobIds.isEmpty()) {
            cronWrapper.notIn(Cron::getJobId, jobIds);
        }
        List<Cron> noGroupJob = this.list(cronWrapper);
        result.put("inGroupJob", inGroupJob);
        result.put("noGroupJob", noGroupJob);
        return result;
    }

    public List<String> queryJobIds(String groupId) {
        LambdaQueryWrapper<BaseCronGroupRelation> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(BaseCronGroupRelation::getGroupId, groupId);
        List<BaseCronGroupRelation> list = baseCronGroupRelationService.list(groupWrapper);
        return list.stream().map(BaseCronGroupRelation::getJobId).collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class)
    public R<?> saveGroupJob(GroupCronParam param) {
        if (param == null) {
            return R.failed("参数为空");
        }
        BaseCronGroup baseCronGroup = new BaseCronGroup();
        String groupId = param.getGroupId();
        if (groupId == null) {
            groupId = IdWorker.getIdStr();
        }
        baseCronGroup.setGroupId(groupId);
        baseCronGroup.setGroupName(param.getGroupName());
        baseCronGroupService.saveOrUpdate(baseCronGroup);
        LambdaQueryWrapper<BaseCronGroupRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseCronGroupRelation::getGroupId, groupId);
        baseCronGroupRelationService.remove(queryWrapper);
        List<String> jobIds = param.getJobIds();
        if (jobIds != null) {
            List<BaseCronGroupRelation> groupRelations = new ArrayList<>();
            for (String jobId : jobIds) {
                BaseCronGroupRelation baseCronGroupRelation = new BaseCronGroupRelation();
                baseCronGroupRelation.setGroupId(groupId);
                baseCronGroupRelation.setJobId(jobId);
                groupRelations.add(baseCronGroupRelation);
            }
            baseCronGroupRelationService.saveBatch(groupRelations);
        }
        Cron groupJob = param.getGroupJob();
        return this.addJob(groupJob);
    }

    public List<RemoteFileInfo> getRemoteFileInfo(String jobId) {
        //查找最新一次执行的日志
        String lastExecuteId = baseCronLogService.getLastExecute(jobId);
        LambdaQueryWrapper<RemoteFileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RemoteFileInfo::getLogId, lastExecuteId);
        return remoteFileInfoService.list(queryWrapper);
    }

    public CronInfo getCronInfo(JobDataMap mergedJobDataMap) {
        String jobId = (String) mergedJobDataMap.get(CronConstant.JOB_ID);
        if (jobId == null) {
            throw new TaskException("运行时缺失jobId");
        }
        Cron cron = this.getById(jobId);
        if (cron == null) {
            throw new TaskException("缺失cronjob" + jobId);
        }
        String logId = (String) mergedJobDataMap.get(CronConstant.LOG_ID);
        Integer flowId = cron.getFlowId();
        if (flowId == null) {
            throw new TaskException("flow为空");
        }
        if (logId == null) {
            throw new TaskException("未找到执行日志");
        }
        FlowList flow = flowListService.getById(flowId);
        if (flow == null) {
            throw new TaskException("配置的流程不存在");
        }
        return new CronInfo(cron, logId, flowId, flow);
    }

    public List<String> getJobIdByClass(Class<?> jobClass) {
        if (jobClass == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Cron> cronLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cronLambdaQueryWrapper.eq(Cron::getClassName, jobClass.getSimpleName());
        List<Cron> list = this.list(cronLambdaQueryWrapper);
        return list.stream().map(Cron::getJobId).collect(Collectors.toList());
    }

    /**
     * 用于标记反射使用方法,实际使用中不会调用
     */
    public void testCal() {
        Set<Date> weekDay2 = marketTradeDayService.getWeekDay("00", 2);
        Set<Date> month1 = marketTradeDayService.getFirstMonthDay("00");
        Set<Date> month2 = marketTradeDayService.getSecondMonthDay("00");
        Set<Date> days15 = new TreeSet<>(marketTradeDayService.getDays("00", List.of(15)));
        Set<Date> monthLast1 = new TreeSet<>(marketTradeDayService.getMonthLastDay("00"));
        Set<Date> monthLast2 = new TreeSet<>(marketTradeDayService.getMonthSecondLastDay("00"));
        log.info("weekDay2");
        weekDay2.forEach(System.out::println);
        log.info("month1");
        month1.forEach(System.out::println);
        log.info("month2");
        month2.forEach(System.out::println);
        log.info("days15");
        days15.forEach(System.out::println);
        log.info("monthLast1");
        monthLast1.forEach(System.out::println);
        log.info("monthLast2");
        monthLast2.forEach(System.out::println);
    }
}




