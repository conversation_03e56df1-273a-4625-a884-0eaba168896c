package cn.sdata.om.al.qrtz.util;

import org.quartz.Calendar;
import org.quartz.impl.calendar.BaseCalendar;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

public class ExecuteCalendar extends BaseCalendar implements Calendar,
        Serializable {

    private TreeSet<Date> dates = new TreeSet<>();

    public ExecuteCalendar() {
    }

    @Override
    public Object clone() {
        ExecuteCalendar clone = (ExecuteCalendar) super.clone();
        clone.dates = new TreeSet<>(dates);
        return clone;
    }

    @Override
    public boolean isTimeIncluded(long timeStamp) {
        if (!super.isTimeIncluded(timeStamp)) {
            return false;
        }

        Date lookFor = getStartOfDayJavaCalendar(timeStamp).getTime();

        return dates.contains(lookFor);
    }


    @Override
    public long getNextIncludedTime(long timeStamp) {

        // Call base calendar implementation first
        long baseTime = super.getNextIncludedTime(timeStamp);
        if ((baseTime > 0) && (baseTime > timeStamp)) {
            timeStamp = baseTime;
        }

        // Get timestamp for 00:00:00
        java.util.Calendar day = getStartOfDayJavaCalendar(timeStamp);
        while (!isTimeIncluded(day.getTime().getTime())) {
            day.add(java.util.Calendar.DATE, 1);
        }

        return day.getTime().getTime();
    }

    public void addAllExecuteDate(Set<Date> allExcludedDate) {
        Set<Date> dealDates = allExcludedDate.stream().map(date -> getStartOfDayJavaCalendar(date.getTime()).getTime()).collect(Collectors.toSet());
        this.dates.addAll(dealDates);
    }

}
