package cn.sdata.om.al.qrtz.util;

import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GroupTriggerListener implements TriggerListener {

    private Scheduler scheduler;

    @Autowired
    public void setScheduler(Scheduler scheduler) {
        this.scheduler = scheduler;
    }

    @Override
    public String getName() {
        return "groupTriggerListener";
    }

    @Override
    public void triggerFired(Trigger trigger, JobExecutionContext context) {

    }

    @Override
    public boolean vetoJobExecution(Trigger trigger, JobExecutionContext context) {
        return false;
    }

    @Override
    public void triggerMisfired(Trigger trigger) {

    }

    @Override
    public void triggerComplete(Trigger trigger, JobExecutionContext context, Trigger.CompletedExecutionInstruction triggerInstructionCode) {
        JobDataMapList jobDataMapList = JobDataMapList.getInstance();
        JobKey thisKey = context.getJobDetail().getKey();
        JobKey nextJobKey = jobDataMapList.getNextJobKey(thisKey);
        if (nextJobKey != null) {
            JobDataMap jobData = jobDataMapList.getJobData(nextJobKey);
            try {
                scheduler.triggerJob(nextJobKey, jobData);
            } catch (SchedulerException e) {
                String message = thisKey + "下一个节点" + nextJobKey + "执行异常";
                log.error(message, e);
            }
        }
    }
}
