package cn.sdata.om.al.qrtz.util;

import lombok.Getter;
import org.quartz.JobDataMap;
import org.quartz.JobKey;

import java.util.concurrent.ConcurrentHashMap;

public class JobDataMapList {

    @Getter
    private static final JobDataMapList instance = new JobDataMapList();

    private static final ConcurrentHashMap<JobKey, JobDataMap> allJobDataMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<JobKey, JobKey> nextJobMap = new ConcurrentHashMap<>();

    private JobDataMapList(){
    }

    public void put(<PERSON><PERSON><PERSON> thisJobKey, <PERSON><PERSON><PERSON> nextJobKey, JobDataMap thisJobDataMap){
        allJobDataMap.put(thisJob<PERSON>ey, thisJobDataMap);
        nextJobMap.put(thisJob<PERSON>ey, nextJobKey);
    }

    public JobDataMap getJobData(JobKey thisJobKey){
        return allJobDataMap.get(thisJobKey);
    }

    public JobKey getNextJobKey(<PERSON><PERSON><PERSON> thisJobKey){
        JobKey jobKey = nextJobMap.get(thisJobKey);
        if (jobKey == null) {
            return null;
        }
        Job<PERSON>ey newJobKey = JobKey.jobKey(jobKey.getName(), jobKey.getGroup());
        nextJobMap.remove(thisJobKey);
        return newJobKey;
    }

}
