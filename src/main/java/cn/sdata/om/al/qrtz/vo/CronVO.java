package cn.sdata.om.al.qrtz.vo;

import cn.sdata.om.al.qrtz.enums.JobCalendar;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class CronVO {

    private String id;
    /**
     * 任务名称
     */
    private String cronName;
    /**
     * 交易日历方案 00 沪深交易日 01 银行间交易日 02 沪港通交易日 03 深港通交易日
     */
    private String tradeCalendar;

    /**
     * 数据日期偏移量
     */
    private Integer dataDateOffset;

    /**
     * 执行日历
     */
    private String executeCalendar;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 最近一次执行状态 运行中 RUNNING 完成 COMPLETE 异常 FAILED
     */
    private JobStatus lastStatus;
    /**
     * 最后一次执行时间
     */
    private String lastExecuteTime;
    /**
     * 最后一次执行数据日期
     */
    private String lastDataDate;

    /**
     * 最后一次执行人
     */
    private String lastExecutor;

    /**
     * 任务状态
     */
    private Long taskStatus;

    /**
     * 表达式
     */
    private String cron;

    private Long isDeleted;

}
