package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.enums.ValuationRPAStatus;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.sdata.om.al.constant.JobConstant.DATA_DATE;
import static cn.sdata.om.al.constant.JobConstant.NET_VALUE_DISCLOSURE_LIST;

@Slf4j
@Component
@AllArgsConstructor
public class BalanceTableHandler implements BaseHandler {

    private final NetValueDisclosureService netValueDisclosureService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        Objects.requireNonNull(files, "RPA返回文件不得为空");
        log.info("BalanceTableHandler.execute开始执行，文件数量: {}", files.size());

        Map<String, NetValueDisclosure> netValueMap = getNetValueMap(param);
        log.info("netValueMap大小: {}", netValueMap.size());

        List<String> productIds = new ArrayList<>();
        String dataDate = null;
        Collection<NetValueDisclosure> values = netValueMap.values();
        String paramDataDate = (String) param.get(DATA_DATE);
        log.info("原始paramDataDate: {}", paramDataDate);

        // 处理不同格式的日期
        String compactDate = paramDataDate.replace("-", "");
        log.info("格式化后的日期: formattedDate={}, compactDate={}", paramDataDate, compactDate);

        for (RemoteFileInfo file : files) {
            String fileName = file.getFileName();
            log.info("处理文件: {}", fileName);

            if (fileName == null) {
                log.warn("文件名为空，跳过");
                continue;
            }

            // 检查文件名是否包含任一格式的日期
            boolean containsDate = fileName.contains(paramDataDate) || fileName.contains(compactDate);
            if (!containsDate) {
                log.warn("文件名不包含任何格式的日期，跳过: {}", fileName);
                continue;
            }

            String id = StringUtil.extractBalanceAccountNumber(fileName);
            log.info("提取的账套编号: {}", id);

            if (id != null) {
                boolean containsKey = netValueMap.containsKey(id);
                log.info("netValueMap中是否存在键 {}: {}", id, containsKey);

                NetValueDisclosure netValueDisclosure = netValueMap.get(id);
                // 文件与净值披露记录匹配上了
                if (netValueDisclosure != null) {
                    String balanceTablePath = StringUtil.concatSeparator(file.getRelativePath(), fileName);
                    log.info("设置balanceTablePath: {}", balanceTablePath);
                    netValueDisclosure.setBalanceTablePath(balanceTablePath);

                    String valuationTablePath = netValueDisclosure.getValuationTablePath();
                    String balanceTablePathAfterSet = netValueDisclosure.getBalanceTablePath();
                    log.info("valuationTablePath: {}, balanceTablePathAfterSet: {}", valuationTablePath,
                            balanceTablePathAfterSet);

                    if (valuationTablePath != null || balanceTablePathAfterSet != null) {
                        dataDate = netValueDisclosure.getValuationDate();
                        productIds.add(netValueDisclosure.getProductId());
                    }
                } else {
                    log.error("未找到匹配的净值披露记录: {}", id);
                }
            } else {
                log.error("未找到匹配的产品名称: {}", fileName);
            }
        }

        log.info("设置balanceRpaStatus为COMPLETED，values大小: {}", values.size());
        values.forEach(netValueDisclosure -> {
            netValueDisclosure.setBalanceRpaStatus(ValuationRPAStatus.COMPLETED.name());
            log.info("产品ID: {}, balanceTablePath: {}, balanceRpaStatus: {}",
                    netValueDisclosure.getProductId(),
                    netValueDisclosure.getBalanceTablePath(),
                    netValueDisclosure.getBalanceRpaStatus());
        });

        try {
            log.info("保存更新批次开始");
            // 使用增量更新替代全量更新，避免并发冲突
            updateNetValueDisclosureBatch(values);
            log.info("保存更新批次完成");
        } catch (Exception e) {
            log.error("保存更新批次失败: {}", e.getMessage(), e);
        }

//        if (dataDate != null && !productIds.isEmpty()) {
//            log.info("发送托管银行邮件，产品IDs: {}, 日期: {}", productIds, dataDate);
//            netValueDisclosureService.sendCustodianBankMail(productIds, dataDate);
//        } else {
//            log.warn("没有需要发送托管银行邮件的产品");
//        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onFail(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        log.info("BalanceTableHandler.onFail开始执行");

        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        if (list == null || list.isEmpty()) {
            log.warn("没有需要处理的净值披露记录");
            return;
        }

        log.info("设置balanceRpaStatus为COMPLETED，list大小: {}", list.size());
        list.forEach(netValueDisclosure -> {
            netValueDisclosure.setBalanceRpaStatus(ValuationRPAStatus.COMPLETED.name());
            log.info("产品ID: {}, balanceRpaStatus: {}",
                    netValueDisclosure.getProductId(),
                    netValueDisclosure.getBalanceRpaStatus());
        });

        try {
            log.info("失败保存更新批次开始");
            // 使用增量更新替代全量更新，避免并发冲突
            updateNetValueDisclosureBatch(list);
            log.info("保存更新批次完成");
        } catch (Exception e) {
            log.error("保存更新批次失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 增量更新净值披露记录，只更新必要字段避免并发冲突
     */
    private void updateNetValueDisclosureBatch(Collection<NetValueDisclosure> values) {
        for (NetValueDisclosure disclosure : values) {
            try {
                LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NetValueDisclosure::getId, disclosure.getId());

                // 只更新余额表相关字段
                if (disclosure.getBalanceTablePath() != null) {
                    updateWrapper.set(NetValueDisclosure::getBalanceTablePath, disclosure.getBalanceTablePath());
                }
                if (disclosure.getBalanceRpaStatus() != null) {
                    updateWrapper.set(NetValueDisclosure::getBalanceRpaStatus, disclosure.getBalanceRpaStatus());
                }

                netValueDisclosureService.update(updateWrapper);
                log.debug("增量更新产品ID: {}", disclosure.getProductId());
            } catch (Exception e) {
                log.error("增量更新产品ID: {} 失败: {}", disclosure.getProductId(), e.getMessage(), e);
            }
        }
    }

}
