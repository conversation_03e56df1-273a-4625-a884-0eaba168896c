package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.BankReconciliationService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.utils.LogBRUtil;
import cn.sdata.om.al.utils.OmFileUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

@Slf4j
@Component
public class BankReconciliationSQHandler implements BaseHandler {

    private RemoteFileInfoService remoteFileInfoService;

    private BankReconciliationService bankReconciliationService;

    @Autowired
    public void setBankReconciliationService(BankReconciliationService bankReconciliationService) {
        this.bankReconciliationService = bankReconciliationService;
    }

    @Autowired
    public void setRemoteFileInfoService(RemoteFileInfoService remoteFileInfoService) {
        this.remoteFileInfoService = remoteFileInfoService;
    }

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        String logId = String.valueOf(param.get(RPAConstant.LOG_ID)),
                dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                executor = String.valueOf(param.get(CronConstant.EXECUTOR));
        try {
            Assert.notNull(param, "param不能为空");
            Assert.notEmpty(files, "files不能为空");
            Cron cron = (Cron) param.get(RPAConstant.CRON);
            log.info("BankReconciliationSQHandler_execute_logId:{},dataDate:{},executor:{}", logId, dataDate, executor);
            Assert.notNull(cron, "cron不能为空");
            Assert.notNull(logId, "logId不能为空");
            Assert.notNull(dataDate, "dataDate不能为空");
            files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
            remoteFileInfoService.saveBatch(files);
            log.info("BankReconciliationSQHandler_execute_files:{}", files);
            String dataDateStr = LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
            log.info("BankReconciliationSQHandler_execute_dataDateStr:{}", dataDateStr);
            List<File> localFiles = OmFileUtil.transformToFiles(files);
            if (CollectionUtil.isNotEmpty(localFiles)) {
                List<MultipartFile> multipartFiles = new ArrayList<>();
                for (File localFile : localFiles) {
                    MultipartFile multipartFile = OmFileUtil.fileToMultipartFile(localFile);
                    multipartFiles.add(multipartFile);
                }
                bankReconciliationService.upload(multipartFiles, "AUTO", DateUtil.today());
            }
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("fileType", "上清");
            params.put("files", files);
            params.put("endTime", DateUtil.now());
            LogBRUtil.postRpaLog(params);
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("fileType", "上清");
            params.put("exception", e.getMessage());
            params.put("endTime", DateUtil.now());
            LogBRUtil.postRpaLog(params);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {
        Object o = param.get(LOG_ID);
        if (o != null) {
            Object errorMsg = param.get("errorMsg");
            String rpaLogId = String.valueOf(o);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", rpaLogId);
            params.put("fileType", "上清");
            params.put("exception", errorMsg != null ? String.valueOf(errorMsg) : "");
            params.put("endTime", DateUtil.now());
            LogBRUtil.postRpaLog(params);
        }
    }
}
