package cn.sdata.om.al.rpa;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.ValuationTableRecords;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.NET_VALUE_DISCLOSURE_LIST;

public interface BaseHandler {

    void execute(Map<String, Object> param, List<RemoteFileInfo> files);

    void onFail(Map<String, Object> param);

    /**
     * 将RPA参数转换为NetValueDisclosureMap, 只要调用RPA即认为完成
     *
     * @param param RPA参数
     * @return NetValueDisclosureMap
     */
    @SuppressWarnings("unchecked")
    default Map<String, NetValueDisclosure> getNetValueMap(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        Objects.requireNonNull(list, "未找到净值披露信息");
        return list.stream()
                .collect(Collectors.toMap(NetValueDisclosure::getProductId,
                        netValueDisclosureT0 -> netValueDisclosureT0,
                        (oldOne, newOne) -> newOne));
    }

    default String getProductIdByFile(RemoteFileInfo file, Map<String, String> productNameIds) {
        Objects.requireNonNull(file, "文件不得为空");
        Objects.requireNonNull(productNameIds, "产品名称Map不得为空");
        String fileName = file.getFileName();
        String productName = StringUtil.extractValuationAccountName(fileName);
        return productNameIds.get(productName);
    }

    default ValuationTableRecords generateRecords(String productId, String valuationDate, RemoteFileInfo file) {
        Objects.requireNonNull(file, "文件不得为空");
        ValuationTableRecords valuationTableRecords = new ValuationTableRecords();
        valuationTableRecords.setId(IdWorker.getIdStr());
        valuationTableRecords.setProductId(productId);
        valuationTableRecords.setValuationDate(valuationDate);
        valuationTableRecords.setStatus(1);
        valuationTableRecords.setDownloadTime(file.getDownloadTime());
        valuationTableRecords.setResultTime(file.getDownloadTime());
        valuationTableRecords.setOperator(BaseConstant.DEFAULT_USERNAME);
        valuationTableRecords.setValuationTablePath(StringUtil.concatSeparator(file.getRelativePath(), file.getFileName()));
        return valuationTableRecords;
    }

}
