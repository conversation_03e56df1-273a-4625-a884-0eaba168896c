package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.cashClearReport.CashClearReportService;
import cn.sdata.om.al.utils.LogCCRUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;

/**
 * 资金清算报表handler
 *
 * <AUTHOR>
 * @Date 2025/4/16 10:44
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CashClearReportHandler implements BaseHandler {

    private final RemoteFileInfoService remoteFileInfoService;

    private final AccountInformationService accountInformationService;

    private final CashClearReportService clearReportService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        String logId = String.valueOf(param.get(RPAConstant.LOG_ID)),
                dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                executor = String.valueOf(param.get(CronConstant.EXECUTOR));
        try {
            Assert.notNull(param, "param不能为空");
            Assert.notNull(files, "files不能为空");
            Cron cron = (Cron) param.get(RPAConstant.CRON);
            log.info("CashClearReportHandler_execute_logId:{},dataDate:{},executor:{}", logId, dataDate, executor);
            Assert.notNull(cron, "cron不能为空");
            Assert.notNull(logId, "logId不能为空");
            Assert.notNull(dataDate, "dataDate不能为空");
            files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
            remoteFileInfoService.saveBatch(files);
            log.info("CashClearReportHandler_execute_files:{}", files);

            String dataDateStr = LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
            log.info("CashClearReportHandler_execute_dataDateStr:{}", dataDateStr);
            //eg:基金的资金清算报表(基金或销售商)_AZ0003_安联资产安享1号资产管理产品_2024年03月05日--2024年03月05日.pdf
            List<String> productCodes = Lists.newArrayList();
            for (RemoteFileInfo remoteFile : files) {
                if (remoteFile == null || !remoteFile.getFileName().contains("资金清算报表"))
                    continue;
                String[] arr = remoteFile.getFileName().split("_");
                log.info("CashClearReportHandler_execute_remoteFile1_arr:{}", Arrays.toString(arr));
                if (arr.length < 4)
                    continue;
                String[] dataArr = arr[3].replaceFirst("[.][^.]+$", "").split("--");
                log.info("CashClearReportHandler_execute_dataArr:{}", Arrays.toString(dataArr));
                String dataArr0 = dataArr[0], dataArr1 = dataArr[1];
                log.info("CashClearReportHandler_execute_remoteFile1_dataArr0:{},dataArr1:{}", dataArr0, dataArr1);
                if (!dataArr0.equals(dataDateStr) || !dataArr0.equals(dataArr1)) {
                    continue;
                }
                String productCode = arr[1];
                productCodes.add(productCode);
            }
            log.info("CashClearReportHandler_execute_productCodes:{}", productCodes);

            //通过从文件名中解析出来的账套号和账套名称查询账套基本信息map code->productId
            Map<String, String> accountCode2IdMap = accountInformationService.list(Wrappers.lambdaQuery(AccountInformation.class)
                            .in(AccountInformation::getProductCode, productCodes))
                    .stream().collect(Collectors.toMap(AccountInformation::getProductCode, AccountInformation::getId, (n1, n2) -> n1));
            log.info("CashClearReportHandler_execute_accountCode2IdMap:{}", accountCode2IdMap);

            //清算列表list
            List<CashClearReportEntity> clearReportEntities = Lists.newArrayList();
            for (RemoteFileInfo remoteFile : files) {
                //eg:基金的资金清算报表(基金或销售商)_AZ0003_安联资产安享1号资产管理产品_2024年03月05日--2024年03月05日.pdf
                if (remoteFile == null || !remoteFile.getFileName().contains("资金清算报表"))
                    continue;
                String[] arr = remoteFile.getFileName().split("_");
                log.info("CashClearReportHandler_execute_remoteFile2_arr:{}", Arrays.toString(arr));

                String productCode = arr[1], productName = arr[2];
                log.info("CashClearReportHandler_execute_remoteFile2_productCode:{},productName:{}", productCode, productName);
                if (accountCode2IdMap.containsKey(productCode)) {
                    CashClearReportEntity clearEntity = new CashClearReportEntity();
                    String productId = accountCode2IdMap.get(productCode);
                    clearEntity.setId(IdUtil.getSnowflakeNextIdStr())
                            .setProductId(productId)
                            .setProductCode(productCode)
                            .setProductName(productName)
                            .setFileName(remoteFile.getFileName())
                            .setDataDate(dataDate)
                            .setFileUpdateTime(new Date())
                            .setCreateTime(new Date())
                            .setUserId(executor)
                            .setMailSendStatus(MailStatus.UNSENT.name())
                            .setRemoteFileId(remoteFile.getId());
                    clearReportEntities.add(clearEntity);
                    log.info("CashClearReportHandler_execute_clearEntity:{}", clearEntity);
                }
            }
            log.info("CashClearReportHandler_execute_clearReportEntities:{}", clearReportEntities);
            if (CollUtil.isNotEmpty(clearReportEntities)) {
                Boolean rFlag = clearReportService.remove(Wrappers.lambdaQuery(CashClearReportEntity.class).eq(CashClearReportEntity::getDataDate, dataDate));
                Boolean sFlag = clearReportService.saveBatch(clearReportEntities);
                log.info("CashClearReportHandler_execute_remove:{},saveBatch:{}", rFlag, sFlag);
            }
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("files", files);
            params.put("endTime", DateUtil.now());
            LogCCRUtil.postRpaLog(params);
        } catch (Exception e) {
            log.error("CashClearReportHandler_execute_error:{},{}", e, e.getMessage());
            JSONObject params = new JSONObject();
            params.put("rpaLogId", logId);
            params.put("exception", e.getMessage());
            params.put("endTime", DateUtil.now());
            LogCCRUtil.postRpaLog(params);
        }

    }

    @Override
    public void onFail(Map<String, Object> param) {
        Object o = param.get(LOG_ID);
        if (o != null) {
            Object errorMsg = param.get("errorMsg");
            String rpaLogId = String.valueOf(o);
            JSONObject params = new JSONObject();
            params.put("rpaLogId", rpaLogId);
            params.put("exception", errorMsg != null ? String.valueOf(errorMsg) : "");
            params.put("endTime", DateUtil.now());
            LogCCRUtil.postRpaLog(params);
        }
    }
}
