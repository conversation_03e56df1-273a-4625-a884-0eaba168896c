package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.ValuationTableRecords;
import cn.sdata.om.al.enums.ValuationRPAStatus;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.ValuationTableRecordsService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.NET_VALUE_DISCLOSURE_LIST;

@Component
@AllArgsConstructor
@Slf4j
public class DBFHandler implements BaseHandler{

    private final NetValueDisclosureService netValueDisclosureService;
    private final AccountInformationService accountInformationService;
    private final ValuationTableRecordsService valuationTableRecordsService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        log.info("执行处理DBF文件");
        Objects.requireNonNull(files, "RPA返回文件不得为空");
        List<ValuationTableRecords> valuationTableRecordsList = new ArrayList<>();
        Map<String, String> productNameIds = accountInformationService.list().stream()
                .collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId, (oldOne, newOne) -> newOne));
        Map<String, NetValueDisclosure> netValueMap = getNetValueMap(param);
        Collection<NetValueDisclosure> values = netValueMap.values();
        for (RemoteFileInfo file : files) {
            String id = getProductIdByFile(file, productNameIds);
            if (id != null) {
                NetValueDisclosure netValueDisclosure = netValueMap.get(id);
                //文件与净值披露记录匹配上了
                if (netValueDisclosure != null) {
                    String valuationDate = netValueDisclosure.getValuationDate();
                    netValueDisclosure.setDbfPath(StringUtil.concatSeparator(file.getRelativePath(), file.getFileName()));
                    ValuationTableRecords valuationTableRecords = generateRecords(id, valuationDate, file);
                    valuationTableRecordsList.add(valuationTableRecords);
                }
            }else{
                log.error("未找到匹配的产品名称:{}", file.getFileName());
            }
        }
        values.forEach(netValueDisclosure -> netValueDisclosure.setDbfStatus(ValuationRPAStatus.COMPLETED.name()));
        // 使用增量更新替代全量更新，避免并发冲突
        updateDbfStatusBatch(values);
        valuationTableRecordsService.saveBatch(valuationTableRecordsList);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onFail(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        list.forEach(netValueDisclosure -> netValueDisclosure.setDbfStatus(ValuationRPAStatus.COMPLETED.name()));
        // 使用增量更新替代全量更新，避免并发冲突
        updateDbfStatusBatch(list);
    }

    /**
     * 增量更新DBF状态，只更新必要字段避免并发冲突
     */
    private void updateDbfStatusBatch(Collection<NetValueDisclosure> values) {
        for (NetValueDisclosure disclosure : values) {
            try {
                LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NetValueDisclosure::getId, disclosure.getId());

                // 只更新DBF相关字段
                if (disclosure.getDbfPath() != null) {
                    updateWrapper.set(NetValueDisclosure::getDbfPath, disclosure.getDbfPath());
                }
                if (disclosure.getDbfStatus() != null) {
                    updateWrapper.set(NetValueDisclosure::getDbfStatus, disclosure.getDbfStatus());
                }

                netValueDisclosureService.update(updateWrapper);
                log.debug("增量更新DBF状态，产品ID: {}", disclosure.getProductId());
            } catch (Exception e) {
                log.error("增量更新DBF状态失败，产品ID: {} 失败: {}", disclosure.getProductId(), e.getMessage(), e);
            }
        }
    }

}
