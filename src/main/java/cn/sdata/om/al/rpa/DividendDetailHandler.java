package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.dto.DividendRemoteFileDealDto;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity;
import cn.sdata.om.al.entity.dividendDetail.DividendDetailFileEntity;
import cn.sdata.om.al.entity.dividendDetail.LogCallRpaDividendDetailEntity;
import cn.sdata.om.al.mapper.dividendDetail.LogCallRpaDividendDetailMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.service.dividendDetail.DividendDetailFileService;
import cn.sdata.om.al.service.dividendDetail.DividendDetailService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 分红信息明细handler
 *
 * <AUTHOR>
 * @Date 2025/4/27 16:43
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DividendDetailHandler implements BaseHandler {

    private final SMBService smbService;

    private final RemoteFileInfoService remoteFileInfoService;

    private final DividendDetailService dividendService;

    private final DividendDetailFileService dividendFileService;

    private final AccountInformationService accountInformationService;

    private final LogCallRpaDividendDetailMapper logCallRpaDividendDetailMapper;

    /**
     * 分红信息明细-本级目录名称
     */
    private final String activeDirName = "dividendDetail";

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        try {
            Assert.notNull(param, "param不能为空");
            Assert.notNull(files, "分红信息明细files不能为空");
            Cron cron = (Cron) param.get(RPAConstant.CRON);
            String logId = String.valueOf(param.get(RPAConstant.LOG_ID)),
                    dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                    executor = String.valueOf(param.get(CronConstant.EXECUTOR)),
                    productIdStr = String.valueOf(param.get("productIdStr")),
                    batchId = String.valueOf(System.nanoTime());

            String userId = String.valueOf(param.get("userId")),
                    userName = String.valueOf(param.get("userName"));

            log.info("DividendDetailHandler_execute_logId:{},dataDate:{},executor:{},productIdStr:{},batchId:{}", logId, dataDate, executor, productIdStr, batchId);
            Assert.notNull(cron, "cron不能为空");
            Assert.notNull(logId, "logId不能为空");
            Assert.notNull(dataDate, "dataDate不能为空");
            files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
            remoteFileInfoService.saveBatch(files);
            log.info("DividendDetailHandler_execute_files:{}", files);

            String parentDir = baseDir + File.separator + activeDirName + File.separator + dataDate + File.separator;
            log.info("DividendDetailHandler_execute_parentDir:" + parentDir);

            //数据库里已经存在的数据日期的账套信息
            Map<String, DividendDetailEntity> existMap = dividendService.list(Wrappers.lambdaQuery(DividendDetailEntity.class).eq(DividendDetailEntity::getDataDate, dataDate))
                    .stream()
                    .collect(Collectors.toMap(DividendDetailEntity::getProductId, Function.identity(), (n1, n2) -> n1));
            List<String> selectProductIds = Lists.newArrayList(), needUpdateProductIds = Lists.newArrayList();
            if (StringUtils.isNotBlank(productIdStr)) {
                //本次选中的账套
                selectProductIds = Arrays.stream(productIdStr.split(",")).distinct().collect(Collectors.toList());
            }
            List<DividendDetailEntity> addDetailList = Lists.newArrayList();
            List<DividendDetailFileEntity> addDetailFileList = Lists.newArrayList();
            for (RemoteFileInfo remoteFile : files) {
                if (remoteFile == null || !remoteFile.getFileName().contains("分红信息明细-" + dateNorm2Pure(dataDate))) {
                    continue;
                }
                log.info("DividendDetailHandler_for_file:{}", remoteFile);
                String sourceFilePath = parentDir + remoteFile.getFileName(),
                        remoteFilePath = StringUtil.concatSeparator(remoteFile.getRelativePath(), remoteFile.getFileName());
                log.info("DividendDetailHandler_for_targetFilePath_remoteFilePath,{},{}", sourceFilePath, remoteFilePath);
                try {
                    downloadRemoteFiles(sourceFilePath, remoteFilePath);

                    //调用rpa日志记录
                    logCallRpaDividendDetailMapper.insert(new LogCallRpaDividendDetailEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                            .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                            .setFilePath(sourceFilePath)
                            .setFileName(Paths.get(sourceFilePath).getFileName().toString())
                            .setUserId(userId)
                            .setUserName(userName)
                            .setCreateTime(new Date())
                            .setProductIdStr(productIdStr)
                    );

                } catch (Exception e) {
                    log.error("DividendDetailHandler_for_download_error:{},{}", e, e.getMessage());
                    continue;
                }
                //String sourceFilePath = "分红信息明细-20250430.xlsx";
                String sheetName = "分红信息明细查询",
                        splitColumnName = "基金名称",
                        outputPrefix = "分红_",
                        dateSuffix = extractDateFromFileName(remoteFile.getFileName());
                List<DividendRemoteFileDealDto> dealDtoList = Lists.newArrayList();
                try {
                    dealDtoList = splitExcelByColumn(sourceFilePath, sheetName, splitColumnName,
                            outputPrefix, ("_" + dateSuffix + ".xlsx"), parentDir);
                } catch (Exception e) {
                    log.error("DividendDetailHandler_for_splitExcelByColumn_error:{},{}", e, e.getMessage());
                    continue;
                }
                if (CollUtil.isEmpty(dealDtoList)) {
                    log.error("DividendDetailHandler_for_dealDtoList_为空");
                    continue;
                }

                List<String> productNames = dealDtoList.stream().map(DividendRemoteFileDealDto::getProductName).distinct().collect(Collectors.toList()),
                        productCodes = dealDtoList.stream().map(DividendRemoteFileDealDto::getProductCode).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(productNames) || CollUtil.isEmpty(productCodes)) {
                    log.error("DividendDetailHandler_for_productNames_productCodes_为空");
                    continue;
                }

                Map<String, String> accMap = accountInformationService.list(
                        Wrappers.lambdaQuery(AccountInformation.class)
                                .in(AccountInformation::getFullProductName, productNames)
                                .in(AccountInformation::getProductCode, productCodes)
                                .ne(AccountInformation::getIsCurrency, 1)
                ).stream().collect(Collectors.toMap(i -> (i.getFullProductName() + i.getProductCode()), AccountInformation::getId, (n1, n2) -> n1));
                if (CollUtil.isEmpty(accMap)) {
                    log.error("DividendDetailHandler_for_accMap_为空");
                    continue;
                }

                for (DividendRemoteFileDealDto fileDealDto : dealDtoList) {
                    String k = fileDealDto.getProductName() + fileDealDto.getProductCode();
                    if (!accMap.containsKey(k)) {
                        log.error("DividendDetailHandler_账套_在账套基础信息中未找到_productCode:{},productName:{}", fileDealDto.getProductCode(), fileDealDto.getProductName());
                        continue;
                    }
                    String productId = accMap.get(k),
                            productCode = fileDealDto.getProductCode(),
                            productName = fileDealDto.getProductName(),
                            fileName = fileDealDto.getFileName(),
                            localFilePath = fileDealDto.getLocalFilePath();
                    log.info("DividendDetailHandler_for_productId:{},productCode:{},productName:{},fileName:{},localFilePath:{}", productId, productCode, productName, fileName, localFilePath);
                    DividendDetailFileEntity detailFile = new DividendDetailFileEntity()
                            .setId(IdWorker.getIdStr())
                            .setBatchId(batchId)
                            .setCreateTime(new Date())
                            .setUpdateTime(new Date())
                            .setFileName(fileName)
                            .setFileUpdateTime(new Date())
                            .setRemoteFileId(remoteFile.getId())
                            .setLocalFilePath(localFilePath)
                            .setStep("init_detail_file");
                    if (existMap.containsKey(productId)) {
                        needUpdateProductIds.add(productId);
                        if (CollUtil.isNotEmpty(selectProductIds) && selectProductIds.contains(productId)) {
                            detailFile.setInitStatus(1).setSelectStatus(1).setDetailId(existMap.get(productId).getId());
                        } else {
                            detailFile.setInitStatus(1).setSelectStatus(0).setDetailId(existMap.get(productId).getId());
                        }
                    } else {
                        DividendDetailEntity detail = new DividendDetailEntity()
                                .setId(IdWorker.getIdStr())
                                .setProductId(productId)
                                .setProductCode(productCode)
                                .setProductName(productName)
                                .setDataDate(dataDate)
                                .setCreateTime(new Date())
                                .setUpdateTime(new Date())
                                .setBatchId(batchId)
                                .setUserId(executor)
                                .setStep("init_detail");
                        addDetailList.add(detail);

                        detailFile.setDetailId(detail.getId());
                    }
                    addDetailFileList.add(detailFile);
                }
            }
            if (CollUtil.isNotEmpty(needUpdateProductIds)) {
                log.info("DividendDetailHandler_execute_needUpdateProductIds:{}", needUpdateProductIds);
                dividendService.update(Wrappers.lambdaUpdate(DividendDetailEntity.class)
                        .eq(DividendDetailEntity::getDataDate, dataDate)
                        .in(DividendDetailEntity::getProductId, needUpdateProductIds)
                        .set(DividendDetailEntity::getUpdateTime, new Date())
                        .set(DividendDetailEntity::getUserId, executor)
                        .set(DividendDetailEntity::getStep, "exits_productId_update")
                );
            }
            if (CollUtil.isNotEmpty(addDetailList)) {
                log.info("DividendDetailHandler_execute_addDetailList:{}", addDetailList);
                dividendService.saveBatch(addDetailList);
            }
            if (CollUtil.isNotEmpty(addDetailFileList)) {
                log.info("DividendDetailHandler_execute_addDetailFileList:{}", addDetailFileList);
                dividendFileService.saveBatch(addDetailFileList);
            }
        } catch (Exception e) {
            log.error("DividendDetailHandler_execute_error:{},{}", e, e.getMessage());
        }

    }

    @Override
    public void onFail(Map<String, Object> param) {
        log.error("DividendDetailHandler_onFail:{}", param);
    }

    /**
     * 下载远程文件到本地
     *
     * @param targetFilePath 目标文件路径
     * @param remoteFilePath 远程文件路径
     * @throws IOException
     */
    public void downloadRemoteFiles(String targetFilePath, String remoteFilePath) throws Exception {
        try {
            byte[] bytes = smbService.download(remoteFilePath);
            Path file = Paths.get(targetFilePath);
            Files.createDirectories(file.getParent());
            Files.write(file, bytes);
        } catch (Exception e) {
            log.error("DividendDetailHandler_downloadRemoteFiles_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String dateNorm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 从文件名中提取yyyyMMdd格式的日期
     *
     * @param fileName 文件名
     * @return yyyyMMdd格式的日期字符串，如"20250430"
     */
    private static String extractDateFromFileName(String fileName) {
        // 匹配文件名中的8位数字日期
        Pattern pattern = Pattern.compile("(\\d{8})");
        Matcher matcher = pattern.matcher(fileName);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    /**
     * 文件操作
     *
     * @param sourceFilePath  源文件
     * @param sheetName       sheet名称
     * @param splitColumnName 要拆分列
     * @param outputPrefix    输出文件的文件名称前缀
     * @param outputSuffix    输出文件的文件名称后缀
     * @param parentDir       输出文件的父目录
     * @return
     * @throws IOException
     */
    public static List<DividendRemoteFileDealDto> splitExcelByColumnV0(String sourceFilePath,
                                                                       String sheetName,
                                                                       String splitColumnName,
                                                                       String outputPrefix,
                                                                       String outputSuffix,
                                                                       String parentDir) throws Exception {
        List<DividendRemoteFileDealDto> results = new ArrayList<>();
        // 调整压缩比阈值（低于默认值 0.01）
        ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
        // 1. 读取源Excel文件
        FileInputStream fis = new FileInputStream(sourceFilePath);
        Workbook sourceWorkbook = WorkbookFactory.create(fis);
        Sheet sourceSheet = sourceWorkbook.getSheet(sheetName);
        if (sourceSheet == null) {
            sourceWorkbook.close();
            throw new IllegalArgumentException("工作表 '" + sheetName + "' 不存在");
        }
        // 2. 获取表头行和确定"基金名称"列索引
        Row headerRow = sourceSheet.getRow(0);
        int splitColumnIndex = -1;
        for (Cell cell : headerRow) {
            if (cell.getStringCellValue().equals(splitColumnName)) {
                splitColumnIndex = cell.getColumnIndex();
                break;
            }
        }
        if (splitColumnIndex == -1) {
            throw new IllegalArgumentException("列 '" + splitColumnName + "' 不存在");
        }
        // 3. 按"基金名称"分组数据行
        Map<String, List<Row>> groupedRows = new LinkedHashMap<>();
        for (int i = 1; i <= sourceSheet.getLastRowNum(); i++) {
            Row row = sourceSheet.getRow(i);
            if (row == null) continue;

            Cell cell = row.getCell(splitColumnIndex);
            if (cell == null) continue;

            String fundName = cell.getStringCellValue();
            String[] parts = fundName.split(":");
            if (parts.length < 2) continue;

            groupedRows.computeIfAbsent(parts[1].trim(), k -> new ArrayList<>()).add(row);
        }

        // 4. 为每个分组创建新的Excel文件
        for (Map.Entry<String, List<Row>> entry : groupedRows.entrySet()) {
            String productName = entry.getKey();
            List<Row> rows = entry.getValue();

            // 从第一行获取productId（假设同组productId相同）
            String productCode = rows.get(0).getCell(splitColumnIndex).getStringCellValue().split(":")[0].trim();
            // 创建新工作簿
            Workbook newWorkbook = new XSSFWorkbook();
            Sheet newSheet = newWorkbook.createSheet(sheetName);

            // 复制表头
            Row newHeaderRow = newSheet.createRow(0);
            copyRowV0(headerRow, newHeaderRow, newWorkbook);

            // 添加分组数据行
            int rowNum = 1;
            for (Row sourceRow : rows) {
                Row newRow = newSheet.createRow(rowNum++);
                copyRowV0(sourceRow, newRow, newWorkbook);
            }
            String outFileName = outputPrefix + productName + outputSuffix;
            String outputPath = parentDir + outFileName;

            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                newWorkbook.write(fos);
                results.add(new DividendRemoteFileDealDto(null, productCode, productName, outFileName, outputPath));
                System.out.println("已生成文件: " + outputPath);
            }
            newWorkbook.close();
        }
        sourceWorkbook.close();
        fis.close();
        return results;
    }

    private static void copyRowV0(Row sourceRow, Row targetRow, Workbook targetWorkbook) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell == null)
                continue;
            Cell targetCell = targetRow.createCell(i);
            CellStyle newStyle = targetWorkbook.createCellStyle();
            newStyle.cloneStyleFrom(sourceCell.getCellStyle());
            targetCell.setCellStyle(newStyle);
            switch (sourceCell.getCellType()) {
                case NUMERIC:
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                    break;
                case FORMULA:
                    targetCell.setCellFormula(sourceCell.getCellFormula());
                    break;
                case BLANK:
                    targetCell.setBlank();
                    break;
                case STRING:
                default:
                    targetCell.setCellValue(sourceCell.getStringCellValue());
            }
        }
    }

    /**
     * 文件操作
     *
     * @param sourceFilePath  源文件
     * @param sheetName       sheet名称
     * @param splitColumnName 要拆分列
     * @param outputPrefix    输出文件的文件名称前缀
     * @param outputSuffix    输出文件的文件名称后缀
     * @param parentDir       输出文件的父目录
     * @return
     * @throws IOException
     */
    public static List<DividendRemoteFileDealDto> splitExcelByColumn(String sourceFilePath,
                                                                     String sheetName,
                                                                     String splitColumnName,
                                                                     String outputPrefix,
                                                                     String outputSuffix,
                                                                     String parentDir) throws Exception {
        List<DividendRemoteFileDealDto> results = new ArrayList<>();
        ZipSecureFile.setMinInflateRatio(0.001);
        try (FileInputStream fis = new FileInputStream(sourceFilePath);
             Workbook sourceWorkbook = WorkbookFactory.create(fis)) {
            Sheet sourceSheet = sourceWorkbook.getSheet(sheetName);
            if (sourceSheet == null) {
                sourceWorkbook.close();
                throw new IllegalArgumentException("工作表 '" + sheetName + "' 不存在");
            }
            // 获取表头行和确定拆分列索引
            Row headerRow = sourceSheet.getRow(0);
            int splitColumnIndex = -1;
            for (Cell cell : headerRow) {
                if (cell.getStringCellValue().equals(splitColumnName)) {
                    splitColumnIndex = cell.getColumnIndex();
                    break;
                }
            }
            if (splitColumnIndex == -1) {
                throw new IllegalArgumentException("列 '" + splitColumnName + "' 不存在");
            }
            // 按分组列分组数据行
            Map<String, List<Row>> groupedRows = new LinkedHashMap<>();
            for (int i = 1; i <= sourceSheet.getLastRowNum(); i++) {
                Row row = sourceSheet.getRow(i);
                if (row == null) continue;
                Cell cell = row.getCell(splitColumnIndex);
                if (cell == null) continue;
                String fundName = cell.getStringCellValue();
                String[] parts = fundName.split(":");
                if (parts.length < 2) continue;
                groupedRows.computeIfAbsent(parts[1].trim(), k -> new ArrayList<>()).add(row);
            }
            // 为每个分组创建新的Excel文件
            for (Map.Entry<String, List<Row>> entry : groupedRows.entrySet()) {
                String productName = entry.getKey();
                List<Row> rows = entry.getValue();
                // 获取productCode
                String productCode = rows.get(0).getCell(splitColumnIndex).getStringCellValue().split(":")[0].trim();
                // 创建新工作簿
                try (Workbook newWorkbook = new XSSFWorkbook()) {
                    Sheet newSheet = newWorkbook.createSheet(sheetName);
                    // 复制表头（只复制A-U列）
                    Row newHeaderRow = newSheet.createRow(0);
                    copyRow(headerRow, newHeaderRow, newWorkbook, 21); // 只复制前21列(A-U)
                    // 添加分组数据行（只复制A-U列）
                    int rowNum = 1;
                    for (Row sourceRow : rows) {
                        Row newRow = newSheet.createRow(rowNum++);
                        copyRow(sourceRow, newRow, newWorkbook, 21); // 只复制前21列(A-U)
                    }
                    // 添加合计行
                    addSummaryRows(newSheet, rowNum, rows.size());

                    // 优化列宽
                    optimizeColumnWidths(newSheet, 21); // 优化前21列

                    // 保存文件
                    String outFileName = outputPrefix + productName + outputSuffix;
                    String outputPath = parentDir + outFileName;
                    try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                        newWorkbook.write(fos);
                        results.add(new DividendRemoteFileDealDto(null, productCode, productName, outFileName, outputPath));
                        log.info("DividendDetailHandler_splitExcelByColumn_生成文件:{}", outputPath);
                    }
                }
            }
        }
        return results;
    }

    /**
     * 优化工作表列宽（真正自适应内容宽度）
     *
     * @param sheet       工作表
     * @param columnCount 需要优化的列数
     */
    private static void optimizeColumnWidths(Sheet sheet, int columnCount) {
        // 计算每列的最大宽度
        int[] maxColumnWidths = new int[columnCount];
        // 遍历所有行（包括表头）
        for (Row row : sheet) {
            if (row == null) continue;
            for (int i = 0; i < columnCount && i < row.getLastCellNum(); i++) {
                Cell cell = row.getCell(i);
                if (cell == null) continue;
                // 根据单元格内容计算宽度
                int cellWidth = calculateCellWidth(cell);
                // 更新该列最大宽度
                if (cellWidth > maxColumnWidths[i]) {
                    maxColumnWidths[i] = cellWidth;
                }
            }
        }
        // 应用计算出的列宽
        for (int i = 0; i < columnCount; i++) {
            // POI中1个字符宽度≈256单位，加2个字符作为缓冲
            int width = (maxColumnWidths[i] + 2) * 256;
            // 设置最小和最大限制
            width = Math.max(width, 10 * 256);   // 最小5个字符
            width = Math.min(width, 100 * 256);  // 最大50个字符
            sheet.setColumnWidth(i, width);
        }
    }

    /**
     * 计算单元格内容的显示宽度（优化数字计算）
     *
     * @param cell 单元格
     * @return 内容的字符宽度（中文按2个字符计算）
     */
    private static int calculateCellWidth(Cell cell) {
        String content;
        switch (cell.getCellType()) {
            case STRING:
                content = cell.getStringCellValue();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    content = cell.getDateCellValue().toString();
                } else {
                    // 优化数字显示宽度的计算
                    double value = cell.getNumericCellValue();
                    content = formatNumericValue(value, cell.getCellStyle());
                }
                break;
            case BOOLEAN:
                content = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                // 处理公式计算结果
                switch (cell.getCachedFormulaResultType()) {
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            content = cell.getDateCellValue().toString();
                        } else {
                            double value = cell.getNumericCellValue();
                            content = formatNumericValue(value, cell.getCellStyle());
                        }
                        break;
                    case STRING:
                        content = cell.getStringCellValue();
                        break;
                    case BOOLEAN:
                        content = String.valueOf(cell.getBooleanCellValue());
                        break;
                    default:
                        content = cell.toString();
                }
                break;
            default:
                content = cell.toString();
        }
        return calculateStringWidth(content);
    }

    /**
     * 格式化数字值以准确计算显示宽度
     *
     * @param value 数字值
     * @param style 单元格样式
     * @return 格式化后的字符串
     */
    private static String formatNumericValue(double value, CellStyle style) {
        // 如果有自定义格式，使用DataFormatter获取显示文本
        DataFormatter formatter = new DataFormatter();
        if (style != null && style.getDataFormat() != BuiltinFormats.getBuiltinFormat("General")) {
            return formatter.formatRawCellContents(value, style.getDataFormat(), style.getDataFormatString());
        }
        // 处理整数
        if (value == (long) value) {
            return String.format("%d", (long) value);
        }
        // 处理小数
        String strValue = String.valueOf(value);
        // 避免科学计数法（如1.23456E7）
        if (strValue.contains("E")) {
            // 转换为普通小数格式，最多保留6位小数
            DecimalFormat df = new DecimalFormat("0.######");
            strValue = df.format(value);
        }
        // 去除不必要的尾随零
        if (strValue.contains(".")) {
            strValue = strValue.replaceAll("0*$", "").replaceAll("\\.$", "");
        }
        return strValue;
    }

    /**
     * 计算字符串显示宽度（优化数字显示）
     *
     * @param str 字符串
     * @return 显示宽度
     */
    private static int calculateStringWidth(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }
        int width = 0;
        boolean hasDot = false;
        int decimalDigits = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '.') {
                hasDot = true;
                width += 1; // 小数点算1个宽度
                continue;
            }
            // 数字字符特殊处理
            if (c >= '0' && c <= '9') {
                if (hasDot) {
                    decimalDigits++;
                    // 小数部分每个数字按1个宽度计算
                    width += 1;
                } else {
                    // 整数部分每个数字按1.2个宽度计算（因为数字不等宽）
                    width += 1.2;
                }
            } else {
                // 非数字字符（如千分位逗号、负号、货币符号等）
                width += (c > 127 || c == '　') ? 2 : 1;
            }
        }
        // 对数字列额外增加1个字符的缓冲空间
        if (str.matches("^[\\d,.+-]+$")) {
            width += 1;
        }
        return (int) Math.ceil(width);
    }

    /**
     * 复制行数据（限制列数）
     *
     * @param sourceRow
     * @param targetRow
     * @param targetWorkbook
     * @param maxColumns
     */
    private static void copyRow(Row sourceRow, Row targetRow, Workbook targetWorkbook, int maxColumns) {
        int columnsToCopy = Math.min(sourceRow.getLastCellNum(), maxColumns);
        for (int i = 0; i < columnsToCopy; i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell == null) continue;

            Cell targetCell = targetRow.createCell(i);
            CellStyle newStyle = targetWorkbook.createCellStyle();
            newStyle.cloneStyleFrom(sourceCell.getCellStyle());
            targetCell.setCellStyle(newStyle);

            switch (sourceCell.getCellType()) {
                case NUMERIC:
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                    break;
                case FORMULA:
                    targetCell.setCellFormula(sourceCell.getCellFormula());
                    break;
                case BLANK:
                    targetCell.setBlank();
                    break;
                case STRING:
                default:
                    targetCell.setCellValue(sourceCell.getStringCellValue());
            }
        }
    }


    /**
     * 添加合计行
     *
     * @param sheet
     * @param startRowNum
     * @param dataRowCount
     */
    private static void addSummaryRows(Sheet sheet, int startRowNum, int dataRowCount) {
        // 添加空行
        sheet.createRow(startRowNum);
        // 添加合计行
        Row summaryRow = sheet.createRow(startRowNum + 1);
        // 在K列(10)设置"合计"
        Cell kCell = summaryRow.createCell(10);
        kCell.setCellValue("合计");
        // 创建数字格式样式（千分位分隔符，保留2位小数，左对齐）
        Workbook workbook = sheet.getWorkbook();
        CellStyle numberStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        numberStyle.setDataFormat(format.getFormat("#,##0.00"));
        // 设置左对齐
        numberStyle.setAlignment(HorizontalAlignment.LEFT);
        // 计算L列(11)的和并设置格式
        Cell lCell = summaryRow.createCell(11);
        lCell.setCellFormula("SUM(L2:L" + (startRowNum) + ")");
        lCell.setCellStyle(numberStyle);
        // 计算N列(13)的和并设置格式
        Cell nCell = summaryRow.createCell(13);
        nCell.setCellFormula("SUM(N2:N" + (startRowNum) + ")");
        nCell.setCellStyle(numberStyle);
    }

    /*public static void main(String[] args) {
        try {
            String dateSuffix = extractDateFromFileName("分红信息明细-20250430.xlsx");
            List<DividendRemoteFileDealDto> list = splitExcelByColumn(
                    "C:\\Users\\<USER>\\Desktop\\test\\分红信息明细-20250430.xlsx",
                    "分红信息明细查询",
                    "基金名称",
                    "分红_",
                    ("_" + dateSuffix + ".xlsx"),
                    "C:\\Users\\<USER>\\Desktop\\test\\");
            for (DividendRemoteFileDealDto dto : list) {
                System.out.println(dto.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}
