package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.investNetReport.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.NetReportFileEnum;
import cn.sdata.om.al.mapper.InvestNetReportMappingMapper;
import cn.sdata.om.al.mapper.InvestNetReportValuationDBMapper;
import cn.sdata.om.al.mapper.investNetReport.LogCallRpaNetReportMapper;
import cn.sdata.om.al.mapper.investNetReport.LogFileGenNetReportMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.service.*;
import cn.sdata.om.al.utils.HsExcelCopyUtil;
import cn.sdata.om.al.utils.SecureUtil;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/17 14:18
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LIITa4Y2M2DHandler implements BaseHandler {

    private final SMBService smbService;

    private final InvestNetReportMappingMapper mappingMapper;

    private final LifeInsuranceValuationTableRecordsService lifeInsValService;

    private final LifeInsuranceValuationTableRecordsService liivService;

    private final InvestNetReportService investNetReportService;

    private final MarketTradeDayService marketTradeDayService;

    private final InvestNetReportValuationDBMapper valuationDBMapper;

    private final BaseCronLogService baseCronLogService;

    private final LogCallRpaNetReportMapper logCallRpaNetReportMapper;

    private final LogFileGenNetReportMapper logFileGenNetReportMapper;

    /**
     * 投连净值播报-本级目录名称
     */
    private final String activeDirName = "investNetReport";

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;


    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {

        try {
            String logId = String.valueOf(param.get(RPAConstant.LOG_ID));
            LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
            uw.eq(BaseCronLog::getId, logId);
            uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
            baseCronLogService.update(uw);
        } catch (Exception e) {
            log.error("LIITa4Y2M2DHandler_update_executeMethod:{},{}", e, e.getMessage());
        }
        String userId = String.valueOf(param.get("userId")),
                userName = String.valueOf(param.get("userName"));
        //获取业务日期参数
        String dataDate = String.valueOf(param.get(BaseConstant.RPA_START_DATE_NAME));
        log.info("寿险投连-TAYYYYMMDD下载执行处理RPA文件:" + dataDate);
        Objects.requireNonNull(files, "寿险投连-TAYYYYMMDD下载执行处理RPA返回文件不得为空");
        log.info("寿险投连-liiTaHandler_files:{}", files);
        String parentDir = baseDir + File.separator + activeDirName + File.separator + dataDate + File.separator;
        log.info("寿险投连-liiTaHandler_parentDir:" + parentDir);
        List<LifeInsuranceValuationTableRecords> records = Lists.newArrayList();
        Map<String, String> productNameToIdMap = mappingMapper.mappingList().stream().collect(
                Collectors.toMap(i -> i.getNetReportName().replaceAll("\\s", ""), MappingEntity::getAccountSetCode, (n1, n2) -> n1)
        );
        log.info("寿险投连-liiTaHandler_productNameToIdMap:{}", productNameToIdMap);
        String remoteFilePath_net_dir = null;
        Integer taFileCount = 0;
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.TA.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "ta_start")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
        for (RemoteFileInfo file : files) {
            if (file != null && !"寿险_投连".equals(file.getFileName()) && file.getFileName().contains(dataDate)) {
                log.info("寿险投连-liiTaHandler_ta_file:{}", file);
                String targetFilePath = parentDir + file.getFileName(),
                        remoteFilePath = StringUtil.concatSeparator(file.getRelativePath(), file.getFileName());
                log.info("寿险投连-liiTaHandler_ta_targetFilePath_remoteFilePath,{},{}", targetFilePath, remoteFilePath);
                if (StringUtils.isBlank(remoteFilePath_net_dir))
                    remoteFilePath_net_dir = file.getRelativePath();
                try {
                    downloadRemoteFiles(targetFilePath, remoteFilePath);
                } catch (Exception e) {

                    //生成文件日志记录
                    logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setUserId(userId)
                            .setUserName(userName)
                            .setStatus("失败")
                            .setStep("下载远程rpa-ta文件downloadRemoteFiles执行报错")
                            .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
                    );

                    //生成文件日志记录
                    logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setUserId(userId)
                            .setUserName(userName)
                            .setStatus("失败")
                            .setStep("下载远程rpa-ta文件downloadRemoteFiles执行报错")
                            .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
                    );

                    log.error("寿险投连-liiTaHandler_ta_downloadRemoteFiles_error,{},{},{}", targetFilePath, remoteFilePath, e);
                    investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.TA.getTypeNo())
                            .set(InvestNetReportEntity::getUpdateTime, new Date())
                            .set(InvestNetReportEntity::getUserId, "ta_downloadRemoteFiles_error")
                            .set(InvestNetReportEntity::getDownloadStatus, 2)
                    );
                    throw new RuntimeException(e);
                }
                if (file.getFileName().contains("导出TA") && !file.getFileName().contains("导出TA(~)")) {
                    taFileCount++;
                    log.info("寿险投连-liiTaHandler_taFile:{}", file);
                }
                if (file.getFileName().contains("导出TA")
                        && !file.getFileName().contains("导出TA(~)")
                        && file.getFileName().contains(dataDate)) {
                    investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.TA.getTypeNo())
                            .set(InvestNetReportEntity::getLocalFilePath, targetFilePath)
                            .set(InvestNetReportEntity::getUpdateTime, new Date())
                            .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                            .set(InvestNetReportEntity::getUserId, "ta_end")
                            .set(InvestNetReportEntity::getDownloadStatus, 1)
                    );

                    //调用rpa日志记录
                    logCallRpaNetReportMapper.insert(new LogCallRpaNetReportEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setFilePath(targetFilePath)
                            .setFileName(Paths.get(targetFilePath).getFileName().toString())
                            .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                            .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                            .setUserId(userId)
                            .setUserName(userName)
                    );

                }
            }
        }
        log.info("寿险投连-liiTaHandler_taFileCount:{}", taFileCount);
        log.info("寿险投连-liiTaHandler_net_remoteFilePath_net_dir:{}", remoteFilePath_net_dir);
        if (StringUtils.isNotBlank(remoteFilePath_net_dir)) {
            String remoteFilePath_net_dir1 = StringUtil.concatSeparator(remoteFilePath_net_dir, "寿险_投连");
            log.info("寿险投连-liiTaHandler_net_remoteFilePath_net_dir_new1:{}", remoteFilePath_net_dir1);
            List<RemoteFileInfo> remoteFileInfos = smbService.listFileInfo(remoteFilePath_net_dir1);
            log.info("寿险投连-liiTaHandler_net_remoteFileInfos:{}", remoteFileInfos);
            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "ul_start")
                    .set(InvestNetReportEntity::getDownloadStatus, 3)
            );
            if (CollUtil.isNotEmpty(remoteFileInfos)) {
                for (RemoteFileInfo file : remoteFileInfos) {
                    if (!file.getFileName().contains(dataDate)) {
                        continue;
                    }
                    log.info("寿险投连-liiTaHandler_net_投连净值文件:{}", file);
                    String id = null;
                    for (Map.Entry<String, String> entry : productNameToIdMap.entrySet()) {
                        String k = entry.getKey(),
                                v = entry.getValue(),
                                tmp_name = k.replaceAll("UL_", "投连").replaceAll("建行", "").replaceAll("\\s", "");
                        log.info("寿险投连-liiTaHandler_net_k,v,tmp_name:{},{},{}", k, v, tmp_name);
                        if (file.getFileName().contains(tmp_name)) {
                            id = v;
                            break;
                        }
                    }
                    log.info("寿险投连-liiTaHandler_net_file_id_fileName:{},{}", id, file.getFileName());
                    if (StringUtils.isBlank(id)) continue;
                    String targetFilePath_net = ensureTrailingSeparator(parentDir) + file.getFileName(),
                            remoteFilePath_net = StringUtil.concatSeparator(file.getRelativePath(), file.getFileName());
                    log.info("寿险投连-liiTaHandler_net_targetFilePath_remoteFilePath,{},{}", targetFilePath_net, remoteFilePath_net);
                    try {
                        downloadRemoteFiles(targetFilePath_net, remoteFilePath_net);
                    } catch (Exception e) {

                        //生成文件日志记录
                        logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                                .setId(IdWorker.getIdStr())
                                .setDataDate(dataDate)
                                .setCreateTime(new Date())
                                .setUserId(userId)
                                .setUserName(userName)
                                .setStatus("失败")
                                .setStep("下载远程rpa估值文件文件downloadRemoteFiles执行报错")
                                .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
                        );

                        //生成文件日志记录
                        logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                                .setId(IdWorker.getIdStr())
                                .setDataDate(dataDate)
                                .setCreateTime(new Date())
                                .setUserId(userId)
                                .setUserName(userName)
                                .setStatus("失败")
                                .setStep("下载远程rpa估值文件downloadRemoteFiles执行报错")
                                .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
                        );

                        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                                .eq(InvestNetReportEntity::getDataDate, dataDate)
                                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                                .set(InvestNetReportEntity::getUpdateTime, new Date())
                                .set(InvestNetReportEntity::getUserId, "寿险_投连_net_downloadRemoteFiles_error")
                                .set(InvestNetReportEntity::getDownloadStatus, 2)
                        );
                        log.error("寿险投连-liiTaHandler_net_downloadRemoteFiles_error,{},{},{}", targetFilePath_net, remoteFilePath_net, e);
                        throw new RuntimeException(e);
                    }
                    log.info("寿险投连-liiTaHandler_net_investNetReportMapper_add_record");
                    LifeInsuranceValuationTableRecords record = new LifeInsuranceValuationTableRecords();
                    record.setId(IdWorker.getIdStr())
                            .setCreateTime(new Date())
                            .setDownloadTime(file.getDownloadTime())
                            .setOperator("rpa")
                            .setRemoteFilePath(remoteFilePath_net)
                            .setLocalFilePath(targetFilePath_net)
                            .setAccountSetCode(id)
                            .setValuationDate(dataDate);
                    records.add(record);

                    //调用rpa日志记录
                    logCallRpaNetReportMapper.insert(new LogCallRpaNetReportEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setFilePath(targetFilePath_net)
                            .setFileName(Paths.get(targetFilePath_net).getFileName().toString())
                            .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                            .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                            .setUserId(userId)
                            .setUserName(userName)
                    );
                }
            } else {

                //生成文件日志记录
                logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setStatus("失败")
                        .setStep("远程rpa账套估值文件目录为空")
                        .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
                );

                //生成文件日志记录
                logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setStatus("失败")
                        .setStep("远程rpa账套估值文件目录为空")
                        .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
                );

                investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                        .eq(InvestNetReportEntity::getDataDate, dataDate)
                        .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                        .set(InvestNetReportEntity::getUpdateTime, new Date())
                        .set(InvestNetReportEntity::getUserId, "该日期无净值文件")
                        .set(InvestNetReportEntity::getDownloadStatus, 2)
                );
                throw new RuntimeException("该日期无净值文件1");
            }
            if (CollUtil.isNotEmpty(records)) {
                log.info("寿险投连-liiTaHandler_net_saveBatch_records_saveBatch");
                lifeInsValService.saveBatch(records);
            }
            List<String> accountSetCodes = getAccountSetCodes();
            try {
                dealUlFile(dataDate, accountSetCodes, mappingMapper.mappingList(), userId, userName);
                //生成文件日志记录
                logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setStatus("成功")
                        .setStep("净值播报文件逻辑处理成功")
                        .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
                        .setFilePath(genFilePath(dataDate, "UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx"))
                );
            } catch (Exception e) {
                log.error("liiTaHandler_dealUlFile_error:{}", e);
                throw new RuntimeException(e.getMessage());
            }
            try {
                netReportExcelLogic(dataDate);
                //生成文件日志记录
                logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setStatus("成功")
                        .setStep("净值播报文件逻辑处理成功")
                        .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
                        .setFilePath(genFilePath(dataDate, "净值播报-" + dateNorm2Pure(dataDate) + ".xlsx"))
                );
            } catch (Exception e) {

                //生成文件日志记录
                logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setUserId(userId)
                        .setUserName(userName)
                        .setStatus("失败")
                        .setStep("净值播报文件逻辑处理错误netReportExcelLogic_error:" + e.getMessage())
                        .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
                );

                investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                        .eq(InvestNetReportEntity::getDataDate, dataDate)
                        .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.REPORT.getTypeNo())
                        .set(InvestNetReportEntity::getUpdateTime, new Date())
                        .set(InvestNetReportEntity::getUserId, "liiTaHandler_netReportExcelLogic_error")
                        .set(InvestNetReportEntity::getDownloadStatus, 2)
                );
                log.error("liiTaHandler_netReportExcelLogic_error:{}", e);
                throw new RuntimeException(e.getMessage());
            }
        } else {
            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("未获取到远程rpa账套估值文件目录")
                    .setFileName("净值播报-" + dateNorm2Pure(dataDate) + ".xlsx")
            );
            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("未获取到远程rpa账套估值文件目录")
                    .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
            );
            throw new RuntimeException("该日期无净值文件2");
        }

    }

    @Override
    public void onFail(Map<String, Object> param) {
        String dataDate = String.valueOf(param.get(BaseConstant.RPA_START_DATE_NAME));
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "LIITa4Y2M2DHandler_onFail")
                .set(InvestNetReportEntity::getDownloadStatus, 2)
        );

    }

    /**
     * 获取投连-配置的账套号
     *
     * @return
     */
    public List<String> getAccountSetCodes() {
        return mappingMapper.mappingList().stream().map(MappingEntity::getAccountSetCode).distinct().collect(Collectors.toList());
    }

    /**
     * 处理ul文件
     *
     * @param dataDate        数据日期
     * @param accountSetCodes 账套编号
     * @param mappings        账套编号配置信息
     * @param userId          用户id
     * @param userName        用户名称
     * @throws Exception
     */
    public void dealUlFile(String dataDate, List<String> accountSetCodes, List<MappingEntity> mappings, String userId, String userName) throws Exception {
        List<LifeInsuranceValuationTableRecords> vRecords = liivService.getLatestList(dataDate, accountSetCodes);
        if (CollUtil.isEmpty(vRecords)) {
            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("无账套估值文件导致生成失败")
                    .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "寿险_投连_vRecords为空")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            throw new Exception("无账套对应的估值表信息");
        }
        Map<String, String> account2Vpath = vRecords.stream().collect(Collectors.toMap(LifeInsuranceValuationTableRecords::getAccountSetCode, LifeInsuranceValuationTableRecords::getLocalFilePath, (n1, n2) -> n1));
        String targetFilePath = genFilePath(dataDate, "UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx");
        try {
            copyDefaultFile("life-insurance-valuation/UL valuation_取数模板.xlsx", targetFilePath);
        } catch (Exception e) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("模板文件生成目标文件copyDefaultFile执行错误:" + e.getMessage())
                    .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "dealUlFile_copyDefaultFile_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            log.error("dealUlFile_copyDefaultFile_error:{},{}", e, e.getMessage());
            throw e;
        }
        try {
            accountSetValuationToUL(dataDate, account2Vpath, targetFilePath, mappings, userId, userName);
        } catch (Exception e) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("账套估值文件内容转化accountSetValuationToUL逻辑执行错误:" + e.getMessage())
                    .setFileName(Paths.get(targetFilePath).getFileName().toString())
                    .setFilePath(targetFilePath)
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "dealUlFile_accountSetValuationToUL_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            log.error("dealUlFile_accountSetValuationToUL_error:{},{}", e, e.getMessage());
        }

        try {
            InvestNetReportEntity entity = investNetReportService.getOne(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.TA.getTypeNo()));
            taToUL(dataDate, entity.getLocalFilePath(), targetFilePath);
        } catch (Exception e) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("导出TA文件内容转化taToUL逻辑执行错误:" + e.getMessage())
                    .setFileName(Paths.get(targetFilePath).getFileName().toString())
                    .setFilePath(targetFilePath)
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "dealUlFile_taToUL_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            log.error("dealUlFile_taToUL_error:{},{}", e, e.getMessage());
            throw e;
        }

        try {
            dealShareChangeExcel(dataDate, userId, userName);
        } catch (Exception e) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("份额变动表内容转化dealShareChangeExcel逻辑执行错误:" + e.getMessage())
                    .setFileName(Paths.get(targetFilePath).getFileName().toString())
                    .setFilePath(targetFilePath)
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "dealUlFile_dealShareChangeExcel_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            log.error("dealUlFile_dealShareChangeExcel_error:{},{}", e, e.getMessage());
            throw e;
        }

        try {
            HsExcelCopyUtil.setFormulaTypeAndCalculate(targetFilePath, "Daily Summary");
        } catch (Exception e) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("UL文件Daily Summary工作表setFormulaTypeAndCalculate逻辑执行错误:" + e.getMessage())
                    .setFileName(Paths.get(targetFilePath).getFileName().toString())
                    .setFilePath(targetFilePath)
            );

            log.error("accountSetValuationToUL_setFormulaTypeAndCalculate_error:{},{}", e, e.getMessage());
            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "accountSetValuationToUL_setFormulaTypeAndCalculate_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            throw e;
        }
    }

    /**
     * 将【导出 TA-YYYYMMDD】文件内容复制到【UL valuation_YYYYMMDD】文件的【Unit price】sheet中
     *
     * @param dataDate
     * @param taExcelPath     UL valuation_YYYYMMDD,其中YYYYMMDD为具体日期
     * @param targetExcelPath UL valuation_YYYYMMDD,其中YYYYMMDD为具体日期
     */
    public void taToUL(String dataDate, String taExcelPath, String targetExcelPath) throws Exception {
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "ta2ul-init")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
        taExcelDataCopy(taExcelPath, "Sheet1", targetExcelPath, "Unit price");
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "ta2ul-end")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
    }

    /**
     * excel内容复制-排除表头
     *
     * @param sourceExcelPath 源文件路径
     * @param sourceSheetName 源文件的sheet名称
     * @param targetExcelPath 目标文件路径
     * @param targetSheetName 目标文件的sheet名称
     */
    public void taExcelDataCopy(String sourceExcelPath,
                                String sourceSheetName,
                                String targetExcelPath,
                                String targetSheetName) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
        Map<CellStyle, CellStyle> styleCache = new HashMap<>();
        try (FileInputStream fis = new FileInputStream(sourceExcelPath);
             Workbook sourceWorkbook = WorkbookFactory.create(fis)) {
            try (FileInputStream targetFis = new FileInputStream(targetExcelPath);
                 Workbook targetWorkbook = WorkbookFactory.create(targetFis)) {
                Sheet sourceSheet = sourceWorkbook.getSheet(sourceSheetName);
                if (sourceSheet == null) {
                    sourceWorkbook.close();
                    targetWorkbook.close();
                    throw new IllegalArgumentException("源文件中未找到名为 " + sourceSheetName + " 的工作表！");
                }
                Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);
                if (targetSheet == null) {
                    sourceWorkbook.close();
                    targetWorkbook.close();
                    throw new IllegalArgumentException("目标文件中未找到名为 " + targetSheetName + " 的工作表！");
                }
                for (int i = targetSheet.getLastRowNum(); i >= 0; i--) {
                    Row row = targetSheet.getRow(i);
                    if (row != null) targetSheet.removeRow(row);
                }
                targetSheet.getMergedRegions().clear();
                for (int i = 0; i < sourceSheet.getLastRowNum(); i++) {
                    Row sourceRow = sourceSheet.getRow(i);
                    if (sourceRow == null) continue;
                    Row targetRow = targetSheet.createRow(targetSheet.getLastRowNum() + 1);
                    for (int j = 0; j < sourceRow.getLastCellNum(); j++) {
                        Cell sourceCell = sourceRow.getCell(j);
                        if (sourceCell == null) continue;
                        Cell targetCell = targetRow.createCell(j);
                        CellStyle sourceStyle = sourceCell.getCellStyle();
                        CellStyle targetCellStyle = styleCache.get(sourceStyle);
                        if (targetCellStyle == null) {
                            targetCellStyle = targetWorkbook.createCellStyle();
                            targetCellStyle.cloneStyleFrom(sourceStyle);
                            styleCache.put(sourceStyle, targetCellStyle);
                        }
                        targetCell.setCellStyle(targetCellStyle);
                        switch (sourceCell.getCellType()) {
                            case STRING:
                                targetCell.setCellValue(sourceCell.getStringCellValue());
                                break;
                            case NUMERIC:
                                targetCell.setCellValue(sourceCell.getNumericCellValue());
                                break;
                            case BOOLEAN:
                                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                                break;
                            case FORMULA:
                                targetCell.setCellFormula(sourceCell.getCellFormula());
                                break;
                            case BLANK:
                                targetCell.setBlank();
                                break;
                            default:
                                targetCell.setCellValue(sourceCell.getStringCellValue());
                        }
                    }
                }
                try (FileOutputStream fos = new FileOutputStream(targetExcelPath)) {
                    targetWorkbook.write(fos);
                }
                sourceWorkbook.close();
                targetWorkbook.close();
            }
        }
    }

    /**
     * 处理余额变动表
     *
     * @param dataDate 数据日期
     * @param userId
     * @param userName
     * @throws Exception
     */
    public synchronized void dealShareChangeExcel(String dataDate, String userId, String userName) throws Exception {
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "dealShareChangeExcel")
                .set(InvestNetReportEntity::getDownloadStatus, 3));
        InvestNetReportEntity scReport = investNetReportService.getOne(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo()));
        if (ObjectUtil.isNull(scReport) || StringUtils.isBlank(scReport.getLocalFilePath())) {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("份额变动表内容转化dealShareChangeExcel逻辑执行错误:份额变动表值不存在")
                    .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
            );

            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "份额变动表值不存在")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
                    .set(InvestNetReportEntity::getSource, "SYSTEM"));
            throw new Exception("份额变动表值不存在");
        }
        String scReportFilePath = scReport.getLocalFilePath();
        Path scPath = Paths.get(scReportFilePath);
        if (Files.exists(scPath) && Files.isRegularFile(scPath) && Files.isReadable(scPath)) {
            InvestNetReportEntity ulReport = investNetReportService.getOne(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
            );
            if (ulReport == null) throw new Exception("该数据日期无对应的初始化数据");
            String ulReportFilePath = ulReport.getLocalFilePath();
            shareChangeToUL(scReportFilePath, ulReportFilePath);
            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "share-end")
                    .set(InvestNetReportEntity::getDownloadStatus, 1)
            );

        } else {

            //生成文件日志记录
            logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setUserId(userId)
                    .setUserName(userName)
                    .setStatus("失败")
                    .setStep("份额变动表内容转化dealShareChangeExcel逻辑执行错误:份额变动表文件异常")
                    .setFileName("UL valuation_" + dateNorm2Pure(dataDate) + ".xlsx")
            );

            log.error("dealShareChangeExcel_sc_LocalFilePath_文件异常");
            investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "份额变动表文件异常")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            throw new Exception("份额变动表文件不存在");
        }

    }

    /**
     * 将邮件拿过来的表【份额变动表】的内容（前四列A-D）复制到【UL valuation_YYYYMMDD】文件的【Fund movement】sheet中
     *
     * @param shareChangeExcelPath 净值变动表
     * @param targetExcelPath      UL valuation_YYYYMMDD,其中YYYYMMDD为数据日期
     */
    public void shareChangeToUL(String shareChangeExcelPath, String targetExcelPath) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
        Map<CellStyle, CellStyle> styleCache = new HashMap<>();
        try (FileInputStream fis = new FileInputStream(shareChangeExcelPath);
             Workbook sourceWorkbook = WorkbookFactory.create(fis)) {
            try (FileInputStream targetFis = new FileInputStream(targetExcelPath);
                 Workbook targetWorkbook = WorkbookFactory.create(targetFis)) {
                Sheet sourceSheet = sourceWorkbook.getSheetAt(0);
                if (sourceSheet == null) {
                    sourceWorkbook.close();
                    targetWorkbook.close();
                    throw new IllegalArgumentException("源文件中未找到工作表！");
                }

                Sheet targetSheet = targetWorkbook.getSheet("Fund movement");
                if (targetSheet == null) {
                    sourceWorkbook.close();
                    targetWorkbook.close();
                    throw new IllegalArgumentException("目标文件中未找到名为Fund movement的工作表！");
                }
                for (int i = targetSheet.getLastRowNum(); i >= 0; i--) {
                    Row row = targetSheet.getRow(i);
                    if (row != null) targetSheet.removeRow(row);
                }
                targetSheet.getMergedRegions().clear();
                for (int i = 0; i <= sourceSheet.getLastRowNum(); i++) {
                    Row sourceRow = sourceSheet.getRow(i);
                    if (sourceRow == null) continue;
                    Row targetRow = targetSheet.createRow(targetSheet.getLastRowNum() + 1);
                    for (int j = 0; j < 4; j++) { // j 对应列索引（0=A, 1=B, 2=C, 3=D）
                        Cell sourceCell = sourceRow.getCell(j);
                        if (sourceCell == null) continue;
                        Cell targetCell = targetRow.createCell(j);
                        CellStyle sourceStyle = sourceCell.getCellStyle();
                        CellStyle targetCellStyle = styleCache.get(sourceStyle);
                        if (targetCellStyle == null) {
                            targetCellStyle = targetWorkbook.createCellStyle();
                            targetCellStyle.cloneStyleFrom(sourceStyle);
                            styleCache.put(sourceStyle, targetCellStyle);
                        }
                        targetCell.setCellStyle(targetCellStyle);
                        switch (sourceCell.getCellType()) {
                            case STRING:
                                targetCell.setCellValue(sourceCell.getStringCellValue());
                                break;
                            case NUMERIC:
                                targetCell.setCellValue(sourceCell.getNumericCellValue());
                                break;
                            case BOOLEAN:
                                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                                break;
                            case FORMULA:
                                targetCell.setCellFormula(sourceCell.getCellFormula());
                                break;
                            case BLANK:
                                targetCell.setBlank();
                                break;
                            default:
                                targetCell.setCellValue(sourceCell.getStringCellValue());
                        }
                    }
                }
                try (FileOutputStream fos = new FileOutputStream(targetExcelPath)) {
                    targetWorkbook.write(fos);
                }
                sourceWorkbook.close();
                targetWorkbook.close();
            }
        }
    }

    /**
     * 初始化默认数据
     *
     * @param dataDate yyyy-mm-dd
     * @return
     */
    public synchronized void initDefaultData(String dataDate) {
        int defaultCount = Math.toIntExact(investNetReportService.count(Wrappers.lambdaQuery(InvestNetReportEntity.class).eq(InvestNetReportEntity::getDataDate, dataDate)));
        if (defaultCount > 0) {
            return;
        }
        List<InvestNetReportEntity> reportEntities = Arrays.stream(NetReportFileEnum.values())
                .map(fileEnum -> new InvestNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setFileName(defaultFileNameReplace(fileEnum.getName(), dataDate))
                        .setDataDate(dataDate)
                        .setDownloadStatus(0)
                        .setNetReportSendStatus(MailStatus.UNSENT.name())
                        .setNetValueSendStatus(MailStatus.UNSENT.name())
                        .setCreateTime(new Date())
                        .setSource(fileEnum.getSource())
                        .setFileTypeNo(fileEnum.getTypeNo()))
                .collect(Collectors.toList());
        for (InvestNetReportEntity reportEntity : reportEntities) {
            if (reportEntity.getFileName().contains(NetReportFileEnum.SHARECHANGE.getName())) {
                reportEntity.setNetReportSendStatus("-");
                reportEntity.setNetReportSendStatus("-");
            }
        }
        if (CollUtil.isNotEmpty(reportEntities)) investNetReportService.saveBatch(reportEntities);
    }

    /**
     * 默认名称YYYYMMDD替换处理
     *
     * @param defaultFileName
     * @param dataDate        yyyy-MM-dd
     * @return
     */
    public static String defaultFileNameReplace(String defaultFileName, String dataDate) {
        return defaultFileName.replace("YYYYMMDD", dateNorm2Pure(dataDate));
    }

    /**
     * 净值播报文件组装
     *
     * @param dataDate
     * @throws Exception
     */
    public synchronized void netReportExcelLogic(String dataDate) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001);
        String targetExcelPath = genFilePath(dataDate, "净值播报-" + dateNorm2Pure(dataDate) + ".xlsx");
        copyDefaultFile("life-insurance-valuation/净值播报-模板.xlsx", targetExcelPath);
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.REPORT.getTypeNo())
                .set(InvestNetReportEntity::getLocalFilePath, targetExcelPath)
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "report-init")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
        Map<String, String> am = Maps.newHashMap(), bm = Maps.newHashMap(), cm = Maps.newHashMap(),
                dm = Maps.newHashMap(), em = Maps.newHashMap(), fm = Maps.newHashMap();
        List<MappingEntity> mappingList = mappingMapper.mappingList();
        List<String> accountSetCodes = mappingList.stream().map(MappingEntity::getAccountSetCode).distinct().collect(Collectors.toList());
        Map<String, String> accountName2CodeMap = mappingList.stream().collect(Collectors.toMap(i -> i.getNetReportName().replaceAll("\\s", ""), i -> i.getAccountSetCode(), (n1, n2) -> n1));
        List<String> dates = marketTradeDayService.getNetReport5TradeDay(dataDate);
        reverseSort(dates);
        log.info("netReportExcelLogic_dates:{}", dates);
        List<ValuationNetValueEntity> netValueList = valuationDBMapper.netValueListByDates(accountSetCodes, dates);
        Map<String, List<ValuationNetValueEntity>> netMap = netValueList.stream().collect(Collectors.groupingBy(ValuationNetValueEntity::getAccountSetCode));
        try (FileInputStream targetFis = new FileInputStream(targetExcelPath);
             Workbook targetWorkbook = WorkbookFactory.create(targetFis)) {
            Map<Integer, String> sheet2IndexToDateMap = Maps.newHashMap();
            Sheet sheet2 = targetWorkbook.getSheet("Sheet2");
            if (dates.size() != 5) {
                targetWorkbook.close();
                return;
            }
            Row dateRow = sheet2.getRow(1);
            for (int j = 1; j <= 5; j++) {
                Cell cell = dateRow.getCell(j);
                if (cell == null) cell = dateRow.createCell(j);
                if (null == dates.get(j - 1)) continue;
                String tmp_date = dates.get(j - 1);
                cell.setCellValue(dateNorm2Chinese(tmp_date));
                sheet2IndexToDateMap.put(j, tmp_date);
            }
            //遍历数据行
            for (int i = 2; i <= sheet2.getLastRowNum(); i++) {
                Row row = sheet2.getRow(i);
                if (null == row || null == row.getCell(0)) continue;
                if (StringUtils.isBlank(row.getCell(0).getStringCellValue())) continue;
                String cell0Value = row.getCell(0).getStringCellValue().replaceAll("\\s", "");
                log.info("netReportExcelLogic_cell0Value:{}", cell0Value);
                for (Map.Entry<String, String> entry : accountName2CodeMap.entrySet()) {
                    if (!entry.getKey().contains(cell0Value)) continue;
                    if (null == netMap.get(entry.getValue())) break;
                    List<ValuationNetValueEntity> list = netMap.get(entry.getValue());
                    if (CollUtil.isEmpty(list)) break;
                    Map<String, String> date2NetMap = list.stream()
                            .filter(Objects::nonNull)
                            .filter(entity -> StringUtils.isNotBlank(entity.getShareNetValue()))
                            .collect(Collectors.toMap(ValuationNetValueEntity::getValuationDate, ValuationNetValueEntity::getShareNetValue, (n1, n2) -> n1));
                    if (CollUtil.isEmpty(date2NetMap)) break;
                    //循环列
                    for (int j = 1; j <= 5; j++) {
                        Cell cell = row.getCell(j);
                        if (cell == null) cell = row.createCell(j);
                        if (CollUtil.isNotEmpty(sheet2IndexToDateMap)
                                && sheet2IndexToDateMap.containsKey(j)
                                && null != sheet2IndexToDateMap.get(j)
                                && date2NetMap.containsKey(sheet2IndexToDateMap.get(j))) {
                            cell.setCellValue(date2NetMap.get(sheet2IndexToDateMap.get(j)));
                        }
                        switch (j) {
                            case 1:
                                bm.put(cell0Value, cell.getStringCellValue());
                            case 2:
                                cm.put(cell0Value, cell.getStringCellValue());
                            case 3:
                                dm.put(cell0Value, cell.getStringCellValue());
                            case 4:
                                em.put(cell0Value, cell.getStringCellValue());
                            case 5:
                                fm.put(cell0Value, cell.getStringCellValue());
                        }

                    }
                }
            }
            try (FileOutputStream fos = new FileOutputStream(targetExcelPath)) {
                targetWorkbook.write(fos);
            }
            targetWorkbook.close();
        }
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.REPORT.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "report-sheet2")
        );
        Map<String, String> account2assetMap = netValueList.stream()
                .filter(Objects::nonNull)
                .filter(i -> dataDate.equals(i.getValuationDate()))
                .collect(Collectors.toMap(ValuationNetValueEntity::getAccountSetCode, ValuationNetValueEntity::getAssetNetValue, (n1, n2) -> n1));
        for (Map.Entry<String, String> entry : accountName2CodeMap.entrySet()) {
            String name = entry.getKey(), account = entry.getValue();
            am.put(name, null);
            if (account2assetMap.containsKey(account))
                am.put(name, account2assetMap.get(account));
        }
        List<Map<String, String>> bcdefMapList = List.of(Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap(), cm, dm, em, fm);
        DecimalFormat df = new DecimalFormat("0.00%");
        try (FileInputStream targetFis = new FileInputStream(targetExcelPath);
             Workbook targetWorkbook = WorkbookFactory.create(targetFis)) {
            Sheet sheet1 = targetWorkbook.getSheet("Sheet1");
            if (null == sheet1) {
                targetWorkbook.close();
                return;
            }

            CellStyle redStyle = targetWorkbook.createCellStyle();
            Font redFont = targetWorkbook.createFont();
            redFont.setColor(IndexedColors.RED.getIndex());
            redStyle.setFont(redFont);
            CellStyle greenStyle = targetWorkbook.createCellStyle();
            Font greenFont = targetWorkbook.createFont();
            greenFont.setColor(IndexedColors.GREEN.getIndex());
            greenStyle.setFont(greenFont);

            for (int i = 1; i <= sheet1.getLastRowNum(); i++) {
                Row row = sheet1.getRow(i);
                if (null == row || null == row.getCell(0)) continue;
                if (StringUtils.isBlank(row.getCell(0).getStringCellValue())) continue;
                String cell0Value = row.getCell(0).getStringCellValue().replaceAll("\\s", "");
                for (int j = 1; j <= 6; j++) {
                    if (j == 1) {
                        if (am.containsKey(cell0Value)) {
                            row.getCell(j).setCellValue(am.get(cell0Value));
                        }
                    } else if (j == 2) {
                        for (Map.Entry<String, String> entry : bm.entrySet()) {
                            if (cell0Value.contains(entry.getKey())) {
                                row.getCell(j).setCellValue(entry.getValue());
                                break;
                            }
                        }
                    } else {
                        for (Map.Entry<String, String> entry : bcdefMapList.get(j).entrySet()) {
                            if (cell0Value.contains(entry.getKey())) {
                                if (null != row.getCell(2)
                                        && null != row.getCell(2).getStringCellValue()
                                        && null != row.getCell(j)
                                        && null != entry.getValue()) {
                                    BigDecimal num1 = new BigDecimal(row.getCell(2).getStringCellValue()),
                                            num2 = new BigDecimal(entry.getValue());
                                    BigDecimal result = num1.divide(num2, 4, RoundingMode.HALF_UP).subtract(BigDecimal.ONE);
                                    String formattedResult = df.format(result);
                                    Cell cell = row.getCell(j);
                                    cell.setCellValue(formattedResult);

                                    CellStyle newStyle = targetWorkbook.createCellStyle();
                                    newStyle.setBorderTop(BorderStyle.THIN);      // 上边框
                                    newStyle.setBorderBottom(BorderStyle.THIN);   // 下边框
                                    newStyle.setBorderLeft(BorderStyle.THIN);     // 左边框
                                    newStyle.setBorderRight(BorderStyle.THIN);    // 右边框
                                    newStyle.setAlignment(HorizontalAlignment.CENTER);      // 水平居中
                                    newStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
                                    if (result.compareTo(BigDecimal.ZERO) > 0) {
                                        log.info("Sheet1_涨:{},{}", j, "红色");
                                        newStyle.setFont(redFont); // 红色
                                    } else if (result.compareTo(BigDecimal.ZERO) < 0) {
                                        log.info("Sheet1_跌:{},{}", j, "绿色");
                                        newStyle.setFont(greenFont); // 绿色
                                    }
                                    cell.setCellStyle(newStyle);

                                    break;
                                }
                            }
                        }
                    }
                }
            }
            try (FileOutputStream fos = new FileOutputStream(targetExcelPath)) {
                targetWorkbook.write(fos);
            }
            targetWorkbook.close();
        }
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.REPORT.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "report-sheet1")
                .set(InvestNetReportEntity::getDownloadStatus, 1)
        );
    }

    /**
     * 交易日列表+倒叙排序
     *
     * @param dates yyyy-mm-dd
     */
    public void reverseSort(List<String> dates) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dates.sort((date1, date2) -> {
            LocalDate localDate1 = LocalDate.parse(date1, formatter);
            LocalDate localDate2 = LocalDate.parse(date2, formatter);
            return localDate2.compareTo(localDate1); // 倒序
        });
    }

    /**
     * 拷贝默认的模板文件UL valuation_取数模板.xlsx
     *
     * @param targetFilePath
     * @throws Exception
     */
    public synchronized void copyDefaultFile(String classPath, String targetFilePath) throws Exception {
        Path targetPath = Paths.get(targetFilePath);
        Path parentDir = targetPath.getParent();
        if (parentDir != null) Files.createDirectories(parentDir);
        try (InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(classPath)) {
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 生成文件全路径地址
     *
     * @param dataDate 数据日期
     * @param fileName 文件名称
     * @return
     */
    public String genFilePath(String dataDate, String fileName) {
        String filePath = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator + fileName;
        log.info("寿险投连-genFilePath:{}", filePath);
        return filePath;
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String dateNorm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 账套估值表->UL valuation_YYYYMMDD逻辑处理
     *
     * @param dataDate
     * @param account2Vpath   账套编号->估值表路径map
     * @param targetExcelPath UL valuation_YYYYMMDD,其中YYYYMMDD为具体日期
     * @param mappings        净值播报映射表数据
     * @param userId
     * @param userName
     */
    public synchronized void accountSetValuationToUL(String dataDate,
                                                     Map<String, String> account2Vpath,
                                                     String targetExcelPath,
                                                     List<MappingEntity> mappings,
                                                     String userId,
                                                     String userName) {
        if (CollUtil.isEmpty(mappings)) {
            mappings = mappingMapper.mappingList();
        }
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                .set(InvestNetReportEntity::getLocalFilePath, targetExcelPath)
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "valuation2ul-init")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
        Map<String, String> account2Type = mappings.stream().collect(Collectors.toMap(MappingEntity::getAccountSetCode, MappingEntity::getInsuranceTypeCode, (n1, n2) -> n1));
        account2Vpath.forEach((account, vPath) -> {
            if (account2Type.containsKey(account)) {
                try {
                    HsExcelCopyUtil.copyByPoi(vPath, "Sheet1", targetExcelPath, ("HS-" + account2Type.get(account)));
                } catch (Exception e) {

                    //生成文件日志记录
                    logFileGenNetReportMapper.insert(new LogFileGenNetReportEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setUserId(userId)
                            .setUserName(userName)
                            .setStatus("失败")
                            .setStep("账套估值文件内容转化UL-copyByPoi逻辑执行错误:" + account + ":" + e.getMessage())
                            .setFileName(Paths.get(targetExcelPath).getFileName().toString())
                            .setFilePath(targetExcelPath)
                    );

                    log.error("accountSetValuationToUL_copyByPoi_error:{},{},{}", account, vPath, e);
                    investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                            .set(InvestNetReportEntity::getUpdateTime, new Date())
                            .set(InvestNetReportEntity::getUserId, "accountSetValuationToUL_copyByPoi_error")
                            .set(InvestNetReportEntity::getDownloadStatus, 2)
                    );
                    throw e;
                }

            }
        });
        investNetReportService.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, "valuation2ul-end")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
        );
    }


    /**
     * 下载远程文件到本地
     *
     * @param targetFilePath 目标文件路径
     * @param remoteFilePath 远程文件路径
     * @throws IOException
     */
    public void downloadRemoteFiles(String targetFilePath, String remoteFilePath) throws IOException {
        byte[] bytes = smbService.download(remoteFilePath);
        Path file = Paths.get(targetFilePath);
        Files.createDirectories(file.getParent());
        Files.write(file, bytes);
    }

    /**
     * 确保文件夹路径以 \或者/ 结尾
     *
     * @param pathStr
     * @return
     */
    private static String ensureTrailingSeparator(String pathStr) {
        String separator = Paths.get(pathStr).getFileSystem().getSeparator();
        if (!pathStr.endsWith(separator)) return pathStr + separator;
        return pathStr;
    }

    /**
     * yyyy-mm-dd转yyyy年MM月dd日
     *
     * @param normDate
     * @return yyyy年MM月dd日
     */
    private static String dateNorm2Chinese(String normDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        return LocalDate.parse(normDate, inputFormatter).format(outputFormatter);
    }

    /**
     * 生成份额变动表文件全路径地址
     *
     * @param dataDate 数据日期
     * @param fileName 文件名称
     * @return
     */
    public String genShareChangeFilePath(String dataDate, String fileName) {
        return ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator + "shareChange" + File.separator + fileName;
    }

    public static String getSCExcelCellDataDate(String filePath) throws Exception {
        Workbook workbook = new XSSFWorkbook(filePath);
        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(2);
        if (row == null) {
            throw new Exception("数据日期行不存在");
        }
        Cell cell = row.getCell(3);
        if (cell == null) {
            throw new Exception("数据日期单元格不存在");
        }
        String dataDate;
        switch (cell.getCellType()) {
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    dataDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    throw new Exception("数据日期单元格不是日期格式，而是数字: " + numericValue);
                }
                break;
            case STRING:
                String dateString = cell.getStringCellValue().trim();
                try {
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                            outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    LocalDate date = LocalDate.parse(dateString, inputFormat);
                    dataDate = date.format(outputFormat);
                } catch (Exception e) {
                    throw new Exception("数据日期单元格日期格式无效: " + dateString);
                }
                break;
            default:
                throw new Exception("数据日期单元格不是日期或数字类型");
        }
        workbook.close();
        return dataDate;
    }
}
