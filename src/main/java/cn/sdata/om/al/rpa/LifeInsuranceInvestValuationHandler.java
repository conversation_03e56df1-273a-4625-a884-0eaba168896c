package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.investNetReport.LifeInsuranceValuationTableRecords;
import cn.sdata.om.al.entity.investNetReport.MappingEntity;
import cn.sdata.om.al.mapper.InvestNetReportMappingMapper;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.LifeInsuranceValuationTableRecordsService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/17 14:18
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LifeInsuranceInvestValuationHandler implements BaseHandler {

    private final InvestNetReportMappingMapper mappingMapper;

    private final AccountInformationService accountInformationService;

    private final LifeInsuranceValuationTableRecordsService lifeInsValService;

    private final SMBService smbService;

    /**
     * 投连净值播报-本级目录名称
     */
    private final String activeDirName = "investNetReport";

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;


    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        //获取业务日期参数
        String dataDate = String.valueOf(param.get("dataDate"));
        log.error("寿险投连估值表下载执行处理RPA文件:" + dataDate);
        Objects.requireNonNull(files, "寿险投连估值表下载的RPA返回文件不得为空");
        //寿险投连估值表下载信息记录
        List<LifeInsuranceValuationTableRecords> records = Lists.newArrayList();
        List<String> accountSetCodes = mappingMapper.mappingList().stream().map(MappingEntity::getAccountSetCode).distinct().collect(Collectors.toList());
        Map<String, String> productNameIds = accountInformationService
                .list(Wrappers.lambdaQuery(AccountInformation.class).in(AccountInformation::getId, accountSetCodes))
                .stream()
                .collect(Collectors.toMap(AccountInformation::getFullProductName, AccountInformation::getId, (oldOne, newOne) -> newOne));
        //要下载到本地的文件的父目录D:\work\tmp_file\investNetReport\2025-03-18\
        String parentDir = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator;
        log.error("liivHandler_parentDir:" + dataDate);
        for (RemoteFileInfo file : files) {
            String id = getProductIdByFile(file, productNameIds);
            if (StringUtils.isNotBlank(id)) {
                log.error("liivHandler_id:" + id);
                String targetFilePath = parentDir + file.getFileName(),
                        remoteFilePath = StringUtil.concatSeparator(file.getRelativePath(), file.getFileName());
                log.error("liivHandler_targetFilePath_remoteFilePath,{},{}", targetFilePath, remoteFilePath);
                try {
                    downloadRemoteFiles(targetFilePath, remoteFilePath);
                } catch (IOException e) {
                    log.error("liivHandler_downloadRemoteFiles_error,{}", e);
                    throw new RuntimeException(e);
                }
                LifeInsuranceValuationTableRecords record = new LifeInsuranceValuationTableRecords();
                record.setId(IdWorker.getIdStr())
                        .setCreateTime(new Date())
                        .setDownloadTime(file.getDownloadTime())
                        .setOperator(String.valueOf(param.get("operator")))
                        .setRemoteFilePath(remoteFilePath)
                        .setLocalFilePath(targetFilePath)
                        .setAccountSetCode(id)
                        .setValuationDate(dataDate);
                records.add(record);
            } else {
                log.error("liivHandler_未找到匹配的产品名称:{}", file.getFileName());
            }
        }
        if (CollUtil.isNotEmpty(records)) {
            log.error("liivHandler_saveBatch_records");
            lifeInsValService.saveBatch(records);
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {

    }


    /**
     * 下载远程文件到本地
     *
     * @param targetFilePath 目标文件路径
     * @param remoteFilePath 远程文件路径
     * @throws IOException
     */
    public void downloadRemoteFiles(String targetFilePath, String remoteFilePath) throws IOException {
        byte[] bytes = smbService.download(remoteFilePath);
        Path file = Paths.get(targetFilePath);
        Path parentDir = file.getParent();
        Files.createDirectories(parentDir);
        Files.write(file, bytes);
    }

    // 确保路径以 \ 结尾
    private String ensureTrailingSeparator(String pathStr) {
        Path path = Paths.get(pathStr);
        String separator = path.getFileSystem().getSeparator();

        if (!pathStr.endsWith(separator)) {
            return pathStr + separator;
        }
        return pathStr;
    }
}
