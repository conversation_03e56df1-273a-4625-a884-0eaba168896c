package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.monthlyData.LogCallRpaMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.MonthlyDataEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.mapper.monthlyData.LogCallRpaMonthlyDataMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.service.monthlyData.MonthlyDataService;
import cn.sdata.om.al.utils.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.sdata.om.al.constant.BaseConstant.RPA_END_DATE_NAME;
import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

/**
 * 月度数据-债权投资计划净值
 *
 * <AUTHOR>
 * @Date 2025/4/8 9:35
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MonthlyDataZQTZJHJZHandler implements BaseHandler {

    /**
     * 月度数据-本级目录名称
     */
    private final String activeDirName = "monthlyData";

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;

    private final SMBService smbService;

    private final MonthlyDataService monthlyDataService;

    private final BaseCronLogService baseCronLogService;

    private final LogCallRpaMonthlyDataMapper logCallRpaMonthlyDataMapper;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        try {
            String dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                    startDate = String.valueOf(param.get(RPA_START_DATE_NAME)),
                    endDate = String.valueOf(param.get(RPA_END_DATE_NAME)),
                    parentDir = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator,
                    executor = String.valueOf(param.get(CronConstant.EXECUTOR)),
                    pureEndDate = dateNorm2Pure(endDate);
            log.info("MonthlyDataZQTZJHJZHandler_execute_dataDate_parentDir:{},{},{},{}", dataDate, parentDir, executor, pureEndDate);

            String userId = String.valueOf(param.get("userId")),
                    userName = String.valueOf(param.get("userName"));

            try {
                String logId = String.valueOf(param.get(RPAConstant.LOG_ID));
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("MonthlyDataZQTZJHJZHandler_update_executeMethod:{},{}", e, e.getMessage());
            }

            monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                    .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_execute_start")
                    .set(MonthlyDataEntity::getDownloadStatus, 3)
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, executor)
            );
            if (CollUtil.isEmpty(files)) {
                monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                        .eq(MonthlyDataEntity::getDataDate, dataDate)
                        .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                        .set(MonthlyDataEntity::getUpdateTime, new Date())
                        .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_execute_远程文件不存在")
                        .set(MonthlyDataEntity::getDownloadStatus, 2)
                );
            }

            for (RemoteFileInfo file : files) {
                if (file == null) {
                    log.error("MonthlyDataZQTZJHJZHandler_remoteFileInfo_null");
                    continue;
                }
                if (!file.getFileName().contains(pureEndDate)) {
                    log.info("MonthlyDataZQTZJHJZHandler_remoteFileInfo_非数据日期文件:{}", file.getFileName());
                    continue;
                }
                String taFilePath = ensureTrailingSeparator(parentDir) + file.getFileName(),
                        remoteFilePath = StringUtil.concatSeparator(file.getRelativePath(), file.getFileName());
                log.info("MonthlyDataZQTZJHJZHandler_taFilePath_remoteFilePath:{},{}", taFilePath, remoteFilePath);
                try {
                    downloadRemoteFiles(taFilePath, remoteFilePath);
                } catch (Exception e) {
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getStep, "MonthlyDataHandler_downloadRemoteFiles_error")
                            .set(MonthlyDataEntity::getDownloadStatus, 2)
                    );
                    log.error("MonthlyDataHandler_downloadRemoteFiles_error,{},{},{}", taFilePath, remoteFilePath, e);
                    throw e;
                }
                monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                        .eq(MonthlyDataEntity::getDataDate, dataDate)
                        .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                        .set(MonthlyDataEntity::getUpdateTime, new Date())
                        .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_execute_rpa导出TA结束")
                );
                String fName = getFileNameNoExtension(file.getFileName());
                log.info("MonthlyDataZQTZJHJZHandler_fName:{}", fName);
                if (!fName.contains("债权投资计划净值-原表")) {
                    continue;
                }

                //调用rpa日志记录
                logCallRpaMonthlyDataMapper.insert(new LogCallRpaMonthlyDataEntity()
                        .setId(IdWorker.getIdStr())
                        .setDataDate(dataDate)
                        .setCreateTime(new Date())
                        .setFileName(fName)
                        .setFilePath(taFilePath)
                        .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                        .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                        .setUserId(userId)
                        .setUserName(userName)
                );

                String newFileName = MonthlyDataFileEnum.ZQJHJZ.getFileName().replaceAll("YYYYMMDD", pureEndDate),
                        zqFilePath = ensureTrailingSeparator(parentDir) + newFileName + ".xlsx";
                log.info("MonthlyDataZQTZJHJZHandler_zqFilePath:{}", zqFilePath);
                try {
                    copyDefaultFile("monthly-data/债权投资计划净值YYYYMMDD.xlsx", zqFilePath);
                    log.info("MonthlyDataZQTZJHJZHandler_copyDefaultFile:{}", zqFilePath);
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_execute_copyDefaultFile")
                    );
                } catch (Exception e) {
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_copyDefaultFile_error")
                            .set(MonthlyDataEntity::getDownloadStatus, 2)
                    );
                    log.error("MonthlyDataZQTZJHJZHandler_copyDefaultFile_error:{},{}", e, e.getMessage());
                    throw e;
                }
                try {
                    Integer error = copyDCTADataV1(taFilePath, zqFilePath);
                    log.info("MonthlyDataZQTZJHJZHandler_error:{}", error);
                    if (error == -1) {
                        monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                                .eq(MonthlyDataEntity::getDataDate, dataDate)
                                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                                .set(MonthlyDataEntity::getUpdateTime, new Date())
                                .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_未找到基金单位净值列")
                                .set(MonthlyDataEntity::getDownloadStatus, 2)
                        );
                        throw new Exception("未找到基金单位净值列");
                    }
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(error > 0, MonthlyDataEntity::getDataStatus, "WR")
                            .set(error == 0, MonthlyDataEntity::getDataStatus, "R")
                            .set(MonthlyDataEntity::getLocalFilePath, zqFilePath)
                            .set(MonthlyDataEntity::getStep, fName)
                            .set(MonthlyDataEntity::getDownloadStatus, 1)
                    );

                    //调用rpa日志记录
                    logCallRpaMonthlyDataMapper.insert(new LogCallRpaMonthlyDataEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setFileName(newFileName)
                            .setFilePath(zqFilePath)
                            .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                            .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                            .setUserId(userId)
                            .setUserName(userName)
                    );
                } catch (Exception e) {
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_copyDCTADataV1_error")
                            .set(MonthlyDataEntity::getDownloadStatus, 2)
                    );
                    log.error("MonthlyDataZQTZJHJZHandler_copyDCTADataV1_error:{},{}", e, e.getMessage());
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error("MonthlyDataZQTZJHJZHandler_execute_error:{},{}", e, e.getMessage());
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {
        String dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                startDate = String.valueOf(param.get(RPA_START_DATE_NAME)),
                endDate = String.valueOf(param.get(RPA_END_DATE_NAME)),
                parentDir = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator,
                executor = String.valueOf(param.get(CronConstant.EXECUTOR)),
                pureEndDate = dateNorm2Pure(endDate);
        log.info("MonthlyDataZQTZJHJZHandler_onFail_dataDate_parentDir:{},{},{},{}", dataDate, parentDir, executor, pureEndDate);
        monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                .set(MonthlyDataEntity::getUpdateTime, new Date())
                .set(MonthlyDataEntity::getFileUpdateTime, null)
                .set(MonthlyDataEntity::getStep, "MonthlyDataZQTZJHJZHandler_onFail")
                .set(MonthlyDataEntity::getDownloadStatus, 2)
                .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                .set(MonthlyDataEntity::getMailSendTime, null)
                .set(MonthlyDataEntity::getLocalFilePath, null)
                .set(MonthlyDataEntity::getUserId, executor)
        );
    }

    /**
     * 复制导出TA净值数据
     *
     * @param sourcePath
     * @param targetPath
     * @throws Exception
     */
    public static void copyDCTAData(String sourcePath, String targetPath) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001);
        try (Workbook sourceWorkbook = WorkbookFactory.create(new FileInputStream(sourcePath))) {
            Workbook targetWorkbook = WorkbookFactory.create(new FileInputStream(targetPath));
            Sheet sourceSheet = sourceWorkbook.getSheet("Sheet1"),
                    targetSheet = targetWorkbook.getSheet("Sheet1");
            String[] headersToCopy = {"账套编号", "日期", "账套名称", "基金代码", "基金总份额", "基金总净值", "基金单位净值", "估值表确认"};
            Map<Integer, Integer> columnMapping = new HashMap<>();
            Map<CellStyle, CellStyle> styleCache = new HashMap<>();
            Row sourceHeaderRow = sourceSheet.getRow(0);
            if (sourceHeaderRow != null) {
                for (int srcCol = 0; srcCol < sourceHeaderRow.getLastCellNum(); srcCol++) {
                    Cell headerCell = sourceHeaderRow.getCell(srcCol);
                    if (headerCell != null) {
                        String headerValue = headerCell.getStringCellValue();
                        for (String targetHeader : headersToCopy) {
                            if (targetHeader.equals(headerValue)) {
                                columnMapping.put(srcCol, columnMapping.size());
                                break;
                            }
                        }
                    }
                }
            }
            for (int srcRowNum = 0; srcRowNum <= sourceSheet.getLastRowNum(); srcRowNum++) {
                Row sourceRow = sourceSheet.getRow(srcRowNum);
                if (sourceRow == null) continue;
                Row targetRow = targetSheet.createRow(srcRowNum);
                for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                    int srcCol = entry.getKey();
                    int targetCol = entry.getValue();
                    Cell sourceCell = sourceRow.getCell(srcCol);
                    if (sourceCell == null) continue;
                    Cell targetCell = targetRow.createCell(targetCol);
                    switch (sourceCell.getCellType()) {
                        case STRING:
                            targetCell.setCellValue(sourceCell.getStringCellValue());
                            break;
                        case NUMERIC:
                            targetCell.setCellValue(sourceCell.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            targetCell.setCellValue(sourceCell.getBooleanCellValue());
                            break;
                        case FORMULA:
                            targetCell.setCellFormula(sourceCell.getCellFormula());
                            break;
                        case BLANK:
                            targetCell.setBlank();
                            break;
                        default:
                            targetCell.setCellValue(sourceCell.toString());
                    }
                    if (sourceCell.getCellStyle() != null) {
                        CellStyle sourceStyle = sourceCell.getCellStyle();
                        CellStyle targetStyle = styleCache.get(sourceStyle);
                        if (targetStyle == null) {
                            targetStyle = targetWorkbook.createCellStyle();
                            targetStyle.cloneStyleFrom(sourceStyle);
                            styleCache.put(sourceStyle, targetStyle);
                        }
                        targetCell.setCellStyle(targetStyle);
                    }
                }
            }
            for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                int srcCol = entry.getKey();
                int targetCol = entry.getValue();
                targetSheet.setColumnWidth(targetCol, sourceSheet.getColumnWidth(srcCol));
            }
            try (FileOutputStream fos = new FileOutputStream(targetPath)) {
                targetWorkbook.write(fos);
            }
            sourceWorkbook.close();
            targetWorkbook.close();
        }
    }

    /**
     * 确保文件夹路径以 \或者/ 结尾
     *
     * @param pathStr
     * @return
     */
    private static String ensureTrailingSeparator(String pathStr) {
        String separator = Paths.get(pathStr).getFileSystem().getSeparator();
        if (!pathStr.endsWith(separator)) return pathStr + separator;
        return pathStr;
    }

    /**
     * 获取不包含后缀名的文件名称
     *
     * @param fileName a.xlsx
     * @return a
     */
    private static String getFileNameNoExtension(String fileName) {
        return fileName.replaceFirst("[.][^.]+$", "");
    }

    /**
     * 下载远程文件到本地
     *
     * @param targetFilePath 目标文件路径
     * @param remoteFilePath 远程文件路径
     * @throws IOException
     */
    public void downloadRemoteFiles(String targetFilePath, String remoteFilePath) throws IOException {
        byte[] bytes = smbService.download(remoteFilePath);
        Path file = Paths.get(targetFilePath);
        Files.createDirectories(file.getParent());
        Files.write(file, bytes);
    }

    /**
     * 拷贝默认的模板文件UL valuation_取数模板.xlsx
     *
     * @param targetFilePath
     * @throws Exception
     */
    public synchronized void copyDefaultFile(String classPath, String targetFilePath) throws Exception {
        Path targetPath = Paths.get(targetFilePath);
        Path parentDir = targetPath.getParent();
        if (parentDir != null) Files.createDirectories(parentDir);
        try (InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(classPath)) {
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 复制导出TA净值数据v1
     *
     * @param sourcePath 源文件
     * @param targetPath 目标文件
     * @return Integer
     * @throws Exception
     */
    public static Integer copyDCTADataV1(String sourcePath, String targetPath) throws Exception {
        int error = 0;
        ZipSecureFile.setMinInflateRatio(0.001);
        try (Workbook sourceWorkbook = WorkbookFactory.create(new FileInputStream(sourcePath))) {
            Workbook targetWorkbook = WorkbookFactory.create(new FileInputStream(targetPath));
            Sheet sourceSheet = sourceWorkbook.getSheet("Sheet1"),
                    targetSheet = targetWorkbook.getSheet("Sheet1");
            String[] headersToCopy = {"账套编号", "日期", "账套名称", "基金代码", "基金总份额", "基金总净值", "基金单位净值", "估值表确认"};
            Map<Integer, Integer> columnMapping = new HashMap<>();
            Map<String, Integer> headerToColumnMap = new HashMap<>();
            Map<CellStyle, CellStyle> styleCache = new HashMap<>();
            Row sourceHeaderRow = sourceSheet.getRow(0);
            if (sourceHeaderRow != null) {
                for (int srcCol = 0; srcCol < sourceHeaderRow.getLastCellNum(); srcCol++) {
                    Cell headerCell = sourceHeaderRow.getCell(srcCol);
                    if (headerCell != null) {
                        String headerValue = headerCell.getStringCellValue();
                        for (String targetHeader : headersToCopy) {
                            if (targetHeader.equals(headerValue)) {
                                columnMapping.put(srcCol, columnMapping.size());
                                headerToColumnMap.put(headerValue, srcCol);
                                break;
                            }
                        }
                    }
                }
            }
            Integer dwjzColumnIndex = headerToColumnMap.get("基金单位净值");
            if (dwjzColumnIndex == null) {
                log.error("未找到基金单位净值列");
                return -1;
            }
            for (int srcRowNum = 0; srcRowNum <= sourceSheet.getLastRowNum(); srcRowNum++) {
                Row sourceRow = sourceSheet.getRow(srcRowNum);
                if (sourceRow == null) continue;
                Row targetRow = targetSheet.createRow(srcRowNum);
                for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                    int srcCol = entry.getKey();
                    int targetCol = entry.getValue();
                    Cell sourceCell = sourceRow.getCell(srcCol);
                    if (sourceCell == null) continue;
                    Cell targetCell = targetRow.createCell(targetCol);
                    switch (sourceCell.getCellType()) {
                        case STRING:
                            targetCell.setCellValue(sourceCell.getStringCellValue());
                            break;
                        case NUMERIC:
                            targetCell.setCellValue(sourceCell.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            targetCell.setCellValue(sourceCell.getBooleanCellValue());
                            break;
                        case FORMULA:
                            targetCell.setCellFormula(sourceCell.getCellFormula());
                            break;
                        case BLANK:
                            targetCell.setBlank();
                            break;
                        default:
                            targetCell.setCellValue(sourceCell.toString());
                    }
                    if (dwjzColumnIndex != null && srcCol == dwjzColumnIndex && srcRowNum > 0) {
                        if (sourceCell.getCellType() == CellType.NUMERIC) {
                            double value = sourceCell.getNumericCellValue();
                            if (value <= 95.0) {
                                error++;
                            }
                        }
                        if (sourceCell.getCellType() == CellType.STRING
                                && StringUtils.isNotBlank(sourceCell.getStringCellValue())) {
                            double value = Double.parseDouble(sourceCell.getStringCellValue());
                            if (value <= 95.0) {
                                error++;
                            }
                        }
                    }
                    if (sourceCell.getCellStyle() != null) {
                        CellStyle sourceStyle = sourceCell.getCellStyle();
                        CellStyle targetStyle = styleCache.get(sourceStyle);
                        if (targetStyle == null) {
                            targetStyle = targetWorkbook.createCellStyle();
                            targetStyle.cloneStyleFrom(sourceStyle);
                            styleCache.put(sourceStyle, targetStyle);
                        }
                        targetCell.setCellStyle(targetStyle);
                    }
                }
            }
            for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                int srcCol = entry.getKey();
                int targetCol = entry.getValue();
                targetSheet.setColumnWidth(targetCol, sourceSheet.getColumnWidth(srcCol));
            }
            try (FileOutputStream fos = new FileOutputStream(targetPath)) {
                targetWorkbook.write(fos);
            }
            sourceWorkbook.close();
            targetWorkbook.close();
        } catch (Exception e) {
            log.error("MonthlyDataZQTZJHJZHandler_copyDCTADataV1_error:{},{}", e, e.getMessage());
            throw e;
        }
        return error;
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String dateNorm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }
}
