package cn.sdata.om.al.rpa;

import cn.hutool.core.date.DatePattern;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.monthlyData.LogCallRpaMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.MonthlyDataEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.mapper.monthlyData.LogCallRpaMonthlyDataMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.service.monthlyData.MonthlyDataService;
import cn.sdata.om.al.utils.*;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.sdata.om.al.constant.BaseConstant.RPA_END_DATE_NAME;
import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

/**
 * 月度数据-增值税
 *
 * <AUTHOR>
 * @Date 2025/4/7 14:52
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MonthlyDataZZSTZHandler implements BaseHandler {

    /**
     * 月度数据-本级目录名称
     */
    private final String activeDirName = "monthlyData";

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;

    // 样式缓存
    private static final Map<String, CellStyle> styleCache = new ConcurrentHashMap<>();
    // 字体缓存
    private static final Map<String, Font> fontCache = new ConcurrentHashMap<>();

    private final SMBService smbService;

    private final MonthlyDataService monthlyDataService;

    private final BaseCronLogService baseCronLogService;

    private final LogCallRpaMonthlyDataMapper logCallRpaMonthlyDataMapper;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        try {
            String dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                    startDate = String.valueOf(param.get(RPA_START_DATE_NAME)),
                    endDate = String.valueOf(param.get(RPA_END_DATE_NAME)),
                    parentDir = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator,
                    pureEndDate = dateNorm2Pure(endDate),
                    executor = String.valueOf(param.get(CronConstant.EXECUTOR));

            String userId = String.valueOf(param.get("userId")),
                    userName = String.valueOf(param.get("userName"));

            try {
                String logId = String.valueOf(param.get(RPAConstant.LOG_ID));
                LambdaUpdateWrapper<BaseCronLog> uw = new LambdaUpdateWrapper<>();
                uw.eq(BaseCronLog::getId, logId);
                uw.set(BaseCronLog::getExecuteMethod, "MANUAL");
                baseCronLogService.update(uw);
            } catch (Exception e) {
                log.error("MonthlyDataZZSTZHandler_update_executeMethod:{},{}", e, e.getMessage());
            }

            log.info("MonthlyDataZZSTZHandler_execute_dataDate:{},parentDir:{},pureEndDate:{},executor:{}", dataDate, parentDir, pureEndDate, executor);
            monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                    .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_execute_start")
                    .set(MonthlyDataEntity::getDownloadStatus, 3)
                    .set(MonthlyDataEntity::getDataStatus, "R")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, executor)
            );
            List<String> fNames = Lists.newArrayList();
            for (RemoteFileInfo file : files) {
                if (file == null) {
                    log.error("MonthlyDataZZSTZHandler_remoteFileInfo_null");
                    continue;
                }
                if (!file.getFileName().contains(pureEndDate)) {
                    log.info("MonthlyDataZZSTZHandler_remoteFileInfo_非数据日期文件:{}", file.getFileName());
                    continue;
                }
                String targetFilePath = ensureTrailingSeparator(parentDir) + file.getFileName(),
                        remoteFilePath = StringUtil.concatSeparator(file.getRelativePath(), file.getFileName()),
                        fName = getFileNameNoExtension(file.getFileName());
                log.info("MonthlyDataZZSTZHandler_targetFilePath:{},remoteFilePath:{},fName:{}", targetFilePath, remoteFilePath, fName);
                try {
                    downloadRemoteFiles(targetFilePath, remoteFilePath);
                } catch (Exception e) {
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getFileName, fName)
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_execute_downloadRemoteFiles_error")
                            .set(MonthlyDataEntity::getDownloadStatus, 2)
                    );
                    log.error("MonthlyDataZZSTZHandler_downloadRemoteFiles_error,{},{},{}", targetFilePath, remoteFilePath, e);
                    throw e;
                }

                if (MonthlyDataFileEnum.JRZRTZ_ZH.getFileName().equals(pureToY4m2d2(fName, pureEndDate))
                        || MonthlyDataFileEnum.DZTKMYE_ZH.getFileName().equals(pureToY4m2d2(fName, pureEndDate))
                        || MonthlyDataFileEnum.DZTKMYE_ZQJH.getFileName().equals(pureToY4m2d2(fName, pureEndDate))) {
                    log.info("MonthlyDataZZSTZHandler_fName_update:{},{}", fName, pureToY4m2d2(fName, pureEndDate));
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getFileName, fName)
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                            .set(MonthlyDataEntity::getLocalFilePath, targetFilePath)
                            .set(MonthlyDataEntity::getStep, fName)
                            .set(MonthlyDataEntity::getDownloadStatus, 1)
                    );
                    fNames.add(fName);

                    //调用rpa日志记录
                    logCallRpaMonthlyDataMapper.insert(new LogCallRpaMonthlyDataEntity()
                            .setId(IdWorker.getIdStr())
                            .setDataDate(dataDate)
                            .setCreateTime(new Date())
                            .setFileName(fName)
                            .setFilePath(targetFilePath)
                            .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                            .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                            .setUserId(userId)
                            .setUserName(userName)
                    );
                }
            }
            log.info("MonthlyDataZZSTZHandler_fNames:{}", fNames);
            //汇总表处理
            String hzFileNameNoExt = y4m2d2ToPure(MonthlyDataFileEnum.ZZSBB_CPHZ.getFileName(), pureEndDate),
                    hzFileName = hzFileNameNoExt + ".xlsx",
                    hzFilePath = ensureTrailingSeparator(parentDir) + hzFileName;
            log.info("MonthlyDataZZSTZHandler_hzFilePath:{}", hzFilePath);
            try {
                copyDefaultFile("monthly-data/增值税相关报表模板-产品汇总.xlsx", hzFilePath);
                log.info("MonthlyDataZZSTZHandler_hzFilePath_copyDefaultFile:{}", hzFilePath);
            } catch (Exception e) {
                monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                        .eq(MonthlyDataEntity::getDataDate, dataDate)
                        .eq(MonthlyDataEntity::getFileName, hzFileNameNoExt)
                        .set(MonthlyDataEntity::getUpdateTime, new Date())
                        .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                        .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_hzFilePath_copyDefaultFile_error")
                        .set(MonthlyDataEntity::getDownloadStatus, 2)
                );
                log.error("MonthlyDataZZSTZHandler_copyDefaultFile_error:{},{}", e, e.getMessage());
                throw e;
            }
            List<MonthlyDataEntity> list = monthlyDataService.list(Wrappers.lambdaQuery(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .in(MonthlyDataEntity::getFileName, fNames));
            for (MonthlyDataEntity entity : list) {
                try {
                    String sourceFilePath = entity.getLocalFilePath(),
                            targetSheetName = getFileNameNoExtension(Paths.get(sourceFilePath).getFileName().toString()).replaceAll(("-" + pureEndDate), "").trim();
                    log.info("MonthlyDataZZSTZHandler_sourceFilePath_targetSheetName:{},{}", sourceFilePath, targetSheetName);

                    if (MonthlyDataFileEnum.DZTKMYE_ZQJH.getFileName().contains(targetSheetName)) {
                        log.info("MonthlyDataDZTZQJHCopyUtil_copyByPoi:{},{}", sourceFilePath, targetSheetName);
                        //MonthlyDataDZTExcelCopyUtil.copyByPoi(sourceFilePath, "Sheet1", hzFilePath, targetSheetName);
                        MonthlyDataDZTZQJHCopyUtil.copyByPoi(sourceFilePath, "Sheet1", hzFilePath, targetSheetName);

                    } else if (MonthlyDataFileEnum.DZTKMYE_ZH.getFileName().contains(targetSheetName)) {
                        log.info("MonthlyDataDZTZHCopyUtil_copyByPoi:{},{}", sourceFilePath, targetSheetName);
                        MonthlyDataDZTZHCopyUtil.copyByPoi(sourceFilePath, "Sheet1", hzFilePath, targetSheetName);

                    } else if (MonthlyDataFileEnum.JRZRTZ_ZH.getFileName().contains(targetSheetName)) {

                        log.info("MonthlyDataJRSPZRTZExcelCopyUtil_copyJRZRTZZHData:{},{}", sourceFilePath, targetSheetName);
                        //copyJRZRTZZHData(sourceFilePath, hzFilePath, targetSheetName);
                        MonthlyDataJRSPZRTZExcelCopyUtil.copyJRZRTZZHData(sourceFilePath, hzFilePath, targetSheetName);

                    }
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getFileName, y4m2d2ToPure(MonthlyDataFileEnum.ZZSBB_CPHZ.getFileName(), pureEndDate))
                            .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getLocalFilePath, hzFilePath)
                            .set(MonthlyDataEntity::getStep, targetSheetName)
                    );

                } catch (Exception e) {
                    monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getFileName, y4m2d2ToPure(MonthlyDataFileEnum.ZZSBB_CPHZ.getFileName(), pureEndDate))
                            .set(MonthlyDataEntity::getUpdateTime, new Date())
                            .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                            .set(MonthlyDataEntity::getLocalFilePath, hzFilePath)
                            .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_copy_error")
                            .set(MonthlyDataEntity::getDownloadStatus, 2)
                    );
                    log.error("MonthlyDataZZSTZHandler_copy_error:{},{},{}", entity.getLocalFilePath(), e, e.getMessage());
                    throw e;
                }
            }
            monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getFileName, y4m2d2ToPure(MonthlyDataFileEnum.ZZSBB_CPHZ.getFileName(), pureEndDate))
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                    .set(MonthlyDataEntity::getDownloadStatus, 1)
                    .set(MonthlyDataEntity::getLocalFilePath, hzFilePath)
                    .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_hzFilePath_ZZSBB_CPHZ_end")
            );
            //调用rpa日志记录
            logCallRpaMonthlyDataMapper.insert(new LogCallRpaMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setCreateTime(new Date())
                    .setFileName(y4m2d2ToPure(MonthlyDataFileEnum.ZZSBB_CPHZ.getFileName(), pureEndDate))
                    .setFilePath(hzFilePath)
                    .setBaseCornLogId(String.valueOf(param.get(RPAConstant.LOG_ID)))
                    .setRpaExecLogId(String.valueOf(param.get("rpaExecLogId")))
                    .setUserId(userId)
                    .setUserName(userName)
            );
        } catch (Exception e) {
            log.error("MonthlyDataZZSTZHandler_execute_error:{},{}", e, e.getMessage());
        }
    }

    @Override
    public void onFail(Map<String, Object> param) {
        String dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME)),
                startDate = String.valueOf(param.get(RPA_START_DATE_NAME)),
                endDate = String.valueOf(param.get(RPA_END_DATE_NAME)),
                parentDir = ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator,
                pureEndDate = dateNorm2Pure(endDate),
                executor = String.valueOf(param.get(CronConstant.EXECUTOR));
        log.info("MonthlyDataZZSTZHandler_onFail_dataDate:{},parentDir:{},pureEndDate:{},executor:{}", dataDate, parentDir, pureEndDate, executor);
        monthlyDataService.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                .set(MonthlyDataEntity::getUpdateTime, new Date())
                .set(MonthlyDataEntity::getFileUpdateTime, null)
                .set(MonthlyDataEntity::getStep, "MonthlyDataZZSTZHandler_onFail")
                .set(MonthlyDataEntity::getDownloadStatus, 2)
                .set(MonthlyDataEntity::getDataStatus, null)
                .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                .set(MonthlyDataEntity::getMailSendTime, null)
                .set(MonthlyDataEntity::getLocalFilePath, null)
                .set(MonthlyDataEntity::getUserId, executor)
        );
    }

    public static String y4m2d2ToPure(String name, String pureEndDate) {
        return name.replaceAll("YYYYMMDD", pureEndDate);
    }

    public static String pureToY4m2d2(String name, String pureEndDate) {
        return name.replaceAll(pureEndDate, "YYYYMMDD");
    }

    /**
     * 拷贝默认的模板文件UL valuation_取数模板.xlsx
     *
     * @param targetFilePath
     * @throws Exception
     */
    public synchronized void copyDefaultFile(String classPath, String targetFilePath) throws Exception {
        Path targetPath = Paths.get(targetFilePath);
        Path parentDir = targetPath.getParent();
        if (parentDir != null) Files.createDirectories(parentDir);
        try (InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(classPath)) {
            assert inputStream != null;
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 下载远程文件到本地
     *
     * @param targetFilePath 目标文件路径
     * @param remoteFilePath 远程文件路径
     * @throws IOException
     */
    public void downloadRemoteFiles(String targetFilePath, String remoteFilePath) throws IOException {
        byte[] bytes = smbService.download(remoteFilePath);
        Path file = Paths.get(targetFilePath);
        Files.createDirectories(file.getParent());
        Files.write(file, bytes);
    }

    /**
     * 确保文件夹路径以 \或者/ 结尾
     *
     * @param pathStr
     * @return
     */
    private static String ensureTrailingSeparator(String pathStr) {
        String separator = Paths.get(pathStr).getFileSystem().getSeparator();
        if (!pathStr.endsWith(separator)) return pathStr + separator;
        return pathStr;
    }

    /**
     * 获取不包含后缀名的文件名称
     *
     * @param fileName a.xlsx
     * @return a
     */
    private static String getFileNameNoExtension(String fileName) {
        return fileName.replaceFirst("[.][^.]+$", "");
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    public String getFileExt(String fileName) {
        return com.google.common.io.Files.getFileExtension(fileName);
    }

    /*public static void main(String[] args) {
        try {
            copyJRZRTZZHData(
                    "C:\\Users\\<USER>\\Desktop\\test\\金融商品转让台账表-组合-20241231.xlsx",
                    "C:\\Users\\<USER>\\Desktop\\test\\增值税相关报表-产品汇总-20241231.xlsx",
                    "金融商品转让台账表-组合");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    /**
     * 复制 金融商品转让台账表-组合->汇总表
     *
     * @param sourcePath
     * @param targetPath
     * @throws IOException
     */
    public static void copyJRZRTZZHData(String sourcePath, String targetPath, String targetSheetName) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001);
        String[] targetHeaders = {"账套", "产品代码", "日期", "当日税基发生额", "当日税基余额", "当日抵扣后税基余额", "计税标志"};
        try {
            Workbook sourceWorkbook = WorkbookFactory.create(new FileInputStream(sourcePath));
            Workbook targetWorkbook = WorkbookFactory.create(new FileInputStream(targetPath));

            // 清空缓存（防止不同工作簿间的样式冲突）
            styleCache.clear();
            fontCache.clear();

            Sheet sourceSheet = sourceWorkbook.getSheet("Sheet1");
            Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);

            if (targetSheet != null) {
                int sheetIndex = targetWorkbook.getSheetIndex(targetSheet);
                targetWorkbook.removeSheetAt(sheetIndex);
                targetWorkbook.createSheet(targetSheetName);
                targetSheet = targetWorkbook.getSheet(targetSheetName);
            } else {
                targetSheet = targetWorkbook.createSheet(targetSheetName);
            }

            // 1. 获取列映射关系（基于第三行表头）
            Row headerRow = sourceSheet.getRow(2); // 第三行是表头
            Map<String, Integer> headerMap = new HashMap<>();
            Map<Integer, Integer> columnMapping = new HashMap<>();

            // 建立表头到列索引的映射
            for (Cell cell : headerRow) {
                String header = cell.getStringCellValue().trim().toLowerCase();
                headerMap.put(header, cell.getColumnIndex());
            }

            // 确定需要复制的列索引和映射关系
            for (int i = 0; i < targetHeaders.length; i++) {
                Integer sourceColIndex = headerMap.get(targetHeaders[i].toLowerCase());
                if (sourceColIndex == null) {
                    throw new Exception("未找到标题列: " + targetHeaders[i]);
                }
                columnMapping.put(sourceColIndex, i); // 目标列索引从0(A)到6(G)
            }

            // 2. 处理第1-2行的合并居中（复制到A-G列）
            copyAndAdjustMergedRows(sourceSheet, targetSheet, 0, 1, columnMapping, targetWorkbook, sourceWorkbook);

            // 3. 复制表头行（第三行）
            Row targetHeaderRow = targetSheet.createRow(2);
            for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                Cell sourceCell = headerRow.getCell(entry.getKey());
                Cell targetCell = targetHeaderRow.createCell(entry.getValue());
                copyCellWithCachedStyle(sourceCell, targetCell, targetWorkbook, sourceWorkbook);
            }

            // 4. 复制数据行（从第四行开始）
            for (int i = 3; i <= sourceSheet.getLastRowNum(); i++) {
                Row sourceRow = sourceSheet.getRow(i);
                if (sourceRow == null) continue;

                Row targetRow = targetSheet.createRow(i);
                for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                    Cell sourceCell = sourceRow.getCell(entry.getKey());
                    if (sourceCell != null) {
                        Cell targetCell = targetRow.createCell(entry.getValue());
                        copyCellWithCachedStyle(sourceCell, targetCell, targetWorkbook, sourceWorkbook);
                    }
                }
            }

            addSignatureToSheet(targetSheet, targetWorkbook);

            // 添加自动调整列宽
            autoSizeColumnsEnhanced(targetSheet);

            // 5. 保存目标工作簿
            try (FileOutputStream fos = new FileOutputStream(targetPath)) {
                targetWorkbook.write(fos);
            }

            sourceWorkbook.close();
            targetWorkbook.close();
        } catch (Exception e) {
            log.error("MonthlyDataZZSTZHandler_copyJRZRTZZHData_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    public static void autoSizeColumnsEnhanced(Sheet sheet) {
        // 获取最大列数（考虑所有行）
        int maxColumns = 0;
        for (Row row : sheet) {
            if (row != null && row.getLastCellNum() > maxColumns) {
                maxColumns = row.getLastCellNum();
            }
        }

        // 自动调整每列宽度
        for (int i = 0; i < maxColumns; i++) {
            sheet.autoSizeColumn(i);

            // 处理合并单元格的情况
            for (int j = 0; j < sheet.getNumMergedRegions(); j++) {
                CellRangeAddress merged = sheet.getMergedRegion(j);
                if (merged.getFirstColumn() == i || merged.getLastColumn() == i) {
                    // 合并单元格的列可能需要更宽
                    int currentWidth = sheet.getColumnWidth(i);
                    sheet.setColumnWidth(i, (int) (currentWidth * 1.2)); // 增加20%宽度
                    break;
                }
            }

            // 设置最小和最大宽度限制
            int currentWidth = sheet.getColumnWidth(i);
            int minWidth = 256 * 5;  // 5个字符宽度
            int maxWidth = 256 * 50;  // 50个字符宽度

            if (currentWidth < minWidth) {
                sheet.setColumnWidth(i, minWidth);
            } else if (currentWidth > maxWidth) {
                sheet.setColumnWidth(i, maxWidth);
            }
        }
    }

    /**
     * 在Sheet的最后一行+2行处添加签名信息
     *
     * @param sheet    目标工作表
     * @param workbook 目标工作簿（用于获取样式）
     */
    private static void addSignatureToSheet(Sheet sheet, Workbook workbook) {
        // 获取最后一行索引（注意：getLastRowNum()返回的是0-based索引）
        int lastRowNum = sheet.getLastRowNum();
        // 在最后一行+4行处添加内容
        int signatureRowNum = lastRowNum + 2;

        // 创建行（如果不存在）
        Row signatureRow = sheet.getRow(signatureRowNum);
        if (signatureRow == null) {
            signatureRow = sheet.createRow(signatureRowNum);
        }

        // 在B列(索引1)设置"制表："
        Cell makerCell = signatureRow.createCell(1);
        makerCell.setCellValue("制表：");

        // 在F列(索引5)设置"复核："
        Cell checkerCell = signatureRow.createCell(5);
        checkerCell.setCellValue("复核：");

        Row signatureRow3 = sheet.createRow(lastRowNum + 3);
        Row signatureRow4 = sheet.createRow(lastRowNum + 4);
        Row signatureRow5 = sheet.createRow(lastRowNum + 5);

    }

    private static void copyAndAdjustMergedRows(Sheet sourceSheet,
                                                Sheet targetSheet,
                                                int startRow,
                                                int endRow,
                                                Map<Integer, Integer> columnMapping,
                                                Workbook targetWorkbook,
                                                Workbook sourceWorkbook) {
        // 1. 先复制单元格内容和样式
        for (int rowNum = startRow; rowNum <= endRow; rowNum++) {
            Row sourceRow = sourceSheet.getRow(rowNum);
            if (sourceRow == null) continue;

            Row targetRow = targetSheet.createRow(rowNum);
            for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                Cell sourceCell = sourceRow.getCell(entry.getKey());
                if (sourceCell != null) {
                    Cell targetCell = targetRow.createCell(entry.getValue());
                    copyCellWithCachedStyle(sourceCell, targetCell, targetWorkbook, sourceWorkbook);
                }
            }
        }

        // 2. 创建新的合并区域（A1-G2）
        CellRangeAddress newMergedRegion = new CellRangeAddress(0, 1, 0, 6);
        targetSheet.addMergedRegion(newMergedRegion);

        // 3. 设置合并单元格的值和样式
        Row targetFirstRow = targetSheet.getRow(0);
        Cell mergedCell = targetFirstRow.getCell(0);

        // 获取合并区域的值
        String mergedValue = "";
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            if (mergedRegion.getFirstRow() == 0 && mergedRegion.getLastRow() <= 1) {
                Row firstRow = sourceSheet.getRow(0);
                if (firstRow != null) {
                    Cell firstCell = firstRow.getCell(mergedRegion.getFirstColumn());
                    if (firstCell != null) {
                        mergedValue = getCellValueAsString(firstCell);
                        break;
                    }
                }
            }
        }

        mergedCell.setCellValue(mergedValue);

        // 创建居中样式（使用目标工作簿）
        CellStyle centeredStyle = targetWorkbook.createCellStyle();
        centeredStyle.setAlignment(HorizontalAlignment.CENTER);
        centeredStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = targetWorkbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        centeredStyle.setFont(font);

        mergedCell.setCellStyle(centeredStyle);
    }

    private static void copyCellWithCachedStyle(Cell sourceCell, Cell targetCell, Workbook targetWorkbook, Workbook sourceWorkbook) {
        // 1. 复制单元格值
        copyCellValue(sourceCell, targetCell);

        // 2. 获取源样式
        CellStyle sourceStyle = sourceCell.getCellStyle();

        // 3. 生成样式唯一键
        String styleKey = "style_" + sourceStyle.getIndex();

        // 4. 从缓存获取或创建新样式
        CellStyle targetStyle = styleCache.computeIfAbsent(styleKey, k -> {
            // 创建新样式（属于目标工作簿）
            CellStyle newStyle = targetWorkbook.createCellStyle();

            // 复制样式属性
            newStyle.cloneStyleFrom(sourceStyle);

            // 处理字体（必须使用目标工作簿的字体）
            Font sourceFont = sourceWorkbook.getFontAt(sourceStyle.getFontIndex());
            String fontKey = "font_" + sourceFont.getIndex();

            Font targetFont = fontCache.computeIfAbsent(fontKey, fk -> {
                Font newFont = targetWorkbook.createFont();
                newFont.setBold(sourceFont.getBold());
                newFont.setItalic(sourceFont.getItalic());
                newFont.setUnderline(sourceFont.getUnderline());
                newFont.setFontName(sourceFont.getFontName());
                newFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
                newFont.setColor(sourceFont.getColor());
                return newFont;
            });

            newStyle.setFont(targetFont);
            return newStyle;
        });

        // 5. 应用样式
        targetCell.setCellStyle(targetStyle);
    }

    private static String generateStyleKey(CellStyle style) {
        // 生成样式唯一标识
        return style.getAlignment().toString() +
                style.getVerticalAlignment().toString() +
                style.getFillForegroundColor() +
                style.getFillPattern().toString() +
                style.getFontIndex();
    }

    private static String generateFontKey(Font font) {
        // 生成字体唯一标识
        return font.getBold() + "|" +
                font.getItalic() + "|" +
                font.getFontHeightInPoints() + "|" +
                font.getFontName();
    }

    private static CellStyle getCachedStyle(Workbook workbook, String key, StyleCreator creator) {
        return styleCache.computeIfAbsent(key, k -> creator.createStyle(workbook));
    }

    private static Font getCachedFont(Workbook workbook, String key, FontCreator creator) {
        return fontCache.computeIfAbsent(key, k -> creator.createFont(workbook));
    }

    private static void copyCellStyle(CellStyle sourceStyle, CellStyle targetStyle, Workbook targetWorkbook, Workbook sourceWorkbook) {
        targetStyle.cloneStyleFrom(sourceStyle);

        // 复制字体（使用缓存）
        Font sourceFont = sourceWorkbook.getFontAt(sourceStyle.getFontIndex());
        String fontKey = generateFontKey(sourceFont);

        Font targetFont = getCachedFont(targetWorkbook, fontKey, (wb) -> {
            Font newFont = wb.createFont();
            copyFont(sourceFont, newFont);
            return newFont;
        });

        targetStyle.setFont(targetFont);
    }

    private static void copyFont(Font sourceFont, Font targetFont) {
        targetFont.setBold(sourceFont.getBold());
        targetFont.setItalic(sourceFont.getItalic());
        targetFont.setUnderline(sourceFont.getUnderline());
        targetFont.setFontName(sourceFont.getFontName());
        targetFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
        targetFont.setColor(sourceFont.getColor());
    }

    private static void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(sourceCell)) {
                    targetCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case BLANK:
                targetCell.setBlank();
                break;
            case STRING:
            default:
                targetCell.setCellValue(sourceCell.getStringCellValue());
        }
    }

    private static String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            case STRING:
            default:
                return cell.getStringCellValue();
        }
    }

    @FunctionalInterface
    private interface StyleCreator {
        CellStyle createStyle(Workbook workbook);
    }

    @FunctionalInterface
    private interface FontCreator {
        Font createFont(Workbook workbook);
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String dateNorm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }
}
