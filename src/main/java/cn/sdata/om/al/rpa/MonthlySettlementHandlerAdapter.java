package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RemoteFileInfo;

import java.util.List;
import java.util.Map;

public abstract class MonthlySettlementHandlerAdapter implements BaseHandler {

    public abstract void execute(Map<String, Object> param, FlowList flowList, String logId);

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {

    }

    @Override
    public void onFail(Map<String, Object> param) {

    }
}
