package cn.sdata.om.al.rpa;

import cn.sdata.om.al.entity.NetValueDisclosure;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.ValuationTableRecords;
import cn.sdata.om.al.enums.ValuationRPAStatus;
import cn.sdata.om.al.service.NetValueDisclosureService;
import cn.sdata.om.al.service.ValuationTableRecordsService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.sdata.om.al.constant.JobConstant.*;

@Component
@AllArgsConstructor
@Slf4j
public class SZTHandler implements BaseHandler{

    private final NetValueDisclosureService netValueDisclosureService;
    private final ValuationTableRecordsService valuationTableRecordsService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        log.info("处理深证通状态");
        List<ValuationTableRecords> valuationTableRecordsList = new ArrayList<>();
        Map<String, NetValueDisclosure> netValueMap = getNetValueMap(param);
        Collection<NetValueDisclosure> values = netValueMap.values();
        values.forEach(netValueDisclosure -> netValueDisclosure.setSztStatus(ValuationRPAStatus.COMPLETED.name()));
        // 使用增量更新替代全量更新，避免并发冲突
        updateSztStatusBatch(values);
        valuationTableRecordsService.saveBatch(valuationTableRecordsList);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void onFail(Map<String, Object> param) {
        Objects.requireNonNull(param, "RPA参数不得为空");
        List<NetValueDisclosure> list = (List<NetValueDisclosure>) param.get(NET_VALUE_DISCLOSURE_LIST);
        list.forEach(netValueDisclosure -> netValueDisclosure.setSztStatus(ValuationRPAStatus.COMPLETED.name()));
        // 使用增量更新替代全量更新，避免并发冲突
        updateSztStatusBatch(list);
    }

    /**
     * 增量更新深证通状态，只更新必要字段避免并发冲突
     */
    private void updateSztStatusBatch(Collection<NetValueDisclosure> values) {
        for (NetValueDisclosure disclosure : values) {
            try {
                LambdaUpdateWrapper<NetValueDisclosure> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(NetValueDisclosure::getId, disclosure.getId());

                // 只更新深证通状态字段
                if (disclosure.getSztStatus() != null) {
                    updateWrapper.set(NetValueDisclosure::getSztStatus, disclosure.getSztStatus());
                }

                netValueDisclosureService.update(updateWrapper);
                log.debug("增量更新深证通状态，产品ID: {}", disclosure.getProductId());
            } catch (Exception e) {
                log.error("增量更新深证通状态失败，产品ID: {} 失败: {}", disclosure.getProductId(), e.getMessage(), e);
            }
        }
    }

}
