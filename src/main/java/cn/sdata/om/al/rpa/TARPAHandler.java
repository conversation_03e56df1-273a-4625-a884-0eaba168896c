package cn.sdata.om.al.rpa;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.RPAConstant;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.enums.MailContactsType;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.service.RemoteFileInfoService;
import cn.sdata.om.al.service.cashClearReport.CashClearReportService;
import cn.sdata.om.al.service.mail.MailInfoService;
import cn.sdata.om.al.utils.LogCCRUtil;
import cn.sdata.om.al.utils.StringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
@Component
@AllArgsConstructor
public class TARPAHandler implements BaseHandler {

    private final RemoteFileInfoService remoteFileInfoService;

    private final AccountInformationService accountInformationService;

    private final MailInfoService mailInfoService;

    private final FuncDataToMailService funcDataToMailService;

    private final CashClearReportService clearReportService;

    @Override
    public void execute(Map<String, Object> param, List<RemoteFileInfo> files) {
        if (param == null) {
            return;
        }
        Cron cron = (Cron) param.get(RPAConstant.CRON);
        String logId = (String) param.get(RPAConstant.LOG_ID);
        String startDateStr = (String) param.get(BaseConstant.START_DATE_NAME);
        String endDateStr = (String) param.get(BaseConstant.END_DATE_NAME);
        String dataDate = String.valueOf(param.get(BaseConstant.SYSTEM_DATE_NAME));
        dealFileNormal(cron, logId, files, startDateStr, endDateStr, dataDate);
    }

    @Override
    public void onFail(Map<String, Object> param) {

    }

    private void dealFileNormal(Cron cron, String logId, List<RemoteFileInfo> files, String startDate, String endDate, String dataDate) {
        if (cron == null || logId == null || files == null) {
            return;
        }
        files.forEach(remoteFileInfo -> remoteFileInfo.setLogId(logId));
        remoteFileInfoService.saveBatch(files);
        log.info("RPA执行成功, 找到文件:{}", files);
        // 完成rpa
        Integer flowId = cron.getFlowId();
        if (flowId == 1) {
            // 记录rpa完成日志
            JSONObject param = new JSONObject();
            param.put("rpaLogId", logId);
            param.put("files", files);
            param.put("endTime", DateUtil.now());
            LogCCRUtil.postRpaLog(param);
        }
        Integer autoMail = cron.getAutoMail();
        if (autoMail != null && autoMail == 1) {
            String contactType = cron.getContactType();
            MailContactsType mailContactsType = MailContactsType.valueOf(contactType);
            Map<String, RemoteFileInfo> remoteFileInfoMap;
            if (mailContactsType == MailContactsType.CUSTODIAN_BANK) {
                remoteFileInfoMap = formatCustodianFiles(files);
            } else {
                remoteFileInfoMap = formatDefaultFiles(files);
            }
            String paramDate = new SimpleDateFormat(BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN).format(Calendar.getInstance().getTime());
            List<SendMailInfo> sendMailInfos = mailInfoService.composeByConfig(cron, paramDate, remoteFileInfoMap.keySet(), remoteFileInfoMap);
            sendMailInfos = mailInfoService.doSendMailInfo(sendMailInfos);
            try {
                if (StringUtils.isNotBlank(dataDate)) {
                    //邮件扩展数据入库
                    List<FuncDataToMailEntity> funcDataToMailEntities = sendMailInfos.stream().map(sendMailInfo -> new FuncDataToMailEntity()
                                    .setId(IdUtil.getSnowflakeNextIdStr())
                                    .setMailId(sendMailInfo.getMailId())
                                    .setMailSendLogId(sendMailInfo.getMailSendLogId())
                                    .setDataDate(dataDate)
                                    .setMailStatus(sendMailInfo.getStatus())
                                    .setFuncType(cron.getJobName())
                                    .setDataType(cron.getJobName())
                                    .setCreateTime(new Date())
                                    .setProductId(String.join(",", sendMailInfo.getProductIds())))
                            .collect(Collectors.toList());
                    log.info("TARPAHandler_dealFileNormal_funcDataToMailEntities:{}", funcDataToMailEntities);
                    if (CollUtil.isNotEmpty(funcDataToMailEntities)) {
                        funcDataToMailService.saveBatch(funcDataToMailEntities);
                    }
                    initDefaultData(dataDate, files, funcDataToMailEntities);
                }
            } catch (Exception e) {
                log.error("TARPAHandler_dealFileNormal_error:{},{}", e, e.getMessage());
            }
            if (flowId == 1) {
                for (SendMailInfo mailInfo : sendMailInfos) {
                    JSONObject params = new JSONObject();
                    params.put("rpaLogId", logId);
                    params.put("logId", IdUtil.getSnowflakeNextIdStr());
                    params.put("beginTime", DateUtil.now());
                    params.put("username", DEFAULT_USERNAME);
                    params.put("dataDate", dataDate);
                    params.put("type", "AUTO");
                    params.put("params", JSON.toJSONString(remoteFileInfoMap));
                    params.put("endTime", DateUtil.now());
                    params.put("status", CommonStatus.SUCCESS.name());
                    params.put("mailIds", mailInfo.getMailId());
                    params.put("sendStatus", mailInfo.getStatus());
                    LogCCRUtil.preSendMailLog(params);
                }
            }
        }
    }

    /**
     * 初始化资金清算报表数据
     *
     * @param dataDate yyyy-MM-dd
     * @param files
     * @throws Exception
     */
    public void initDefaultData(String dataDate, List<RemoteFileInfo> files, List<FuncDataToMailEntity> funcDataToMailEntities) throws Exception {
        try {
            if (LocalDate.parse(dataDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).isAfter(LocalDate.now()))
                throw new Exception("数据日期大于当前日期");

            String dataDateStr = LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
            log.info("TARPAHandler_execute_dataDateStr:{}", dataDateStr);

            //eg:基金的资金清算报表(基金或销售商)_AZ0003_安联资产安享1号资产管理产品_2024年03月05日--2024年03月05日.pdf
            List<String> productCodes = Lists.newArrayList();
            for (RemoteFileInfo remoteFile : files) {
                if (remoteFile == null || !remoteFile.getFileName().contains("资金清算报表"))
                    continue;
                String[] arr = remoteFile.getFileName().split("_");
                log.info("TARPAHandler_execute_remoteFile1_arr:{}", arr);
                if (arr.length < 4)
                    continue;
                String[] dataArr = arr[3].replaceFirst("[.][^.]+$", "").split("--");
                String dataArr0 = dataArr[0], dataArr1 = dataArr[1];
                log.info("TARPAHandler_execute_remoteFile1_dataArr0_dataArr1:{},{}", dataArr0, dataArr1);
                if (!dataArr0.equals(dataDateStr) || !dataArr0.equals(dataArr1)) {
                    continue;
                }
                String productCode = arr[1];
                productCodes.add(productCode);
            }
            log.info("TARPAHandler_execute_productCodes:{}", productCodes);

            //通过从文件名中解析出来的账套号和账套名称查询账套基本信息map code->productId
            Map<String, String> accountCode2IdMap = accountInformationService
                    .list(Wrappers.lambdaQuery(AccountInformation.class)
                            .in(AccountInformation::getProductCode, productCodes))
                    .stream()
                    .collect(Collectors.toMap(AccountInformation::getProductCode, AccountInformation::getId, (n1, n2) -> n1));
            log.info("TARPAHandler_execute_accountCode2IdMap:{}", accountCode2IdMap);

            //邮件扩展数据 productId->FuncMail
            Map<String, FuncDataToMailEntity> productId2FuncMailMap = funcDataToMailEntities.stream()
                    .collect(Collectors.toMap(FuncDataToMailEntity::getProductId, Function.identity(), (n1, n2) -> n1));

            //清算列表list
            List<CashClearReportEntity> clearReportEntities = Lists.newArrayList();
            for (RemoteFileInfo remoteFile : files) {
                //eg:基金的资金清算报表(基金或销售商)_AZ0003_安联资产安享1号资产管理产品_2024年03月05日--2024年03月05日.pdf
                if (remoteFile == null || !remoteFile.getFileName().contains("资金清算报表"))
                    continue;
                String[] arr = remoteFile.getFileName().split("_");
                log.info("TARPAHandler_execute_remoteFile2_arr:{}", arr);

                String productCode = arr[1], productName = arr[2];
                log.info("TARPAHandler_execute_remoteFile2_productCode_productName:{},{}", productCode, productName);
                if (accountCode2IdMap.containsKey(productCode)) {
                    CashClearReportEntity clearEntity = new CashClearReportEntity();
                    String productId = accountCode2IdMap.get(productCode);
                    clearEntity.setId(IdUtil.getSnowflakeNextIdStr())
                            .setProductId(productId)
                            .setProductCode(productCode)
                            .setProductName(productName)
                            .setFileName(remoteFile.getFileName())
                            .setDataDate(dataDate)
                            .setFileUpdateTime(new Date())
                            .setCreateTime(new Date())
                            .setUserId("TaRpa")
                            .setMailSendStatus(MailStatus.UNSENT.name())
                            .setRemoteFileId(remoteFile.getId());
                    if (CollUtil.isNotEmpty(productId2FuncMailMap)) {
                        for (Map.Entry<String, FuncDataToMailEntity> funcData : productId2FuncMailMap.entrySet()) {
                            if (funcData.getKey().contains(productId)) {
                                clearEntity.setMailSendStatus(funcData.getValue().getMailStatus());
                                clearEntity.setMailSendTime(funcData.getValue().getCreateTime());
                                break;
                            }
                        }
                    }

                    clearReportEntities.add(clearEntity);
                    log.info("TARPAHandler_execute_clearEntity:{}", clearEntity);
                }
            }
            log.info("TARPAHandler_execute_clearReportEntities:{}", clearReportEntities);
            if (CollUtil.isNotEmpty(clearReportEntities)) {
                clearReportService.remove(Wrappers.lambdaQuery(CashClearReportEntity.class).eq(CashClearReportEntity::getDataDate, dataDate));
                clearReportService.saveBatch(clearReportEntities);
            }

        } catch (Exception e) {
            log.error("TARPAHandler_initDefaultData_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    private Map<String, RemoteFileInfo> formatDefaultFiles(List<RemoteFileInfo> files) {
        Map<String, RemoteFileInfo> result = new LinkedHashMap<>();
        for (int i = 0; i < files.size(); i++) {
            result.put(BaseConstant.DEFAULT_MAP_KEY + i, files.get(i));
        }
        return result;
    }

    private Map<String, RemoteFileInfo> formatCustodianFiles(List<RemoteFileInfo> files) {
        Objects.requireNonNull(files, "文件信息不为空");
        List<AccountInformation> list = accountInformationService.list();
        Map<String, String> nameIds = list.stream().collect(Collectors.toMap(
                AccountInformation::getFullProductName,
                AccountInformation::getId,
                (oldId, newId) -> newId));
        Map<String, RemoteFileInfo> fileNameMap = files.stream().collect(Collectors.toMap(RemoteFileInfo::getFileName,
                remoteFileInfo -> remoteFileInfo,
                (oldFile, newFile) -> newFile, LinkedHashMap::new));
        Map<String, RemoteFileInfo> result = new LinkedHashMap<>();
        for (Map.Entry<String, RemoteFileInfo> entry : fileNameMap.entrySet()) {
            String fileName = entry.getKey();
            String fullProductName = StringUtil.extractTAAccountName(fileName);
            if (fullProductName == null) {
                log.error("文件名格式不正确，无法提取账套名称。");
                continue;
            }
            String id = nameIds.get(fullProductName);
            if (id == null) {
                log.error("未找到对应的账套名称:{}", fullProductName);
                continue;
            }
            result.put(id, entry.getValue());
        }
        return result;
    }
}
