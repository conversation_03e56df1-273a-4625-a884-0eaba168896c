package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import com.github.pagehelper.PageInfo;

public interface BankReconciliationLogService {

    PageInfo<LogBRImportFileRecord> pageLogImport(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogBRMarkDiffRecord> pageLogDiff(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogBRExportFileRecord> pageLogExport(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogBRSyncValuationRecord> pageLogSync(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogBRRPARecord> getLogBRRPARecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    Boolean reExecuteRpa(String rpaLogId);

}
