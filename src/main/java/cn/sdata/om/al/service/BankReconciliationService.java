package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.BankReconciliation;
import cn.sdata.om.al.entity.BankReconciliationQuery;
import cn.sdata.om.al.entity.CommonEntity;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface BankReconciliationService {
    PageInfo<BankReconciliation> page(BankReconciliationQuery bankReconciliationQuery);

    List<CommonEntity> settlementLocationList();

    List<CommonEntity> securityNameList();

    List<CommonEntity> securitiesCodeList();

    Boolean markDifference(JSONObject jsonObject);

    List<File> downloadSettlementCompanyFiles(String beginDate, String endDate);

    String syncBankReconciliation(String beginDate, String endDate);

    String upload(List<MultipartFile> files, String executeType, String date);

    List<CommonEntity> sourceCodeList();

    JSONObject selectSyncTimeByUserId(String id);

    void updateSyncTime(String endDate, String now);

    void insertSyncTime(String endDate, String now);

    void updateSettlementSyncTime(String id, String now);

    void insertSettlementSyncTime(String id, String now);

    void updateAllSyncTime(String id, String now);

    void insertAllSyncTime(String id, String now);

    JSONObject selectSyncTimeByEndDate(String endDate);

    List<JSONObject> selectAllSyncTime();

    List<JSONObject> selectDataDateByIds(List<String> ids);

    void executeSQRpa(JSONObject params);

    void executeZZRpa(JSONObject params);

    String syncBankReconciliationV2(String beginDate, String endDate);

    String uploadV2(List<MultipartFile> files, String manual, String date);

}
