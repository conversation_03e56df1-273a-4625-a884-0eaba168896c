package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.LogCCRExportRecord;
import cn.sdata.om.al.entity.LogCCRRPARecord;
import cn.sdata.om.al.entity.LogCCRSendMailRecord;
import com.github.pagehelper.PageInfo;

public interface CashClearReportLogService {
    PageInfo<LogCCRRPARecord> getLogCCRRPARecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogCCRExportRecord> getLogCCRExportRecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogCCRSendMailRecord> getLogCCRSendMailRecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    Boolean reExecuteRpa(String rpaLogId);

}
