package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.utils.ReflexUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.*;


@Service
@Slf4j
public class CommonQueryService<T> {
    /**
     * 通用的list查询
     *
     * @param commonPageParam 查询条件
     * @param entityClass     反射类
     * @return 数据集合
     */
    public List<T> commonList(CommonPageParam<T> commonPageParam, Class<T> entityClass) {

        String entityName = entityClass.getSimpleName();
        String serviceName = entityName.substring(0, 1).toLowerCase() + entityName.substring(1) + "Service";
        Object service = SpringUtil.getBean(serviceName);

        QueryWrapper<T> queryWrapper = null == commonPageParam ?
                new QueryWrapper<>() : getQueryWrapper(commonPageParam, entityClass);

        ReflexUtil<T> reflexUtil = new ReflexUtil<>();
        return reflexUtil.doQuery(service, queryWrapper);
    }

    /**
     * 通用的分页查询
     *
     * @param commonPageParam 参数
     * @param entityClass     反射类
     * @return 分页封套
     */
    public Page<T> commonPage(@NonNull CommonPageParam<T> commonPageParam, Class<T> entityClass) {
        IPage<T> pageParam = new Page<>();
        pageParam.setCurrent(commonPageParam.getCurrent());
        pageParam.setSize(commonPageParam.getSize());
        String entityName = entityClass.getSimpleName();
        String serviceName = entityName.substring(0, 1).toLowerCase() + entityName.substring(1) + "Service";
        Object service = SpringUtil.getBean(serviceName);
        QueryWrapper<T> queryWrapper = getQueryWrapper(commonPageParam, entityClass);
        ReflexUtil<T> reflexUtil = new ReflexUtil<>();
        return reflexUtil.doQueryWithPage(service, pageParam, queryWrapper);
    }

    /**
     * 获取查询wrapper
     *
     * @param commonPageParam       查询参数
     * @param entityClass 反射类
     * @return 查询封套
     */
    private QueryWrapper<T> getQueryWrapper(CommonPageParam<T> commonPageParam, Class<T> entityClass) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        Map<String, Object> param = commonPageParam.getParam();
        List<String> like = commonPageParam.getLike();

        Map<String, String> orderColumn = commonPageParam.getOrderColumn();

        List<CommonPageParam.BetweenParam> between = commonPageParam.getBetween();

        if (null != orderColumn && !orderColumn.isEmpty()) {
            for (Map.Entry<String, String> entry : orderColumn.entrySet()) {
                String value = entry.getValue();
                String key = entry.getKey();
                if ("desc".equals(value)) {
                    queryWrapper.orderByDesc(key);
                }else {
                    queryWrapper.orderByAsc(key);
                }
            }
        }

        if (param == null) {
            return queryWrapper;
        }
        // 处理开始/结束时间
        String startDate = (String) param.get(START_DATE_NAME);
        String endDate = (String) param.get(END_DATE_NAME);
        if (startDate != null && endDate != null) {
            param.remove(START_DATE_NAME);
            param.remove(END_DATE_NAME);
            queryWrapper.between(SYSTEM_DATE_NAME, startDate, endDate);
        }

        if (between != null && !between.isEmpty()) {
            for (CommonPageParam.BetweenParam betweenParam : between) {
                String field = betweenParam.getField();
                if (field == null) {
                    continue;
                }
                String tableCol = getTableValue(entityClass, field);
                queryWrapper.between(tableCol, betweenParam.getStart(), betweenParam.getEnd());
            }
        }

        for (Map.Entry<String, Object> entry : param.entrySet()) {
            String paramKey = entry.getKey();
            Object paramValue = entry.getValue();
            if (Objects.isNull(paramValue)
                    || "".equals(paramValue)
                    || ((paramValue instanceof List) && (((List<?>) paramValue).isEmpty()))) {
                continue;
            }
            String tableCol = getTableValue(entityClass, paramKey);
            Class<Enum<?>> enumType = getEnumType(entityClass, paramKey);
            // 特殊处理一下difference
            if (DIFFERENCE_STR.equals(paramKey)) {
                // 无差异传0
                if (NO_DIFFERENCE.equals(paramValue)) {
                    queryWrapper.and(wrapper ->  wrapper.eq(tableCol, paramValue).or().isNull(tableCol));
                } else {// 差异传1
                    queryWrapper.isNotNull(tableCol).ne(tableCol, 0);
                }
                continue;
            }

            if (tableCol.isEmpty()) {
                continue;
            }

            if (paramValue instanceof List) {
                if (enumType != null) {
                    List<?> list = (List<?>) paramValue;
                    List<Integer> valueList = list.stream()
                            .map((Function<Object, Integer>) o -> getEnumOrdinalByName(enumType, (String) o))
                            .filter(ordinal -> ordinal != -1)
                            .collect(Collectors.toList());
                    queryWrapper.in(tableCol, valueList);
                }else{
                    queryWrapper.in(tableCol, (List<?>) paramValue);
                }
            } else {

                Set<String> likeSet = new HashSet<>();
                if (CollUtil.isNotEmpty(like)) {
                    likeSet = new HashSet<>(like);
                }

                if (likeSet.contains(paramKey)) {
                    queryWrapper.like(tableCol, paramValue);
                }else{
                    if (enumType != null) {
                        int ordinal = getEnumOrdinalByName(enumType, (String) paramValue);
                        if (ordinal!= -1) {
                            queryWrapper.eq(tableCol, ordinal);
                        }
                    }else{
                        queryWrapper.eq(tableCol, paramValue);
                    }
                }

            }

        }

        return queryWrapper;
    }

    private String getTableValue(Class<T> entityClass, String paramKey){
        Field f;
        String tableCol = "";
        try {
            f = entityClass.getDeclaredField(paramKey);
            TableField tableField = f.getAnnotation(TableField.class);
            TableId tableId = f.getAnnotation(TableId.class);
            if (tableField != null) {
                tableCol = tableField.value();
            } else if (tableId != null) {
                tableCol = tableId.value();
            }

        } catch (NoSuchFieldException e) {
            log.debug("类中无{}字段:", paramKey);
        }
        return tableCol;
    }

    /**
     * 获取字段的枚举类型
     *
     * @param entityClass 实体类
     * @param fieldName 字段名
     * @return 枚举类型，如果不是枚举则返回null
     */
    @SuppressWarnings("unchecked")
    private Class<Enum<?>> getEnumType(Class<T> entityClass, String fieldName) {
        try {
            Field field = entityClass.getDeclaredField(fieldName);
            Class<?> type = field.getType();
            if (type.isEnum()) {
                return (Class<Enum<?>>) type;
            }
            return null;
        } catch (NoSuchFieldException e) {
            log.debug("类中无此枚举{}字段:", fieldName);
            return null;
        }
    }

    /**
     * 通过枚举的name获取其ordinal值
     *
     * @param enumClass 枚举类
     * @param enumName 枚举名称
     * @return 枚举的ordinal值，如果找不到对应的枚举则返回-1
     */
    private int getEnumOrdinalByName(Class<Enum<?>> enumClass, String enumName) {
        try {
            Enum<?>[] enumConstants = enumClass.getEnumConstants();
            for (Enum<?> enumConstant : enumConstants) {
                if (enumConstant.name().equals(enumName)) {
                    return enumConstant.ordinal();
                }
            }
            return -1;
        } catch (Exception e) {
            log.error("获取枚举ordinal失败: {}", e.getMessage());
            return -1;
        }
    }
}
