package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MapKey;
import cn.sdata.om.al.vo.CustodianBankContactsVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.mapper.CustodianBankContactsMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class CustodianBankContactsService extends ServiceImpl<CustodianBankContactsMapper, CustodianBankContacts> {

    private final CustodianBankContactsViewService custodianBankContactsViewService;

    @SuppressWarnings("unchecked")
    public Page<CustodianBankContactsVO> getPageResult(Page<CustodianBankContactsVO> page, Map<String, Object> param) {
        List<String> bankName = null, products = null, custodianRole = null;
        if (param != null) {
            bankName = (List<String>) param.get(MapKey.CUSTODIAN_PARAM_BANK_NAME.getFrontValue());
            products = (List<String>) param.get(MapKey.CUSTODIAN_PARAM_PRODUCTS.getFrontValue());
            custodianRole = (List<String>) param.get(MapKey.CUSTODIAN_PARAM_ROLE.getFrontValue());
        }
        return this.getBaseMapper().pageResult(bankName, products, custodianRole, page);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveFromParam(CustodianBankContactsParam custodianBankContactsParam) {
        List<CustodianBankContacts> custodianBankContactsList = this.getByParam(custodianBankContactsParam);
        this.saveBatch(custodianBankContactsList);
        String id = IdWorker.getIdStr();
        List<CustodianBankContactsView> result =
                custodianBankContactsList.stream()
                        .map(contacts -> new CustodianBankContactsView(id, contacts.getId()))
                        .collect(Collectors.toList());
        custodianBankContactsViewService.saveBatch(result);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFromParam(CustodianBankContactsParam custodianBankContactsParam){
        Objects.requireNonNull(custodianBankContactsParam, "更新时参数不得为空");
        String id = custodianBankContactsParam.getId();
        Objects.requireNonNull(custodianBankContactsParam, "更新时参数不得为空");
        if (id == null) {
            throw new RuntimeException("更新时ID不得为空");
        }
        //查询关联的数据并删除
        List<String> ids = getSubIds(id);
        LambdaQueryWrapper<CustodianBankContactsView> viewLambdaQueryWrapper = new LambdaQueryWrapper<>();
        viewLambdaQueryWrapper.eq(CustodianBankContactsView::getId, id);
        custodianBankContactsViewService.remove(viewLambdaQueryWrapper);
        this.removeBatchByIds(ids);
        //合成真实数据
        List<CustodianBankContacts> custodianBankContactsList = this.getByParam(custodianBankContactsParam);
        //保存并获取ID
        this.saveBatch(custodianBankContactsList);
        List<CustodianBankContactsView> result =
                custodianBankContactsList.stream()
                        .map(contacts -> new CustodianBankContactsView(id, contacts.getId()))
                        .collect(Collectors.toList());
        custodianBankContactsViewService.saveBatch(result);

    }

    public List<CustodianBankContacts> getByParam(CustodianBankContactsParam custodianBankContactsParam){
        List<CustodianBankContacts> custodianBankContactsList = new ArrayList<>();
        if (custodianBankContactsParam == null) {
            throw new RuntimeException("参数不得为空");
        }
        String bankName = custodianBankContactsParam.getBankName();
        String custodianRole = custodianBankContactsParam.getCustodianRole();
        String recipient = custodianBankContactsParam.getRecipient();
        String recipientCc = custodianBankContactsParam.getRecipientCc();
        String phone = custodianBankContactsParam.getPhone();
        Set<String> productIds = custodianBankContactsParam.getProductId();
        if (productIds == null) {
            throw new RuntimeException("账套不得为空");
        }
        for (String productId : productIds) {
            CustodianBankContacts custodianBankContacts = new CustodianBankContacts();
            custodianBankContacts.setCustodianBank(bankName);
            custodianBankContacts.setCustodianRole(custodianRole);
            custodianBankContacts.setProductId(productId);
            custodianBankContacts.setRecipient(recipient);
            custodianBankContacts.setRecipientCc(recipientCc);
            custodianBankContacts.setPhone(phone);
            custodianBankContactsList.add(custodianBankContacts);
        }
        return custodianBankContactsList;
    }

    private List<String> getSubIds(String id) {
        LambdaQueryWrapper<CustodianBankContactsView> queryViewWrapper = new LambdaQueryWrapper<>();
        queryViewWrapper.eq(CustodianBankContactsView::getId, id);
        List<CustodianBankContactsView> list = custodianBankContactsViewService.list(queryViewWrapper);
        return list.stream().map(CustodianBankContactsView::getSubId).collect(Collectors.toList());
    }

    public CustodianBankContactsParam getParamById(String id) {
        List<String> ids = getSubIds(id);
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("未查到相关数据");
        }
        LambdaQueryWrapper<CustodianBankContacts> queryConcatWrapper = new LambdaQueryWrapper<>();
        queryConcatWrapper.in(CustodianBankContacts::getId, ids);
        List<CustodianBankContacts> contacts = this.list(queryConcatWrapper);
        CustodianBankContactsParam custodianBankContactsParam = new CustodianBankContactsParam();
        for (CustodianBankContacts contact : contacts) {
            custodianBankContactsParam.setId(id);
            custodianBankContactsParam.setBankName(contact.getCustodianBank());
            custodianBankContactsParam.setCustodianRole(contact.getCustodianRole());
            custodianBankContactsParam.setRecipientCc(contact.getRecipientCc());
            custodianBankContactsParam.setRecipient(contact.getRecipient());
            custodianBankContactsParam.setPhone(contact.getPhone());
            Set<String> productIds = custodianBankContactsParam.getProductId();
            if (productIds == null) {
                productIds = new LinkedHashSet<>();
                custodianBankContactsParam.setProductId(productIds);
            }
            productIds.add(contact.getProductId());
        }
        return custodianBankContactsParam;
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeAll(String id) {
        Objects.requireNonNull(id, "删除时ID不得为空");
        List<String> ids = getSubIds(id);
        LambdaQueryWrapper<CustodianBankContactsView> viewLambdaQueryWrapper = new LambdaQueryWrapper<>();
        viewLambdaQueryWrapper.eq(CustodianBankContactsView::getId, id);
        custodianBankContactsViewService.remove(viewLambdaQueryWrapper);
        this.removeBatchByIds(ids);
    }

    public List<CustodianBankContactsVO> getListResult(String custodianRole) {
        return this.getBaseMapper().listResult(custodianRole);
    }
}
