package cn.sdata.om.al.service;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @Date 2025/3/26 16:10
 * @Version 1.0
 */
@Service
public class ExecutionLockService {
    private static final ConcurrentHashMap<String, AtomicBoolean> LOCK_MAP = new ConcurrentHashMap<>();

    /**
     * 尝试获取指定名称的锁
     *
     * @param lockName 锁名称
     * @return 是否获取成功
     */
    public boolean tryLock(String lockName) {
        // 获取或创建指定名称的锁
        AtomicBoolean lock = LOCK_MAP.computeIfAbsent(lockName, k -> new AtomicBoolean(false));
        return lock.compareAndSet(false, true);
    }

    /**
     * 释放指定名称的锁
     *
     * @param lockName 锁名称
     */
    public void unlock(String lockName) {
        AtomicBoolean lock = LOCK_MAP.get(lockName);
        if (lock != null) {
            lock.set(false);
        }
    }

    /**
     * 检查指定名称的锁是否已被占用
     *
     * @param lockName 锁名称
     * @return 是否已被锁定
     */
    public boolean isLocked(String lockName) {
        AtomicBoolean lock = LOCK_MAP.get(lockName);
        return lock != null && lock.get();
    }

    /**
     * 清除指定名称的锁（释放资源）
     *
     * @param lockName 锁名称
     */
    public void removeLock(String lockName) {
        LOCK_MAP.remove(lockName);
    }
}
