package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.mapper.FuncDataToMailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/13 21:38
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FuncDataToMailService extends ServiceImpl<FuncDataToMailMapper, FuncDataToMailEntity> {

    /**
     * 根据数据日期和功能类型查询最新关联邮件信息
     *
     * @param dataDate 数据日期
     * @param funcType 功能类型
     * @return
     */
    public List<FuncDataToMailEntity> getLatestMailByDataDateAndFuncType(String dataDate, String funcType) {
        return this.baseMapper.getLatestMailByDataDateAndFuncType(dataDate, funcType);
    }

    /**
     * 获取资金清算报表功能对应的发送邮件数据
     *
     * @param dataDate 数据日期
     * @return
     */
    public List<FuncDataToMailEntity> getCashClearReportMailList(String beginDate, String endDate) {
        return this.baseMapper.getCashClearReportMailList(beginDate, endDate);
    }

}
