package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.InsuranceRegistrationFees;
import cn.sdata.om.al.entity.InsuranceRegistrationFeesFile;
import cn.sdata.om.al.entity.InsuranceRegistrationFeesQuery;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface InsuranceRegistrationFeesService {
    PageInfo<InsuranceRegistrationFees> page(InsuranceRegistrationFeesQuery insuranceRegistrationFeesQuery);

    String upload(MultipartFile[] files, String mailId, String noticeDate);

    String importO32(List<String> ids);

    List<JSONObject> getById(String beginDate, String endDate);

    boolean update(List<JSONObject> jsonObjects);

    String previewFile(String fileId);

    List<InsuranceRegistrationFeesFile> getFileByIds(List<String> ids);

    String syncPaymentStatus(List<String> ids);

    List<InsuranceRegistrationFees> selectList(List<String> ids);

    boolean sendEmail(List<String> ids);

    List<InsuranceRegistrationFees> selectLastQuarterData(String quarterMonth, String quarterMonth1);

    boolean deleteById(String id);

}
