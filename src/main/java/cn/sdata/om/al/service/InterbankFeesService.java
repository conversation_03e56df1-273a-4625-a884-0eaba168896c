package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface InterbankFeesService {
    PageInfo<InterBankFees> page(InterbankFeesQuery interbankFeesQuery);

    String upload(MultipartFile[] files, String mailId, String noticeDate);

    InterBankFees getById(String id);

    boolean update(InterBankFees interbankFees);

    List<InterBankFeesFile> getFileByIds(List<String> ids);

    String importO32(List<String> ids);

    List<FileAndIds> generateO32File(List<InterBankFees> interbankFees, int flag, String initDate);

    List<InterBankFees> selectList(List<String> ids);

    FileAndIds generateNoninvestFile(List<InterBankFees> fees, List<O32AssetUnit> o32AssetUnits, String initDate);

    FileAndIds generateAssetFile(List<InterBankFees> fees, List<O32AssetUnit> o32AssetUnits, String initDate);

    boolean uploadShareFolder(List<FileAndIds> o32Files, int flag, boolean executeTask, String username, String type);

    String syncPaymentStatus(List<String> ids, int type);

    List<FileAndIds> generateInsuranceRegistrationFeesO32File(List<InsuranceRegistrationFees> fees, int flag, String initDate);

    boolean sendPaymentNotice(List<String> ids);

    void executeRPA(String flowId, List<String> ids, int flag, String dirPath, String uploadOrder, String username, String type, String fileType) throws Exception;

    List<InterBankFees> transformInsuranceRegistrationFeesServiceToInterBankFees(List<InsuranceRegistrationFees> insuranceRegistrationFees);

    List<InterBankFees> selectLastQuarterData(String quarterMonth, String quarterMonth1);

    boolean deleteById(String id);

}
