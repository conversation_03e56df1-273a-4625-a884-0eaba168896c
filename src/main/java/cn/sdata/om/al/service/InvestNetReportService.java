package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.dto.InvestNetReportLogDto;
import cn.sdata.om.al.dto.InvestNetReportShareChangeDto;
import cn.sdata.om.al.entity.investNetReport.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.NetReportFileEnum;
import cn.sdata.om.al.job.InvestNetReportJob;
import cn.sdata.om.al.job.InvestNetReportMailJob;
import cn.sdata.om.al.job.InvestNetValueMailJob;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.mapper.investNetReport.LogCallRpaNetReportMapper;
import cn.sdata.om.al.mapper.investNetReport.LogFileGenNetReportMapper;
import cn.sdata.om.al.mapper.investNetReport.LogFileOperNetReportMapper;
import cn.sdata.om.al.mapper.investNetReport.LogMailSendNetReportMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.sdata.om.al.constant.BaseConstant.RPA_PRODUCT_IDS;
import static cn.sdata.om.al.constant.JobConstant.*;

/**
 * <AUTHOR>
 * @Date 2025/3/10 13:58
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvestNetReportService extends ServiceImpl<InvestNetReportMapper, InvestNetReportEntity> {

    /**
     * 投连净值播报-本级目录名称
     */
    private final String activeDirName = "investNetReport";

    private final InvestNetReportMappingMapper mappingMapper;

    private final ExecutionLockService lockService;

    private final CronService cronService;

    private final BaseCronLogService baseCronLogService;

    private final MarketTradeDayService marketTradeDayService;

    private final LogFileOperNetReportMapper logFileOperMapper;

    private final LogMailSendNetReportMapper logMailSendMapper;

    private final LogCallRpaNetReportMapper logCallRpaMapper;

    private final LogFileGenNetReportMapper logFileGenMapper;

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;

    private final Lock vlock = new ReentrantLock();

    private final Lock rlock = new ReentrantLock();

    /**
     * 获取份额变动表附件
     *
     * @param bizDate
     * @return
     */
    public List<String> getShareChangeExcelLocalPath(String bizDate) {
        try {
            return this.baseMapper.getShareChangeExcelLocalPath(bizDate);
        } catch (Exception e) {
            log.error("getShareChangeExcelLocalPath_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取份额变动表附件v1
     *
     * @param bizDate
     * @return
     */
    public List<InvestNetReportShareChangeDto> getShareChangeExcelLocalPathV1(String bizDate) {
        try {
            return this.baseMapper.getShareChangeExcelLocalPathV1(bizDate);
        } catch (Exception e) {
            log.error("getShareChangeExcelLocalPathV1_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 执行rpa以及数据处理
     *
     * @param dataDate yyyy-mm-dd
     * @return
     * @throws Exception
     */
    public void exeRpaAndHandleExcel(String dataDate) throws Exception {
        String lockName = "netReport_lock_" + dataDate;
        if (lockService.tryLock(lockName)) {
            try {
                log.info("InvestNetReportService_exeRpa_run:{}", dataDate);
                dealRpa(dataDate, getAccountSetCodes());
            } catch (Exception e) {
                log.error("InvestNetReportService_exeRpaAndHandleExcel_error:{},{}", e, e.getMessage());
                throw e;
            } finally {
                lockService.unlock(lockName);
            }
        } else {
            throw new Exception("正在下载中...");
        }

    }


    /**
     * 从rpa调用日志处操作rpa调用
     *
     * @param id
     * @throws Exception
     */
    public void logCallRpa(String id) throws Exception {
        Assert.notNull(id, "参数不能为空");
        LogCallRpaNetReportEntity rpaLog = logCallRpaMapper.selectById(id);
        if (ObjectUtil.isNull(rpaLog)) {
            log.error("InvestNetReportService_logCallRpa_error:{}", "错误日志数据");
            throw new Exception("错误日志数据");
        }
        String dataDate = rpaLog.getDataDate();
        if (StringUtils.isBlank(dataDate)) {
            log.error("InvestNetReportService_logCallRpa_error:{}", "数据日期为空");
            throw new Exception("数据日期为空");
        }
        exeRpaAndHandleExcel(dataDate);
    }

    /**
     * 获取投连-配置的账套号
     *
     * @return
     */
    public List<String> getAccountSetCodes() {
        try {
            return mappingMapper.mappingList().stream().map(MappingEntity::getAccountSetCode).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getAccountSetCodes_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    public static String getSCExcelCellDataDateV1(String filePath) throws Exception {
        Workbook workbook = new XSSFWorkbook(filePath);
        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(2);
        if (row == null) {
            workbook.close();
            throw new Exception("数据日期行不存在");
        }
        Cell cell = row.getCell(3);
        if (cell == null) {
            workbook.close();
            throw new Exception("数据日期单元格不存在");
        }
        String dataDate;
        switch (cell.getCellType()) {
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    dataDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    throw new Exception("数据日期单元格不是日期格式，而是数字: " + numericValue);
                }
                break;
            case STRING:
                String dateString = cell.getStringCellValue().trim();
                try {
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                            outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    LocalDate date = LocalDate.parse(dateString, inputFormat);
                    dataDate = date.format(outputFormat);
                } catch (Exception e) {
                    throw new Exception("数据日期单元格日期格式无效: " + dateString);
                }
                break;
            default:
                throw new Exception("数据日期单元格不是日期或数字类型");
        }
        workbook.close();
        return dataDate;
    }


    /**
     * 下载寿险-投连估值表
     *
     * @param dataDate yyyy-mm-dd
     * @return
     */
    public synchronized void dealRpa(String dataDate, List<String> accountSetCodes) throws Exception {
        initDefaultData(dataDate);
        int defaultCount = Math.toIntExact(this.count(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.UL.getTypeNo())));
        if (defaultCount == 0)
            throw new Exception("无数据日期对应的UL初始化数据信息");

        initShareChangeFile2DataV1(dataDate);

        InvestNetReportEntity scReport = this.getOne(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo()));
        if (ObjectUtil.isNull(scReport) || StringUtils.isBlank(scReport.getLocalFilePath())) {
            throw new Exception("份额变动表值不存在");
        }
        if (!Files.exists(Paths.get(scReport.getLocalFilePath()))) {
            throw new Exception("份额变动表文件不存在");
        }
        List<String> jobIds = cronService.getJobIdByClass(InvestNetReportJob.class);
        String newDataDateStr = LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
        BaseCronLog latestLog = baseCronLogService.getLatestLog(jobIds.get(0), (newDataDateStr + "-" + newDataDateStr));
        if (latestLog != null
                && latestLog.getRpaStatus() != null
                && latestLog.getRpaStatus().equals(JobStatus.RUNNING)) {
            throw new Exception("该数据日期对应的rpa任务正在运行中");
        }
        if (latestLog != null && (latestLog.getRpaStatus() == null || latestLog.getStatus().equals(JobStatus.RUNNING))) {
            throw new Exception("该数据日期对应的下载任务正在运行中");
        }
        this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, null)
                .set(InvestNetReportEntity::getUserId, "dealRpa_start")
                .set(InvestNetReportEntity::getDownloadStatus, 3)
                .set(InvestNetReportEntity::getNetReportSendStatus, MailStatus.UNSENT.name())
                .set(InvestNetReportEntity::getNetReportSendTime, null)
                .set(InvestNetReportEntity::getNetValueSendStatus, MailStatus.UNSENT.name())
                .set(InvestNetReportEntity::getNetValueSendTime, null)
                .set(InvestNetReportEntity::getLocalFilePath, null)
                .set(InvestNetReportEntity::getUserId, null)
        );
        exeRpaExportTa4y2m2d(dataDate, accountSetCodes, jobIds);
    }

    /**
     * 投连净值rpa调用
     *
     * @param dataDate        数据日期
     * @param accountSetCodes 账套编号
     */
    public synchronized void exeRpaExportTa4y2m2d(String dataDate, List<String> accountSetCodes, List<String> jobIds) {
        try {
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(CronConstant.START_DATE, dataDate);
            jobDataMap.put(CronConstant.END_DATE, dataDate);
            jobDataMap.put(RPA_PRODUCT_IDS, String.join(",", accountSetCodes));
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                    .eq(InvestNetReportEntity::getDataDate, dataDate)
                    .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
                    .set(InvestNetReportEntity::getUpdateTime, new Date())
                    .set(InvestNetReportEntity::getUserId, "exeRpaExportTa4y2m2d_error")
                    .set(InvestNetReportEntity::getDownloadStatus, 2)
            );
            log.error("exeRpaExportTa4y2m2d_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 净值播报发送邮件
     *
     * @param dataDate        数据日期
     * @param filePaths       文件路径
     * @param accountSetCodes 账套号列表
     */
    public void sendNetReportMailV1(String dataDate, List<String> filePaths, List<String> accountSetCodes) {
        try {
            List<String> jobIds = cronService.getJobIdByClass(InvestNetReportMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(FILE_PATHS, String.join(", ", filePaths));
            jobDataMap.put(ACCOUNT_SET_CODES, String.join(", ", accountSetCodes));
            jobDataMap.put(CronConstant.START_DATE, dataDate);
            jobDataMap.put(CronConstant.END_DATE, dataDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            log.info("sendNetReportMailV1_jobIds:{},jobDataMap:{}", jobIds, jobDataMap.toString());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            log.error("sendNetReportMailV1_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 发送产品净值邮件
     *
     * @param dataDate
     * @param filePaths
     * @param accountSetCodes
     */
    public void sendNetValueMailV1(String dataDate, List<String> filePaths, List<String> accountSetCodes) {
        try {
            List<String> jobIds = cronService.getJobIdByClass(InvestNetValueMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(FILE_PATHS, String.join(", ", filePaths));
            jobDataMap.put(ACCOUNT_SET_CODES, String.join(", ", accountSetCodes));
            jobDataMap.put(CronConstant.START_DATE, dataDate);
            jobDataMap.put(CronConstant.END_DATE, dataDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            log.info("sendNetValueMailV1_jobIds:{},jobDataMap:{}", jobIds, jobDataMap.toString());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            log.error("sendNetValueMailV1_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 分页接口
     *
     * @param dataDate
     * @param downloadStatus
     * @param netReportSendStatus
     * @param netValueSendStatus
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<InvestNetReportEntity> page(String dataDate,
                                            String downloadStatus,
                                            String netReportSendStatus,
                                            String netValueSendStatus,
                                            int pageNo,
                                            int pageSize) {

        if (StringUtils.isNotBlank(dataDate)) {
            initDefaultData(dataDate);
            //initShareChangeFile2Data(dataDate);
            initShareChangeFile2DataV1(dataDate);
        }
        return this.page(new Page<>(pageNo, pageSize), Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(StringUtils.isNotBlank(downloadStatus), InvestNetReportEntity::getDownloadStatus, downloadStatus)
                .eq(StringUtils.isNotBlank(netReportSendStatus), InvestNetReportEntity::getNetReportSendStatus, netReportSendStatus)
                .eq(StringUtils.isNotBlank(netValueSendStatus), InvestNetReportEntity::getNetValueSendStatus, netValueSendStatus)
                .eq(StringUtils.isNotBlank(dataDate), InvestNetReportEntity::getDataDate, dataDate)
        );
    }

    /**
     * 投连-份额变动表文件初始化
     *
     * @param dataDate
     */
    public synchronized void initShareChangeFile2Data(String dataDate) {
        String nextBizDate = marketTradeDayService.getDefaultMarketNextTradeDay(dataDate);
        log.info("InvestNetReportService_initShareChangeFile_nextBizDate:{}", nextBizDate);
        List<String> filePaths = getShareChangeExcelLocalPath(nextBizDate);
        if (CollUtil.isNotEmpty(filePaths)) {
            String scFilePath = filePaths.get(0);
            log.info("InvestNetReportService_initShareChangeFile_scFilePath_邮件分拣到的文件:{}", scFilePath);
            Path scPath = Paths.get(scFilePath);
            if (Files.exists(scPath) && Files.isRegularFile(scPath) && Files.isReadable(scPath)) {
                this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                        .eq(InvestNetReportEntity::getDataDate, dataDate)
                        .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo())
                        .set(InvestNetReportEntity::getLocalFilePath, scFilePath)
                        .set(InvestNetReportEntity::getUpdateTime, new Date())
                        .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                        .set(InvestNetReportEntity::getUserId, "邮件分拣成功")
                        .set(InvestNetReportEntity::getDownloadStatus, 1)
                        .set(InvestNetReportEntity::getSource, "SYSTEM"));
            } else {
                log.error("dealShareChangeExcel_initShareChangeFile_scFilePath_邮件分拣到的文件读取异常:{}", scFilePath);
            }
        } else {
            log.error("dealShareChangeExcel_initShareChangeFile_filePaths_邮件分拣未找到文件");
        }
    }

    /**
     * 投连-份额变动表文件初始化v1
     *
     * @param dataDate
     */
    public synchronized void initShareChangeFile2DataV1(String dataDate) {
        String nextBizDate = marketTradeDayService.getDefaultMarketNextTradeDay(dataDate);
        log.info("InvestNetReportService_initShareChangeFile2DataV1_nextBizDate:{}", nextBizDate);
        List<InvestNetReportShareChangeDto> shareChangeDtos = getShareChangeExcelLocalPathV1(nextBizDate);
        log.info("InvestNetReportService_initShareChangeFile2DataV1_shareChangeDtos:{}", shareChangeDtos);
        if (CollUtil.isNotEmpty(shareChangeDtos) && ObjectUtil.isNotNull(shareChangeDtos.get(0))) {
            InvestNetReportShareChangeDto dto = shareChangeDtos.get(0);
            String scFilePath = dto.getUrl(), receivedTime = dto.getReceivedTime();
            log.info("InvestNetReportService_initShareChangeFile2DataV1_scFilePath_邮件分拣到的文件:scFilePath:{},receivedTime:{}", scFilePath, receivedTime);
            if (StringUtils.isNotBlank(scFilePath)) {
                Path scPath = Paths.get(scFilePath);
                if (Files.exists(scPath) && Files.isRegularFile(scPath) && Files.isReadable(scPath)) {
                    this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo())
                            .set(InvestNetReportEntity::getLocalFilePath, scFilePath)
                            .set(InvestNetReportEntity::getUpdateTime, new Date())
                            .set(StringUtils.isNotBlank(receivedTime), InvestNetReportEntity::getFileUpdateTime, cn.hutool.core.date.DateUtil.parse(receivedTime, DatePattern.NORM_DATETIME_PATTERN))
                            .set(InvestNetReportEntity::getUserId, "邮件分拣成功")
                            .set(InvestNetReportEntity::getDownloadStatus, 1)
                            .set(InvestNetReportEntity::getSource, "SYSTEM"));

                    if (StringUtils.isNotBlank(receivedTime)) {
                        Integer num = Math.toIntExact(
                                logFileOperMapper.selectCount(Wrappers.lambdaQuery(LogFileOperNetReportEntity.class)
                                        .eq(LogFileOperNetReportEntity::getDataDate, dataDate)
                                        .eq(LogFileOperNetReportEntity::getSource, "邮件分拣")
                                        .eq(LogFileOperNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo())
                                ));
                        if (num == 0) {
                            logFileOperMapper.insert(new LogFileOperNetReportEntity()
                                    .setId(IdWorker.getIdStr())
                                    .setDataDate(dataDate)
                                    .setSource("邮件分拣")
                                    .setCreateTime(cn.hutool.core.date.DateUtil.parse(receivedTime, DatePattern.NORM_DATETIME_PATTERN))
                                    .setFilePath(scFilePath)
                                    .setFileName(scPath.getFileName().toString())
                                    .setFileTypeNo(NetReportFileEnum.SHARECHANGE.getTypeNo()));
                        }
                    }

                } else {
                    log.error("InvestNetReportService_initShareChangeFile2DataV1_shareChangeDtos_scFilePath_邮件分拣到的文件读取异常:{}", scFilePath);
                }
            } else {
                log.info("InvestNetReportService_initShareChangeFile2DataV1_scFilePath为空");
            }
        } else {
            log.error("InvestNetReportService_initShareChangeFile2DataV1_shareChangeDtos_邮件分拣未找到文件");
        }

    }

    /**
     * 初始化默认数据
     *
     * @param dataDate yyyy-mm-dd
     * @return
     */
    public synchronized void initDefaultData(String dataDate) {
        int defaultCount = Math.toIntExact(this.count(Wrappers.lambdaQuery(InvestNetReportEntity.class).eq(InvestNetReportEntity::getDataDate, dataDate)));
        if (defaultCount > 0) {
            return;
        }
        List<InvestNetReportEntity> reportEntities = Arrays.stream(NetReportFileEnum.values())
                .map(fileEnum -> new InvestNetReportEntity()
                        .setId(IdWorker.getIdStr())
                        .setFileName(defaultFileNameReplace(fileEnum.getName(), dataDate))
                        .setDataDate(dataDate)
                        .setDownloadStatus(0)
                        .setNetReportSendStatus(MailStatus.UNSENT.name())
                        .setNetValueSendStatus(MailStatus.UNSENT.name())
                        .setCreateTime(new Date())
                        .setSource(fileEnum.getSource())
                        .setFileTypeNo(fileEnum.getTypeNo()))
                .collect(Collectors.toList());
        for (InvestNetReportEntity reportEntity : reportEntities) {
            if (reportEntity.getFileName().contains(NetReportFileEnum.SHARECHANGE.getName())) {
                reportEntity.setNetReportSendStatus(null);
                reportEntity.setNetValueSendStatus(null);
            }
        }
        if (CollUtil.isNotEmpty(reportEntities)) this.saveBatch(reportEntities);
    }

    /**
     * 上传【index reportYYYYMMDD】文件
     *
     * @param file          文件
     * @param dataDateParam 业务日期 yyyy-mm-dd
     * @return
     */
    public Boolean uploadIndexFile(MultipartFile file, String dataDate) throws Exception {
        if (StringUtils.isBlank(dataDate)) {
            throw new Exception("数据日期为空");
        }
        String targetFilePath,
                fileNameNoExt = getFileNameNoExtension(Objects.requireNonNull(file.getOriginalFilename()));
        if (fileNameNoExt.contains("index report")) {
            String tmpDataDate = indexFileGetDataDate(fileNameNoExt);
            log.info("uploadIndexFile_index report_tmpDataDate:{}", tmpDataDate);
            if (StringUtils.isBlank(tmpDataDate) || !tmpDataDate.equals(dataDate)) {
                throw new Exception("非数据日期对应文件、不允许上传!");
            }
        } else if (NetReportFileEnum.SHARECHANGE.getName().equals(fileNameNoExt.trim())) {
            log.info("uploadIndexFile1_dateDate:{},fileNameNoExt:{}", dataDate, fileNameNoExt);
            /*String tmpDataDate = getSCExcelCellDataDate(file);
            log.info("uploadIndexFile_份额变动表_tmpDataDate:{}", tmpDataDate);
            if (StringUtils.isBlank(tmpDataDate) || !tmpDataDate.equals(preBizDate)) {
                throw new Exception("非数据日期对应文件、不允许上传!");
            }*/
        } else {
            throw new Exception("非功能文件不允许上传!");
        }
        log.info("uploadIndexFile2_dateDate:{},fileNameNoExt:{}", dataDate, fileNameNoExt);

        LambdaQueryWrapper<InvestNetReportEntity> qw = Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate);
        LambdaUpdateWrapper<InvestNetReportEntity> uw = Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .set(InvestNetReportEntity::getUpdateTime, new Date())
                .set(InvestNetReportEntity::getFileUpdateTime, new Date())
                .set(InvestNetReportEntity::getUserId, SecureUtil.currentUserId())
                .set(InvestNetReportEntity::getDownloadStatus, 1);
        Integer tmpFileTypeNo = null;
        if (defaultFileNameReplace(NetReportFileEnum.SHARECHANGE.getName(), dataDate).equals(fileNameNoExt)) {
            qw.eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo());
            uw.eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.SHARECHANGE.getTypeNo());
            targetFilePath = genShareChangeFilePath(dataDate, file.getOriginalFilename());
            tmpFileTypeNo = NetReportFileEnum.SHARECHANGE.getTypeNo();
        } else if (defaultFileNameReplace(NetReportFileEnum.INDEX.getName(), dataDate).equals(fileNameNoExt)) {
            qw.eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.INDEX.getTypeNo());
            uw.eq(InvestNetReportEntity::getFileTypeNo, NetReportFileEnum.INDEX.getTypeNo());
            targetFilePath = genFilePath(dataDate, file.getOriginalFilename());
            tmpFileTypeNo = NetReportFileEnum.INDEX.getTypeNo();
        } else {
            throw new Exception("非功能文件不允许上传");
        }
        initDefaultData(dataDate);
        int defaultCount = Math.toIntExact(this.count(qw));
        if (defaultCount == 0)
            throw new Exception("该数据日期无对应的初始化数据");
        Path parentDir = Paths.get(targetFilePath).getParent();
        if (parentDir != null) {
            Files.createDirectories(parentDir);
            file.transferTo(new File(targetFilePath));
        }
        if (ObjectUtil.isNotNull(tmpFileTypeNo)) {
            logFileOperMapper.insert(new LogFileOperNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setDataDate(dataDate)
                    .setSource("手动上传")
                    .setCreateTime(new Date())
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setFilePath(targetFilePath)
                    .setFileName(fileNameNoExt)
                    .setFileTypeNo(tmpFileTypeNo));
        }
        uw.set(InvestNetReportEntity::getLocalFilePath, targetFilePath);
        uw.set(InvestNetReportEntity::getSource, "MANUAL");
        return this.update(uw);

    }

    /**
     * 生成文件全路径地址
     *
     * @param dataDate 数据日期
     * @param fileName 文件名称
     * @return
     */
    public String genFilePath(String dataDate, String fileName) {
        return ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator + fileName;
    }

    /**
     * 生成份额变动表文件全路径地址
     *
     * @param dataDate 数据日期
     * @param fileName 文件名称
     * @return
     */
    public String genShareChangeFilePath(String dataDate, String fileName) {
        return ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator + "shareChange" + File.separator + fileName;
    }

    /**
     * 默认名称YYYYMMDD替换处理
     *
     * @param defaultFileName
     * @param dataDate        yyyy-MM-dd
     * @return
     */
    public String defaultFileNameReplace(String defaultFileName, String dataDate) {
        return defaultFileName.replace("YYYYMMDD", norm2Pure(dataDate));
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String norm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)).format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }


    /**
     * 发送净值邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     * @throws Exception
     */
    public Boolean sendNetValueMail(String dataDate) throws Exception {
        vlock.lock();
        try {
            List<String> filePaths = this.list(
                    Wrappers.lambdaQuery(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getDownloadStatus, 1)
                            .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.INDEX.getTypeNo()))
            ).stream().map(InvestNetReportEntity::getLocalFilePath).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(filePaths) || filePaths.size() < 3) {
                updateNetValueSendStatus(dataDate,
                        Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.INDEX.getTypeNo()),
                        MailStatus.FAILED.name(), null);
                log.error("sendNetValueMail_文件缺失_error:{}", filePaths.size());
                throw new Exception("NetValue文件缺失");
            }
            sendNetValueMailV1(dataDate, filePaths, getAccountSetCodes());
            return updateNetValueSendStatus(dataDate,
                    Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.INDEX.getTypeNo()),
                    MailStatus.SENDING.name(), new Date());

        } catch (Exception e) {

            //邮件发送日志记录
            logMailSendMapper.insert(new LogMailSendNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setTaskName("投连产品净值邮件发送")
                    .setSendType("手工")
                    .setStatus(MailStatus.FAILED.name())
                    .setDataDate(dataDate)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setStep("sendNetValueMail发送邮件异常:" + e.getMessage())
            );

            updateNetValueSendStatus(dataDate,
                    Lists.newArrayList(NetReportFileEnum.TA.getTypeNo(), NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.INDEX.getTypeNo()),
                    MailStatus.FAILED.name(), new Date());
            log.error("sendNetValueMail_error:{},{}", e, e.getMessage());
            throw e;
        } finally {
            vlock.unlock();
        }

    }

    /**
     * 更新邮件发送状态
     *
     * @param dataDate
     * @param mailSendStatus
     * @param netValueSendTime
     */
    public Boolean updateNetValueSendStatus(String dataDate, List<Integer> fileTypeNoList, String mailSendStatus, Date netValueSendTime) {
        return this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .in(InvestNetReportEntity::getFileTypeNo, fileTypeNoList)
                .set(InvestNetReportEntity::getNetValueSendStatus, mailSendStatus)
                .set(netValueSendTime != null, InvestNetReportEntity::getNetValueSendTime, netValueSendTime)
                .set(InvestNetReportEntity::getUpdateTime, new Date()));
    }

    /**
     * 更新邮件发送状态
     *
     * @param dataDate
     * @param mailSendStatus
     * @param netReportSendTime
     */
    public Boolean updateNetReportSendStatus(String dataDate, List<Integer> fileTypeNoList, String mailSendStatus, Date netReportSendTime) {
        return this.update(Wrappers.lambdaUpdate(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate)
                .in(InvestNetReportEntity::getFileTypeNo, fileTypeNoList)
                .set(InvestNetReportEntity::getNetReportSendStatus, mailSendStatus)
                .set(netReportSendTime != null, InvestNetReportEntity::getNetReportSendTime, netReportSendTime)
                .set(InvestNetReportEntity::getUpdateTime, new Date()));
    }

    /**
     * 发送播报邮件
     *
     * @param dataDate 数据日期 yyyy-mm-dd
     * @return
     * @throws Exception
     */
    public Boolean sendNetReportMail(String dataDate) throws Exception {
        rlock.lock();
        try {
            List<String> filePaths = this.list(
                    Wrappers.lambdaQuery(InvestNetReportEntity.class)
                            .eq(InvestNetReportEntity::getDataDate, dataDate)
                            .eq(InvestNetReportEntity::getDownloadStatus, 1)
                            .in(InvestNetReportEntity::getFileTypeNo, Lists.newArrayList(NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()))
            ).stream().map(InvestNetReportEntity::getLocalFilePath).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(filePaths) || filePaths.size() < 2) {
                updateNetReportSendStatus(dataDate,
                        Lists.newArrayList(NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()),
                        MailStatus.FAILED.name(), null);
                log.error("sendNetReportMail_文件缺失_error:{}", filePaths.size());
                throw new Exception("NetReport文件缺失");
            }
            sendNetReportMailV1(dataDate, filePaths, getAccountSetCodes());
            return updateNetReportSendStatus(dataDate,
                    Lists.newArrayList(NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()),
                    MailStatus.SENDING.name(), new Date());
        } catch (Exception e) {

            //邮件发送日志记录
            logMailSendMapper.insert(new LogMailSendNetReportEntity()
                    .setId(IdWorker.getIdStr())
                    .setTaskName("净值播报邮件发送")
                    .setSendType("手工")
                    .setStatus(MailStatus.FAILED.name())
                    .setDataDate(dataDate)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setStep("sendNetReportMail发送邮件异常:" + e.getMessage())
            );

            updateNetReportSendStatus(dataDate,
                    Lists.newArrayList(NetReportFileEnum.UL.getTypeNo(), NetReportFileEnum.REPORT.getTypeNo()),
                    MailStatus.FAILED.name(), new Date());
            log.error("sendNetReportMail_error:{},{}", e, e.getMessage());
            throw e;
        } finally {
            rlock.unlock();
        }
    }


    /**
     * 获取不包含后缀名的文件名称
     *
     * @param fileName a.xlsx
     * @return a
     */
    private static String getFileNameNoExtension(String fileName) {
        return fileName.replaceFirst("[.][^.]+$", "");
    }

    /**
     * 下载
     *
     * @param dataDate yyyy-mm-dd
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadReport4Files(String dataDate) throws Exception {
        if (StringUtils.isBlank(dataDate)) throw new Exception("日期参数为空");
        initDefaultData(dataDate);
        List<InvestNetReportEntity> list = this.list(Wrappers.lambdaQuery(InvestNetReportEntity.class)
                .eq(InvestNetReportEntity::getDataDate, dataDate));
        if (list.size() != 5)
            throw new Exception("日期数据异常");
        List<String> filePaths = list.stream().filter(i -> StringUtils.isNotBlank(i.getLocalFilePath())).map(InvestNetReportEntity::getLocalFilePath).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(filePaths)) {
            throw new Exception("无可下载文件");
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
            Map<String, Integer> fileNameCountMap = new HashMap<>();
            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (ObjectUtil.isNotNull(file) && file.exists()) {
                    String fileName = file.getName();
                    int count = fileNameCountMap.getOrDefault(fileName, 0);
                    fileNameCountMap.put(fileName, count + 1);
                    String entryName = count == 0 ? fileName : fileName.replace(".", "_" + count + ".");
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipOut.putNextEntry(zipEntry);
                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = fis.read(buffer)) > 0) {
                            zipOut.write(buffer, 0, len);
                        }
                    }
                    zipOut.closeEntry();
                }
            }
        }
        byte[] zipBytes = baos.toByteArray();
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(zipBytes));
        HttpHeaders headers = new HttpHeaders();
        String conStr = "attachment; filename=yyyy-mm-dd.zip";
        headers.add(HttpHeaders.CONTENT_DISPOSITION, conStr.replaceAll("yyyy-mm-dd", dataDate));
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(zipBytes.length)
                .body(resource);
    }

    /**
     * 下载具体的单文件
     *
     * @param id
     * @return
     * @throws IOException
     */
    public ResponseEntity<InputStreamResource> downloadById(String id) throws Exception {
        InvestNetReportEntity reportEntity = this.getOne(Wrappers.lambdaQuery(InvestNetReportEntity.class).eq(InvestNetReportEntity::getId, id));
        File file = new File(reportEntity.getLocalFilePath());
        if (ObjectUtil.isNull(file) || !file.exists())
            throw new Exception("目标文件不存在");
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadByLogId(String id, String logType) throws Exception {
        String filePath = "";
        switch (logType) {
            case "fileOper":
                LogFileOperNetReportEntity fileOper = logFileOperMapper.selectById(id);
                if (ObjectUtil.isNotNull(fileOper)) {
                    filePath = fileOper.getFilePath();
                }
                break;
            case "callRpa":
                LogCallRpaNetReportEntity callRpa = logCallRpaMapper.selectById(id);
                if (ObjectUtil.isNotNull(callRpa)) {
                    filePath = callRpa.getFilePath();
                }
                break;
            case "fileGen":
                LogFileGenNetReportEntity fileGen = logFileGenMapper.selectById(id);
                if (ObjectUtil.isNotNull(fileGen)) {
                    filePath = fileGen.getFilePath();
                }
                break;
        }
        if (StringUtils.isBlank(filePath)) {
            throw new Exception("文件不存在");
        }
        File file = new File(filePath);
        if (ObjectUtil.isNull(file) || !file.exists()) {
            throw new Exception("目标文件不存在");
        }
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }

    /**
     * 获取日志记录数据
     *
     * @param dataDate
     * @return
     */
    public InvestNetReportLogDto getNetRportLogs(String dataDate) {
        InvestNetReportLogDto dto = new InvestNetReportLogDto();
        dto.setLogFileOpers(logFileOperMapper.selectList(Wrappers.lambdaQuery(LogFileOperNetReportEntity.class)
                .eq(LogFileOperNetReportEntity::getDataDate, dataDate)
                .orderByDesc(LogFileOperNetReportEntity::getCreateTime)
        ));
        dto.setLogFileGens(logFileGenMapper.selectList(Wrappers.lambdaQuery(LogFileGenNetReportEntity.class)
                .eq(LogFileGenNetReportEntity::getDataDate, dataDate)
                .orderByDesc(LogFileGenNetReportEntity::getCreateTime)
        ));
        dto.setLogCallRpas(logCallRpaMapper.getCallRpaLogsByDataDate(dataDate));
        dto.setLogMailSend(logMailSendMapper.selectList(Wrappers.lambdaQuery(LogMailSendNetReportEntity.class)
                .eq(LogMailSendNetReportEntity::getDataDate, dataDate)
                .orderByDesc(LogMailSendNetReportEntity::getCreateTime)
        ));
        return dto;
    }

    /**
     * 确保路径以 \或者/ 结尾
     *
     * @param pathStr
     * @return
     */
    private static String ensureTrailingSeparator(String pathStr) {
        String separator = Paths.get(pathStr).getFileSystem().getSeparator();
        if (!pathStr.endsWith(separator)) return pathStr + separator;
        return pathStr;
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    public String getFileExt(String fileName) {
        return com.google.common.io.Files.getFileExtension(fileName);
    }

    /**
     * index文件名称中获取数据日期
     *
     * @param input
     * @return
     * @throws Exception
     */
    public static String indexFileGetDataDate(String input) throws Exception {
        Matcher matcher = Pattern.compile("(\\d{8})").matcher(input);
        if (matcher.find()) {
            return pure2Norm(matcher.group(1));
        } else {
            throw new Exception("未找到日期格式");
        }
    }

    /**
     * yyyyMMdd转yyyy-MM-dd
     *
     * @param input yyyyMMdd
     * @return
     */
    public static String pure2Norm(String input) {
        return LocalDate.parse(input, DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 获取份额变动表中的数据日期
     *
     * @param file
     * @return
     * @throws Exception
     */
    public static String getSCExcelCellDataDate(MultipartFile file) throws Exception {
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xlsx")) {
            throw new Exception("只支持XLSX格式的Excel文件");
        }
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(2);
        if (row == null) {
            throw new Exception("数据日期行不存在");
        }
        Cell cell = row.getCell(3);
        if (cell == null) {
            throw new Exception("数据日期单元格不存在");
        }
        String dataDate;
        switch (cell.getCellType()) {
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    dataDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    throw new Exception("数据日期单元格不是日期格式，而是数字: " + numericValue);
                }
                break;
            case STRING:
                String dateString = cell.getStringCellValue().trim();
                try {
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                            outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    LocalDate date = LocalDate.parse(dateString, inputFormat);
                    dataDate = date.format(outputFormat);
                } catch (Exception e) {
                    throw new Exception("数据日期单元格日期格式无效: " + dateString);
                }
                break;
            default:
                throw new Exception("数据日期单元格不是日期或数字类型");
        }
        workbook.close();
        return dataDate;
    }

}
