package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.CommonPageParam;
import cn.sdata.om.al.entity.InvestorContactsParam;
import cn.sdata.om.al.entity.InvestorContactsView;
import cn.sdata.om.al.enums.MapKey;
import cn.sdata.om.al.vo.InvestorContactVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.mapper.InvestorContactsMapper;
import cn.sdata.om.al.entity.InvestorContacts;
import org.springframework.transaction.annotation.Transactional;

import static cn.sdata.om.al.constant.BaseConstant.INVESTOR_TYPE;

@Service
@AllArgsConstructor
public class InvestorContactsService extends ServiceImpl<InvestorContactsMapper, InvestorContacts> {

    private final InvestorContactsViewService investorContactsViewService;

    /**
     * 通过参数组合成数据库数据,保存用
     * @param investorContactsParam 参数
     * @return List
     */
    public List<InvestorContacts> getByParam(InvestorContactsParam investorContactsParam){
        List<InvestorContacts> investorContactsList = new ArrayList<>();
        if (investorContactsParam == null) {
            throw new RuntimeException("参数不得为空");
        }
        String investor = investorContactsParam.getInvestor();
        String recipient = investorContactsParam.getRecipient();
        String recipientCc = investorContactsParam.getRecipientCc();
        Set<String> productIds = investorContactsParam.getProductId();
        String sztPath = investorContactsParam.getSztPath();
        Map<String, Set<String>> disclosureMethod = investorContactsParam.getDisclosureMethod();
        if (productIds == null || disclosureMethod == null) {
            throw new RuntimeException("账套以及披露方式不得为空");
        }
        for (String productId : productIds) {
            for (Map.Entry<String, Set<String>> entry : disclosureMethod.entrySet()) {
                Set<String> handlers = entry.getValue();
                String method = entry.getKey();
                for (String handler : handlers) {
                    InvestorContacts investorContacts = new InvestorContacts();
                    investorContacts.setInvestor(investor);
                    investorContacts.setMethod(method);
                    investorContacts.setHandler(handler);
                    investorContacts.setRecipient(recipient);
                    investorContacts.setRecipientCc(recipientCc);
                    investorContacts.setProductId(productId);
                    investorContacts.setSztPath(sztPath);
                    investorContactsList.add(investorContacts);
                }
            }
        }
        return investorContactsList;
    }

    @SuppressWarnings("unchecked")
    public Page<InvestorContactVO> getPageResult(CommonPageParam<InvestorContacts> commonPageParam, String type){
        List<String> investors = null, products = null, methods = null;
        Page<InvestorContactVO> page = new Page<>();
        page.setSize(commonPageParam.getSize());
        page.setCurrent(commonPageParam.getCurrent());
        Map<String, Object> param = commonPageParam.getParam();
        if (param != null) {
            investors = (List<String>) param.get(MapKey.INVESTOR_PARAM_INVESTORS.getFrontValue());
            products = (List<String>) param.get(MapKey.INVESTOR_PARAM_PRODUCTS.getFrontValue());
            methods = (List<String>) param.get(MapKey.INVESTOR_PARAM_METHODS.getFrontValue());
        }
        return this.getBaseMapper().pageResult(investors, products, methods, type, page);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveFromParam(InvestorContactsParam investorContactsParam, String type){
        List<InvestorContacts> investorContactsList = this.getByParam(investorContactsParam);
        this.saveBatch(investorContactsList);
        String id = IdWorker.getIdStr();
        List<InvestorContactsView> result =
                investorContactsList.stream()
                        .map(contacts -> new InvestorContactsView(id, contacts.getId(), type)).collect(Collectors.toList());
        investorContactsViewService.saveBatch(result);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFromParam(InvestorContactsParam investorContactsParam, String type){
        Objects.requireNonNull(investorContactsParam, "更新时参数不得为空");
        String id = investorContactsParam.getId();
        Objects.requireNonNull(id, "更新时ID不得为空");
        //查询关联的数据并删除
        List<String> ids = getSubIds(id);
        LambdaQueryWrapper<InvestorContactsView> viewLambdaQueryWrapper = new LambdaQueryWrapper<>();
        viewLambdaQueryWrapper.eq(InvestorContactsView::getId, id);
        investorContactsViewService.remove(viewLambdaQueryWrapper);
        this.removeBatchByIds(ids);
        //合成真实数据
        List<InvestorContacts> investorContactsList = this.getByParam(investorContactsParam);
        //保存并获取ID
        this.saveBatch(investorContactsList);
        List<InvestorContactsView> result =
                investorContactsList.stream()
                        .map(contacts -> new InvestorContactsView(id, contacts.getId(), type))
                        .collect(Collectors.toList());
        investorContactsViewService.saveBatch(result);

    }

    /**
     * 通过id获取参数,回显用
     * @param id id
     * @return InvestorContactsParam
     */
    public InvestorContactsParam getParamById(String id){
        List<String> ids = getSubIds(id);
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("未查到相关数据");
        }
        LambdaQueryWrapper<InvestorContacts> queryConcatWrapper = new LambdaQueryWrapper<>();
        queryConcatWrapper.in(InvestorContacts::getId, ids);
        List<InvestorContacts> contacts = this.list(queryConcatWrapper);
        InvestorContactsParam investorContactsParam = new InvestorContactsParam();
        for (InvestorContacts contact : contacts) {
            investorContactsParam.setId(id);
            investorContactsParam.setInvestor(contact.getInvestor());
            investorContactsParam.setRecipient(contact.getRecipient());
            investorContactsParam.setRecipientCc(contact.getRecipientCc());
            investorContactsParam.setSztPath(contact.getSztPath());
            Set<String> productIds = investorContactsParam.getProductId();
            if (productIds == null) {
                productIds = new LinkedHashSet<>();
                investorContactsParam.setProductId(productIds);
            }
            productIds.add(contact.getProductId());
            Map<String, Set<String>> disclosureMethod = investorContactsParam.getDisclosureMethod();
            String method = contact.getMethod();
            if (disclosureMethod == null) {
                disclosureMethod= new HashMap<>();
                investorContactsParam.setDisclosureMethod(disclosureMethod);
            }
            Set<String> methods = disclosureMethod.computeIfAbsent(method, k -> new LinkedHashSet<>());
            methods.add(contact.getHandler());
        }
        return investorContactsParam;
    }

    private List<String> getSubIds(String id) {
        LambdaQueryWrapper<InvestorContactsView> queryViewWrapper = new LambdaQueryWrapper<>();
        queryViewWrapper.eq(InvestorContactsView::getId, id);
        List<InvestorContactsView> list = investorContactsViewService.list(queryViewWrapper);
        return list.stream().map(InvestorContactsView::getSubId).collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class)
    public void removeAll(String id, String type) {
        if (id == null) {
            throw new RuntimeException("删除时ID不得为空");
        }
        List<String> ids = getSubIds(id);
        LambdaQueryWrapper<InvestorContactsView> viewLambdaQueryWrapper = new LambdaQueryWrapper<>();
        viewLambdaQueryWrapper.eq(InvestorContactsView::getId, id);
        viewLambdaQueryWrapper.eq(InvestorContactsView::getType, type);
        investorContactsViewService.remove(viewLambdaQueryWrapper);
        this.removeBatchByIds(ids);
    }

    public List<InvestorContactVO> getListResult(String type) {
        return this.getBaseMapper().listResult(type);
    }
}
