package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.investNetReport.LifeInsuranceValuationTableRecords;
import cn.sdata.om.al.mapper.LifeInsuranceValuationTableRecordsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/17 15:34
 * @Version 1.0
 */
@Service
public class LifeInsuranceValuationTableRecordsService extends ServiceImpl<LifeInsuranceValuationTableRecordsMapper, LifeInsuranceValuationTableRecords> {

    /**
     * 获取数据日期对应的最新的寿险-投连估值表下载记录数据
     *
     * @param valuationDate   数据日期
     * @param accountSetCodes 账套编号
     * @return
     */
    public List<LifeInsuranceValuationTableRecords> getLatestList(String valuationDate, List<String> accountSetCodes) {
        if (StringUtils.isBlank(valuationDate)) {
            return Lists.newArrayList();
        }
        return getBaseMapper().getLatestList(valuationDate, accountSetCodes);
    }
}
