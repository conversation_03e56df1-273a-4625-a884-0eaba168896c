package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import com.github.pagehelper.PageInfo;

public interface LogFYBService {
    PageInfo<LogFYBGenerateFileRecord> getLogFYBGenerateFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBDownloadFileRecord> getLogFYBDownloadFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBImportO32Record> getLogFYBImportO32Record(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBO32ConfirmRecord> getLogFYBO32ConfirmRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);


    PageInfo<LogFYBSendMailRecord> getLogFYBSendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBSyncPayStatusRecord> getLogFYBSyncPayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBUpdatePayStatusRecord> getLogFYBUpdatePayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYBRPARecord> getLogFYBRPARecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    Boolean reExecuteRpa(String rpaLogId);

}
