package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import com.github.pagehelper.PageInfo;

public interface LogFYIService {
    PageInfo<LogFYIGenerateFileRecord> getLogFYIGenerateFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYIDownloadFileRecord> getLogFYIDownloadFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYIImportO32Record> getLogFYIImportO32Record(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYIO32ConfirmRecord> getLogFYIO32ConfirmRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);


    PageInfo<LogFYISendMailRecord> getLogFYISendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYISyncPayStatusRecord> getLogFYISyncPayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYIUpdatePayStatusRecord> getLogFYIUpdatePayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogFYIUploadRecord> getLogFYIUploadRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);
}
