package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;

/**
 * 开基确认单日志服务接口
 */
public interface LogOpenFundConfirmationService {

    /**
     * 记录上传日志
     */
    void logUpload(String fileName, String filePath, String fileId, String confirmationId, String operator, OperationStatus status);

    /**
     * 记录下载日志
     */
    void logDownload(String fileName, String filePath, String fileId, String confirmationId, String operator, OperationStatus status);

    /**
     * 记录邮件发送日志
     */
    void logMailSend(String confirmationId, String taskName, SendMethod sendMethod, String mailStatus, String operator, String mailLogId);

    /**
     * 记录OCR确认日志
     */
    void logOcrConfirm(String confirmationId, String fileName, String accountSetName, String transactionChannel, String operator);

    /**
     * 记录删除日志
     */
    void logDelete(String confirmationId, String fileName, String accountSetName, String transactionChannel, String operator);

    /**
     * 删除确认单相关的所有日志
     */
    void deleteLogsByConfirmationId(String confirmationId);

    /**
     * 获取上传日志
     */
    Page<LogOpenFundConfirmationUpload> getUploadLogs(Page<LogOpenFundConfirmationUpload> page, Date startDate, Date endDate);

    /**
     * 获取下载日志
     */
    Page<LogOpenFundConfirmationDownload> getDownloadLogs(Page<LogOpenFundConfirmationDownload> page, Date startDate, Date endDate);

    /**
     * 获取邮件发送日志
     */
    Page<LogOpenFundConfirmationMail> getMailLogs(Page<LogOpenFundConfirmationMail> page, Date startDate, Date endDate);

    /**
     * 获取OCR确认日志
     */
    Page<LogOpenFundConfirmationOcr> getOcrLogs(Page<LogOpenFundConfirmationOcr> page, Date startDate, Date endDate);

    /**
     * 获取删除记录日志
     */
    Page<LogOpenFundConfirmationDelete> getDeleteLogs(Page<LogOpenFundConfirmationDelete> page, Date startDate, Date endDate);
}
