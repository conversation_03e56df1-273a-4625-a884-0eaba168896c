package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;

/**
 * 开基对账单日志服务接口
 */
public interface LogOpenFundReconciliationService {

    /**
     * 记录上传日志
     */
    void logUpload(String fileName, String filePath, String fileId, String reconciliationId, String operator, OperationStatus status);

    /**
     * 记录下载日志
     */
    void logDownload(String fileName, String filePath, String fileId, String reconciliationId, String operator, OperationStatus status);

    /**
     * 记录邮件发送日志
     */
    void logMailSend(String reconciliationId, String taskName, SendMethod sendMethod, String mailStatus, String operator, String mailLogId);

    /**
     * 记录OCR确认日志
     */
    void logOcrConfirm(String reconciliationId, String fileName, String accountSetName, String transactionChannel, String operator);

    /**
     * 记录删除日志
     */
    void logDelete(String reconciliationId, String fileName, String accountSetName, String transactionChannel, String operator);

    /**
     * 删除对账单相关的所有日志
     */
    void deleteLogsByReconciliationId(String reconciliationId);

    /**
     * 获取上传日志
     */
    Page<LogOpenFundReconciliationUpload> getUploadLogs(Page<LogOpenFundReconciliationUpload> page, Date startDate, Date endDate);

    /**
     * 获取下载日志
     */
    Page<LogOpenFundReconciliationDownload> getDownloadLogs(Page<LogOpenFundReconciliationDownload> page, Date startDate, Date endDate);

    /**
     * 获取邮件发送日志
     */
    Page<LogOpenFundReconciliationMail> getMailLogs(Page<LogOpenFundReconciliationMail> page, Date startDate, Date endDate);

    /**
     * 获取OCR确认日志
     */
    Page<LogOpenFundReconciliationOcr> getOcrLogs(Page<LogOpenFundReconciliationOcr> page, Date startDate, Date endDate);

    /**
     * 获取删除记录日志
     */
    Page<LogOpenFundReconciliationDelete> getDeleteLogs(Page<LogOpenFundReconciliationDelete> page, Date startDate, Date endDate);
}
