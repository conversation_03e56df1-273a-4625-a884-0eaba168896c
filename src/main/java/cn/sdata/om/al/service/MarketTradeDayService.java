package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.MarketTradeDay;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.MarketTradeDayMapper;
import cn.sdata.om.al.utils.C;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DATE_FORMAT_PATTERN;
import static cn.sdata.om.al.constant.BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MarketTradeDayService extends ServiceImpl<MarketTradeDayMapper, MarketTradeDay> {

    public String getNetValueTradeDay(String baseDate, String tradeType, int offset) {
        if (baseDate == null) {
            baseDate = DateUtil.format(DateUtil.date(), BaseConstant.DATE_FORMAT_PATTERN);
        }
        MarketTradeDay offsetTradeDay = this.getOffsetTradeDay(tradeType, baseDate, offset);
        if (offsetTradeDay == null && offset == 0) {
            offsetTradeDay = this.getOffsetTradeDay(tradeType, baseDate, -1);
        }
        if (offsetTradeDay == null) {
            throw new RuntimeException("获取交易日失败");
        }
        Integer date = offsetTradeDay.getDate();
        return DateUtil.format(DateUtil.parse(date.toString(), BaseConstant.DATE_FORMAT_PATTERN), HORIZONTAL_DATE_FORMAT_PATTERN);
    }

    public Map<String, String> getNetValueBetweenTradeDay(String baseDate, String tradeType, int offset) {
        if (baseDate == null) {
            baseDate = DateUtil.format(DateUtil.date(), BaseConstant.DATE_FORMAT_PATTERN);
        }
        if (0 == offset) {
            boolean isLastWorkDay = judgeLastWorkDay(baseDate, tradeType);
            if (isLastWorkDay) {
                List<String> resultList = getAllHoliday(baseDate, tradeType);
                String startDate = this.getNetValueTradeDay(baseDate, tradeType, offset);
                String endDate = DateUtil.format(DateUtil.parse(resultList.get(resultList.size() - 1), BaseConstant.DATE_FORMAT_PATTERN), HORIZONTAL_DATE_FORMAT_PATTERN);
                return Map.of(BaseConstant.START_DATE_NAME, startDate, BaseConstant.END_DATE_NAME, endDate);
            }
        }
        String startDate = this.getNetValueTradeDay(baseDate, tradeType, offset);
        return Map.of(BaseConstant.START_DATE_NAME, startDate, BaseConstant.END_DATE_NAME, startDate);
    }

    public boolean judgeLastWorkDay(String baseDate, String tradeType) {
        //如果T+1不等于下一个自然日,说明T是最后一个工作日
        MarketTradeDay offsetTradeDay = this.getOffsetTradeDay(tradeType, baseDate, 1);
        //取下一个自然日
        String nextNaturalDay = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(baseDate, DATE_FORMAT_PATTERN), 1), DATE_FORMAT_PATTERN);
        boolean isLastWorkDay = false;
        if (offsetTradeDay != null && offsetTradeDay.getDate() != null) {
            isLastWorkDay = !offsetTradeDay.getDate().toString().equals(nextNaturalDay);
        }
        return isLastWorkDay;
    }

    public List<String> getAllHoliday(String baseDate, String tradeType) {
        List<String> nextHolidayList = this.getBaseMapper().getNextHolidayList(tradeType, baseDate);
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < nextHolidayList.size(); i++) {
            String holiday = nextHolidayList.get(i);
            String holidayNaturalDay = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(baseDate, DATE_FORMAT_PATTERN), 1 + i), DATE_FORMAT_PATTERN);
            if (holiday.equals(holidayNaturalDay)) {
                resultList.add(holiday);
            }
        }
        return resultList;
    }

    /**
     * 获取入参日期上一交易日
     *
     * @param date      日期格式：yyyy-MM-dd
     * @param tradeType 交易市场
     * @param tradeFlag 是否交易日
     * @return T-n交易日,日期格式：yyyy-MM-dd
     */
    public String getTradeDay(String date, String tradeType, String tradeFlag) {
        Assert.notBlank(date, "交易日期不能为空");

        Integer tradeDay = baseMapper.ltTradeDay(getNumberDate(date), tradeType, tradeFlag);

        return Optional.ofNullable(tradeDay)
                .map(item -> getStringDate(tradeDay))
                .orElseThrow(() -> new RuntimeException(StrUtil.format("{}上一交易日不存在", date)));
    }


    /**
     * 获取入参日期下若干个交易日
     *
     * @param date      日期格式：yyyy-MM-dd
     * @param tradeType 交易市场
     * @param tradeFlag 是否交易日
     * @return T-n交易日,日期格式：yyyy-MM-dd
     */
    public String getTradeDay(String date, String tradeType, String tradeFlag, int n) {
        Assert.notBlank(date, "交易日期不能为空");

        List<Integer> tradeDays = baseMapper.gtNTradeDay(getNumberDate(date), tradeType, tradeFlag, n);
        if (CollectionUtil.isNotEmpty(tradeDays) && tradeDays.size() == n) {
            Integer tradeDay = tradeDays.get(n - 1);
            return Optional.ofNullable(tradeDay)
                    .map(item -> getStringDate(tradeDay))
                    .orElseThrow(() -> new RuntimeException(StrUtil.format("{}下{}个交易日不存在", date, n)));
        }
        BusinessException.throwException(StrUtil.format("{}下{}个交易日不存在", date, n));
        return null;
    }

    /**
     * 是否为港股通/沪港通交易日
     *
     * @param date 日期
     * @return true/false
     */
    public boolean isTradeDay(String date) {
        Assert.notBlank(date, "日期不能为空");
        LambdaQueryWrapper<MarketTradeDay> wrapper = Wrappers.lambdaQuery(MarketTradeDay.class)
                .eq(MarketTradeDay::getDate, getNumberDate(date))
                .eq(MarketTradeDay::getTradeFlag, "1")
                .in(MarketTradeDay::getTradeDayType, List.of("02", "03"));
        return baseMapper.selectCount(wrapper) > 0;
    }

    /**
     * 获取数字类型日期,字符类型日期转换为数字类型日期
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return yyyyMMdd
     */
    private Integer getNumberDate(String date) {
        return Integer.parseInt(LocalDate.parse(date).format(DateTimeFormatter.BASIC_ISO_DATE));
    }

    /**
     * 获取字符串类型日期,数字类型日期转换为字符串类型日期
     *
     * @param date 日期格式：yyyyMMdd
     * @return yyyy-MM-dd
     */
    private String getStringDate(Integer date) {
        return LocalDate.parse(String.valueOf(date), DateTimeFormatter.BASIC_ISO_DATE)
                .format(DateTimeFormatter.ISO_LOCAL_DATE);

    }

    /**
     * 获取上周最后一天日期-勿删
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return 上周最后一天日期
     */
    private String getPreWeekLastDay(LocalDate date) {
        return C.defaultValue(date, LocalDate.now())
                .minusDays(date.getDayOfWeek().getValue()).toString();
    }

    /**
     * 获取上月最后一天日期-勿删
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return 上月最后一天日期
     */
    private String getPreMonthLastDay(LocalDate date) {
        return C.defaultValue(date, LocalDate.now()).minusMonths(1L)
                .with(TemporalAdjusters.lastDayOfMonth()).toString();
    }


    /**
     * 获取本月第一天
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return 上月最后一天日期
     */
    private String getMonthFirstDay(LocalDate date) {
        return C.defaultValue(date, LocalDate.now())
                .with(TemporalAdjusters.firstDayOfMonth())
                .toString();
    }

    /**
     * 获取本周第一天
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return 上周最后一天日期
     */
    private String getWeekFirstDay(LocalDate date) {
        return C.defaultValue(date, LocalDate.now())
                .with(DayOfWeek.MONDAY).toString();
    }

    /**
     * 反射调用,请勿删除
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateTradeDayBatch(Collection<MarketTradeDay> list) {
        // 使用截断不能用事务,暂时先放弃
        boolean remove = this.remove(new QueryWrapper<>());
        return this.saveBatch(list);
    }

    public MarketTradeDay getOffsetTradeDay(String tradeDayType, String baseDate, Integer offset) {
        if (offset == null) {
            return null;
        }
        if (offset.equals(0)) {
            return this.getBaseMapper().getOffsetTradeDayZero(tradeDayType, baseDate);
        }
        if (offset > 0) {
            offset = offset - 1;
            return this.getBaseMapper().getOffsetTradeDayPositive(tradeDayType, baseDate, offset);
        } else {
            offset = Math.abs(offset) - 1;
            return this.getBaseMapper().getOffsetTradeDayNegative(tradeDayType, baseDate, offset);
        }

    }


    private Map<Integer, Map<Integer, List<Date>>> getAllWeekDay(String tradeDayType) {
        Map<Integer, List<String>> yearCollect = groupByYear(tradeDayType);
        Map<Integer, Map<Integer, List<Date>>> resultMap = new TreeMap<>();
        for (Map.Entry<Integer, List<String>> entry : yearCollect.entrySet()) {
            Integer year = entry.getKey();
            List<String> dateList = entry.getValue();
            Map<Integer, List<Date>> weekList = dateList.stream().map(this::getDateByStr).collect(Collectors.groupingBy(DateUtil::weekOfYear));
            resultMap.put(year, weekList);
        }
        return resultMap;
    }

    public List<Date> getFirstWeekDay(String tradeDayType) {
        List<Date> weekFirstDate = new ArrayList<>();
        Map<Integer, Map<Integer, List<Date>>> allWeekDay = getAllWeekDay(tradeDayType);
        allWeekDay.forEach((year, weekDate) -> weekDate.forEach((week, dates) -> weekFirstDate.add(dates.get(0))));
        return weekFirstDate;
    }

    public Set<Date> getWeekDay(String tradeDayType, Integer count) {
        if (count < 1) {
            return new TreeSet<>();
        }
        int index = count - 1;
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allWeekDay = getAllWeekDay(tradeDayType);
        allWeekDay.forEach((year, weekDate) -> weekDate.forEach((week, dates) -> {
            int size = dates.size();
            if (size > index) {
                try {
                    result.add(dates.get(index));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }));
        return result;
    }

    public Set<Date> getMonthDay(String tradeDayType, Integer count) {
        if (count < 1) {
            return new TreeSet<>();
        }
        int index = count - 1;
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allMonthDay = getAllMonthDay(tradeDayType);
        allMonthDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> {
            int size = dates.size();
            if (size > index) {
                try {
                    result.add(dates.get(index));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }));
        return result;
    }

    public Set<Date> getMonthDay(String tradeDayType, Integer startCount, Integer endCount) {
        if (startCount < 1 || endCount < 1) {
            return new TreeSet<>();
        }
        int startIndex = startCount - 1;
        int endIndex = endCount - 1;
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allMonthDay = getAllMonthDay(tradeDayType);
        allMonthDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> {
            int size = dates.size();
            if (size > endCount) {
                try {
                    for (int i = startIndex; i <= endIndex; i++) {
                        result.add(dates.get(i));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }));
        return result;
    }

    public Set<Date> getFirstMonthDay(String tradeDayType, Integer startCount, Integer endCount) {
        if (startCount < 1 || endCount < 1) {
            return new TreeSet<>();
        }
        Set<Integer> firstMonth = Set.of(0, 3, 6, 9);
        int startIndex = startCount - 1;
        int endIndex = endCount - 1;
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allMonthDay = getAllMonthDay(tradeDayType);
        allMonthDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> {
            if (!firstMonth.contains(month)) {
                return;
            }
            int size = dates.size();
            if (size > endCount) {
                try {
                    for (int i = startIndex; i <= endIndex; i++) {
                        result.add(dates.get(i));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }));
        return result;
    }

    public Set<Date> getMonthLastDay(String tradeDayType) {
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allMonthDay = getAllMonthDay(tradeDayType);
        allMonthDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> {
            int size = dates.size();
            if (!dates.isEmpty()) {
                result.add(dates.get(size - 1));
            }
        }));
        return result;
    }

    public Set<Date> getMonthSecondLastDay(String tradeDayType) {
        Set<Date> result = new TreeSet<>();
        Map<Integer, Map<Integer, List<Date>>> allMonthDay = getAllMonthDay(tradeDayType);
        allMonthDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> {
            int size = dates.size();
            if (size > 1) {
                result.add(dates.get(size - 2));
            }
        }));
        return result;
    }

    public Set<Date> getFirstMonthDay(String tradeDayType) {
        Set<Date> monthFirstDate = new LinkedHashSet<>();
        Map<Integer, Map<Integer, List<Date>>> allWeekDay = getAllMonthDay(tradeDayType);
        allWeekDay.forEach((year, monthDate) -> monthDate.forEach((month, dates) -> monthFirstDate.add(dates.get(0))));
        return monthFirstDate;
    }

    public List<Date> getSecondWeekDay(String tradeDayType) {
        List<Date> weekSecondDate = new ArrayList<>();
        Map<Integer, Map<Integer, List<Date>>> allWeekDay = getAllWeekDay(tradeDayType);
        int offset = 1;
        allWeekDay.forEach((year, weekDate) -> weekDate.forEach((week, dates) -> {
            int size = dates.size();
            if (size > offset) {
                try {
                    weekSecondDate.add(dates.get(offset));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }));
        return weekSecondDate;
    }

    public Set<Date> getSecondMonthDay(String tradeDayType) {
        Set<Date> monthSecondDate = new LinkedHashSet<>();
        Map<Integer, Map<Integer, List<Date>>> allWeekDay = getAllMonthDay(tradeDayType);
        int offset = 1;
        allWeekDay.forEach((year, monthDate) -> monthDate.forEach((week, dates) -> {
            int size = dates.size();
            if (size > offset) {
                monthSecondDate.add(dates.get(offset));
            }
        }));
        return monthSecondDate;
    }

    public Map<Integer, Map<Integer, List<Date>>> getAllMonthDay(String tradeDayType) {
        Map<Integer, List<String>> yearCollect = groupByYear(tradeDayType);
        TreeMap<Integer, Map<Integer, List<Date>>> resultMap = new TreeMap<>();
        for (Map.Entry<Integer, List<String>> entry : yearCollect.entrySet()) {
            Integer year = entry.getKey();
            List<String> dateList = entry.getValue();
            Map<Integer, List<Date>> weekList = dateList.stream().map(this::getDateByStr).collect(Collectors.groupingBy(date -> {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                return calendar.get(Calendar.MONTH);
            }));
            resultMap.put(year, weekList);
        }
        return resultMap;
    }

    public Set<Date> getDays(String tradeDayType, List<Integer> days) {
        Set<Date> resultSet = new HashSet<>();
        for (Integer day : days) {
            List<String> allTradeDay = this.getBaseMapper().getAllTradeDay(tradeDayType);
            Map<Integer, List<String>> yearCollect = allTradeDay.stream()
                    .collect(Collectors.groupingBy(dateStr -> Integer.parseInt(dateStr.substring(0, 4)), TreeMap::new, Collectors.toList()));
            for (Map.Entry<Integer, List<String>> entry : yearCollect.entrySet()) {
                List<String> dateList = entry.getValue();
                Map<Integer, List<Date>> monthList = dateList.stream().map(this::getDateByStr).collect(Collectors.groupingBy(date -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    return calendar.get(Calendar.MONTH);
                }));
                monthList.forEach((month, dates) -> {
                    for (Date thisDate : dates) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(thisDate);
                        int thisDateInt = calendar.get(Calendar.DAY_OF_MONTH);
                        if (day == thisDateInt) {
                            resultSet.add(thisDate);
                            return;
                        } else if (day < thisDateInt) {
                            //跨月这里暂时没有处理
                            resultSet.add(thisDate);
                            return;
                        }
                    }
                });
            }
        }
        return resultSet;
    }

    public Set<Date> getLastMonthDays(String tradeDayType, List<Integer> days) {
        Set<Integer> lastMont = Set.of(2, 5, 8, 11);
        return getMonthDates(tradeDayType, days, lastMont);
    }

    public Set<Date> getFirstMonthDays(String tradeDayType, List<Integer> days) {
        Set<Integer> firstMonth = Set.of(0, 3, 6, 9);
        return getMonthDates(tradeDayType, days, firstMonth);
    }

    public Set<Date> getNaturalDay(List<String> startYDs, List<String> endYDs) {
        Set<Date> dates = new TreeSet<>();
        for (int i = 0; i < startYDs.size(); i++) {
            String startYD = startYDs.get(i);
            String endYD = endYDs.get(i);
            //以当前年份为基础,前后各推5年
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            int thisYear = calendar.get(Calendar.YEAR);
            int startYear = thisYear - 5;
            int endYear = thisYear + 5;
            for (int year = startYear; year <= endYear; year++) {
                String startDate = year + startYD;
                LocalDate startLocalDate = getLocalDateByStr(startDate);
                String endDate = year + endYD;
                LocalDate endLocalDate = getLocalDateByStr(endDate);
                dates.addAll(getDatesBetween(startLocalDate, endLocalDate));
            }
        }
        return dates;
    }

    private Set<Date> getDatesBetween(LocalDate start, LocalDate end) {
        Set<Date> dates = new TreeSet<>();
        LocalDate currentDate = start;
        while (!currentDate.isAfter(end)) {
            dates.add(Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }

    private Set<Date> getMonthDates(String tradeDayType, List<Integer> days, Set<Integer> lastMont) {
        Set<Date> resultSet = new TreeSet<>();
        for (Integer day : days) {
            List<String> allTradeDay = this.getBaseMapper().getAllTradeDay(tradeDayType);
            Map<Integer, List<String>> yearCollect = allTradeDay.stream()
                    .collect(Collectors.groupingBy(dateStr -> Integer.parseInt(dateStr.substring(0, 4)), TreeMap::new, Collectors.toList()));
            for (Map.Entry<Integer, List<String>> entry : yearCollect.entrySet()) {
                List<String> dateList = entry.getValue();
                Map<Integer, List<Date>> monthList = dateList.stream().map(this::getDateByStr).collect(Collectors.groupingBy(date -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    return calendar.get(Calendar.MONTH);
                }));
                monthList.forEach((month, dates) -> {
                    if (!lastMont.contains(month)) {
                        return;
                    }
                    for (Date thisDate : dates) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(thisDate);
                        int thisDateInt = calendar.get(Calendar.DAY_OF_MONTH);
                        if (day == thisDateInt) {
                            resultSet.add(thisDate);
                            return;
                        } else if (day < thisDateInt) {
                            //跨月这里暂时没有处理
                            resultSet.add(thisDate);
                            return;
                        }
                    }
                });
            }
        }
        return resultSet;
    }

    public Map<Integer, Map<Integer, List<Date>>> getAllMonthDay(String tradeDayType, String startDay, String endDay) {
        Map<Integer, List<String>> yearCollect = groupByYear(tradeDayType);
        TreeMap<Integer, Map<Integer, List<Date>>> resultMap = new TreeMap<>();
        for (Map.Entry<Integer, List<String>> entry : yearCollect.entrySet()) {
            Integer year = entry.getKey();
            List<String> dateList = entry.getValue();
            Map<Integer, List<Date>> monthList = dateList.stream().map(this::getDateByStr).collect(Collectors.groupingBy(date -> {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                return calendar.get(Calendar.MONTH);
            }));
            resultMap.put(year, monthList);
        }
        return resultMap;
    }

    private Map<Integer, List<String>> groupByYear(String tradeDayType) {
        List<String> allTradeDay = this.getBaseMapper().getAllTradeDay(tradeDayType);
        return allTradeDay.stream()
                .collect(Collectors.groupingBy(date -> {
                    Integer weekYear = getWeekYear(getCalendarByStr(date));
                    return Objects.requireNonNullElse(weekYear, 0);
                }, TreeMap::new, Collectors.toList()));
    }


    private Calendar getCalendarByStr(String str) {
        if (str == null) {
            return null;
        }
        try {
            SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT_PATTERN);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(format.parse(str));
            return calendar;
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private Integer getWeekYear(Calendar calendar) {
        if (calendar == null) {
            return null;
        }
        return calendar.getWeekYear();
    }

    private LocalDate getLocalDateByStr(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4, 6));
        int day = Integer.parseInt(dateStr.substring(6, 8));
        return LocalDate.of(year, month, day);
    }

    public Date getDateByStr(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4, 6)) - 1;
        int day = Integer.parseInt(dateStr.substring(6, 8));
        GregorianCalendar gregorianCalendar = new GregorianCalendar(year, month, day);
        return gregorianCalendar.getTime();
    }

    /**
     * 获取每日交易日,如果当日不是交易日,返回null
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getDaily(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        Integer tradeDay = baseMapper.eqTradeDay(getNumberDate(DateUtil.today()), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每月第一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getMonthFirst(String tradeType, String firstDayOfMonth) {
        Assert.notBlank(tradeType, "tradeType is null");

        if (StringUtils.isEmpty(firstDayOfMonth)) {
            firstDayOfMonth = DateUtil.formatDate(DateUtil.beginOfMonth(DateUtil.date()));
        }
        Integer tradeDay = baseMapper.geTradeDay(getNumberDate(firstDayOfMonth), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每月最后15号交易日,如果不是交易日,返回T-1
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getMonth15(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取本月15号
        String dateStr = LocalDate.now().withDayOfMonth(15).toString();
        Integer tradeDay = baseMapper.geTradeDay(getNumberDate(dateStr), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每月最后20号交易日,如果不是交易日,返回T-1
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getMonth20(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取本月20号
        String dateStr = LocalDate.now().withDayOfMonth(20).toString();
        Integer tradeDay = baseMapper.leTradeDay(getNumberDate(dateStr), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每月最后一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getMonthLast(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取本月最后一天
        String lastDayOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).toString();
        Integer tradeDay = baseMapper.leTradeDay(getNumberDate(lastDayOfMonth), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    public String getTOrT1TradeDay(String tradeType, String dataStr) {
        Assert.notBlank(tradeType, "tradeType is null");
        Assert.notBlank(dataStr, "dataStr is null");
        Integer tradeDay = baseMapper.leTradeDay(getNumberDate(dataStr), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每季度第一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getQuarterFirst(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取本季度第一天
        String firstDayOfQuarter = getStartOrEndDayOfQuarter(true).toString();
        Integer tradeDay = baseMapper.geTradeDay(getNumberDate(firstDayOfQuarter), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每季度最后一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getQuarterLast(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取本季度最后一天
        String lastDayOfQuarter = getStartOrEndDayOfQuarter(false).toString();
        Integer tradeDay = baseMapper.leTradeDay(getNumberDate(lastDayOfQuarter), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每年第一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getYearFirst(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取每年第一天
        String firstDayOfYear = LocalDate.now().with(TemporalAdjusters.firstDayOfYear()).toString();
        Integer tradeDay = baseMapper.geTradeDay(getNumberDate(firstDayOfYear), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    /**
     * 获取每年最后一个交易日
     *
     * @param tradeType 交易类型
     * @return 交易日
     */
    public String getYearLast(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        // 获取每年最后一天
        String lastDayOfYear = LocalDate.now().with(TemporalAdjusters.lastDayOfYear()).toString();
        Integer tradeDay = baseMapper.leTradeDay(getNumberDate(lastDayOfYear), tradeType, "1");
        return Optional.ofNullable(tradeDay).map(item -> getStringDate(tradeDay)).orElse(null);
    }

    public List<String> thisMonthTradeDay(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        int month = Integer.parseInt(StrUtil.sub(LocalDate.now().toString()
                .replace(StrUtil.DASHED, StrUtil.EMPTY), 0, 6));
        return C.stream(baseMapper.likeTradeDay(month, tradeType))
                .map(this::getStringDate).collect(Collectors.toList());
    }

    public List<String> thisQuarterTradeDay(String tradeType) {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 计算当前季度的第一个月
        int firstMonthOfQuarter = (now.getMonthValue() - 1) / 3 * 3 + 1;
        // 构建一个包含当前季度所有月份的列表
        List<String> quarterDates = new ArrayList<>();
        for (int i = firstMonthOfQuarter; i <= firstMonthOfQuarter + 2; i++) {
            // 创建一个代表该月份第一天的LocalDate对象
            LocalDate firstDayOfMonth = LocalDate.of(now.getYear(), i, 1);
            int month = Integer.parseInt(StrUtil.sub(firstDayOfMonth.toString()
                    .replace(StrUtil.DASHED, StrUtil.EMPTY), 0, 6));
            C.stream(baseMapper.likeTradeDay(month, tradeType))
                    .forEach(item -> quarterDates.add(getStringDate(item)));
        }
        return quarterDates;
    }

    public List<String> thisYearTradeDay(String tradeType) {
        Assert.notBlank(tradeType, "tradeType is null");
        int year = Integer.parseInt(StrUtil.sub(LocalDate.now().toString()
                .replace(StrUtil.DASHED, StrUtil.EMPTY), 0, 4));
        return C.stream(baseMapper.likeTradeDay(year, tradeType))
                .map(this::getStringDate).collect(Collectors.toList());
    }

    /**
     * 获取当前日期所在季度的开始日期和结束日期
     *
     * @param isFirst 如果为true，则返回本季度开始日期；如果为false，则返回本季度结束日期
     * @return 返回对应季度的第一天或最后一天的日期
     */
    public static LocalDate getStartOrEndDayOfQuarter(Boolean isFirst) {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取当前月份
        Month month = today.getMonth();

        // 根据当前月份找到所在季度的第一个月
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();

        // 计算所在季度的最后一个月
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);

        // 如果参数isFirst为true，则返回本季度的第一天（即季度首月的1号）
        if (isFirst) {
            return LocalDate.of(today.getYear(), firstMonthOfQuarter, 1);
        }
        // 否则，如果isFirst为false，则返回本季度的最后一天
        else {
            // 结束月份的最后一天取决于是否是闰年，对于2月来说会有所不同
            return LocalDate.of(today.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(today.isLeapYear()));
        }
    }

    public List<String> getThisMonthSpecialTradeDay(String ym) {
        return baseMapper.getThisMonthSpecialTradeDay(ym);
    }

    public List<String> getThisMonthNoTradeDay(String ym) {
        return baseMapper.getThisMonthNoTradeDay(ym);
    }

    /**
     * 获取净值播报功能对应的5个交易日
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return
     */
    public List<String> getNetReport5TradeDay(String date) {
        List<String> dates = Lists.newArrayList();
        if (!isDefaultMarketTradeDay(date)) {
            date = getDefaultMarketPreTradeDay(date);
        }
        if (null == date) {
            return Lists.newArrayList();
        }
        String preTradeDate = getDefaultMarketPreTradeDay(date);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(date, formatter);
        String lastWeekDate = localDate.minusWeeks(1).format(formatter);
        if (!isDefaultMarketTradeDay(lastWeekDate)) {
            lastWeekDate = getDefaultMarketPreTradeDay(lastWeekDate);
        }
        String lastMonthDate = localDate.minusMonths(1).format(formatter);
        if (!isDefaultMarketTradeDay(lastMonthDate)) {
            lastMonthDate = getDefaultMarketPreTradeDay(lastMonthDate);
        }
        String lastYearLastDate = localDate.minusYears(1).withMonth(12).withDayOfMonth(31).format(formatter);
        if (!isDefaultMarketTradeDay(lastYearLastDate)) {
            lastYearLastDate = getDefaultMarketPreTradeDay(lastYearLastDate);
        }
        dates.addAll(List.of(date, preTradeDate, lastWeekDate, lastMonthDate, lastYearLastDate));
        return dates;
    }

    /**
     * 获取沪深交易市场上一个交易日
     *
     * @param date
     * @return
     */
    public String getDefaultMarketPreTradeDay(String date) {
        Integer tradeDay = baseMapper.ltTradeDay(getNumberDate(date), "00", "1");
        if (null == tradeDay) {
            return null;
        }
        return getStringDate(tradeDay);
    }

    /**
     * 是否为沪深交易市场交易日
     *
     * @param date 日期格式：yyyy-MM-dd
     * @return
     */
    public Boolean isDefaultMarketTradeDay(String date) {
        Assert.notBlank(date, "交易日期不能为空");
        LambdaQueryWrapper<MarketTradeDay> wrapper = Wrappers.lambdaQuery(MarketTradeDay.class)
                .eq(MarketTradeDay::getDate, getNumberDate(date))
                .eq(MarketTradeDay::getTradeFlag, "1")
                .eq(MarketTradeDay::getTradeDayType, "00");
        return baseMapper.selectCount(wrapper) > 0;
    }

    /**
     * 获取沪深交易市场下一个交易日
     *
     * @param date
     * @return
     */
    public String getDefaultMarketNextTradeDay(String date) {
        Integer tradeDay = baseMapper.gtTradeDay(getNumberDate(date), "00", "1");
        if (null == tradeDay) {
            return null;
        }
        return getStringDate(tradeDay);
    }

    public List<String> getNonTradingDays(String beginDate, String endDate) {
        Integer begin = getNumberDate(beginDate);
        Integer end = getNumberDate(endDate);
        return baseMapper.getNonTradingDays(begin, end);
    }

    /**
     * 判断是否为银行间交易日
     * @param dateStr
     * @return
     */
    public boolean isMarketTradeDay(String dateStr,String type) {
        LambdaQueryWrapper<MarketTradeDay> wrapper = Wrappers.lambdaQuery(MarketTradeDay.class)
                .eq(MarketTradeDay::getDate, dateStr)
                .eq(MarketTradeDay::getTradeFlag, "1")
                .eq(MarketTradeDay::getTradeDayType, type);
        if (baseMapper.selectCount(wrapper) > 0) {
            return true;
        }
        return false;
    }
}




