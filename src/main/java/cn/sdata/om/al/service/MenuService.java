package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollUtil;
import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.mapper.MenuMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/9 15:02
 * @Version 1.0
 */
@Service
public class MenuService extends ServiceImpl<MenuMapper, Menu> {

    /**
     * 生成树
     *
     * @param menuList
     * @return
     */
    public static List<Menu> buildTree(List<Menu> menuList) {
        if (CollUtil.isEmpty(menuList)) {
            return new ArrayList<>();
        }
        Map<String, Menu> menuMap = menuList.stream().collect(Collectors.toMap(Menu::getId, i -> i, (n1, n2) -> n1));
        List<Menu> rootNodes = new ArrayList<>();
        for (Menu menu : menuList) {
            String parentId = menu.getParentId();
            if (null == parentId || !menuMap.containsKey(parentId)) {
                rootNodes.add(menu);
            } else {
                Menu parent = menuMap.get(parentId);
                parent.getChildren().add(menu);
            }
        }
        return rootNodes;
    }

    /**
     * 打印树状结构
     *
     * @param nodes 节点列表
     * @param level 当前层级（用于缩进）
     */
    public static void printTree(List<Menu> nodes, int level) {
        for (Menu node : nodes) {
            for (int i = 0; i < level; i++) {
                System.out.print("--");
            }
            System.out.println(node.getId());
            printTree(node.getChildren(), level + 1);
        }
    }
}
