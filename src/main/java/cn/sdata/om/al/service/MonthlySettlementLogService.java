package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.LogMSExportRecord;
import cn.sdata.om.al.entity.LogMSFileOptRecord;
import cn.sdata.om.al.entity.LogMSRPARecord;
import cn.sdata.om.al.entity.LogMSSendMailRecord;
import com.github.pagehelper.PageInfo;

public interface MonthlySettlementLogService {
    PageInfo<LogMSExportRecord> getLogMSExportRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogMSFileOptRecord> getLogMSFileOptRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogMSRPARecord> getLogMSRPARecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    PageInfo<LogMSSendMailRecord> getLogMSSendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate);

    Boolean reExecuteRpa(String rpaLogId);

}
