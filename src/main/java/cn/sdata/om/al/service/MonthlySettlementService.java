package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.MonthlySettlementList;
import cn.sdata.om.al.entity.MonthlySettlementListQuery;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface MonthlySettlementService {
    PageInfo<MonthlySettlementList> page(MonthlySettlementListQuery monthlySettlementListQuery);

    List<MonthlySettlementList> selectMonthlySettlementByIds(List<String> ids);

    String generateFiles(String date, List<String> orders);

    void excelContentHandleLogic(String filePath);

    List<String> upload(MultipartFile[] files, String date);

    void sendMail(File attachmentFile, String date, List<String> orders) throws Exception;

    void generateFilesSync(String date, List<String> orders, String username, String logId);

    int selectDownloadStatus(String date, String order);

    void sendMailV1(String date, List<String> orders, List<MonthlySettlementList> settlementLists);

}
