package cn.sdata.om.al.service;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.mapper.NetValueDisclosureT1BatchMapper;
import cn.sdata.om.al.entity.NetValueDisclosureT1Batch;

import java.util.*;

@Service
public class NetValueDisclosureT1BatchService extends ServiceImpl<NetValueDisclosureT1BatchMapper, NetValueDisclosureT1Batch> {

    public Set<String> getAllBatchProductIds() {
        Set<String> allProductIds = new HashSet<>();
        for (NetValueDisclosureT1Batch netValueDisclosureT1Batch : this.list()) {
            String productIdStr = netValueDisclosureT1Batch.getProductIds();
            if (productIdStr != null && !productIdStr.isEmpty()) {
                allProductIds.addAll(Arrays.asList(productIdStr.split(",")));
            }
        }
        return allProductIds;
    }

}
