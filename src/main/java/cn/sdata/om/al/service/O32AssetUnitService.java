package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.O32AssetUnit;
import cn.sdata.om.al.entity.O32AssetUnitQuery;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface O32AssetUnitService {
    boolean importO32AssetUnit(MultipartFile file);

    PageInfo<O32AssetUnit> page(O32AssetUnitQuery o32AssetUnitQuery);

    O32AssetUnit getById(String id);

    boolean saveOrUpdate(O32AssetUnit o32AssetUnit);

    List<CommonEntity> codeList();

    List<CommonEntity> nameList();

}
