package cn.sdata.om.al.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.HexUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.JobConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.MailUser;
import cn.sdata.om.al.enums.MailRuleMatchStatus;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.job.OpenFundMailConfirmationSendJob;
import cn.sdata.om.al.mapper.OpenFundConfirmationStatementMapper;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.ocr.OCRResult;
import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.open.OpenConstant;
import cn.sdata.om.al.open.ParseParam;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailPickService;
import cn.sdata.om.al.utils.DateNormalizer;
import cn.sdata.om.al.utils.SecureUtil;
import cn.sdata.om.al.utils.StringUtil;
import cn.sdata.om.al.vo.OpenFundConfirmationVO;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.openhft.hashing.LongHashFunction;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.sdata.om.al.constant.JobConstant.PRODUCT_ID;
import static cn.sdata.om.al.constant.JobConstant.REMOTE_FILE;
import static cn.sdata.om.al.enums.MailRuleMatchStatus.UNMATCHED;
import static cn.sdata.om.al.enums.OpenFundFields.CONFIRM_DATE;
import static cn.sdata.om.al.enums.OpenFundFields.DATA_DATE;
import static cn.sdata.om.al.qrtz.constant.CronConstant.*;

@Service
@Slf4j
public class OpenFundConfirmationStatementService extends ServiceImpl<OpenFundConfirmationStatementMapper, OpenFundConfirmationStatement> {

    private OCRUtil ocrUtil;
    private AccountFundInformationService accountFundInformationService;
    private AccountInformationService accountInformationService;
    private OpenFundFieldMappingService openFundFieldMappingService;
    private OpenFundConfirmationTypeService openFundConfirmationTypeService;
    private OpenFundConfirmationAccountService openFundConfirmationAccountService;
    private MailContentMapper mailContentMapper;
    private MailPickService mailPickService;
    private CronService cronService;
    private LogOpenFundConfirmationService logOpenFundConfirmationService;

    private static final ThreadLocal<List<OpenFundConfirmationStatement>> statementThreadLocal =
            ThreadLocal.withInitial(ArrayList::new);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${open.fund.confirmation.path:}")
    private String openConfirmationPath;

    @Autowired
    public void setAccountFundInformationService(AccountFundInformationService accountFundInformationService) {
        this.accountFundInformationService = accountFundInformationService;
    }

    @Autowired
    public void setOcrUtil(OCRUtil ocrUtil) {
        this.ocrUtil = ocrUtil;
    }

    @Autowired
    public void setOpenFundFieldMappingService(OpenFundFieldMappingService openFundFieldMappingService) {
        this.openFundFieldMappingService = openFundFieldMappingService;
    }

    @Autowired
    public void setAccountInformationService(AccountInformationService accountInformationService) {
        this.accountInformationService = accountInformationService;
    }

    @Autowired
    public void setOpenFundConfirmationTypeService(OpenFundConfirmationTypeService openFundConfirmationTypeService) {
        this.openFundConfirmationTypeService = openFundConfirmationTypeService;
    }

    @Autowired
    public void setOpenFundConfirmationAccountService(OpenFundConfirmationAccountService openFundConfirmationAccountService) {
        this.openFundConfirmationAccountService = openFundConfirmationAccountService;
    }

    @Autowired
    public void setMailContentMapper(MailContentMapper mailContentMapper) {
        this.mailContentMapper = mailContentMapper;
    }

    @Autowired
    public void setMailPickService(MailPickService mailPickService) {
        this.mailPickService = mailPickService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Autowired
    public void setLogOpenFundConfirmationService(LogOpenFundConfirmationService logOpenFundConfirmationService) {
        this.logOpenFundConfirmationService = logOpenFundConfirmationService;
    }

    public Page<OpenFundConfirmationVO> pageQuery(OpenFundConfirmationParam openFundConfirmationParam, Page<OpenFundConfirmationParam> page) {
        return this.baseMapper.pageQuery(openFundConfirmationParam, page);
    }

    public OpenFundConfirmationStatement getOpenFundConfirmationStatement(String id) {
        OpenFundConfirmationStatement openFundConfirmationStatement = this.getById(id);
        LambdaQueryWrapper<OpenFundConfirmationAccount> openFundConfirmationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationAccountLambdaQueryWrapper.eq(OpenFundConfirmationAccount::getOpenFundId, id);
        List<String> productIds = openFundConfirmationAccountService
                .list(openFundConfirmationAccountLambdaQueryWrapper).stream()
                .map(OpenFundConfirmationAccount::getProductId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<OpenFundConfirmationType> openFundConfirmationTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationTypeLambdaQueryWrapper.eq(OpenFundConfirmationType::getOpenFundId, id);
        List<String> businessTypes = openFundConfirmationTypeService
                .list(openFundConfirmationTypeLambdaQueryWrapper).stream()
                .map(OpenFundConfirmationType::getBusinessType)
                .collect(Collectors.toList());
        openFundConfirmationStatement.setProductIds(productIds);
        openFundConfirmationStatement.setBusinessTypes(businessTypes);
        openFundConfirmationStatement.setFileContent(Base64.encode(FileUtil.readBytes(openFundConfirmationStatement.getFilePath())));
        return openFundConfirmationStatement;
    }

    public void upload(@NonNull List<MultipartFile> files) {
        ParseParam parseParam = this.initMap();
        String operator = SecureUtil.currentUserName();

        for (MultipartFile file : files) {
            String originalFilename = file.getOriginalFilename();
            String filePath = openConfirmationPath + File.separator + IdWorker.getIdStr() + File.separator + originalFilename;
            String confirmationId = null;
            String fileId = null;
            OperationStatus status = OperationStatus.SUCCESS;

            try {
                byte[] bytes = file.getBytes();
                long hash = LongHashFunction.xx().hashBytes(bytes);
                fileId = HexUtil.toHex(hash);

                // 处理文件并获取创建的确认单ID
                List<String> createdIds = this.dealAndReturnIds(bytes, filePath, parseParam, true);
                if (!createdIds.isEmpty()) {
                    confirmationId = createdIds.get(0); // 取第一个ID
                }

            } catch (Exception e) {
                log.error("文件处理失败", e);
                status = OperationStatus.FAILED;
            } finally {
                // 记录上传日志 - 只有在有confirmationId时才记录，避免数据库约束错误
                if (confirmationId != null) {
                    logOpenFundConfirmationService.logUpload(originalFilename, filePath, fileId, confirmationId, operator, status);
                } else {
                    log.warn("确认单ID为空，跳过日志记录: {}", originalFilename);
                }
            }
        }
    }

    public FileInfo download(List<String> ids) {
        return download(ids, true);
    }

    public FileInfo download(List<String> ids, boolean logDownload) {
        List<OpenFundConfirmationStatement> statements = this.listByIds(ids);
        String operator = SecureUtil.currentUserName();
        OperationStatus status = OperationStatus.SUCCESS;

        if (statements.isEmpty()) {
            return null;
        }

        try {
            if (statements.size() == 1) {
                OpenFundConfirmationStatement openFundConfirmationStatement = statements.get(0);
                String filePath = openFundConfirmationStatement.getFilePath();
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileName(openFundConfirmationStatement.getAttachmentName());
                fileInfo.setFileData(FileUtil.readBytes(filePath));

                // 记录下载日志
                if (logDownload) {
                    logOpenFundConfirmationService.logDownload(
                            openFundConfirmationStatement.getAttachmentName(),
                            filePath,
                            openFundConfirmationStatement.getFileId(),
                            openFundConfirmationStatement.getId(),
                            operator,
                            status
                    );
                }

                return fileInfo;
            } else {
                FileInfo zipFile = dealZip(statements);

                // 记录批量下载日志
                if (logDownload) {
                    for (OpenFundConfirmationStatement statement : statements) {
                        logOpenFundConfirmationService.logDownload(
                                statement.getAttachmentName(),
                                statement.getFilePath(),
                                statement.getFileId(),
                                statement.getId(),
                                operator,
                                status
                        );
                    }
                }

                return zipFile;
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
            status = OperationStatus.FAILED;

            // 记录失败日志
            if (logDownload) {
                for (OpenFundConfirmationStatement statement : statements) {
                    logOpenFundConfirmationService.logDownload(
                            statement.getAttachmentName(),
                            statement.getFilePath(),
                            statement.getFileId(),
                            statement.getId(),
                            operator,
                            status
                    );
                }
            }
            throw e;
        }
    }

    private @NonNull FileInfo dealZip(@NonNull List<OpenFundConfirmationStatement> statements) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName("确认单.zip");
        try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
            for (int i = 0; i < statements.size(); i++) {
                OpenFundConfirmationStatement statement = statements.get(i);
                String filePath = statement.getFilePath();
                String attachmentName = statement.getAttachmentName();
                byte[] bytes = FileUtil.readBytes(filePath);
                String zipEntryName = (i + 1) + "_" + attachmentName;
                zos.putNextEntry(new ZipEntry(zipEntryName));
                try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                    int length;
                    byte[] buffer = new byte[1024];
                    while ((length = bis.read(buffer)) >= 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
            }
        } catch (Exception e) {
            throw new RuntimeException("生成zip出错", e);
        }
        fileInfo.setFileData(outputStream.toByteArray());
        return fileInfo;
    }

    /**
     * 处理文件并返回创建的确认单ID列表
     */
    private List<String> dealAndReturnIds(byte[] bytes, String filePath, ParseParam parseParam) {
        return this.dealWithReturn(bytes, filePath, parseParam, false);
    }

    /**
     * 处理文件并返回创建的确认单ID列表
     */
    private List<String> dealAndReturnIds(byte[] bytes, String filePath, ParseParam parseParam, boolean setNoneForUpload) {
        return this.dealWithReturn(bytes, filePath, parseParam, setNoneForUpload);
    }

    /**
     * 业务处理
     * @param bytes  pdf文件byte流
     * @param filePath pdf文件保存路径
     * @param parseParam  业务处理参数
     */
    public void deal(byte[] bytes, String filePath, ParseParam parseParam) {
        dealWithReturn(bytes, filePath, parseParam, false);
    }

    /**
     * 业务处理并返回创建的确认单ID列表
     * @param bytes  pdf文件byte流
     * @param filePath pdf文件保存路径
     * @param parseParam  业务处理参数
     * @return 创建的确认单ID列表
     */
    private List<String> dealWithReturn(byte[] bytes, String filePath, ParseParam parseParam, boolean setNoneForUpload) {
        List<String> createdIds = new ArrayList<>();
        try {
            FileUtil.writeBytes(bytes, new File(filePath));
            OCRResult ocrResult = ocrUtil.executeOCRv2(bytes);
            log.info("ocrResult:{}", ocrResult);
            this.createInstance().composeByMail(parseParam).parseOcrResult(ocrResult, parseParam).dealData(parseParam);
            List<OpenFundConfirmationStatement> allStatement = getAllStatement();
            // 仅upload场景赋值为NONE
            if (setNoneForUpload) {
                for (OpenFundConfirmationStatement statement : allStatement) {
                    statement.setEmailRuleMatchStatus(MailRuleMatchStatus.NONE);
                }
            }
            String newFileName = transFileName(parseParam, null);
            File rename = FileUtil.rename(new File(filePath), newFileName, true);
            allStatement.forEach(openFundConfirmationStatement -> {
                openFundConfirmationStatement.setFilePath(rename.getAbsolutePath());
                openFundConfirmationStatement.setAttachmentName(rename.getName());
                long hash = LongHashFunction.xx().hashBytes(bytes);
                openFundConfirmationStatement.setFileId(HexUtil.toHex(hash));
            });
            OpenFundConfirmationStatementService openFundConfirmationStatementService = (OpenFundConfirmationStatementService) AopContext.currentProxy();
            openFundConfirmationStatementService.saveResult();

            // 保存成功后，收集所有创建的确认单ID
            createdIds = allStatement.stream()
                    .map(OpenFundConfirmationStatement::getId)
                    .collect(Collectors.toList());

            statementThreadLocal.remove();
        } catch (Exception e) {
            log.error("文件解析异常", e);
            throw e; // 重新抛出异常，让上层处理
        } finally {
            statementThreadLocal.remove();
        }
        return createdIds;
    }

    public @NonNull ParseParam initMap() {
        ParseParam parseParam = new ParseParam();
        Map<String, String> suffixAdministrator = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getEmailSuffix,
                AccountFundInformation::getAdministrator,
                (oldOne, newOne) -> newOne));
        parseParam.setSuffixAdministrator(suffixAdministrator);
        Map<String, String> accountNameProductId = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getAccountName,
                AccountFundInformation::getProductId,
                (oldOne, newOne) -> newOne));
        parseParam.setAccountNameProductId(accountNameProductId);
        Map<String, String> accountIdProductName = accountInformationService.list().stream()
                .collect(Collectors.toMap(AccountInformation::getId,
                        AccountInformation::getFullProductName,
                        (oldOne, newOne) -> newOne));
        parseParam.setProductIdName(accountIdProductName);
        Map<String, OpenFundFieldMapping> keyFieldMapping = openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.KEY_CLASS.equals(mapping.getValueClass()))
                .collect(Collectors.toMap(OpenFundFieldMapping::getValue,
                        mapping -> mapping,
                        (oldMapping, newMapping) -> newMapping));
        Map<String, OpenFundFieldMapping> valueFieldMapping = openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.VALUE_CLASS.equals(mapping.getValueClass()))
                .collect(Collectors.toMap(OpenFundFieldMapping::getValue,
                        mapping -> mapping,
                        (oldMapping, newMapping) -> newMapping));
        parseParam.setKeyFieldMapping(keyFieldMapping);
        parseParam.setValueFieldMapping(valueFieldMapping);
        return parseParam;
    }

    private @NonNull OpenFundConfirmationStatementService createInstance() {
        statementThreadLocal.get().add(new OpenFundConfirmationStatement());
        return this;
    }

    public OpenFundConfirmationStatement getLastStatement() {
        List<OpenFundConfirmationStatement> list = statementThreadLocal.get();
        if (list.isEmpty()) {
            throw new IllegalStateException("没有找到OpenFundConfirmationStatement");
        }
        return list.get(list.size() - 1);
    }

    public List<OpenFundConfirmationStatement> getAllStatement() {
        return statementThreadLocal.get();
    }

    private @NonNull OpenFundConfirmationStatementService composeByMail(@NonNull ParseParam parseParam) {
        MailContent mailContent = parseParam.getMailContent();
        MailAttachment mailAttachment = parseParam.getMailAttachment();
        OpenFundConfirmationStatement openFundConfirmationStatement = getLastStatement();
        try {
            if (mailContent != null && mailAttachment != null) {
                openFundConfirmationStatement.setEmailId(mailContent.getId());
                openFundConfirmationStatement.setEmailReceivedTime(mailContent.getReceivedTime());
                openFundConfirmationStatement.setFilePath(mailAttachment.getFilePath());
                String mailFromStr = mailContent.getMailFromStr();
                MailUser mailUser = objectMapper.readValue(mailFromStr, new TypeReference<>() {
                });
                String mail = mailUser.getMail();
                String[] split = mail.split("@");
                Map<String, String> suffixAdministrator = parseParam.getSuffixAdministrator();
                String mailSuffix = "@" + split[1];
                String administrator = suffixAdministrator.get(mailSuffix);
                openFundConfirmationStatement.setTransactionChannel(Objects.requireNonNullElse(administrator, ""));
            } else {
                openFundConfirmationStatement.setEmailReceivedTime(new Date());
            }
        } catch (Exception e) {
            log.error("邮件组装过程异常", e);
        }
        return this;
    }

    private @NonNull OpenFundConfirmationStatementService parseOcrResult(@NonNull OCRResult ocrResult, @NonNull ParseParam parseParam) {
        Map<String, OpenFundFieldMapping> keyFieldMapping = parseParam.getKeyFieldMapping();
        OpenFundConfirmationStatement lastStatement = getLastStatement();
        List<Map<String, String>> usefulAndGroupCommonData = getUsefulAndGroupCommonData(ocrResult, parseParam);
        for (Map<String, String> usefulAndGroupCommonDatum : usefulAndGroupCommonData) {
            usefulAndGroupCommonDatum.forEach((key, value) -> {
                OpenFundFieldMapping keyMapping = keyFieldMapping.get(key);
                if (keyMapping != null) {
                    String methodName = keyMapping.getMethodName();
                    String newValue = value;
                    if (CONFIRM_DATE.getValue().equals(methodName) || DATA_DATE.getValue().equals(methodName)) {
                        newValue = DateNormalizer.normalizeDate(value);
                    }
                    if (methodName.startsWith("get")) {
                        dealGet(methodName, newValue, lastStatement);
                    } else if (methodName.startsWith("set")) {
                        dealSet(methodName, newValue, lastStatement);
                    }
                }
            });
        }
        return this;
    }

    @SuppressWarnings("unchecked")
    private void dealGet(String methodName, String value, OpenFundConfirmationStatement lastStatement) {
        try {
            Class<OpenFundConfirmationStatement> aClass = OpenFundConfirmationStatement.class;
            Method method = aClass.getMethod(methodName);
            Object result = method.invoke(lastStatement);
            if (result instanceof List) {
                List<String> list = (List<String>) result;
                list.add(value);
            }
        } catch (Exception e) {
            log.error("映射异常", e);
        }
    }

    private void dealSet(String methodName, String value, OpenFundConfirmationStatement lastStatement) {
        try {
            Class<OpenFundConfirmationStatement> aClass = OpenFundConfirmationStatement.class;
            Method method = aClass.getMethod(methodName, String.class);
            method.invoke(lastStatement, value);
        } catch (Exception e) {
            log.error("映射异常", e);
        }
    }

    @SuppressWarnings("unchecked")
    private void dealData(@NonNull ParseParam parseParam) {
        //转换账套ID
        OpenFundConfirmationStatement openFundConfirmationStatement = getLastStatement();
        List<String> accountNames = openFundConfirmationStatement.getAccountNames();
        Map<String, String> accountNameProductId = parseParam.getAccountNameProductId();
        if (accountNameProductId != null) {
            openFundConfirmationStatement.setProductIds(new ArrayList<>(accountNames.size()));
            for (int i = 0; i < accountNames.size(); i++) {
                String accountName = accountNames.get(i);
                accountName = StringUtil.dealAccountName(accountName);
                String productId = accountNameProductId.get(accountName);
                openFundConfirmationStatement.getProductIds().add(i, productId);
            }
        }
        //转换value
        Map<String, OpenFundFieldMapping> valueFieldMapping = parseParam.getValueFieldMapping();
        List<String> methodNames = valueFieldMapping.values().stream().map(OpenFundFieldMapping::getMethodName).distinct().collect(Collectors.toList());
        for (String methodName : methodNames) {
            try {
                Class<OpenFundConfirmationStatement> aClass = OpenFundConfirmationStatement.class;
                Method method = aClass.getMethod(methodName);
                Object result = method.invoke(openFundConfirmationStatement);
                if (result instanceof List) {
                    List<String> list = (List<String>) result;
                    for (int i = 0; i < list.size(); i++) {
                        String value = list.get(i);
                        OpenFundFieldMapping openFundFieldMapping = valueFieldMapping.get(value);
                        if (openFundFieldMapping != null) {
                            list.set(i, openFundFieldMapping.getName());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("映射异常", e);
            }
        }
    }

    private @NonNull String transFileName(@NonNull ParseParam parseParam, OpenFundConfirmationStatement lastStatement) {
        Map<String, String> productIdName = parseParam.getProductIdName();
        if (lastStatement == null) {
            lastStatement = getLastStatement();
        }
        List<String> productIds = lastStatement.getProductIds();
        productIds = productIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<String> nameList = new ArrayList<>(productIds.size());
        for (int i = 0; i < productIds.size(); i++) {
            String productId = productIds.get(i);
            String productName = productIdName.get(productId);
            nameList.add(i, productName);
        }
        String productName = StringUtil.getProductName(nameList);
        String transactionChannel = lastStatement.getTransactionChannel();
        // 如果交易渠道为空，使用默认值
        if (transactionChannel == null || transactionChannel.trim().isEmpty()) {
            transactionChannel = "未知渠道";
        }
        String dataDate = lastStatement.getDataDate();
        String formatDate = "";
        if (dataDate != null && !dataDate.isEmpty()) {
            formatDate = DateUtil.format(DateUtil.parse(dataDate, BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN), BaseConstant.CHINESE_DATE_FORMAT_PATTERN);
        }
        return productName + "_交易_" + transactionChannel + "_" + formatDate + ".pdf";
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveResult() {
        List<OpenFundConfirmationStatement> allStatement = getAllStatement();
        removeAll();
        List<OpenFundConfirmationType> openFundConfirmationTypes = new ArrayList<>();
        List<OpenFundConfirmationAccount> openFundConfirmationAccounts = new ArrayList<>();
        this.saveBatch(allStatement);
        for (OpenFundConfirmationStatement openFundConfirmationStatement : allStatement) {
            String id = openFundConfirmationStatement.getId();
            List<String> businessTypes = openFundConfirmationStatement.getBusinessTypes();
            for (String businessType : businessTypes) {
                OpenFundConfirmationType openFundConfirmationType = new OpenFundConfirmationType();
                openFundConfirmationType.setOpenFundId(id);
                openFundConfirmationType.setBusinessType(businessType);
                openFundConfirmationTypes.add(openFundConfirmationType);
            }
            List<String> accountNames = openFundConfirmationStatement.getAccountNames();
            List<String> productIds = openFundConfirmationStatement.getProductIds();
            for (int i = 0; i < accountNames.size(); i++) {
                String accountName = accountNames.get(i);
                String productId = productIds.get(i);
                OpenFundConfirmationAccount openFundConfirmationAccount = new OpenFundConfirmationAccount();
                openFundConfirmationAccount.setOpenFundId(id);
                openFundConfirmationAccount.setAccountName(accountName);
                openFundConfirmationAccount.setProductId(productId);
                openFundConfirmationAccounts.add(openFundConfirmationAccount);
            }
        }
        openFundConfirmationTypeService.saveBatch(openFundConfirmationTypes);
        openFundConfirmationAccountService.saveBatch(openFundConfirmationAccounts);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeAll() {
        List<OpenFundConfirmationStatement> allStatement = getAllStatement();
        List<String> mailIds = allStatement.stream().map(OpenFundConfirmationStatement::getEmailId).filter(Objects::nonNull).collect(Collectors.toList());
        if (mailIds.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<OpenFundConfirmationStatement> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.in(OpenFundConfirmationStatement::getEmailId, mailIds);
        List<OpenFundConfirmationStatement> list = this.list(removeWrapper);
        List<String> ids = list.stream().map(OpenFundConfirmationStatement::getId).collect(Collectors.toList());
        if (!mailIds.isEmpty() && !ids.isEmpty()) {
            LambdaQueryWrapper<OpenFundConfirmationAccount> removeAccountIdWrapper = new LambdaQueryWrapper<>();
            removeAccountIdWrapper.in(OpenFundConfirmationAccount::getOpenFundId, ids);
            LambdaQueryWrapper<OpenFundConfirmationType> removeTypeIdWrapper = new LambdaQueryWrapper<>();
            removeTypeIdWrapper.in(OpenFundConfirmationType::getOpenFundId, ids);
            openFundConfirmationAccountService.remove(removeAccountIdWrapper);
            openFundConfirmationTypeService.remove(removeTypeIdWrapper);
            this.remove(removeWrapper);
        }
    }

    private @NonNull List<Map<String, String>> getUsefulAndGroupCommonData(@NonNull OCRResult ocrResult, @NonNull ParseParam parseParam) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, OpenFundFieldMapping> fieldMapping = parseParam.getKeyFieldMapping();
        Map<String, String> commonData = ocrResult.getCommonData();
        Map<String, String> usefulCommonData = new HashMap<>();
        commonData.forEach((key, value) -> {
            String simpleKeyStr = StringUtil.getKeyStr(key);
            if (fieldMapping.containsKey(simpleKeyStr)) {
                usefulCommonData.put(simpleKeyStr, value);
            }
        });
        int maxNo = getMaxNo(usefulCommonData);
        for (int i = 0; i < maxNo; i++) {
            result.add(new HashMap<>());
        }
        usefulCommonData.forEach((key, value) -> {
            int no = getNo(key);
            Map<String, String> subCommonData = result.get(no);
            String simpleKeyStr = StringUtil.getKeyStr(key);
            if (fieldMapping.containsKey(simpleKeyStr)) {
                subCommonData.put(simpleKeyStr, value);
            }
        });
        return result;
    }

    private int getMaxNo(@NonNull Map<String, String> commonData) {
        int max = 1;
        for (String key : commonData.keySet()) {
            String[] split = key.split("-");
            if (split.length > 1) {
                max = Integer.parseInt(split[1]);
            }
        }
        return max;
    }

    private int getNo(@NonNull String input) {
        int no = 0;
        String[] split = input.split("-");
        if (split.length > 1) {
            no = Integer.parseInt(split[1]);
        }
        return no;
    }

    @Transactional(rollbackFor = Exception.class)
    public void confirm(@NonNull OpenFundConfirmationStatement openFundConfirmationStatement) {
        Map<String, String> productIdAccountName = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getProductId,
                AccountFundInformation::getAccountName,
                (oldOne, newOne) -> newOne));
        String id = openFundConfirmationStatement.getId();
        OpenFundConfirmationStatement inDb = this.getById(id);
        if (inDb == null) {
            throw new IllegalStateException("OpenFundConfirmationStatement不存在");
        }

        // 使用确认时的最新数据生成文件名，而不是数据库中的旧数据
        // 将用户确认的产品ID和业务类型设置到inDb对象中，以便生成正确的文件名
        inDb.setProductIds(openFundConfirmationStatement.getProductIds());
        inDb.setBusinessTypes(openFundConfirmationStatement.getBusinessTypes());

        // 如果用户确认的数据中包含交易渠道信息，也需要同步
        if (openFundConfirmationStatement.getTransactionChannel() != null) {
            inDb.setTransactionChannel(openFundConfirmationStatement.getTransactionChannel());
        }

        // 如果交易渠道仍然为空，尝试从产品信息中推导
        if (inDb.getTransactionChannel() == null || inDb.getTransactionChannel().trim().isEmpty()) {
            // 从第一个产品ID对应的账户基金信息中获取管理机构作为交易渠道
            List<String> productIds = inDb.getProductIds();
            if (productIds != null && !productIds.isEmpty()) {
                String firstProductId = productIds.get(0);
                // 根据产品ID查找对应的管理机构
                Map<String, String> productIdAdministrator = accountFundInformationService.list().stream()
                        .collect(Collectors.toMap(AccountFundInformation::getProductId,
                                AccountFundInformation::getAdministrator,
                                (oldOne, newOne) -> newOne));
                String administrator = productIdAdministrator.get(firstProductId);
                if (administrator != null && !administrator.trim().isEmpty()) {
                    inDb.setTransactionChannel(administrator);
                }
            }
        }

        // 按照上传时的重命名规则生成新文件名
        String newFileName = transFileName(initMap(), inDb);
        File rename = FileUtil.rename(new File(inDb.getFilePath()), newFileName, true);

        // 更新文件路径和附件名称
        openFundConfirmationStatement.setFilePath(rename.getAbsolutePath());
        openFundConfirmationStatement.setAttachmentName(rename.getName());
        openFundConfirmationStatement.setOcrConfirmationStatus(OcrConfirmationStatus.CONFIRMED);
        this.updateById(openFundConfirmationStatement);
        List<String> productIds = openFundConfirmationStatement.getProductIds();
        Objects.requireNonNull(productIds, "产品id不能为空");
        LambdaQueryWrapper<OpenFundConfirmationAccount> openFundConfirmationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationAccountLambdaQueryWrapper.eq(OpenFundConfirmationAccount::getOpenFundId, id);
        openFundConfirmationAccountService.remove(openFundConfirmationAccountLambdaQueryWrapper);
        List<OpenFundConfirmationAccount> openFundConfirmationAccounts = new ArrayList<>();
        for (String productId : productIds) {
            OpenFundConfirmationAccount openFundConfirmationAccount = new OpenFundConfirmationAccount();
            openFundConfirmationAccount.setOpenFundId(id);
            openFundConfirmationAccount.setProductId(productId);
            String accountName = productIdAccountName.get(productId);
            openFundConfirmationAccount.setAccountName(accountName);
            openFundConfirmationAccounts.add(openFundConfirmationAccount);
        }
        openFundConfirmationAccountService.saveBatch(openFundConfirmationAccounts);
        List<String> businessTypes = openFundConfirmationStatement.getBusinessTypes();
        Objects.requireNonNull(businessTypes, "业务类型不能为空");
        LambdaQueryWrapper<OpenFundConfirmationType> openFundConfirmationTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationTypeLambdaQueryWrapper.eq(OpenFundConfirmationType::getOpenFundId, id);
        openFundConfirmationTypeService.remove(openFundConfirmationTypeLambdaQueryWrapper);
        List<OpenFundConfirmationType> openFundConfirmationTypes = new ArrayList<>();
        for (String businessType : businessTypes) {
            OpenFundConfirmationType openFundConfirmationType = new OpenFundConfirmationType();
            openFundConfirmationType.setOpenFundId(id);
            openFundConfirmationType.setBusinessType(businessType);
            openFundConfirmationTypes.add(openFundConfirmationType);
        }
        openFundConfirmationTypeService.saveBatch(openFundConfirmationTypes);

        // 记录OCR确认日志
        String operator = SecureUtil.currentUserName();
        String accountSetNames = openFundConfirmationAccounts.stream()
                .map(OpenFundConfirmationAccount::getAccountName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        logOpenFundConfirmationService.logOcrConfirm(
                id,
                openFundConfirmationStatement.getAttachmentName(),
                accountSetNames,
                openFundConfirmationStatement.getTransactionChannel(),
                operator
        );
    }

    public void removeAllInfo(String id) {
        OpenFundConfirmationStatement openFundConfirmationStatement = this.getById(id);
        Objects.requireNonNull(openFundConfirmationStatement, "id不存在");

        // 获取账套信息用于日志记录
        LambdaQueryWrapper<OpenFundConfirmationAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(OpenFundConfirmationAccount::getOpenFundId, id);
        List<OpenFundConfirmationAccount> accounts = openFundConfirmationAccountService.list(accountQueryWrapper);
        String accountSetNames = accounts.stream()
                .map(OpenFundConfirmationAccount::getAccountName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));

        String filePath = openFundConfirmationStatement.getFilePath();
        FileUtil.del(filePath);

        // 删除相关的上传和下载日志
        logOpenFundConfirmationService.deleteLogsByConfirmationId(id);

        LambdaQueryWrapper<OpenFundConfirmationAccount> openFundConfirmationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationAccountLambdaQueryWrapper.eq(OpenFundConfirmationAccount::getOpenFundId, id);
        openFundConfirmationAccountService.remove(openFundConfirmationAccountLambdaQueryWrapper);
        LambdaQueryWrapper<OpenFundConfirmationType> openFundConfirmationTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundConfirmationTypeLambdaQueryWrapper.eq(OpenFundConfirmationType::getOpenFundId, id);
        openFundConfirmationTypeService.remove(openFundConfirmationTypeLambdaQueryWrapper);
        this.removeById(id);

        // 记录删除日志
        String operator = SecureUtil.currentUserName();
        logOpenFundConfirmationService.logDelete(
                id,
                openFundConfirmationStatement.getAttachmentName(),
                accountSetNames,
                openFundConfirmationStatement.getTransactionChannel(),
                operator
        );
    }

    public List<String> getChannelList() {
        return accountFundInformationService.list().stream().map(AccountFundInformation::getAdministrator).distinct().collect(Collectors.toList());
    }

    public List<String> getBusinessList() {
        return openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.VALUE_CLASS.equals(mapping.getValueClass()))
                .map(OpenFundFieldMapping::getName).distinct().collect(Collectors.toList());
    }

    public void retryPickMail() {
        List<String> mailIds = this.list().stream().map(OpenFundConfirmationStatement::getEmailId).collect(Collectors.toList());
        List<String> changeIds = new ArrayList<>();
        List<MailContent> mailContents = mailContentMapper.listAllContent(mailIds);
        Map<String, String> mailContentsOriginal = mailContentMapper.listAllContent(mailIds).stream().collect(Collectors.toMap(MailContent::getId, MailContent::getBoxId, (oldOne, newOne) -> newOne));
        mailPickService.pickMailSimple(mailContents);
        for (MailContent mailContent : mailContents) {
            String id = mailContent.getId();
            String originalBoxId = mailContentsOriginal.get(id);
            String newBoxId = mailContent.getBoxId();
            if (!originalBoxId.equals(newBoxId)) {
                changeIds.add(id);
            }
        }
        LambdaUpdateWrapper<OpenFundConfirmationStatement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpenFundConfirmationStatement::getEmailRuleMatchStatus, UNMATCHED);
        updateWrapper.in(OpenFundConfirmationStatement::getEmailId, changeIds);
    }

    public void sendMail(List<String> ids) {
        List<OpenFundConfirmationStatement> openFundConfirmationStatements = this.listByIds(ids);
        LambdaQueryWrapper<OpenFundConfirmationAccount> openFundConfirmationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        Map<String, RemoteFileInfo> fileInfoMap = new HashMap<>();
        String operator = SecureUtil.currentUserName();

        for (OpenFundConfirmationStatement openFundConfirmationStatement : openFundConfirmationStatements) {
            String id = openFundConfirmationStatement.getId();
            openFundConfirmationAccountLambdaQueryWrapper.eq(OpenFundConfirmationAccount::getOpenFundId, id);
            List<String> productIds = openFundConfirmationAccountService.list(openFundConfirmationAccountLambdaQueryWrapper).stream()
                    .map(OpenFundConfirmationAccount::getProductId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            String transactionChannel = openFundConfirmationStatement.getTransactionChannel();
            for (String productId : productIds) {
                RemoteFileInfo remoteFileInfo = fileInfoMap.get(productId);
                if (remoteFileInfo == null) {
                    remoteFileInfo = new RemoteFileInfo();
                    remoteFileInfo.setLocation("local");
                    //fileInfoMap.put(productId, remoteFileInfo);
                    String dataDate = openFundConfirmationStatement.getDataDate();
                    fileInfoMap.put(dataDate + "_" + (StringUtils.isNotBlank(transactionChannel) ? transactionChannel + "_" : "") + productId, remoteFileInfo);
                }
                remoteFileInfo.getOpenFundId().add(id);
                remoteFileInfo.getOpenFile().add(new File(openFundConfirmationStatement.getFilePath()));
            }
            openFundConfirmationAccountLambdaQueryWrapper.clear();
        }
        log.info("组装的fileInfoMap为: = {}", JSON.toJSONString(fileInfoMap));
        executeSend(fileInfoMap);
    }

    public void executeSend(@NonNull Map<String, RemoteFileInfo> fileInfoMap) {
        List<String> jobIds = cronService.getJobIdByClass(OpenFundMailConfirmationSendJob.class);
        Map<String, Map<String, Object>> groupedMap = fileInfoMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> {
                            String key = entry.getKey();
                            int idx = key.indexOf("_");
                            return idx != -1 ? key.substring(0, idx) : "UNKNOWN"; // 处理无效键
                        },
                        // 向下游收集为子 Map
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)
                ));
        log.info("分组后的map = {}", JSON.toJSONString(groupedMap));
        if (MapUtil.isNotEmpty(groupedMap)) {
            groupedMap.forEach((key, value) -> {
                Map<String, RemoteFileInfo> res = new HashMap<>();
                if (MapUtil.isNotEmpty(value)) {
                    value.forEach((k, v) -> {
                        String[] kArr = k.split("_");
                        if (kArr.length == 2) {
                            res.put(kArr[1], (RemoteFileInfo) v);
                        } else if (kArr.length == 3) {
                            res.put(kArr[2], (RemoteFileInfo) v);
                        }
                        List<String> productIds = new ArrayList<>();
                        String dataDate = null;
                        String[] s = k.split("_");
                        if (s.length == 2) {
                            productIds.add(s[1]);
                            dataDate = s[0];
                        } else if (s.length == 3) {
                            productIds.add(s[2]);
                            dataDate = s[0];
                        }
                        if (StringUtils.isNotBlank(dataDate) && CollectionUtil.isNotEmpty(productIds)) {
                            log.info("-------------发送dataDate = {}的邮件", dataDate);
                            JobDataMap jobDataMap = new JobDataMap();
                            jobDataMap.put(PRODUCT_ID, productIds);
                            log.info("发送的附件信息为:{}", JSON.toJSONString(res));
                            jobDataMap.put(REMOTE_FILE, res);
                            jobDataMap.put(SYNC, true);
                            jobDataMap.put(START_DATE, dataDate);
                            jobDataMap.put(JobConstant.DATA_DATE, dataDate);
                            jobDataMap.put(END_DATE, dataDate);
                            jobDataMap.put(OPERATOR, SecureUtil.currentUserName());
                            jobDataMap.put(SEND_METHOD, SendMethod.MANUAL);
                            jobDataMap.put("STATEMENT_TYPE", "CONFIRMATION"); // 标识为确认单
                            cronService.startJobNow(jobIds, jobDataMap);
                        }
                    });
                }
            });
        }
    }
}
