package cn.sdata.om.al.service;

import cn.sdata.om.al.open.OpenConstant;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.NonNull;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.sdata.om.al.entity.OpenFundFieldMapping;
import cn.sdata.om.al.mapper.OpenFundFieldMappingMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OpenFundFieldMappingService extends ServiceImpl<OpenFundFieldMappingMapper, OpenFundFieldMapping> {

    public IPage<OpenFundFieldMapping> keyPageQuery(Page<OpenFundFieldMapping> page, String value) {
        return baseMapper.keyPageQuery(value, page);
    }

    public IPage<OpenFundFieldMapping> valuePageQuery(Page<OpenFundFieldMapping> page, String name, String value) {
        return baseMapper.valuePageQuery(name, value, page);
    }

    @Transactional
    public void keyAdd(@NonNull List<OpenFundFieldMapping> list) {
        list.forEach(openFundFieldMapping -> openFundFieldMapping.setValueClass(OpenConstant.KEY_CLASS));
        this.saveBatch(list);
    }

    @Transactional
    public void valueAdd(@NonNull List<OpenFundFieldMapping> list) {
        list.forEach(openFundFieldMapping -> openFundFieldMapping.setValueClass(OpenConstant.VALUE_CLASS));
        this.saveBatch(list);
    }

    @Transactional
    public void keyUpdate(@NonNull List<OpenFundFieldMapping> list) {
        keyDelete(list);
        List<OpenFundFieldMapping> newList = new ArrayList<>();
        for (OpenFundFieldMapping openFundFieldMapping : list) {
            String value = openFundFieldMapping.getValue();
            if (value.contains(";")) {
                String[] split = value.split(";");
                for (String subValue : split) {
                    OpenFundFieldMapping subMapping = new OpenFundFieldMapping();
                    subMapping.setName(openFundFieldMapping.getName());
                    subMapping.setType(openFundFieldMapping.getType());
                    subMapping.setMethodName(openFundFieldMapping.getMethodName());
                    subMapping.setValue(subValue);
                    subMapping.setValueClass(OpenConstant.KEY_CLASS);
                    newList.add(subMapping);
                }
            }else{
                openFundFieldMapping.setValueClass(OpenConstant.KEY_CLASS);
                newList.add(openFundFieldMapping);
            }
        }
        this.saveBatch(newList);
    }

    @Transactional
    public void valueUpdate(@NonNull List<OpenFundFieldMapping> list) {
        valueDelete(list);
        List<OpenFundFieldMapping> newList = new ArrayList<>();
        for (OpenFundFieldMapping openFundFieldMapping : list) {
            String value = openFundFieldMapping.getValue();
            if (value.contains(";")) {
                String[] split = value.split(";");
                for (String subValue : split) {
                    OpenFundFieldMapping subMapping = new OpenFundFieldMapping();
                    subMapping.setName(openFundFieldMapping.getName());
                    subMapping.setType(openFundFieldMapping.getType());
                    subMapping.setMethodName(openFundFieldMapping.getMethodName());
                    subMapping.setValue(subValue);
                    subMapping.setValueClass(OpenConstant.VALUE_CLASS);
                    newList.add(subMapping);
                }
            }else{
                openFundFieldMapping.setValueClass(OpenConstant.VALUE_CLASS);
                newList.add(openFundFieldMapping);
            }
        }
        this.saveBatch(newList);
    }

    @Transactional
    public void valueDelete(@NonNull List<OpenFundFieldMapping> list) {
        List<String> typeList = list.stream().map(OpenFundFieldMapping::getType).distinct().collect(Collectors.toList());
        List<String> nameList = list.stream().map(OpenFundFieldMapping::getName).distinct().collect(Collectors.toList());
        if (typeList.isEmpty() && nameList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<OpenFundFieldMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenFundFieldMapping::getValueClass, OpenConstant.VALUE_CLASS);
        if (!typeList.isEmpty()) {
            wrapper.in(OpenFundFieldMapping::getType, typeList);
        }
        if (!nameList.isEmpty()) {
            wrapper.in(OpenFundFieldMapping::getName, nameList);
        }
        this.remove(wrapper);
    }

    @Transactional
    public void keyDelete(@NonNull List<OpenFundFieldMapping> list) {
        List<String> typeList = list.stream().map(OpenFundFieldMapping::getType).distinct().collect(Collectors.toList());
        if (typeList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<OpenFundFieldMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenFundFieldMapping::getValueClass, OpenConstant.KEY_CLASS);
        wrapper.in(OpenFundFieldMapping::getType, typeList);
        this.remove(wrapper);
    }

    public OpenFundFieldMapping keyGetOne(String type) {
        return baseMapper.keyGetOne(type);
    }

    public OpenFundFieldMapping valueGetOne(String type, String name) {
        return baseMapper.valueGetOne(type, name);
    }


}
