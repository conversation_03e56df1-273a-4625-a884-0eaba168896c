package cn.sdata.om.al.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.HexUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.constant.JobConstant;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.MailUser;
import cn.sdata.om.al.enums.MailRuleMatchStatus;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.job.OpenFundMailSendJob;
import cn.sdata.om.al.mapper.OpenFundReconciliationStatementMapper;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.ocr.OCRResult;
import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.open.OpenConstant;
import cn.sdata.om.al.open.ParseParam;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.mail.MailPickService;
import cn.sdata.om.al.utils.DateNormalizer;
import cn.sdata.om.al.utils.SecureUtil;
import cn.sdata.om.al.utils.StringUtil;
import cn.sdata.om.al.vo.OpenFundReconciliationVO;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.openhft.hashing.LongHashFunction;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.sdata.om.al.constant.JobConstant.PRODUCT_ID;
import static cn.sdata.om.al.constant.JobConstant.REMOTE_FILE;
import static cn.sdata.om.al.enums.MailRuleMatchStatus.UNMATCHED;
import static cn.sdata.om.al.enums.OpenFundFields.CONFIRM_DATE;
import static cn.sdata.om.al.enums.OpenFundFields.DATA_DATE;
import static cn.sdata.om.al.qrtz.constant.CronConstant.*;

@Service
@Slf4j
public class OpenFundReconciliationStatementService extends ServiceImpl<OpenFundReconciliationStatementMapper, OpenFundReconciliationStatement> {
    private OCRUtil ocrUtil;
    private AccountFundInformationService accountFundInformationService;
    private AccountInformationService accountInformationService;
    private OpenFundFieldMappingService openFundFieldMappingService;
    private OpenFundReconciliationAccountService openFundReconciliationAccountService;
    private MailContentMapper mailContentMapper;
    private MailPickService mailPickService;
    private CronService cronService;
    private LogOpenFundReconciliationService logOpenFundReconciliationService;

    private static final ThreadLocal<List<OpenFundReconciliationStatement>> statementThreadLocal =
            ThreadLocal.withInitial(ArrayList::new);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${open.fund.reconciliation.path:}")
    private String openReconciliationPath;

    @Autowired
    public void setAccountFundInformationService(AccountFundInformationService accountFundInformationService) {
        this.accountFundInformationService = accountFundInformationService;
    }

    @Autowired
    public void setOcrUtil(OCRUtil ocrUtil) {
        this.ocrUtil = ocrUtil;
    }

    @Autowired
    public void setOpenFundFieldMappingService(OpenFundFieldMappingService openFundFieldMappingService) {
        this.openFundFieldMappingService = openFundFieldMappingService;
    }

    @Autowired
    public void setAccountInformationService(AccountInformationService accountInformationService) {
        this.accountInformationService = accountInformationService;
    }

    @Autowired
    public void setOpenFundReconciliationAccountService(OpenFundReconciliationAccountService openFundReconciliationAccountService) {
        this.openFundReconciliationAccountService = openFundReconciliationAccountService;
    }

    @Autowired
    public void setMailContentMapper(MailContentMapper mailContentMapper) {
        this.mailContentMapper = mailContentMapper;
    }

    @Autowired
    public void setMailPickService(MailPickService mailPickService) {
        this.mailPickService = mailPickService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Autowired
    public void setLogOpenFundReconciliationService(LogOpenFundReconciliationService logOpenFundReconciliationService) {
        this.logOpenFundReconciliationService = logOpenFundReconciliationService;
    }

    public Page<OpenFundReconciliationVO> pageQuery(OpenFundReconciliationParam openFundReconciliationParam, Page<OpenFundReconciliationParam> page) {
        return this.baseMapper.pageQuery(openFundReconciliationParam, page);
    }

    public OpenFundReconciliationStatement getOpenFundReconciliationStatement(String id) {
        OpenFundReconciliationStatement OpenFundReconciliationStatement = this.getById(id);
        LambdaQueryWrapper<OpenFundReconciliationAccount> openFundReconciliationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundReconciliationAccountLambdaQueryWrapper.eq(OpenFundReconciliationAccount::getOpenFundId, id);
        List<String> productIds = openFundReconciliationAccountService
                .list(openFundReconciliationAccountLambdaQueryWrapper).stream()
                .map(OpenFundReconciliationAccount::getProductId)
                .collect(Collectors.toList());
        OpenFundReconciliationStatement.setProductIds(productIds);
        OpenFundReconciliationStatement.setFileContent(Base64.encode(FileUtil.readBytes(OpenFundReconciliationStatement.getFilePath())));
        return OpenFundReconciliationStatement;
    }

    public void upload(@NonNull List<MultipartFile> files) {
        ParseParam parseParam = this.initMap();
        String operator = SecureUtil.currentUserName();

        for (MultipartFile file : files) {
            String originalFilename = file.getOriginalFilename();
            String filePath = openReconciliationPath + File.separator + IdWorker.getIdStr() + File.separator + originalFilename;
            String reconciliationId = null;
            String fileId = null;
            OperationStatus status = OperationStatus.SUCCESS;

            try {
                byte[] bytes = file.getBytes();
                long hash = LongHashFunction.xx().hashBytes(bytes);
                fileId = HexUtil.toHex(hash);

                // 处理文件并获取创建的对账单ID
                List<String> createdIds = this.dealAndReturnIds(bytes, filePath, parseParam);
                if (!createdIds.isEmpty()) {
                    reconciliationId = createdIds.get(0); // 取第一个ID
                }

            } catch (Exception e) {
                log.error("文件处理失败", e);
                status = OperationStatus.FAILED;
            } finally {
                // 记录上传日志 - 只有在有reconciliationId时才记录，避免数据库约束错误
                if (reconciliationId != null) {
                    logOpenFundReconciliationService.logUpload(originalFilename, filePath, fileId, reconciliationId, operator, status);
                } else {
                    log.warn("对账单ID为空，跳过日志记录: {}", originalFilename);
                }
            }
        }
    }

    public FileInfo download(List<String> ids) {
        return download(ids, true);
    }

    public FileInfo download(List<String> ids, boolean logDownload) {
        List<OpenFundReconciliationStatement> statements = this.listByIds(ids);
        String operator = SecureUtil.currentUserName();
        OperationStatus status = OperationStatus.SUCCESS;

        if (statements.isEmpty()) {
            return null;
        }

        try {
            if (statements.size() == 1) {
                OpenFundReconciliationStatement statement = statements.get(0);
                String filePath = statement.getFilePath();
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileName(statement.getAttachmentName());
                fileInfo.setFileData(FileUtil.readBytes(filePath));

                // 记录单个文件下载日志
                if (logDownload) {
                    logOpenFundReconciliationService.logDownload(
                            statement.getAttachmentName(),
                            statement.getFilePath(),
                            statement.getFileId(),
                            statement.getId(),
                            operator,
                            status
                    );
                }

                return fileInfo;
            } else {
                FileInfo zipFile = dealZip(statements);

                // 记录批量下载日志
                if (logDownload) {
                    for (OpenFundReconciliationStatement statement : statements) {
                        logOpenFundReconciliationService.logDownload(
                                statement.getAttachmentName(),
                                statement.getFilePath(),
                                statement.getFileId(),
                                statement.getId(),
                                operator,
                                status
                        );
                    }
                }

                return zipFile;
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
            status = OperationStatus.FAILED;

            // 记录失败日志
            if (logDownload) {
                for (OpenFundReconciliationStatement statement : statements) {
                    logOpenFundReconciliationService.logDownload(
                            statement.getAttachmentName(),
                            statement.getFilePath(),
                            statement.getFileId(),
                            statement.getId(),
                            operator,
                            status
                    );
                }
            }
            throw e;
        }
    }

    private @NonNull FileInfo dealZip(@NonNull List<OpenFundReconciliationStatement> statements) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName("对账单.zip");
        try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
            for (int i = 0; i < statements.size(); i++) {
                OpenFundReconciliationStatement statement = statements.get(i);
                String filePath = statement.getFilePath();
                String attachmentName = statement.getAttachmentName();
                byte[] bytes = FileUtil.readBytes(filePath);
                String zipEntryName = (i + 1) + "_" + attachmentName;
                zos.putNextEntry(new ZipEntry(zipEntryName));
                try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                    int length;
                    byte[] buffer = new byte[1024];
                    while ((length = bis.read(buffer)) >= 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
            }
        } catch (Exception e) {
            throw new RuntimeException("生成zip出错", e);
        }
        fileInfo.setFileData(outputStream.toByteArray());
        return fileInfo;
    }

    /**
     * 兼容upload场景
     */
    private List<String> dealAndReturnIds(byte[] bytes, String filePath, ParseParam parseParam) {
        return this.dealWithReturn(bytes, filePath, parseParam, true);
    }

    /**
     * 业务处理
     * @param bytes  pdf文件byte流
     * @param filePath pdf文件保存路径
     * @param parseParam  业务处理参数
     */
    public void deal(byte[] bytes, String filePath, ParseParam parseParam) {
        dealWithReturn(bytes, filePath, parseParam, false);
    }

    /**
     * 业务处理并返回创建的对账单ID列表
     * @param bytes  pdf文件byte流
     * @param filePath pdf文件保存路径
     * @param parseParam  业务处理参数
     * @return 创建的对账单ID列表
     */
    private List<String> dealWithReturn(byte[] bytes, String filePath, ParseParam parseParam, boolean setNoneForUpload) {
        List<String> createdIds = new ArrayList<>();
        try {
            FileUtil.writeBytes(bytes, new File(filePath));
            OCRResult ocrResult = ocrUtil.executeOCRv2(bytes);
            log.info("ocrResult:{}", ocrResult);
            this.createInstance().composeByMail(parseParam).parseOcrResult(ocrResult, parseParam).dealData(parseParam);
            List<OpenFundReconciliationStatement> allStatement = getAllStatement();
            // 仅upload场景赋值为NONE
            if (setNoneForUpload) {
                for (OpenFundReconciliationStatement statement : allStatement) {
                    statement.setEmailRuleMatchStatus(MailRuleMatchStatus.NONE);
                }
            }
            String newFileName = transFileName(parseParam, null);
            File rename = FileUtil.rename(new File(filePath), newFileName, true);
            allStatement.forEach(statement -> {
                statement.setFilePath(rename.getAbsolutePath());
                statement.setAttachmentName(rename.getName());
                long hash = LongHashFunction.xx().hashBytes(bytes);
                statement.setFileId(HexUtil.toHex(hash));
            });
            OpenFundReconciliationStatementService openFundReconciliationStatementService = (OpenFundReconciliationStatementService) AopContext.currentProxy();
            openFundReconciliationStatementService.saveResult();

            // 保存成功后，收集所有创建的对账单ID
            createdIds = allStatement.stream()
                    .map(OpenFundReconciliationStatement::getId)
                    .collect(Collectors.toList());

            statementThreadLocal.remove();
        } catch (Exception e) {
            log.error("文件解析异常", e);
            throw e; // 重新抛出异常，让上层处理
        } finally {
            statementThreadLocal.remove();
        }
        return createdIds;
    }

    public @NonNull ParseParam initMap() {
        ParseParam parseParam = new ParseParam();
        Map<String, String> suffixAdministrator = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getEmailSuffix,
                AccountFundInformation::getAdministrator,
                (oldOne, newOne) -> newOne));
        parseParam.setSuffixAdministrator(suffixAdministrator);
        Map<String, String> accountNameProductId = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getAccountName,
                AccountFundInformation::getProductId,
                (oldOne, newOne) -> newOne));
        parseParam.setAccountNameProductId(accountNameProductId);
        Map<String, String> accountIdProductName = accountInformationService.list().stream()
                .collect(Collectors.toMap(AccountInformation::getId,
                        AccountInformation::getFullProductName,
                        (oldOne, newOne) -> newOne));
        parseParam.setProductIdName(accountIdProductName);
        Map<String, OpenFundFieldMapping> keyFieldMapping = openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.KEY_CLASS.equals(mapping.getValueClass()))
                .collect(Collectors.toMap(OpenFundFieldMapping::getValue,
                        mapping -> mapping,
                        (oldMapping, newMapping) -> newMapping));
        Map<String, OpenFundFieldMapping> valueFieldMapping = openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.VALUE_CLASS.equals(mapping.getValueClass()))
                .collect(Collectors.toMap(OpenFundFieldMapping::getValue,
                        mapping -> mapping,
                        (oldMapping, newMapping) -> newMapping));
        parseParam.setKeyFieldMapping(keyFieldMapping);
        parseParam.setValueFieldMapping(valueFieldMapping);
        return parseParam;
    }

    private @NonNull OpenFundReconciliationStatementService createInstance() {
        statementThreadLocal.get().add(new OpenFundReconciliationStatement());
        return this;
    }

    public OpenFundReconciliationStatement getLastStatement() {
        List<OpenFundReconciliationStatement> list = statementThreadLocal.get();
        if (list.isEmpty()) {
            throw new IllegalStateException("没有找到OpenFundConfirmationStatement");
        }
        return list.get(list.size() - 1);
    }

    public List<OpenFundReconciliationStatement> getAllStatement() {
        return statementThreadLocal.get();
    }

    private @NonNull OpenFundReconciliationStatementService composeByMail(@NonNull ParseParam parseParam) {
        MailContent mailContent = parseParam.getMailContent();
        MailAttachment mailAttachment = parseParam.getMailAttachment();
        OpenFundReconciliationStatement OpenFundReconciliationStatement = getLastStatement();
        try {
            if (mailContent != null && mailAttachment != null) {
                OpenFundReconciliationStatement.setEmailId(mailContent.getId());
                OpenFundReconciliationStatement.setEmailReceivedTime(mailContent.getReceivedTime());
                OpenFundReconciliationStatement.setFilePath(mailAttachment.getFilePath());
                String mailFromStr = mailContent.getMailFromStr();
                MailUser mailUser = objectMapper.readValue(mailFromStr, new TypeReference<>() {
                });
                String mail = mailUser.getMail();
                String[] split = mail.split("@");
                Map<String, String> suffixAdministrator = parseParam.getSuffixAdministrator();
                String mailSuffix = "@" + split[1];
                String administrator = suffixAdministrator.get(mailSuffix);
                OpenFundReconciliationStatement.setTransactionChannel(Objects.requireNonNullElse(administrator, ""));
            } else {
                OpenFundReconciliationStatement.setEmailReceivedTime(new Date());
            }
        } catch (Exception e) {
            log.error("邮件组装过程异常", e);
        }
        return this;
    }

    private @NonNull OpenFundReconciliationStatementService parseOcrResult(@NonNull OCRResult ocrResult, @NonNull ParseParam parseParam) {
        Map<String, OpenFundFieldMapping> keyFieldMapping = parseParam.getKeyFieldMapping();
        OpenFundReconciliationStatement lastStatement = getLastStatement();
        List<Map<String, String>> usefulAndGroupCommonData = getUsefulAndGroupCommonData(ocrResult, parseParam);
        for (Map<String, String> usefulAndGroupCommonDatum : usefulAndGroupCommonData) {
            usefulAndGroupCommonDatum.forEach((key, value) -> {
                OpenFundFieldMapping keyMapping = keyFieldMapping.get(key);
                if (keyMapping != null) {
                    String methodName = keyMapping.getMethodName();
                    String newValue = value;
                    if (CONFIRM_DATE.getValue().equals(methodName) || DATA_DATE.getValue().equals(methodName)) {
                        newValue = DateNormalizer.normalizeDate(value);
                    }
                    if (methodName.startsWith("get")) {
                        dealGet(methodName, newValue, lastStatement);
                    } else if (methodName.startsWith("set")) {
                        dealSet(methodName, newValue, lastStatement);
                    }
                }
            });
        }
        List<Map<String, String>> tableData = ocrResult.getTableData();
        if (!tableData.isEmpty()) {
            Map<String, String> rowData = tableData.get(0);
            LambdaQueryWrapper<OpenFundFieldMapping> openFundFieldMappingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            openFundFieldMappingLambdaQueryWrapper.eq(OpenFundFieldMapping::getMethodName, CONFIRM_DATE.getValue());
            Map<String, String> dateValueMap = openFundFieldMappingService.list(openFundFieldMappingLambdaQueryWrapper).stream()
                    .collect(Collectors.toMap(OpenFundFieldMapping::getValue, OpenFundFieldMapping::getMethodName, (oldOne, newOne) -> newOne));
            dateValueMap.forEach((key, methodName) -> {
                String result = rowData.get(key);
                result = DateNormalizer.normalizeDate(result);
                if (result != null) {
                    dealSet(methodName, result, lastStatement);
                }
            });
        }
        return this;
    }

    @SuppressWarnings("unchecked")
    private void dealGet(String methodName, String value, OpenFundReconciliationStatement lastStatement) {
        try {
            Class<OpenFundReconciliationStatement> aClass = OpenFundReconciliationStatement.class;
            Method method = aClass.getMethod(methodName);
            Object result = method.invoke(lastStatement);
            if (result instanceof List) {
                List<String> list = (List<String>) result;
                list.add(value);
            }
        } catch (Exception e) {
            log.error("映射异常", e);
        }
    }

    private void dealSet(String methodName, String value, OpenFundReconciliationStatement lastStatement) {
        try {
            Class<OpenFundReconciliationStatement> aClass = OpenFundReconciliationStatement.class;
            Method method = aClass.getMethod(methodName, String.class);
            method.invoke(lastStatement, value);
        } catch (Exception e) {
            log.error("映射异常", e);
        }
    }

    private void dealData(@NonNull ParseParam parseParam) {
        //转换账套ID
        OpenFundReconciliationStatement OpenFundReconciliationStatement = getLastStatement();
        List<String> accountNames = OpenFundReconciliationStatement.getAccountNames();
        Map<String, String> accountNameProductId = parseParam.getAccountNameProductId();
        if (accountNameProductId != null) {
            OpenFundReconciliationStatement.setProductIds(new ArrayList<>(accountNames.size()));
            for (int i = 0; i < accountNames.size(); i++) {
                String accountName = accountNames.get(i);
                accountName = StringUtil.dealAccountName(accountName);
                String productId = accountNameProductId.get(accountName);
                OpenFundReconciliationStatement.getProductIds().add(i, productId);
            }
        }
    }

    private String transFileName(@NonNull ParseParam parseParam, OpenFundReconciliationStatement lastStatement) {
        Map<String, String> productIdName = parseParam.getProductIdName();
        if (lastStatement == null) {
            lastStatement = getLastStatement();
        }
        List<String> productIds = lastStatement.getProductIds();
        productIds = productIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<String> nameList = new ArrayList<>(productIds.size());
        for (int i = 0; i < productIds.size(); i++) {
            String productId = productIds.get(i);
            String productName = productIdName.get(productId);
            nameList.add(i, productName);
        }
        String productName = StringUtil.getProductName(nameList);
        String transactionChannel = lastStatement.getTransactionChannel();
        String dataDate = lastStatement.getDataDate();
        String formatDate = "";
        try {
            formatDate = DateUtil.format(DateUtil.parse(dataDate, BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN), BaseConstant.CHINESE_DATE_FORMAT_PATTERN);
        } catch (Exception e) {
            log.error("日期转换异常", e);
        }
        return productName + "_交易_" + transactionChannel + "_" + formatDate + ".pdf";
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveResult() {
        List<OpenFundReconciliationStatement> allStatement = getAllStatement();
        removeAll(allStatement);
        List<OpenFundReconciliationAccount> openFundReconciliationAccounts = new ArrayList<>();
        this.saveBatch(allStatement);
        for (OpenFundReconciliationStatement OpenFundReconciliationStatement : allStatement) {
            String id = OpenFundReconciliationStatement.getId();
            List<String> accountNames = OpenFundReconciliationStatement.getAccountNames();
            List<String> productIds = OpenFundReconciliationStatement.getProductIds();
            for (int i = 0; i < accountNames.size(); i++) {
                String accountName = accountNames.get(i);
                String productId = productIds.get(i);
                OpenFundReconciliationAccount openFundReconciliationAccount = new OpenFundReconciliationAccount();
                openFundReconciliationAccount.setOpenFundId(id);
                openFundReconciliationAccount.setAccountName(accountName);
                openFundReconciliationAccount.setProductId(productId);
                openFundReconciliationAccounts.add(openFundReconciliationAccount);
            }
        }
        openFundReconciliationAccountService.saveBatch(openFundReconciliationAccounts);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeAll(List<OpenFundReconciliationStatement> allStatement) {
        if (allStatement == null || allStatement.isEmpty()) return;
        List<String> emailIds = allStatement.stream()
                .map(OpenFundReconciliationStatement::getEmailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> fileIds = allStatement.stream()
                .map(OpenFundReconciliationStatement::getFileId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (emailIds.isEmpty() || fileIds.isEmpty()) return;

        LambdaQueryWrapper<OpenFundReconciliationStatement> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.in(OpenFundReconciliationStatement::getEmailId, emailIds);
        removeWrapper.in(OpenFundReconciliationStatement::getFileId, fileIds);
        List<OpenFundReconciliationStatement> list = this.list(removeWrapper);
        List<String> ids = list.stream().map(OpenFundReconciliationStatement::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            LambdaQueryWrapper<OpenFundReconciliationAccount> removeAccountIdWrapper = new LambdaQueryWrapper<>();
            removeAccountIdWrapper.in(OpenFundReconciliationAccount::getOpenFundId, ids);
            openFundReconciliationAccountService.remove(removeAccountIdWrapper);
            this.remove(removeWrapper);
        }
    }

    private @NonNull List<Map<String, String>> getUsefulAndGroupCommonData(@NonNull OCRResult ocrResult, @NonNull ParseParam parseParam) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, OpenFundFieldMapping> fieldMapping = parseParam.getKeyFieldMapping();
        Map<String, String> commonData = ocrResult.getCommonData();
        Map<String, String> usefulCommonData = new HashMap<>();
        commonData.forEach((key, value) -> {
            String simpleKeyStr = StringUtil.getKeyStr(key);
            if (fieldMapping.containsKey(simpleKeyStr)) {
                usefulCommonData.put(simpleKeyStr, value);
            }
        });
        int maxNo = getMaxNo(usefulCommonData);
        for (int i = 0; i < maxNo; i++) {
            result.add(new HashMap<>());
        }
        usefulCommonData.forEach((key, value) -> {
            int no = getNo(key);
            Map<String, String> subCommonData = result.get(no);
            String simpleKeyStr = StringUtil.getKeyStr(key);
            if (fieldMapping.containsKey(simpleKeyStr)) {
                subCommonData.put(simpleKeyStr, value);
            }
        });
        return result;
    }

    private int getMaxNo(@NonNull Map<String, String> commonData) {
        int max = 1;
        for (String key : commonData.keySet()) {
            String[] split = key.split("-");
            if (split.length > 1) {
                max = Integer.parseInt(split[1]);
            }
        }
        return max;
    }

    private int getNo(@NonNull String input) {
        int no = 0;
        String[] split = input.split("-");
        if (split.length > 1) {
            no = Integer.parseInt(split[1]);
        }
        return no;
    }

    @Transactional(rollbackFor = Exception.class)
    public void confirm(@NonNull OpenFundReconciliationStatement openFundReconciliationStatement) {
        Map<String, String> productIdAccountName = accountFundInformationService.list().stream().collect(Collectors.toMap(AccountFundInformation::getProductId,
                AccountFundInformation::getAccountName,
                (oldOne, newOne) -> newOne));
        String id = openFundReconciliationStatement.getId();
        OpenFundReconciliationStatement inDb = this.getById(id);
        if (inDb == null) {
            throw new IllegalStateException("OpenFundReconciliationStatement不存在");
        }

        // 使用确认时的最新数据生成文件名，而不是数据库中的旧数据
        // 将用户确认的产品ID设置到inDb对象中，以便生成正确的文件名
        inDb.setProductIds(openFundReconciliationStatement.getProductIds());

        // 如果用户确认的数据中包含交易渠道信息，也需要同步
        if (openFundReconciliationStatement.getTransactionChannel() != null) {
            inDb.setTransactionChannel(openFundReconciliationStatement.getTransactionChannel());
        }

        // 如果交易渠道仍然为空，尝试从产品信息中推导
        if (inDb.getTransactionChannel() == null || inDb.getTransactionChannel().trim().isEmpty()) {
            // 从第一个产品ID对应的账户基金信息中获取管理机构作为交易渠道
            List<String> productIds = inDb.getProductIds();
            if (productIds != null && !productIds.isEmpty()) {
                String firstProductId = productIds.get(0);
                // 根据产品ID查找对应的管理机构
                Map<String, String> productIdAdministrator = accountFundInformationService.list().stream()
                        .collect(Collectors.toMap(AccountFundInformation::getProductId,
                                AccountFundInformation::getAdministrator,
                                (oldOne, newOne) -> newOne));
                String administrator = productIdAdministrator.get(firstProductId);
                if (administrator != null && !administrator.trim().isEmpty()) {
                    inDb.setTransactionChannel(administrator);
                }
            }
        }

        // 按照上传时的重命名规则生成新文件名
        String newFileName = transFileName(initMap(), inDb);
        File rename = FileUtil.rename(new File(inDb.getFilePath()), newFileName, true);

        // 更新文件路径和附件名称
        openFundReconciliationStatement.setFilePath(rename.getAbsolutePath());
        openFundReconciliationStatement.setAttachmentName(rename.getName());
        openFundReconciliationStatement.setOcrConfirmationStatus(OcrConfirmationStatus.CONFIRMED);
        this.updateById(openFundReconciliationStatement);
        List<String> productIds = openFundReconciliationStatement.getProductIds();
        Objects.requireNonNull(productIds, "产品id不能为空");
        LambdaQueryWrapper<OpenFundReconciliationAccount> openFundReconciliationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        openFundReconciliationAccountLambdaQueryWrapper.eq(OpenFundReconciliationAccount::getOpenFundId, id);
        openFundReconciliationAccountService.remove(openFundReconciliationAccountLambdaQueryWrapper);
        List<OpenFundReconciliationAccount> openFundReconciliationAccounts = new ArrayList<>();
        for (String productId : productIds) {
            OpenFundReconciliationAccount openFundReconciliationAccount = new OpenFundReconciliationAccount();
            openFundReconciliationAccount.setOpenFundId(id);
            openFundReconciliationAccount.setProductId(productId);
            String accountName = productIdAccountName.get(productId);
            openFundReconciliationAccount.setAccountName(accountName);
            openFundReconciliationAccounts.add(openFundReconciliationAccount);
        }
        openFundReconciliationAccountService.saveBatch(openFundReconciliationAccounts);

        // 记录OCR确认日志
        String operator = SecureUtil.currentUserName();
        String accountSetNames = openFundReconciliationAccounts.stream()
                .map(OpenFundReconciliationAccount::getAccountName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
        logOpenFundReconciliationService.logOcrConfirm(
                id,
                openFundReconciliationStatement.getAttachmentName(),
                accountSetNames,
                openFundReconciliationStatement.getTransactionChannel(),
                operator
        );
    }

    public void removeAllInfo(String id) {
        OpenFundReconciliationStatement statement = this.getById(id);
        Objects.requireNonNull(statement, "id不存在");

        // 获取账套信息用于日志记录
        LambdaQueryWrapper<OpenFundReconciliationAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.eq(OpenFundReconciliationAccount::getOpenFundId, id);
        List<OpenFundReconciliationAccount> accounts = openFundReconciliationAccountService.list(accountQueryWrapper);
        String accountSetNames = accounts.stream()
                .map(OpenFundReconciliationAccount::getAccountName)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));

        String filePath = statement.getFilePath();
        FileUtil.del(filePath);

        // 删除相关的上传和下载日志
        logOpenFundReconciliationService.deleteLogsByReconciliationId(id);

        openFundReconciliationAccountService.remove(accountQueryWrapper);
        this.removeById(id);

        // 记录删除日志
        String operator = SecureUtil.currentUserName();
        logOpenFundReconciliationService.logDelete(
                id,
                statement.getAttachmentName(),
                accountSetNames,
                statement.getTransactionChannel(),
                operator
        );
    }

    public List<String> getChannelList() {
        return accountFundInformationService.list().stream().map(AccountFundInformation::getAdministrator).distinct().collect(Collectors.toList());
    }

    public List<String> getBusinessList() {
        return openFundFieldMappingService.list().stream()
                .filter(mapping -> OpenConstant.VALUE_CLASS.equals(mapping.getValueClass()))
                .distinct()
                .map(OpenFundFieldMapping::getName).collect(Collectors.toList());
    }

    public void retryPickMail() {
        List<String> mailIds = this.list().stream().map(OpenFundReconciliationStatement::getEmailId).collect(Collectors.toList());
        List<String> changeIds = new ArrayList<>();
        List<MailContent> mailContents = mailContentMapper.listAllContent(mailIds);
        Map<String, String> mailContentsOriginal = mailContentMapper.listAllContent(mailIds).stream().collect(Collectors.toMap(MailContent::getId, MailContent::getBoxId, (oldOne, newOne) -> newOne));
        mailPickService.pickMailSimple(mailContents);
        for (MailContent mailContent : mailContents) {
            String id = mailContent.getId();
            String originalBoxId = mailContentsOriginal.get(id);
            String newBoxId = mailContent.getBoxId();
            if (!originalBoxId.equals(newBoxId)) {
                changeIds.add(id);
            }
        }
        LambdaUpdateWrapper<OpenFundReconciliationStatement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OpenFundReconciliationStatement::getEmailRuleMatchStatus, UNMATCHED);
        updateWrapper.in(OpenFundReconciliationStatement::getEmailId, changeIds);
    }

    public void sendMail(List<String> ids) {
        List<OpenFundReconciliationStatement> openFundReconciliationStatements = this.listByIds(ids);
        LambdaQueryWrapper<OpenFundReconciliationAccount> openFundReconciliationAccountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        Map<String, RemoteFileInfo> fileInfoMap = new HashMap<>();

        for (OpenFundReconciliationStatement statement : openFundReconciliationStatements) {
            String id = statement.getId();
            openFundReconciliationAccountLambdaQueryWrapper.eq(OpenFundReconciliationAccount::getOpenFundId, id);
            List<String> productIds = openFundReconciliationAccountService.list(openFundReconciliationAccountLambdaQueryWrapper).stream()
                    .map(OpenFundReconciliationAccount::getProductId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            log.info("statement的id = {}, 查询出的productId的= {}", statement.getId(), productIds);
            String transactionChannel = statement.getTransactionChannel();
            for (String productId : productIds) {
                RemoteFileInfo remoteFileInfo = fileInfoMap.get(productId);
                if (remoteFileInfo == null) {
                    remoteFileInfo = new RemoteFileInfo();
                    remoteFileInfo.setLocation("local");
                    String dataDate = statement.getDataDate();
                    fileInfoMap.put(dataDate + "_" + (StringUtils.isNotBlank(transactionChannel) ? transactionChannel + "_" : "") + productId, remoteFileInfo);
                }
                remoteFileInfo.getOpenFundId().add(id);
                remoteFileInfo.getOpenFile().add(new File(statement.getFilePath()));
            }
            openFundReconciliationAccountLambdaQueryWrapper.clear();
        }
        log.info("组装的fileMap = {}", JSON.toJSONString(fileInfoMap));
        executeSend(fileInfoMap);
    }

    public void executeSend(@NonNull Map<String, RemoteFileInfo> fileInfoMap) {
        List<String> jobIds = cronService.getJobIdByClass(OpenFundMailSendJob.class);
        Map<String, Map<String, Object>> groupedMap = fileInfoMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> {
                            String key = entry.getKey();
                            int idx = key.indexOf("_");
                            return idx != -1 ? key.substring(0, idx) : "UNKNOWN"; // 处理无效键
                        },
                        // 向下游收集为子 Map
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)
                ));
        log.info("分组后的map = {}", JSON.toJSONString(groupedMap));
        if (MapUtil.isNotEmpty(groupedMap)) {
            groupedMap.forEach((key, value) -> {
                Map<String, RemoteFileInfo> res = new HashMap<>();
                if (MapUtil.isNotEmpty(value)) {
                    value.forEach((k, v) -> {
                        String[] kArr = k.split("_");
                        if (kArr.length == 2) {
                            res.put(kArr[1], (RemoteFileInfo) v);
                        } else if (kArr.length == 3) {
                            res.put(kArr[2], (RemoteFileInfo) v);
                        }
                        List<String> productIds = new ArrayList<>();
                        String dataDate = null;
                        String[] s = k.split("_");
                        if (s.length == 2) {
                            productIds.add(s[1]);
                            dataDate = s[0];
                        } else if (s.length == 3) {
                            productIds.add(s[2]);
                            dataDate = s[0];
                        }
                        if (StringUtils.isNotBlank(dataDate) && CollectionUtil.isNotEmpty(productIds)) {
                            log.info("-------------发送dataDate = {}的邮件", dataDate);
                            JobDataMap jobDataMap = new JobDataMap();
                            jobDataMap.put(PRODUCT_ID, productIds);
                            log.info("发送的附件信息为:{}", JSON.toJSONString(res));
                            jobDataMap.put(REMOTE_FILE, res);
                            jobDataMap.put(SYNC, true);
                            jobDataMap.put(START_DATE, dataDate);
                            jobDataMap.put(JobConstant.DATA_DATE, dataDate);
                            jobDataMap.put(END_DATE, dataDate);
                            jobDataMap.put(OPERATOR, SecureUtil.currentUserName());
                            jobDataMap.put(SEND_METHOD, SendMethod.MANUAL);
                            jobDataMap.put("STATEMENT_TYPE", "RECONCILIATION"); // 标识为对账单
                            cronService.startJobNow(jobIds, jobDataMap);
                        }
                    });
                }
            });
        }
    }
}
