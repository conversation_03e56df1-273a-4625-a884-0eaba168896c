package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSetGroup;
import cn.sdata.om.al.result.R;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface ProjectGroupService {
    PageInfo<AccountSetGroup> page(Integer pageNo, Integer pageSize, List<String> accountSetGroupIds, List<String> productIds);

    R<Boolean> saveOrUpdate(AccountSetGroup accountSetGroup);

    AccountSetGroup getById(String id);

    List<CommonEntity> list();

    boolean delete(List<String> ids);

    List<CommonEntity> accountSetListNoGroup();

}
