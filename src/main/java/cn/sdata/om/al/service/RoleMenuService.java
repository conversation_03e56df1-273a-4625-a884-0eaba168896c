package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.RoleMenu;
import cn.sdata.om.al.mapper.RoleMenuMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/9 15:03
 * @Version 1.0
 */
@Service
public class RoleMenuService extends ServiceImpl<RoleMenuMapper, RoleMenu> {
    /**
     * 菜单授权
     *
     * @param list
     */
    public void grantMenu(List<RoleMenu> list) {
        getBaseMapper().insertBatch(list);
    }
}
