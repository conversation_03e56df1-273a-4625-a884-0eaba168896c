package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.entity.Role;
import cn.sdata.om.al.entity.User;
import cn.sdata.om.al.mapper.RoleMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 16:10
 * @Version 1.0
 */
@Service
public class RoleService extends ServiceImpl<RoleMapper, Role> {

    /**
     * 通过角色获取拥有该角色的用户列表
     *
     * @param roleId
     * @return
     */
    public List<User> getRole2userList(String roleId) {
        if (StringUtils.isBlank(roleId)) {
            return null;
        }
        return getBaseMapper().getRole2userList(roleId);
    }

    /**
     * 通过角色获取角色对应的菜单列表
     *
     * @param roleId
     * @return
     */
    public List<Menu> getRole2menuList(String roleId) {
        if (StringUtils.isBlank(roleId)) {
            return null;
        }
        return getBaseMapper().getRole2menuList(roleId);
    }
}
