package cn.sdata.om.al.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.entity.RpaExecLogQuery;
import cn.sdata.om.al.enums.RpaExecStateEnum;
import cn.sdata.om.al.mapper.RpaExecLogMapper;
import cn.sdata.om.al.utils.C;
import cn.sdata.om.al.vo.RpaExecLogVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.RPA_END_DATE_NAME;
import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

@Service
@Slf4j
public class RpaExecLogService extends ServiceImpl<RpaExecLogMapper, RpaExecLog> {

    @Resource
    private RpaService rpaService;


    public List<String> bizTypeList() {

        return this.list().stream()
                .map(RpaExecLog::getRpaBizType)
                .distinct()
                .collect(Collectors.toList());
    }

    public IPage<RpaExecLogVO> queryPage(RpaExecLogQuery query) {
        LambdaQueryWrapper<RpaExecLog> wrapper = buildQueryCondition(query);
        return this.page(new Page<>(query.getCurrent(), query.getSize()), wrapper)
                .convert(item -> BeanUtil.toBean(item, RpaExecLogVO.class));
    }

    private RpaExecLog getOne(String execTime, String flowId, String execId) {
        RpaExecLog result = getOne(Wrappers.lambdaQuery(RpaExecLog.class)
                .eq(RpaExecLog::getExecTime, execTime)
                .eq(RpaExecLog::getFlowId, flowId)
                .eq(RpaExecLog::getExecId, execId));
        return C.defaultValue(result, new RpaExecLog());
    }

    private LambdaQueryWrapper<RpaExecLog> buildQueryCondition(RpaExecLogQuery query) {

        // bugfix: 不太理解之前是怎么设计的 我只能这么打补丁了 我觉得ugly了！！！
        if (CollUtil.isEmpty(query.getSceneList())) {
            if ("1".equals(query.getType())) {
                query.setSceneList(Arrays.asList("3", "5"));
            } else if ("2".equals(query.getType())) {
                query.setSceneList(Arrays.asList("1", "2", "4"));
            }
        }
        return Wrappers.lambdaQuery(RpaExecLog.class)
                .between(StrUtil.isNotBlank(query.getExecTimeStart()) && StrUtil.isNotBlank(query.getExecTimeEnd()),
                        RpaExecLog::getExecTime, query.getExecTimeStart(), query.getExecTimeEnd())
                .in(RpaExecLog::getRpaBizScene, query.getSceneList())
                .in(CollUtil.isNotEmpty(query.getBizList()), RpaExecLog::getRpaBizType, query.getBizList())
                .eq(StrUtil.isNotBlank(query.getExecSate()), RpaExecLog::getExecState, query.getExecSate())
                .like(StrUtil.isNotBlank(query.getExecutor()), RpaExecLog::getExecutor, query.getExecutor())
                .orderByDesc(RpaExecLog::getUpdatedTime)
                .orderByDesc(RpaExecLog::getId);
    }


    public RpaExecLog doUpdateExecLogInfo(RpaExecLog item) {
        // 填充流程截图
        fillFlowImage(item, false);
        this.updateById(item);
        return item;
    }

    public void fillFlowImage(RpaExecLog execLog, boolean netEnvFlag) {
        // 非执行成功都是错误的情况，填充流程截图
        try {
            if (!StrUtil.equals(execLog.getExecState(),
                    RpaExecStateEnum.SUCCESS.getValue())) {
                execLog.setErrorImage(rpaService.getFlowImage(
                        execLog.getExecId(), execLog.getExecTime(), netEnvFlag));
            }
        } catch (Exception e) {
            log.error(StrUtil.format("获取流程截图失败:{}", e.getMessage()));
        }
    }

    public RpaExecLog updateExecLog(RpaExecLog item, Map<String, String> flowExecState) {
        if (MapUtil.isNotEmpty(flowExecState)) {
            flowExecState.forEach((k, v) -> {
                item.setExecState(k);
                item.setExecResult(v);
            });
        }
        item.setFinishTime(DateUtil.now());
        item.setUpdatedTime(DateUtil.now());
        // 填充流程截图
        fillFlowImage(item, true);
        this.updateById(item);
        return item;
    }

    public RpaExecLog buildSynLog(String flowId, String execId, FlowList flow, Map<String, Object> flowExtendParams) {
        RpaExecLog rpaExecLog = new RpaExecLog();
        if (flow != null) {
            rpaExecLog.setRpaBizScene(flow.getShowName());
        }
        rpaExecLog.setId(IdWorker.getIdStr());
        rpaExecLog.setFlowId(flowId);
        rpaExecLog.setExecId(execId);
        rpaExecLog.setExecTime(DateUtil.today());
        rpaExecLog.setExecState(RpaExecStateEnum.EXECUTE.getValue());
        rpaExecLog.setExecResult(RpaExecStateEnum.EXECUTE.getName());
        rpaExecLog.setExecutor(BaseConstant.DEFAULT_USERNAME);
        rpaExecLog.setStartTime(DateUtil.now());
        if (flowExtendParams != null) {
            String startDate = (String) flowExtendParams.get(RPA_START_DATE_NAME);
            String endDate = (String) flowExtendParams.get(RPA_END_DATE_NAME);
            String memo = "开始日期:" + startDate + ",结束日期:" + endDate;
            rpaExecLog.setMemo(memo);
        }
        rpaExecLog.setCreatedTime(DateUtil.now());
        rpaExecLog.setUpdatedTime(DateUtil.now());
        return rpaExecLog;
    }

    public List<String> getErrorImageByLogId(String logId) {
        return baseMapper.getErrorImageByLogId(logId);
    }
}




