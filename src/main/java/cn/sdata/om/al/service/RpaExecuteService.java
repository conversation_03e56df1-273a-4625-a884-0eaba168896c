package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.FlowList;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.RpaExecLog;
import cn.sdata.om.al.enums.RpaExecStateEnum;
import cn.sdata.om.al.exception.TaskException;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.rpa.BaseHandler;
import cn.sdata.om.al.rpa.MonthlySettlementHandler;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.RPAConstant.LOG_ID;
import static cn.sdata.om.al.utils.StringUtil.concatSeparator;
import static cn.sdata.om.al.utils.StringUtil.getNowDateStr;

@Service
@AllArgsConstructor
@Slf4j
public class RpaExecuteService {

    private final RpaService rpaService;
    private final RpaExecLogService rpaExecLogService;
    private final BaseCronLogService baseCronLogService;
    private final SMBService smbService;
    private final ApplicationContext applicationContext;

    public RpaExecLog executeRPA(FlowList flow, Map<String, Object> flowExtendParams, String logId) {
        if (flow == null) {
            throw new TaskException("配置的流程不存在");
        }
        // 获取流程id
        log.info("RPA参数:{}", flowExtendParams);
        int netEnvFlag = flow.getNetEnvFlag();
        String rpaFlowId = rpaService.getFlowIdByFullPath(flow.getName(), getBoolean(netEnvFlag));
        String execId = rpaService.startFlow(rpaFlowId, flowExtendParams, getBoolean(netEnvFlag));
        RpaExecLog rpaExecLog = rpaExecLogService.buildSynLog(rpaFlowId, execId, flow, flowExtendParams);
        updateRPAStatus(logId, JobStatus.RUNNING);
        // 更新execId到任务log表
        updateExecId(logId, execId);
        rpaExecLogService.save(rpaExecLog);
        return rpaExecLog;
    }

    private void updateExecId(String logId, String execId) {
        LambdaUpdateWrapper<BaseCronLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaseCronLog::getId, logId);
        updateWrapper.set(BaseCronLog::getExecId, execId);
        baseCronLogService.update(updateWrapper);
    }

    public void getRPAResult(Timer timer, FlowList flow, RpaExecLog rpaExecLog, Map<String, Object> param,
            String logId) {
        if (rpaExecLog == null || flow == null) {
            log.error("日志或者流程为空");
            return;
        }

        // 记录RPA任务信息
        log.info("检查RPA执行状态: execId={}, flowId={}, flowName={}, logId={}",
                rpaExecLog.getExecId(), flow.getId(), flow.getName(), logId);

        Map<String, String> flowExecState = rpaService.getFlowExecState(rpaExecLog.getFlowId(),
                rpaExecLog.getExecId(), rpaExecLog.getExecTime(), true);
        log.info("获取rpa流程执行信息结果为{}", flowExecState);
        param.put(LOG_ID, logId);
        String execSate = "-1";
        if (flowExecState != null) {
            execSate = flowExecState.keySet().stream().findFirst().orElse("-1");
        }
        // 为空大概率说明服务正在排队,如何处理有待商榷,先做持续等待处理
        String handlerName = flow.getHandlerName();
        log.info("RPA执行状态: handlerName={}, execSate={}, flowExecState为空={}",
                handlerName, execSate, flowExecState == null);

        Map<String, BaseHandler> beansOfType = applicationContext.getBeansOfType(BaseHandler.class);
        BaseHandler baseRPAHandler = beansOfType.get(handlerName);

        if (!CharSequenceUtil.equals(execSate, RpaExecStateEnum.EXECUTE.getValue())) {
            // 不是正在执行状态
            log.info("RPA任务不是正在执行状态: execId={}, execSate={}", rpaExecLog.getExecId(), execSate);

            if (CharSequenceUtil.equals(execSate, RpaExecStateEnum.SUCCESS.getValue())) {
                log.info("RPA任务执行成功: execId={}", rpaExecLog.getExecId());
                try {
                    // 成功
                    // 开始寻找文件
                    List<Integer> monthlyFlowIds;
                    List<Integer> feeManagerFlowIds;
                    String flowIds = SpringUtil.getProperty("monthly-settlement.flow-ids");
                    String feeManagerFlowIdsStr = SpringUtil.getProperty("inter-bank-fees.flow-ids");
                    if (StringUtils.isEmpty(flowIds)) {
                        monthlyFlowIds = CollectionUtil.newArrayList(4, 5, 9, 10, 11, 12, 13, 14, 15, 17, 18, 28);
                    } else {
                        monthlyFlowIds = ListUtil.toList(flowIds.split(",")).stream().map(Integer::parseInt)
                                .collect(Collectors.toList());
                    }
                    if (StringUtils.isEmpty(feeManagerFlowIdsStr)) {
                        feeManagerFlowIds = CollectionUtil.newArrayList(24, 25);
                    } else {
                        feeManagerFlowIds = ListUtil.toList(feeManagerFlowIdsStr.split(",")).stream()
                                .map(Integer::parseInt).collect(Collectors.toList());
                    }
                    log.info("RpaExecuteService_monthlyFlowIds:{},feeManagerFlowIds:{}", monthlyFlowIds,
                            feeManagerFlowIds);
                    // 月结文件存储路径逻辑与标准不同 这里不进行验证文件
                    if (!monthlyFlowIds.contains(flow.getId())) {
                        updateStatus(logId, JobStatus.COMPLETE, null);
                        if (!feeManagerFlowIds.contains(flow.getId())) {
                            String path = concatSeparator(flow.getBasePath(),
                                    getNowDateStr(BaseConstant.DATE_FORMAT_PATTERN));
                            log.info("查找文件路径: path={}", path);
                            List<RemoteFileInfo> files = new ArrayList<>();
                            try {
                                files = smbService.listFileInfo(path);
                                log.info("找到文件数量: {}", files.size());
                                if (!files.isEmpty()) {
                                    log.info("文件列表: {}", files.stream().map(RemoteFileInfo::getFileName)
                                            .collect(Collectors.joining(", ")));
                                }
                            } catch (Exception e) {
                                log.error("远程文件夹文件不存在:{}", e.getMessage(), e);
                            }

                            if (baseRPAHandler != null) {
                                log.info("调用Handler.execute处理: handlerName={}, execId={}, 文件数量={}",
                                        handlerName, rpaExecLog.getExecId(), files.size());
                                baseRPAHandler.execute(param, files);
                            } else {
                                log.warn("未找到对应的Handler: handlerName={}", handlerName);
                            }
                        }
                    } else {
                        updateStatus(logId, JobStatus.COMPLETE, null);
                        if (baseRPAHandler != null) {
                            log.info("调用MonthlySettlementHandler.execute处理: execId={}", rpaExecLog.getExecId());
                            MonthlySettlementHandler handler = (MonthlySettlementHandler) baseRPAHandler;
                            handler.execute(param, flow, logId);
                        }
                    }
                } catch (Throwable e) {
                    log.error("处理RPA成功结果时发生异常: execId={}, error={}", rpaExecLog.getExecId(), e.getMessage(), e);
                    updateStatus(logId, JobStatus.FAILED, e.getMessage());
                    if (baseRPAHandler != null) {
                        param.put("errorMsg", e.getMessage());
                        log.info("调用Handler.onFail处理异常: handlerName={}, execId={}", handlerName,
                                rpaExecLog.getExecId());
                        baseRPAHandler.onFail(param);
                    }
                } finally {
                    log.info("更新RPA状态为COMPLETE: execId={}", rpaExecLog.getExecId());
                    updateRPAStatus(logId, JobStatus.COMPLETE);
                }
            } else {
                log.warn("RPA任务执行失败: execId={}, execSate={}", rpaExecLog.getExecId(), execSate);
                updateStatus(logId, JobStatus.FAILED, null);
                if (CharSequenceUtil.equals(execSate, RpaExecStateEnum.STOP.getValue())) {
                    log.info("更新RPA状态为STOP: execId={}", rpaExecLog.getExecId());
                    updateRPAStatus(logId, JobStatus.STOP);
                } else if (CharSequenceUtil.equals(execSate, RpaExecStateEnum.EXCEPTION.getValue())) {
                    log.info("更新RPA状态为EXCEPTION: execId={}", rpaExecLog.getExecId());
                    updateRPAStatus(logId, JobStatus.EXCEPTION);
                } else if (CharSequenceUtil.equals(execSate, RpaExecStateEnum.TIMEOUT.getValue())) {
                    log.info("更新RPA状态为TIMEOUT: execId={}", rpaExecLog.getExecId());
                    updateRPAStatus(logId, JobStatus.TIMEOUT);
                } else {
                    log.info("更新RPA状态为FAILED: execId={}", rpaExecLog.getExecId());
                    updateRPAStatus(logId, JobStatus.FAILED);
                }
                if (baseRPAHandler != null) {
                    log.info("调用Handler.onFail处理失败: handlerName={}, execId={}", handlerName, rpaExecLog.getExecId());
                    baseRPAHandler.onFail(param);
                }
            }
            rpaExecLogService.updateExecLog(rpaExecLog, flowExecState);
            log.info("取消定时器: execId={}", rpaExecLog.getExecId());
            timer.cancel();
        } else {
            log.debug("RPA任务正在执行中，继续等待: execId={}", rpaExecLog.getExecId());
        }
    }

    private void updateRPAStatus(String logId, JobStatus jobStatus) {
        LambdaUpdateWrapper<BaseCronLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaseCronLog::getId, logId);
        updateWrapper.set(BaseCronLog::getRpaStatus, jobStatus);
        baseCronLogService.update(updateWrapper);
    }

    private void updateStatus(String logId, JobStatus jobStatus, String errorMessage) {
        LambdaUpdateWrapper<BaseCronLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaseCronLog::getId, logId);
        updateWrapper.set(BaseCronLog::getStatus, jobStatus);
        updateWrapper.set(BaseCronLog::getTaskInfo, errorMessage);
        updateWrapper.set(BaseCronLog::getEndDateTime, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        baseCronLogService.update(updateWrapper);
    }

    public void startTimer(RpaExecLog rpaExecLog, FlowList flow, Map<String, Object> param, String logId) {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                getRPAResult(timer, flow, rpaExecLog, param, logId);

            }
        }, 10000, 10000);

        log.info("RPA执行状态检查定时器已启动: execId={}", rpaExecLog.getExecId());
    }

    private boolean getBoolean(int input) {
        return input != 0;
    }

}
