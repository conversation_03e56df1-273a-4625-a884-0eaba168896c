package cn.sdata.om.al.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.sdata.om.al.config.RpaProperties;
import cn.sdata.om.al.enums.RpaExecStateEnum;
import cn.sdata.om.al.enums.RpaInterfaceEnum;
import cn.sdata.om.al.utils.C;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * rpa相关服务
 */
@Component
@Slf4j
@AllArgsConstructor
public class RpaService {

    private final RpaProperties rpaProperties;

    /**
     * 发起rpa请求
     *
     * @param interfaceEnum rpa枚举
     * @param extendInfo    请求内容的扩展信息
     * @param netEnvFlag    网络环境标识,true外网/false内网
     * @return rpa返回结果
     */
    private JSONArray process(RpaInterfaceEnum interfaceEnum, List<Map<String, Object>> extendInfo, boolean netEnvFlag) {
        Assert.notNull(interfaceEnum, "RPA接口类型不能为空");
        List<Map<String, Object>> defaultParam = initDefaultParam(interfaceEnum, netEnvFlag);
        // 添加扩展信息
        defaultParam.addAll(extendInfo);
        String requestParam = JSONUtil.toJsonStr(defaultParam);
        try {
            // 获取rpa访问地址
            String address = C.condition(netEnvFlag,
                    rpaProperties::getOuterNetAddress,
                    rpaProperties::getInterNetAddress);
            Assert.notBlank(address, "rpa访问地址不能为空");
            log.info("调用rpa[{}]接口,访问地址为:[{}],请求参数信息为:{}",
                    interfaceEnum.getDesc(), address, requestParam);
            // 调用接口设置超时时间10秒
            // String str = HttpUtil.post(address, requestParam, 10000);
            // 调用接口设置超时时间 30分钟 30 * 60 * 1000
            String str = HttpUtil.post(address, requestParam, 1800000);
            log.info("--------------获取rpa请求结果:{}", str);
            Assert.notBlank(str, StrUtil.format("接口调用返回信息为空"));
            String result = StrUtil.removeAllLineBreaks(str);
            // 返回jsonArray对象
            return JSONUtil.parseArray(result);
        } catch (Exception e) {
            // 接口调用异常
            throw new RuntimeException(StrUtil.format("rpa接口[{}]调用异常:{}",
                    interfaceEnum.getDesc(), e.getMessage()), e);
        }
    }

    /**
     * 通过流程路径获取流程id
     *
     * @param flowPtah   流程路径
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return 流程id
     */
    public String getFlowIdByFullPath(String flowPtah, boolean netEnvFlag) {
        Assert.notBlank(flowPtah, "流程全路径不能为空");
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        requestParam.add(putParam(4, "FlowPath", flowPtah));
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_01, requestParam, netEnvFlag);
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            if (StrUtil.equals("FlowID", jsonObject.getStr("Name"))) {
                String flowId = jsonObject.getStr("Value");
                log.info("[获取流程ID]flowPtah:{},flowId:{}", flowPtah, flowId);
                return flowId;
            }
            // 获取流程路径有错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "通过流程路径获取流程id失败[{}]");
        }
        return null;
    }

    /**
     * 执行流程
     *
     * @param flowId           流程id
     * @param flowExtendParams 流程扩展参数
     * @param netEnvFlag       网络环境标识,true外网/false内网
     * @return 执行id
     */
    public String startFlow(String flowId, Map<String, Object> flowExtendParams, boolean netEnvFlag) {
        Assert.notBlank(flowId, "流程id不能为空");
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        requestParam.add(putParam(4, "FlowID", flowId));
        // 启动流程添加扩展参数
        if (MapUtil.isNotEmpty(flowExtendParams)) {
            addFlowExtendParams(requestParam, flowExtendParams);
        }
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_02, requestParam, netEnvFlag);
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            if (StrUtil.equals("ExecID", jsonObject.getStr("Name"))) {
                String execId = jsonObject.getStr("Value");
                if (StrUtil.isNotBlank(execId)) {
                    log.info("[执行流程]flowId:{},execId:{}", flowId, execId);
                    return execId;
                }
            }
            // 执行流程有错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "执行流程失败[{}]");
        }
        return null;
    }

    /**
     * 抛出rpa错误信息
     *
     * @param jsonObject json对象
     * @param msg        错误信息
     */
    private void throwErrorMsg(JSONObject jsonObject, String msg) {
        if (StrUtil.equals(rpaProperties.getErrorMsgKey(), jsonObject.getStr("Name"))) {
            String errorMsg = jsonObject.getStr("Value");
            if (StrUtil.isNotBlank(errorMsg)) {
                throw new RuntimeException(StrUtil.format(msg, errorMsg));
            }
        }
    }

    /**
     * 执行流程添加流程扩展参数
     *
     * @param requestParam     请求参数
     * @param flowExtendParams 流程扩展参数
     */
    private void addFlowExtendParams(ArrayList<Map<String, Object>> requestParam,
                                     Map<String, Object> flowExtendParams) {
        Assert.notEmpty(flowExtendParams, "流程扩展参数不能为空");
        flowExtendParams.forEach((k, v) -> requestParam.add(putParam(4,
                StrUtil.format("FlowParamsValue_{}", k), v)));
        String join = CollectionUtil.join(flowExtendParams.keySet(), "\r\n");
        requestParam.add(putParam(1, "IsThird", true));
        requestParam.add(putParam(1, "IsQueue", true));
        requestParam.add(putParam(7, "FlowParamsNames", join));
    }

    /**
     * 获取流程执行状态
     *
     * @param flowId     流程id
     * @param execId     执行id
     * @param execTime   执行时间
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return <执行状态,执行结果>
     */
    public Map<String, String> getFlowExecState(String flowId, String execId, String execTime, boolean netEnvFlag) {
        Assert.notBlank(flowId, "流程id不能为空");
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        execTime = LocalDate.parse(execTime).format(DateTimeFormatter.BASIC_ISO_DATE);
        requestParam.add(putParam(4, "IDs", flowId));
        requestParam.add(putParam(4, "DBDate", execTime));
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_03, requestParam, netEnvFlag);
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            if (StrUtil.equals(jsonObject.getStr("Name"), "k_flow")) {
                JSONArray valueArray = (JSONArray) jsonObject.get("Value");
                if (valueArray.size() > 1) {
                    for (int i = valueArray.size() - 1; i >= 0; i--) {
                        JSONArray array = (JSONArray) valueArray.get(i);
                        JSONObject objectExecId = (JSONObject) array.get(0);
                        String valueExecId = objectExecId.getStr("Value");
                        if (StrUtil.equals(execId, valueExecId)) {
                            JSONObject objectExecState = (JSONObject) array.get(9);
                            JSONObject objectExecResult = (JSONObject) array.get(12);
                            //-1：正在执行；0：手动停止；1：执行成功；2：执行超时；3：节点异常
                            String execState = objectExecState.getStr("Value");
                            String execResult = objectExecResult.getStr("Value");
                            Assert.notNull(RpaExecStateEnum.getByValue(execState),
                                    "获取rpa执行状态枚举异常");
                            Map<String, String> result = new HashMap<>();
                            result.put(execState, execResult);
                            log.info("[流程执行状态]flowId:{},execId:{},execState:{},execResult:{}",
                                    flowId, execId, execState, execResult);
                            return result;
                        }
                    }
                }
            }
            // 获取流程执行状态有错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "获取流程执行状态失败[{}]");
        }
        return null;
    }

    /**
     * 获取流程结果
     *
     * @param execId     执行id
     * @param execTime   执行时间
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return 执行结果
     */
    public Map<String, String> getBatchRpaChatParam(String execId, String execTime, boolean netEnvFlag) {
        Assert.notBlank(execId, "执行id不能为空");
        Assert.notBlank(execTime, "执行时间不能为空");
        execTime = LocalDate.parse(execTime).format(DateTimeFormatter.BASIC_ISO_DATE);
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        requestParam.add(putParam(4, "ExecIDs", execId));
        requestParam.add(putParam(4, "StartDate", execTime));
        requestParam.add(putParam(4, "EndDate", execTime));
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_04, requestParam, netEnvFlag);
        Map<String, String> result = new HashMap<>();
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            String name = jsonObject.getStr("Name");
            if (StrUtil.equals(StrUtil.format("Param2Value_{}", execId), name)) {
                String value = jsonObject.getStr("Value");
                result.put(name, value);
            }
            if (StrUtil.equals(StrUtil.format("Param3Value_{}", execId), name)) {
                String value = jsonObject.getStr("Value");
                result.put(name, value);
            }
            // 导入结果返回
            if (StrUtil.equals(StrUtil.format("Param0Value_{}", execId), name)) {
                String value = jsonObject.getStr("Value");
                result.put(name, value);
            }
            // 导入结果返回
            if (StrUtil.equals(StrUtil.format("Param1Value_{}", execId), name)) {
                String value = jsonObject.getStr("Value");
                result.put(name, value);
            }
            // 获取流程结果错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "获取流程结果失败[{}]");
        }
        log.info("[获取流程结果]result:{}", result);
        return result;
    }

    /**
     * 获取流程截图信息
     *
     * @param execId     执行id
     * @param execTime   执行时间
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return 执行结果
     */
    public String getFlowScreenshotInfo(String execId, String execTime, boolean netEnvFlag) {
        String result = null;
        Assert.notBlank(execId, "执行id不能为空");
        Assert.notBlank(execTime, "执行时间不能为空");
        execTime = LocalDate.parse(execTime).format(DateTimeFormatter.BASIC_ISO_DATE);
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        requestParam.add(putParam(4, "ExecID", execId));
        requestParam.add(putParam(4, "DBDate", execTime));
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_05, requestParam, netEnvFlag);
        TreeMap<Integer, String> resultMap = new TreeMap<>();
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            String name = jsonObject.getStr("Name");
            if (StrUtil.contains(name, "_File")) {
                List<String> split = StrUtil.split(name, "_");
                resultMap.put(Integer.parseInt(split.get(0)), jsonObject.getStr("Value"));
            }
            // 获取流程结果错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "获取流程截图信息失败[{}]");
        }
        if (MapUtil.isNotEmpty(resultMap)) {
            result = resultMap.lastEntry().getValue();
        }
        log.info("[获取流程截图信息]result:{}", result);
        return result;
    }

    /**
     * 获取流程截图十六进制
     *
     * @param str        agentId|fileName
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return 执行结果
     */
    public String getFlowScreenshot(String str, boolean netEnvFlag) {
        String result = null;
        Assert.notBlank(str, "[agentId|fileName]不能为空");
        ArrayList<Map<String, Object>> requestParam = new ArrayList<>();
        String[] split = str.split("\\|");
        Assert.isTrue(split.length == 2, "[agentId|fileName]分割后数据有误");
        requestParam.add(putParam(4, "AgentID", split[0]));
        requestParam.add(putParam(4, "Name", split[1]));
        JSONArray jsonArray = process(RpaInterfaceEnum.RPA_06, requestParam, netEnvFlag);
        for (Object data : jsonArray) {
            JSONObject jsonObject = (JSONObject) data;
            String name = jsonObject.getStr("Name");
            if (StrUtil.equals(name, "Buf")) {
                result = jsonObject.getStr("Value");
            }
            // 获取流程结果错误信息的情况,抛出异常
            throwErrorMsg(jsonObject, "获取流程截图失败[{}]");
        }
        return result;
    }

    /**
     * 获取流程截图
     *
     * @param execId     执行id
     * @param execTime   执行时间
     * @param netEnvFlag 网络环境标识,true外网/false内网
     * @return 执行结果
     */
    public String getFlowImage(String execId, String execTime, boolean netEnvFlag) {
        String flowScreenshotInfo = getFlowScreenshotInfo(execId, execTime, netEnvFlag);
        if (StrUtil.isNotBlank(flowScreenshotInfo)) {
            return getFlowScreenshot(flowScreenshotInfo, netEnvFlag);
        }
        return null;
    }

    /**
     * 初始化rpa请求默认参数【必须参数】
     *
     * @param interfaceEnum rpa枚举
     * @param netEnvFlag    网络环境标识,true外网/false内网
     * @return 请求信息
     */
    private List<Map<String, Object>> initDefaultParam(RpaInterfaceEnum interfaceEnum, boolean netEnvFlag) {
        ArrayList<Map<String, Object>> result = new ArrayList<>();
        // type（0 整型,1 布尔型,3 浮点型,4 字符串,5 数据流,7 字符串列表,8 十六进制字符串,16 64 位整型,19 表格）
        Assert.notBlank(rpaProperties.getModelKey(), "rpa请求模块名称未配置");
        Assert.notBlank(rpaProperties.getMethodKey(), "rpa请求方法名称未配置");
        result.add(putParam(4, rpaProperties.getModelKey(), interfaceEnum.getModel()));
        result.add(putParam(4, rpaProperties.getMethodKey(), interfaceEnum.getMethod()));

        // 获取rpa AppName
        String appName = C.condition(netEnvFlag,
                rpaProperties::getOuterNetAppName,
                rpaProperties::getInterNetAppName);
        // 获取rpa AppPass
        String appPass = C.condition(netEnvFlag,
                rpaProperties::getOuterNetAppPass,
                rpaProperties::getInterNetAppPass);
        Assert.notBlank(appName, "rpa appName未配置");
        Assert.notBlank(appPass, "rpa appPass未配置");

        result.add(putParam(4, "AppName", appName));
        result.add(putParam(4, "AppPass", appPass));
        return result;
    }


    /**
     * 添加自定义请求参数
     *
     * @param type （0 整型,1 布尔型,3 浮点型,4 字符串,5 数据流,7 字符串列表,8 十六进制字符串,16 64 位整型,19 表格）
     * @param name 字段名
     * @param vale 字段值
     * @return 请求信息
     */
    private Map<String, Object> putParam(int type, String name, Object vale) {
        Map<String, Object> paramMap = new LinkedHashMap<>(3);
        paramMap.put("Type", type);
        paramMap.put("Name", name);
        paramMap.put("Value", vale);
        return paramMap;
    }

}
