package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.FileInfo;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.mail.ShareDownloadFile;
import cn.sdata.om.al.utils.SMBManager;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SMBService {

    private final SMBManager smbManager;

    public SMBService(SMBManager smbManager) {
        this.smbManager = smbManager;
    }

    public List<String> listDir(String path) {
        return smbManager.listDir(path);
    }

    public List<RemoteFileInfo> listFileInfo(String path) {
        return smbManager.listFileInfo(path);
    }


    /**
     * 获取文件的相对路径
     *
     * @param path 目录路径
     * @return 文件相对路径
     */
    public List<String> listRelativeDir(String path) {
        return smbManager.listRelativeDir(path);
    }

    public byte[] download(String filePath) {
        return smbManager.downloadFile(filePath);
    }

    public FileInfo downloadBatch(List<RemoteFileInfo> files) {
        return smbManager.downloadBatch(files);
    }

    public void upload(String path, String fileName, byte[] bytes) {
        smbManager.uploadFile(path, fileName, bytes);
    }

    public ShareDownloadFile downloadReturnFile(String filePath) {
        return smbManager.downloadReturnFile(filePath);
    }
}
