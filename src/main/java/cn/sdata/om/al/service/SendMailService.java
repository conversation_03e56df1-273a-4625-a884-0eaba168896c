package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.CustodianBankContacts;
import cn.sdata.om.al.entity.InvestorContacts;
import cn.sdata.om.al.entity.OtherContacts;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.enums.DisclosureMethod;
import cn.sdata.om.al.enums.MailContentHandler;
import cn.sdata.om.al.enums.MailSplitType;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.sdata.om.al.utils.StringUtil.splitStr;

@Service
@AllArgsConstructor
@Slf4j
public class SendMailService {

    private final InvestorContactsViewService investorContactsViewService;

    private final CustodianBankContactsService custodianBankContactsService;

    private final OtherContactsService otherContactsService;

    public List<SendMailInfo> getInvestorContact(List<String> values, String splitType, Set<String> paramProductIds, String handler, String type, Integer immediateSend) {
        Objects.requireNonNull(values, "values不能为空");
        Objects.requireNonNull(splitType, "拆分规则不得为空");
        //如果为空,查询所有,即所有投资人用同一个模板,不做区分
        MailSplitType mailSplitType = MailSplitType.valueOf(splitType);
        switch (mailSplitType) {
            case ACCOUNT_SET:
                List<InvestorContacts> investorContacts = investorContactsViewService.getBaseMapper().getRecipientInfoByProduct(values, DisclosureMethod.MAIL.name(), handler, type);
                return generateSendMailInfoFromInvestor(investorContacts, paramProductIds, immediateSend);
            case INVESTOR:
            case THREE_PARTY_ORGAN:
                List<InvestorContacts> recipientInfo = investorContactsViewService.getBaseMapper().getRecipientInfo(values, DisclosureMethod.MAIL.name(), handler, type);
                return generateSendMailInfoFromInvestor(recipientInfo, paramProductIds, immediateSend);
        }
        return new ArrayList<>();
    }

    public List<SendMailInfo> getCustodianBankContact(List<String> values, String splitType, String role, Set<String> paramProductIds, Integer immediateSend) {
        Objects.requireNonNull(values, "values不能为空");
        Objects.requireNonNull(splitType, "拆分规则不得为空");
        Objects.requireNonNull(role, "托管角色不得为空");
        //如果为空,查询所有,即所有投资人用同一个模板,不做区分
        MailSplitType mailSplitType = MailSplitType.valueOf(splitType);
        log.info("发送邮件的mailSplitType = {}", mailSplitType.name());
        switch (mailSplitType) {
            case ACCOUNT_SET:
                LambdaQueryWrapper<CustodianBankContacts> queryWrapper = new LambdaQueryWrapper<>();
                if (!values.isEmpty()) {
                    queryWrapper.in(CustodianBankContacts::getProductId, values);
                }
                queryWrapper.in(CustodianBankContacts::getCustodianRole, role);
                List<CustodianBankContacts> custodianBankContacts = custodianBankContactsService.list(queryWrapper);
                return generateSendMailInfoFromCustodianBank(custodianBankContacts, paramProductIds, immediateSend);
            case CUSTODIAN_BANK:
                List<CustodianBankContacts> recipientInfo = custodianBankContactsService.getBaseMapper().getRecipientInfo(values, role);
                log.info("查询的联系人为:{}, 参数values = {}, role = {}", JSON.toJSONString(recipientInfo), values, role);
                return generateSendMailInfoFromCustodianBank(recipientInfo, paramProductIds, immediateSend);
        }
        return new ArrayList<>();
    }

    public List<SendMailInfo> getOtherContact(List<String> values) {
        Objects.requireNonNull(values, "values不能为空");
        LambdaQueryWrapper<OtherContacts> queryWrapper = new LambdaQueryWrapper<>();
        if (!values.isEmpty()) {
            queryWrapper.in(OtherContacts::getId, values);
        }
        List<OtherContacts> otherContacts = otherContactsService.list(queryWrapper);
        return generateSendMailInfoFromOther(otherContacts);
    }

    private List<SendMailInfo> generateSendMailInfoFromInvestor(List<InvestorContacts> investorContactsList, Set<String> paramProductIds, Integer immediateSend) {
        List<SendMailInfo> sendMailInfos = new ArrayList<>();
        for (InvestorContacts investorContacts : investorContactsList) {
            SendMailInfo sendMailInfo = new SendMailInfo();
            //只有这条记录全部包含在查询条件中才能够发送
            List<String> queried = splitStr(investorContacts.getProductId(), ",");
            boolean flag = getFlag(paramProductIds, queried, immediateSend);
            if (flag) {
                sendMailInfo.setRecipient(splitStr(investorContacts.getRecipient(), ";"));
                sendMailInfo.setCc(splitStr(investorContacts.getRecipientCc(), ";"));
                sendMailInfo.setProductIds(queried);
                sendMailInfo.setContactName(investorContacts.getInvestor());
                sendMailInfo.setHandler(investorContacts.getHandler());
                sendMailInfos.add(sendMailInfo);
            }
        }
        return sendMailInfos;
    }

    private List<SendMailInfo> generateSendMailInfoFromCustodianBank(List<CustodianBankContacts> custodianBankContactsList, Set<String> paramProductIds, Integer immediateSend) {
        List<SendMailInfo> sendMailInfos = new ArrayList<>();
        for (CustodianBankContacts custodianBankContacts : custodianBankContactsList) {
            SendMailInfo sendMailInfo = new SendMailInfo();
            List<String> queried = splitStr(custodianBankContacts.getProductId(), ",");
            boolean flag = getFlag(paramProductIds, queried, immediateSend);
            log.info("是否将联系人加入到发送联系人中:{}, 参数为:{} - {} - {}", flag, paramProductIds, queried, immediateSend);
            if (flag) {
                sendMailInfo.setRecipient(splitStr(custodianBankContacts.getRecipient(), ";"));
                sendMailInfo.setCc(splitStr(custodianBankContacts.getRecipientCc(), ";"));
                sendMailInfo.setProductIds(queried);
                sendMailInfo.setContactName(custodianBankContacts.getCustodianBank());
                sendMailInfo.setHandler(MailContentHandler.CUSTODIAN_BANK_VALUATION.name());
                sendMailInfos.add(sendMailInfo);
            }
        }
        return sendMailInfos;
    }

    private boolean getFlag(Set<String> paramProductIds, List<String> queried, Integer immediateSend) {
        boolean flag;
        if (Objects.equals(1, immediateSend)) {
            queried.retainAll(paramProductIds);
            flag = !queried.isEmpty();
        } else {
            flag = paramProductIds.containsAll(queried);
        }
        return flag;
    }

    private List<SendMailInfo> generateSendMailInfoFromOther(List<OtherContacts> otherContacts) {
        Objects.requireNonNull(otherContacts, "联系人不得为空");
        return otherContacts.stream().map(otherContact -> {
            SendMailInfo sendMailInfo = new SendMailInfo();
            sendMailInfo.setRecipient(splitStr(otherContact.getRecipient(), ";"));
            sendMailInfo.setCc(splitStr(otherContact.getRecipientCc(), ";"));
            sendMailInfo.setHandler(MailContentHandler.DEFAULT_HANDLER.name());
            sendMailInfo.setContactName(otherContact.getName());
            return sendMailInfo;
        }).collect(Collectors.toList());
    }

    public List<String> dealAll(@NonNull List<String> values) {
        for (String value : values) {
            if ("ALL".equals(value)) {
                return new ArrayList<>();
            }
        }
        return values;
    }


}
