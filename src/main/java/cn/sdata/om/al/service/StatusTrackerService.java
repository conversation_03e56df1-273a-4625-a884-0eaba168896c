package cn.sdata.om.al.service;

import cn.sdata.om.al.dto.OperationStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Date 2025/3/26 16:11
 * @Version 1.0
 */
@Service
public class StatusTrackerService {
    private final AtomicReference<OperationStatus> currentStatus = new AtomicReference<>();

    public void startOperation(String operationName) {
        OperationStatus status = new OperationStatus();
        status.setOperationName(operationName);
        status.setCurrentStep("初始化");
        status.setStartTime(LocalDateTime.now());
        status.setLastUpdateTime(LocalDateTime.now());
        status.setCompleted(false);
        currentStatus.set(status);
    }

    public void updateStep(String step) {
        Optional.ofNullable(currentStatus.get())
                .ifPresent(status -> {
                    status.setCurrentStep(step);
                    status.setLastUpdateTime(LocalDateTime.now());
                });
    }

    public void completeOperation(boolean success, String errorMessage) {
        Optional.ofNullable(currentStatus.get())
                .ifPresent(status -> {
                    status.setCompleted(true);
                    status.setLastUpdateTime(LocalDateTime.now());
                    if (!success) {
                        status.setErrorMessage(errorMessage);
                    }
                });
    }

    public OperationStatus getCurrentStatus() {
        return currentStatus.get();
    }

    public void clear() {
        currentStatus.set(null);
    }
}
