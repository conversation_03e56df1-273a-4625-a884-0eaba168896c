package cn.sdata.om.al.service;

import cn.sdata.om.al.entity.UserRole;
import cn.sdata.om.al.mapper.UserRoleMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 16:10
 * @Version 1.0
 */
@Service
public class UserRoleService extends ServiceImpl<UserRoleMapper, UserRole> {

    /**
     * 用户授权
     *
     * @param list
     */
    public void grantRole(List<UserRole> list) {
        getBaseMapper().insertBatch(list);
    }

    /**
     * 用户对应的权限接口
     *
     * @param userId
     * @param url
     * @return
     */
    public Integer user2ApiCount(String userId, String url) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(url)) {
            return 0;
        }
        return getBaseMapper().user2ApiCount(userId, url);
    }
}
