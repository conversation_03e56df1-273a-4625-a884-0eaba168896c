package cn.sdata.om.al.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.entity.Menu;
import cn.sdata.om.al.entity.Role;
import cn.sdata.om.al.entity.User;
import cn.sdata.om.al.mapper.UserMapper;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/8 14:42
 * @Version 1.0
 */
@Service
@Slf4j
public class UserService extends ServiceImpl<UserMapper, User> {

    @Value("${login.lock-count}")
    private int lockCount;

    /**
     * 获取用户对应的角色列表
     *
     * @param userId
     * @return
     */
    public List<Role> getUser2roleList(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        return getBaseMapper().getUser2roleList(userId);
    }

    /**
     * 获取用户对应的菜单列表
     *
     * @param userId
     * @return
     */
    public List<Menu> getUser2menuList(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        return getBaseMapper().getUser2menuList(userId);
    }

    /**
     * 记录登录失败
     *
     * @param account 账号
     * @param msg     失败原因
     * @param time    解锁时间
     */
    public void recordLoginFailed(String id, String account, String msg, Date time) {
        getBaseMapper().recordLoginFailed(id, account, msg, time);
    }

    /**
     * 查询用户是否被锁定
     *
     * @param account 账号
     * @return 是否被锁定
     */
    public boolean getLoginFailedCount(String account) {
        JSONObject jsonObject = getBaseMapper().getLoginFailedByAccount(account);
        log.info("*****查询出来的账号={}的登录失败信息为:{}", account, jsonObject);
        if (jsonObject != null && !jsonObject.isEmpty()) {
            Date expireTime = jsonObject.getDate("expire_time");
            Integer count = jsonObject.getInteger("count");
            if (expireTime != null && count != null) {
                // 如果登录次数 > 配置锁定次数
                if (count > lockCount) {
                    DateTime current = DateUtil.date();
                    // 如果超过了锁定时间 则尝试登录
                    boolean res = current.isAfterOrEquals(expireTime);
                    if (res) {
                        // 如果此时时间已经超过了锁定时间则应该重置登录失败次数
                        getBaseMapper().resetLoginFailedCount(account);
                    }
                    return res;
                } else {
                    // 没超过次数则尝试登录
                    return true;
                }
            }
        }
        return true;
    }

    public void resetLoginFailedCount(String account) {
        getBaseMapper().resetLoginFailedCount(account);
    }
}
