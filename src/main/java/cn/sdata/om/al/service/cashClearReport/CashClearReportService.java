package cn.sdata.om.al.service.cashClearReport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.cashClear.CashClearReportEntity;
import cn.sdata.om.al.entity.mail.MailInfo;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.job.CashClearReportMailJob;
import cn.sdata.om.al.job.CashClearReportRpaJob;
import cn.sdata.om.al.mapper.cashClearReport.CashClearReportMapper;
import cn.sdata.om.al.mapper.mail.MailInfoMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.AccountInformationService;
import cn.sdata.om.al.service.ExecutionLockService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.JobConstant.DATA_DATE;

/**
 * <AUTHOR>
 * @Date 2025/4/15 10:10
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CashClearReportService extends ServiceImpl<CashClearReportMapper, CashClearReportEntity> {

    private final AccountInformationService accountInformationService;

    private final FuncDataToMailService funcDataToMailService;

    private final MailInfoMapper mailInfoMapper;

    private final ExecutionLockService lockService;

    private final CronService cronService;

    private final BaseCronLogService baseCronLogService;

    /**
     * 分页接口
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param productIds   账套ids
     * @param mailSendStatus 邮件发送状态
     * @param pageNo 第几页
     * @param pageSize 每页多少数据
     * @return 列表数据
     */
    public Page<CashClearReportEntity> page(String beginDate,
                                            String endDate,
                                            List<String> productIds,
                                            String mailSendStatus,
                                            Integer pageNo,
                                            Integer pageSize) throws Exception {
        Page<CashClearReportEntity> page = this.page(new Page<>(pageNo, pageSize), Wrappers.lambdaQuery(CashClearReportEntity.class)
                .ge(StringUtils.isNotBlank(beginDate), CashClearReportEntity::getDataDate, beginDate)
                .le(StringUtils.isNotBlank(endDate), CashClearReportEntity::getDataDate, endDate)
                .in(CollectionUtil.isNotEmpty(productIds), CashClearReportEntity::getProductId, productIds)
                .eq(StringUtils.isNotBlank(mailSendStatus), CashClearReportEntity::getMailSendStatus, mailSendStatus)
                .like(CashClearReportEntity::getFileName, DateUtil.format(DateUtil.parseDate(beginDate), "yyyy年MM月dd日"))
        );
        if (CollUtil.isNotEmpty(page.getRecords())) {
            for (CashClearReportEntity reportEntity : page.getRecords()) {
                String dataDate = reportEntity.getDataDate();
                if (StringUtils.isNotBlank(dataDate)) {
                    reportEntity.setDataDate(DateUtil.format(DateUtil.parseDate(dataDate), "yyyy-MM-dd"));
                }
            }
            List<FuncDataToMailEntity> funcDataToMailEntities = funcDataToMailService.getCashClearReportMailList(beginDate, endDate);
            //所有的mailIds
            List<String> mailIds = funcDataToMailEntities.stream().map(FuncDataToMailEntity::getMailId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(mailIds)) {
                //所有的mailId->mailInfo
                // Map<String, MailInfo> mailId2MailInfoMap = mailInfoMapper.getMailInfoByMailIds(mailIds).stream().collect(Collectors.toMap(MailInfo::getId, Function.identity(), (n1, n2) -> n1));
                //所有的 productId->List<FuncDataToMailEntity>
                Map<String, List<FuncDataToMailEntity>> productId2FuncMailMap = funcDataToMailEntities.stream().collect(Collectors.groupingBy(FuncDataToMailEntity::getProductId));
                for (CashClearReportEntity reportEntity : page.getRecords()) {
                    List<FuncDataToMailEntity> tmp_list = Lists.newArrayList();
                    for (String productId : productId2FuncMailMap.keySet()) {
                        if (productId.contains(reportEntity.getProductId())) {
                            tmp_list = productId2FuncMailMap.get(productId);
                            break;
                        }
                    }
                    if (CollUtil.isNotEmpty(tmp_list)) {
                        List<JSONObject> list = new ArrayList<>();
                        for (FuncDataToMailEntity funcDataToMailEntity : tmp_list) {
                            /*if (mailId2MailInfoMap.containsKey(funcDataToMailEntity.getMailId())) {
                                reportEntity.getMailInfos().add(mailId2MailInfoMap.get(funcDataToMailEntity.getMailId()));
                            }*/
                            JSONObject obj = new JSONObject();
                            obj.put("mailId", funcDataToMailEntity.getMailId());
                            obj.put("mailStatus", funcDataToMailEntity.getMailStatus());
                            obj.put("createTime", funcDataToMailEntity.getCreateTime());
                            obj.put("fileUpdateTime", funcDataToMailEntity.getCreateTime());
                            list.add(obj);
                        }
                        reportEntity.setHistoryMails(list);
                    }
                    List<MailInfo> mailInfos = reportEntity.getMailInfos();
                    if (CollectionUtil.isNotEmpty(mailInfos)) {
                        mailInfos.sort((mail1, mail2) -> mail2.getCreateTime().compareTo(mail1.getCreateTime()));
                    }
                }
            }
        }
        return page;
    }

    /**
     * 执行rpa
     *
     * @param dataDate yyyy-MM-dd
     * @throws Exception
     */
    public void exeRpa(String dataDate) throws Exception {
        String lockName = "cashClearReport_lock_" + dataDate;
        log.info("CashClearReportService_exeRpa_dataDate_run:{},{}", dataDate, lockName);
        if (lockService.isLocked(lockName)) {
            throw new Exception("资金清算报表-任务正在下载中...");
        }
        if (lockService.tryLock(lockName)) {
            try {
                List<String> jobIds = cronService.getJobIdByClass(CashClearReportRpaJob.class);
                if (CollUtil.isEmpty(jobIds)) {
                    throw new Exception("资金清算报表-无对应流程配置信息");
                }
                String newStartDate = toLocalDateStr(dataDate), newEndDate = toLocalDateStr(dataDate);
                BaseCronLog latestLog = baseCronLogService.getLatestLog(jobIds.get(0), (newStartDate + "-" + newEndDate));
                if (latestLog != null && latestLog.getRpaStatus() != null && latestLog.getRpaStatus().equals(JobStatus.RUNNING)) {
                    throw new Exception("资金清算报表-该数据日期对应的rpa任务正在运行中");
                }
                /*if (latestLog != null && (latestLog.getRpaStatus() == null || latestLog.getStatus().equals(JobStatus.RUNNING))) {
                    throw new Exception("资金清算报表-该数据日期对应的下载任务正在运行中");
                }*/
                JobDataMap jobDataMap = new JobDataMap();
                jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
                jobDataMap.put(BaseConstant.SYSTEM_DATE_NAME, dataDate);
                cronService.startJobNow(jobIds, jobDataMap);
            } catch (Exception e) {
                log.error("CashClearReportService_exeRpa_error:{},{}", e, e.getMessage());
                throw e;
            } finally {
                lockService.unlock(lockName);
            }
        }
    }

    /**
     * yyyy-MM-dd转yyyy年MM月dd日
     *
     * @param dataDate yyyy-MM-dd
     * @return
     */
    public String toLocalDateStr(String dataDate) {
        return LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
    }

    /**
     * 发送邮件
     *
     * @param dataDate
     * @return
     * @throws Exception
     */
    public Boolean sendMail(String dataDate) throws Exception {
        try {
            int num1 = Math.toIntExact(this.count(Wrappers.lambdaQuery(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .isNotNull(CashClearReportEntity::getRemoteFileId))),
                    num2 = Math.toIntExact(this.count(Wrappers.lambdaQuery(CashClearReportEntity.class)
                            .eq(CashClearReportEntity::getDataDate, dataDate)
                            .isNull(CashClearReportEntity::getRemoteFileId)));
            if (num1 == 0 || num2 > 0) {
                this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                        .eq(CashClearReportEntity::getDataDate, dataDate)
                        .set(CashClearReportEntity::getMailSendStatus, MailStatus.FAILED.name())
                        .set(CashClearReportEntity::getMailSendTime, new Date())
                        .set(CashClearReportEntity::getUpdateTime, new Date()));
                log.error("CashClearReportService_sendMail_num1:{},num2:{}", num1, num2);
                throw new Exception("相关文件缺失");
            }
            List<String> jobIds = cronService.getJobIdByClass(CashClearReportMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(CronConstant.START_DATE, dataDate);
            jobDataMap.put(CronConstant.END_DATE, dataDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            log.info("CashClearReportService_sendMail_jobIds:{},jobDataMap:{}", jobIds, jobDataMap);
            cronService.startJobNow(jobIds, jobDataMap);
            return this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .set(CashClearReportEntity::getMailSendStatus, MailStatus.SENDING.name())
                    .set(CashClearReportEntity::getMailSendTime, null)
                    .set(CashClearReportEntity::getUpdateTime, new Date()));
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .set(CashClearReportEntity::getMailSendStatus, MailStatus.FAILED.name())
                    .set(CashClearReportEntity::getMailSendTime, new Date())
                    .set(CashClearReportEntity::getUpdateTime, new Date()));
            log.error("CashClearReportService_sendMail_error:{},{}", e, e.getMessage());
            throw e;
        }
    }


    /**
     * 发送邮件
     *
     * @param dataDate
     * @return
     * @throws Exception
     */
    public Boolean sendMailV2(String dataDate, List<String> ids) throws Exception {
        try {
            int num1 = Math.toIntExact(this.count(Wrappers.lambdaQuery(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .isNotNull(CashClearReportEntity::getRemoteFileId))),
                    num2 = Math.toIntExact(this.count(Wrappers.lambdaQuery(CashClearReportEntity.class)
                            .eq(CashClearReportEntity::getDataDate, dataDate)
                            .isNull(CashClearReportEntity::getRemoteFileId)));
            if (num1 == 0 || num2 > 0) {
                this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                        .eq(CashClearReportEntity::getDataDate, dataDate)
                        .set(CashClearReportEntity::getMailSendStatus, MailStatus.FAILED.name())
                        .set(CashClearReportEntity::getMailSendTime, new Date())
                        .set(CashClearReportEntity::getUpdateTime, new Date()));
                log.error("CashClearReportService_sendMail_num1:{},num2:{}", num1, num2);
                throw new Exception("相关文件缺失");
            }
            List<String> jobIds = cronService.getJobIdByClass(CashClearReportMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(CronConstant.START_DATE, dataDate);
            jobDataMap.put(CronConstant.END_DATE, dataDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put("clearIds", ids);
            log.info("CashClearReportService_sendMail_jobIds:{},jobDataMap:{}", jobIds, jobDataMap);
            cronService.startJobNow(jobIds, jobDataMap);
            return this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .in(CollectionUtil.isNotEmpty(ids), CashClearReportEntity::getId, ids)
                    .set(CashClearReportEntity::getMailSendStatus, MailStatus.SENDING.name())
                    .set(CashClearReportEntity::getMailSendTime, null)
                    .set(CashClearReportEntity::getUpdateTime, new Date()));
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(CashClearReportEntity.class)
                    .eq(CashClearReportEntity::getDataDate, dataDate)
                    .in(CollectionUtil.isNotEmpty(ids), CashClearReportEntity::getId, ids)
                    .set(CashClearReportEntity::getMailSendStatus, MailStatus.FAILED.name())
                    .set(CashClearReportEntity::getMailSendTime, new Date())
                    .set(CashClearReportEntity::getUpdateTime, new Date()));
            log.error("CashClearReportService_sendMail_error:{},{}", e, e.getMessage());
            throw e;
        }
    }
}
