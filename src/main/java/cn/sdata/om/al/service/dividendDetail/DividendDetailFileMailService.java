package cn.sdata.om.al.service.dividendDetail;

import cn.sdata.om.al.entity.dividendDetail.DividendDetailFileMailEntity;
import cn.sdata.om.al.mapper.dividendDetail.DividendDetailFileMailMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分红信息明细邮件service
 *
 * <AUTHOR>
 * @Date 2025/5/7 13:29
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendDetailFileMailService extends ServiceImpl<DividendDetailFileMailMapper, DividendDetailFileMailEntity> {
}
