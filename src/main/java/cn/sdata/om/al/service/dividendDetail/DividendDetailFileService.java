package cn.sdata.om.al.service.dividendDetail;

import cn.sdata.om.al.entity.dividendDetail.DividendDetailFileEntity;
import cn.sdata.om.al.mapper.dividendDetail.DividendDetailFileMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分红信息明细文件service
 *
 * <AUTHOR>
 * @Date 2025/5/6 9:54
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendDetailFileService extends ServiceImpl<DividendDetailFileMapper, DividendDetailFileEntity> {
}
