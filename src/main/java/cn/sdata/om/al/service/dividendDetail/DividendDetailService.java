package cn.sdata.om.al.service.dividendDetail;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.dto.DividendDetailLogDto;
import cn.sdata.om.al.dto.DividendParamDto;
import cn.sdata.om.al.entity.dividendDetail.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.job.DividendDetailCustodianBankMailJob;
import cn.sdata.om.al.job.DividendDetailInvestorMailJob;
import cn.sdata.om.al.job.DividendDetailRpaJob;
import cn.sdata.om.al.mapper.dividendDetail.DividendDetailMapper;
import cn.sdata.om.al.mapper.dividendDetail.LogCallRpaDividendDetailMapper;
import cn.sdata.om.al.mapper.dividendDetail.LogMailSendDividendDetailMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.ExecutionLockService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.io.Files;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分红信息明细service
 *
 * <AUTHOR>
 * @Date 2025/4/27 15:16
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DividendDetailService extends ServiceImpl<DividendDetailMapper, DividendDetailEntity> {

    private final ExecutionLockService lock;

    private final CronService cronService;

    private final BaseCronLogService baseCronLogService;

    private final DividendDetailFileService detailFileService;

    private final DividendDetailFileMailService detailMailService;

    private final LogMailSendDividendDetailMapper logMailSendDividendDetailMapper;

    private final LogCallRpaDividendDetailMapper logCallRpaDividendDetailMapper;

    /**
     * 生成文件rpa执行
     *
     * @param dataDate
     * @param productIds
     * @throws Exception
     */
    public void exeRpa(String dataDate, List<String> productIds) throws Exception {
        if (StringUtils.isBlank(dataDate)) {
            throw new Exception("数据日期参数为空");
        }
        String lockName = "dividendDetail_lock_" + dataDate;
        log.info("DividendDetailService_exeRpa_dataDate:{},lockName:{}", dataDate, lockName);
        if (lock.isLocked(lockName)) {
            log.info("DividendDetailService_exeRpa_分红信息明细任务正在下载中...");
            throw new Exception("分红信息明细任务正在下载中...");
        }
        if (lock.tryLock(lockName)) {
            try {
                String localDateStr = toLocalDateStr(dataDate);
                log.info("DividendDetailService_exeRpa_localDate:{}", localDateStr);
                List<String> jobIds = cronService.getJobIdByClass(DividendDetailRpaJob.class);
                if (CollUtil.isEmpty(jobIds)) {
                    log.error("DividendDetailService_exeRpa_无对应rpa流程配置信息");
                    throw new Exception("无对应rpa流程配置信息");
                }
                BaseCronLog latestLog = baseCronLogService.getLatestLog(jobIds.get(0), (localDateStr + "-" + localDateStr));
                log.info("DividendDetailService_exeRpa_latestLog:{}", latestLog);
                if (latestLog != null && latestLog.getRpaStatus() != null && latestLog.getRpaStatus().equals(JobStatus.RUNNING)) {
                    log.error("DividendDetailService_exeRpa_该数据日期对应的rpa任务正在运行中");
                    throw new Exception("该数据日期对应的rpa任务正在运行中");
                }
                JobDataMap jobDataMap = new JobDataMap();
                jobDataMap.put(CronConstant.START_DATE, dataDate);
                jobDataMap.put(CronConstant.END_DATE, dataDate);
                jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
                jobDataMap.put(BaseConstant.SYSTEM_DATE_NAME, dataDate);
                jobDataMap.put(CronConstant.MANUAL, "MANUAL");
                jobDataMap.put("productIdStr", CollUtil.isEmpty(productIds) ? "" : String.join(",", productIds));
                jobDataMap.put("userId", SecureUtil.currentUserId());
                jobDataMap.put("userName", SecureUtil.currentUserName());
                cronService.startJobNow(jobIds, jobDataMap);
            } catch (Exception e) {
                log.error("DividendDetailService_exeRpa_error:{},{}", e, e.getMessage());
                throw e;
            } finally {
                lock.unlock(lockName);
            }
        }
    }

    /**
     * 从rpa调用日志处操作rpa调用
     *
     * @param id
     * @throws Exception
     */
    public void logCallRpa(String id) throws Exception {
        Assert.notNull(id, "参数不能为空");
        LogCallRpaDividendDetailEntity rpaLog = logCallRpaDividendDetailMapper.getCallRpaLogById(id);
        if (ObjectUtil.isNull(rpaLog)) {
            log.error("DividendDetailService_logCallRpa_error:{}", "错误日志数据");
            throw new Exception("错误日志数据");
        }
        String dataDate = rpaLog.getDataDate(), productIdStr = rpaLog.getProductIdStr();
        if (StringUtils.isBlank(dataDate)) {
            log.error("DividendDetailService_logCallRpa_error:{}", "数据日期为空");
            throw new Exception("数据日期为空");
        }
        List<String> productIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(productIdStr)) {
            productIds = Arrays.stream(productIdStr.split(",")).distinct().collect(Collectors.toList());
        }
        exeRpa(dataDate, productIds);
    }

    /**
     * yyyy-MM-dd转yyyy年MM月dd日
     *
     * @param dataDate yyyy-MM-dd
     * @return
     */
    public String toLocalDateStr(String dataDate) {
        return LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
    }

    /**
     * 发送邮件
     *
     * @param list
     */
    public void sendMail(List<DividendDetailEntity> list) {
        try {
            Assert.notNull(list, "sendInvestorMail_账套参数为空");
            String batchId = String.valueOf(System.nanoTime());
            List<DividendDetailFileMailEntity> detailFileMailEntities_bank = Lists.newArrayList(),
                    detailFileMailEntities_investor = Lists.newArrayList();
            for (DividendDetailEntity detailEntity : list) {
                String dataDate = detailEntity.getDataDate(),
                        productId = detailEntity.getProductId();
                DividendDetailEntity detail = this.getOne(Wrappers.lambdaQuery(DividendDetailEntity.class)
                        .eq(DividendDetailEntity::getDataDate, dataDate)
                        .eq(DividendDetailEntity::getProductId, productId));
                List<DividendDetailFileEntity> fileEntities = detailFileService.list(Wrappers.lambdaQuery(DividendDetailFileEntity.class)
                        .eq(DividendDetailFileEntity::getDetailId, detail.getId())
                        .and(wq -> wq
                                .and(wq1 -> wq1.eq(DividendDetailFileEntity::getInitStatus, 0).eq(DividendDetailFileEntity::getSelectStatus, 0))
                                .or(wq2 -> wq2.eq(DividendDetailFileEntity::getInitStatus, 1).eq(DividendDetailFileEntity::getSelectStatus, 1))
                        )
                        .orderByDesc(DividendDetailFileEntity::getCreateTime));
                if (CollUtil.isEmpty(fileEntities) || StringUtils.isEmpty(fileEntities.get(0).getLocalFilePath())) {
                    log.error("DividendDetailService_sendMail_for_fileEntities为空");
                    continue;
                }
                DividendDetailFileEntity fileEntity = fileEntities.get(0);
                detailFileMailEntities_bank.add(new DividendDetailFileMailEntity()
                        .setId(IdWorker.getIdStr())
                        .setDetailId(detail.getId())
                        .setDetailFileId(fileEntity.getId())
                        .setCreateTime(new Date())
                        .setMailSendStatus(MailStatus.SENDING.name())
                        .setMailType("custodianBank")
                        .setBatchId(batchId));
                detailFileMailEntities_investor.add(new DividendDetailFileMailEntity()
                        .setId(IdWorker.getIdStr())
                        .setDetailId(detail.getId())
                        .setDetailFileId(fileEntity.getId())
                        .setCreateTime(new Date())
                        .setMailSendStatus(MailStatus.SENDING.name())
                        .setMailType("investor")
                        .setBatchId(batchId));
            }
            if (CollUtil.isNotEmpty(detailFileMailEntities_bank)) {
                detailMailService.saveBatch(detailFileMailEntities_bank);
            }
            if (CollUtil.isNotEmpty(detailFileMailEntities_investor)) {
                detailMailService.saveBatch(detailFileMailEntities_investor);
            }

            JobDataMap jobDataMap_bank = new JobDataMap();
            jobDataMap_bank.put("detailFileMailIds", detailFileMailEntities_bank.stream().map(DividendDetailFileMailEntity::getId).distinct().collect(Collectors.joining(",")));
            jobDataMap_bank.put(CronConstant.SYNC, true);
            jobDataMap_bank.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap_bank.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap_bank.put("userId", SecureUtil.currentUserId());
            jobDataMap_bank.put("userName", SecureUtil.currentUserName());
            log.info("DividendDetailService_sendMail_jobDataMap_bank:{}", jobDataMap_bank.toString());

            JobDataMap jobDataMap_investor = new JobDataMap();
            jobDataMap_investor.put("detailFileMailIds", detailFileMailEntities_investor.stream().map(DividendDetailFileMailEntity::getId).distinct().collect(Collectors.joining(",")));
            jobDataMap_investor.put(CronConstant.SYNC, true);
            jobDataMap_investor.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap_investor.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap_investor.put("userId", SecureUtil.currentUserId());
            jobDataMap_investor.put("userName", SecureUtil.currentUserName());
            log.info("DividendDetailService_sendMail_jobDataMap_investor:{}", jobDataMap_investor.toString());

            cronService.startJobNow(cronService.getJobIdByClass(DividendDetailCustodianBankMailJob.class), jobDataMap_bank);
            cronService.startJobNow(cronService.getJobIdByClass(DividendDetailInvestorMailJob.class), jobDataMap_investor);
        } catch (Exception e) {
            log.error("DividendDetailService_sendMail_error:{},{}", e, e.getMessage());
        }
    }

    /**
     * 发送邮件-托管行
     *
     * @param list
     */
    public void sendMailBank(List<DividendDetailEntity> list) {
        try {
            Assert.notNull(list, "sendMailBank_账套参数为空");
            String batchId = String.valueOf(System.nanoTime());
            List<DividendDetailFileMailEntity> detailFileMailEntities_bank = Lists.newArrayList();
            for (DividendDetailEntity detailEntity : list) {
                String dataDate = detailEntity.getDataDate(),
                        productId = detailEntity.getProductId();
                DividendDetailEntity detail = this.getOne(Wrappers.lambdaQuery(DividendDetailEntity.class)
                        .eq(DividendDetailEntity::getDataDate, dataDate)
                        .eq(DividendDetailEntity::getProductId, productId));
                List<DividendDetailFileEntity> fileEntities = detailFileService.list(Wrappers.lambdaQuery(DividendDetailFileEntity.class)
                        .eq(DividendDetailFileEntity::getDetailId, detail.getId())
                        .and(wq -> wq
                                .and(wq1 -> wq1.eq(DividendDetailFileEntity::getInitStatus, 0).eq(DividendDetailFileEntity::getSelectStatus, 0))
                                .or(wq2 -> wq2.eq(DividendDetailFileEntity::getInitStatus, 1).eq(DividendDetailFileEntity::getSelectStatus, 1))
                        )
                        .orderByDesc(DividendDetailFileEntity::getCreateTime));
                if (CollUtil.isEmpty(fileEntities) || StringUtils.isEmpty(fileEntities.get(0).getLocalFilePath())) {
                    log.error("DividendDetailService_sendMailBank_for_fileEntities为空");
                    continue;
                }
                DividendDetailFileEntity fileEntity = fileEntities.get(0);
                detailFileMailEntities_bank.add(new DividendDetailFileMailEntity()
                        .setId(IdWorker.getIdStr())
                        .setDetailId(detail.getId())
                        .setDetailFileId(fileEntity.getId())
                        .setCreateTime(new Date())
                        .setMailSendStatus(MailStatus.SENDING.name())
                        .setMailType("custodianBank")
                        .setBatchId(batchId));
            }
            if (CollUtil.isNotEmpty(detailFileMailEntities_bank)) {
                detailMailService.saveBatch(detailFileMailEntities_bank);
            }

            JobDataMap jobDataMap_bank = new JobDataMap();
            jobDataMap_bank.put("detailFileMailIds", detailFileMailEntities_bank.stream().map(DividendDetailFileMailEntity::getId).distinct().collect(Collectors.joining(",")));
            jobDataMap_bank.put(CronConstant.SYNC, true);
            jobDataMap_bank.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap_bank.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap_bank.put("userId", SecureUtil.currentUserId());
            jobDataMap_bank.put("userName", SecureUtil.currentUserName());
            log.info("DividendDetailService_sendMailBank_jobDataMap_bank:{}", jobDataMap_bank.toString());

            cronService.startJobNow(cronService.getJobIdByClass(DividendDetailCustodianBankMailJob.class), jobDataMap_bank);
        } catch (Exception e) {
            log.error("DividendDetailService_sendMailBank_error:{},{}", e, e.getMessage());
        }
    }

    /**
     * 发送邮件-投资人
     *
     * @param list
     */
    public void sendMailInvestor(List<DividendDetailEntity> list) {
        try {
            Assert.notNull(list, "sendMailInvestor_账套参数为空");
            String batchId = String.valueOf(System.nanoTime());
            List<DividendDetailFileMailEntity> detailFileMailEntities_investor = Lists.newArrayList();
            for (DividendDetailEntity detailEntity : list) {
                String dataDate = detailEntity.getDataDate(),
                        productId = detailEntity.getProductId();
                DividendDetailEntity detail = this.getOne(Wrappers.lambdaQuery(DividendDetailEntity.class)
                        .eq(DividendDetailEntity::getDataDate, dataDate)
                        .eq(DividendDetailEntity::getProductId, productId));
                List<DividendDetailFileEntity> fileEntities = detailFileService.list(Wrappers.lambdaQuery(DividendDetailFileEntity.class)
                        .eq(DividendDetailFileEntity::getDetailId, detail.getId())
                        .and(wq -> wq
                                .and(wq1 -> wq1.eq(DividendDetailFileEntity::getInitStatus, 0).eq(DividendDetailFileEntity::getSelectStatus, 0))
                                .or(wq2 -> wq2.eq(DividendDetailFileEntity::getInitStatus, 1).eq(DividendDetailFileEntity::getSelectStatus, 1))
                        )
                        .orderByDesc(DividendDetailFileEntity::getCreateTime));
                if (CollUtil.isEmpty(fileEntities) || StringUtils.isEmpty(fileEntities.get(0).getLocalFilePath())) {
                    log.error("DividendDetailService_sendMailInvestor_for_fileEntities为空");
                    continue;
                }
                DividendDetailFileEntity fileEntity = fileEntities.get(0);
                detailFileMailEntities_investor.add(new DividendDetailFileMailEntity()
                        .setId(IdWorker.getIdStr())
                        .setDetailId(detail.getId())
                        .setDetailFileId(fileEntity.getId())
                        .setCreateTime(new Date())
                        .setMailSendStatus(MailStatus.SENDING.name())
                        .setMailType("investor")
                        .setBatchId(batchId));
            }
            if (CollUtil.isNotEmpty(detailFileMailEntities_investor)) {
                detailMailService.saveBatch(detailFileMailEntities_investor);
            }

            JobDataMap jobDataMap_investor = new JobDataMap();
            jobDataMap_investor.put("detailFileMailIds", detailFileMailEntities_investor.stream().map(DividendDetailFileMailEntity::getId).distinct().collect(Collectors.joining(",")));
            jobDataMap_investor.put(CronConstant.SYNC, true);
            jobDataMap_investor.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap_investor.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap_investor.put("userId", SecureUtil.currentUserId());
            jobDataMap_investor.put("userName", SecureUtil.currentUserName());
            log.info("DividendDetailService_sendMailInvestor_jobDataMap_investor:{}", jobDataMap_investor.toString());

            cronService.startJobNow(cronService.getJobIdByClass(DividendDetailInvestorMailJob.class), jobDataMap_investor);
        } catch (Exception e) {
            log.error("DividendDetailService_sendMailInvestor_error:{},{}", e, e.getMessage());
        }
    }

    /**
     * page查询
     *
     * @param dto
     * @return
     */
    public Page<DividendDetailEntity> detailPage(DividendParamDto dto) {
        try {
            if (dto.getPageNo() == null || dto.getPageNo() == 0) {
                dto.setPageNo(1);
            }
            if (dto.getPageSize() == null || dto.getPageSize() == 0) {
                dto.setPageSize(20);
            }
            List<DividendDetailEntity> details = this.baseMapper.getD2F2MListV1(dto.getStartDate(), dto.getEndDate(), dto.getProductNames());
            if (StringUtils.isNotEmpty(dto.getMailSendStatus())) {
                details = details.stream().filter(
                        i -> (dto.getMailSendStatus().equals(i.getMailBankSendStatus()) || dto.getMailSendStatus().equals(i.getMailInvestorSendStatus()))
                ).distinct().collect(Collectors.toList());
            }
            if (StringUtils.isNotEmpty(dto.getMailBankSendStatus())) {
                details = details.stream().filter(
                        i -> (dto.getMailBankSendStatus().equals(i.getMailBankSendStatus()))
                ).distinct().collect(Collectors.toList());
            }
            if (StringUtils.isNotEmpty(dto.getMailInvestorSendStatus())) {
                details = details.stream().filter(
                        i -> (dto.getMailInvestorSendStatus().equals(i.getMailInvestorSendStatus()))
                ).distinct().collect(Collectors.toList());
            }
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            details.sort(Comparator.comparing((DividendDetailEntity d) -> d.getDataDate() != null ? LocalDate.parse(d.getDataDate(), dateFormatter) : null,
                    Comparator.nullsLast(Comparator.reverseOrder()))
                    .thenComparing(DividendDetailEntity::getProductId, Comparator.nullsLast(Comparator.naturalOrder()))
            );
            int total = details.size();
            int fromIndex = (dto.getPageNo() - 1) * dto.getPageSize();
            int toIndex = Math.min(fromIndex + dto.getPageSize(), total);
            List<DividendDetailEntity> pageList = details.subList(fromIndex, toIndex);
            Page<DividendDetailEntity> page = new Page<>(dto.getPageNo(), dto.getPageSize(), total);
            page.setRecords(pageList);
            return page;
        } catch (Exception e) {
            log.error("DividendDetailService_detailPage_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 历史邮件查询
     *
     * @param detailId
     * @param dataDate
     * @return
     */
    public List<DividendDetailEntity> historySendMails(String detailId, String dataDate) {
        try {
            return this.baseMapper.getSendMails(detailId, dataDate);
        } catch (Exception e) {
            log.error("DividendDetailService_historySendMails_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取账套名称列表
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public List<String> getProductNames(String startDate, String endDate) {
        try {
            return this.list(Wrappers.lambdaQuery(DividendDetailEntity.class).between(DividendDetailEntity::getDataDate, startDate, endDate))
                    .stream()
                    .map(DividendDetailEntity::getProductName).distinct().sorted().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("DividendDetailService_getProductNames_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 单文件下载
     *
     * @param fileId
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadById(String fileId) throws Exception {
        DividendDetailFileEntity fileEntity = detailFileService.getById(fileId);
        File file = new File(fileEntity.getLocalFilePath());
        if (ObjectUtil.isNull(file) || !file.exists())
            throw new Exception("目标文件不存在");
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }

    /**
     * 获取不包含后缀名的文件名称
     *
     * @param fileName a.xlsx
     * @return a
     */
    private static String getFileNameNoExtension(String fileName) {
        return fileName.replaceFirst("[.][^.]+$", "");
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    public String getFileExt(String fileName) {
        return Files.getFileExtension(fileName);
    }

    /**
     * 获取分红明细日志数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public DividendDetailLogDto getDividendDetailLogs(String startDate, String endDate) {
        DividendDetailLogDto dto = new DividendDetailLogDto();
        dto.setLogMailSend(logMailSendDividendDetailMapper.selectList(Wrappers.lambdaQuery(LogMailSendDividendDetailEntity.class)
                .between(LogMailSendDividendDetailEntity::getDataDate, startDate, endDate)
                .orderByDesc(LogMailSendDividendDetailEntity::getCreateTime)
        ));
        dto.setLogCallRpas(logCallRpaDividendDetailMapper.getCallRpaLogsByDataDate(startDate, endDate));
        return dto;
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadByLogId(String id, String logType) throws Exception {
        String filePath = "";
        switch (logType) {
            case "callRpa":
                LogCallRpaDividendDetailEntity callRpa = logCallRpaDividendDetailMapper.selectById(id);
                if (ObjectUtil.isNotNull(callRpa)) {
                    filePath = callRpa.getFilePath();
                }
                break;
        }
        if (StringUtils.isBlank(filePath)) {
            throw new Exception("文件不存在");
        }
        File file = new File(filePath);
        if (ObjectUtil.isNull(file) || !file.exists()) {
            throw new Exception("目标文件不存在");
        }
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }
}
