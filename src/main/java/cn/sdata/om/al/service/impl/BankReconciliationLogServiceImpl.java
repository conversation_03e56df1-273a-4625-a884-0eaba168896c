package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.job.BankReconciliationSQRpaJob;
import cn.sdata.om.al.job.BankReconciliationZZRpaJob;
import cn.sdata.om.al.mapper.BankReconciliationLogMapper;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.BankReconciliationLogService;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

@Service
public class BankReconciliationLogServiceImpl implements BankReconciliationLogService {

    private BankReconciliationLogMapper bankReconciliationLogMapper;

    @Autowired
    public void setBankReconciliationLogMapper(BankReconciliationLogMapper bankReconciliationLogMapper) {
        this.bankReconciliationLogMapper = bankReconciliationLogMapper;
    }

    @Override
    public PageInfo<LogBRImportFileRecord> pageLogImport(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogBRImportFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogBRImportFileRecord> logBRImportFileRecords = bankReconciliationLogMapper.pageLogImport(beginDataDate, endDataDate);
            return new PageInfo<>(logBRImportFileRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogBRMarkDiffRecord> pageLogDiff(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogBRMarkDiffRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogBRMarkDiffRecord> logBRMarkDiffRecords = bankReconciliationLogMapper.pageLogDiff(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logBRMarkDiffRecords)) {
                for (LogBRMarkDiffRecord logBRMarkDiffRecord : logBRMarkDiffRecords) {
                    String id = logBRMarkDiffRecord.getId();
                    List<LogBRMarkDiffDesc> logBRMarkDiffDescList = bankReconciliationLogMapper.getDiffDesc(id);
                    logBRMarkDiffRecord.setMarkDesc(logBRMarkDiffDescList);
                }
            }
            return new PageInfo<>(logBRMarkDiffRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogBRExportFileRecord> pageLogExport(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogBRExportFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogBRExportFileRecord> logBRExportFileRecords = bankReconciliationLogMapper.pageLogExport(beginDataDate, endDataDate);
            return new PageInfo<>(logBRExportFileRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogBRSyncValuationRecord> pageLogSync(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogBRSyncValuationRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogBRSyncValuationRecord> logBRSyncValuationRecords = bankReconciliationLogMapper.pageLogSync(beginDataDate, endDataDate);
            return new PageInfo<>(logBRSyncValuationRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogBRRPARecord> getLogBRRPARecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogBRRPARecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogBRRPARecord> logBRRPARecords = bankReconciliationLogMapper.getLogBRRPARecordList(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logBRRPARecords)) {
                for (LogBRRPARecord record : logBRRPARecords) {
                    String status = record.getStatus();
                    String fileType = record.getFileType();
                    if (StringUtils.isNotBlank(fileType)) {
                        if ("中债".equals(fileType)) {
                            record.setFileTypeValue(0);
                        } else if ("上清".equals(fileType)) {
                            record.setFileTypeValue(1);
                        }
                    }
                    if (StringUtils.isNotBlank(status)) {
                        if ("COMPLETE".equals(status)) {
                            record.setRpaResult("执行完成");
                        } else if ("FAILED".equals(status)) {
                            record.setRpaResult("执行失败");
                        } else {
                            record.setRpaResult("执行中");
                        }
                    }
                }
            }
            return new PageInfo<>(logBRRPARecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public Boolean reExecuteRpa(String rpaLogId) {
        LogBRRPARecord logBRRPARecord = bankReconciliationLogMapper.getBRRpaLogById(rpaLogId);
        if (logBRRPARecord != null) {
            String rpaParam = logBRRPARecord.getRpaParam();
            if (StringUtils.isNotBlank(rpaParam)) {
                JSONObject rpaParamJson = JSONObject.parseObject(rpaParam);
                if (rpaParamJson != null && rpaParamJson.containsKey("flowExtendParams")) {
                    JSONObject extendParams = rpaParamJson.getJSONObject("flowExtendParams");
                    FlowList flow = rpaParamJson.getObject("flow", FlowList.class);
                    CronService cronService = SpringUtil.getBean(CronService.class);
                    if (flow != null) {
                        String dataDate = extendParams.getString(RPA_START_DATE_NAME);
                        if (flow.getShowName().contains("上清")) {
                            List<String> fySQRpaJobIds = cronService.getJobIdByClass(BankReconciliationSQRpaJob.class);
                            if (CollectionUtil.isNotEmpty(fySQRpaJobIds)) {
                                JobDataMap sqJobDataMap = new JobDataMap();
                                sqJobDataMap.put("dataDate", dataDate);
                                sqJobDataMap.put("username", SecureUtil.currentUserName());
                                sqJobDataMap.put("type", "MANUAL");
                                cronService.startJobNow(fySQRpaJobIds, sqJobDataMap);
                            }
                        } else if (flow.getShowName().contains("中债")) {
                            List<String> fyZZRpaJobIds = cronService.getJobIdByClass(BankReconciliationZZRpaJob.class);
                            if (CollectionUtil.isNotEmpty(fyZZRpaJobIds)) {
                                JobDataMap zzJobDataMap = new JobDataMap();
                                // 中债 对账单下载
                                zzJobDataMap.put("username", SecureUtil.currentUserName());
                                zzJobDataMap.put("dataDate", dataDate);
                                zzJobDataMap.put("type", "MANUAL");
                                cronService.startJobNow(fyZZRpaJobIds, zzJobDataMap);
                            }
                        }
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
