package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.sdata.om.al.config.BankReconciliationSyncConfig;
import cn.sdata.om.al.entity.BankReconciliation;
import cn.sdata.om.al.entity.BankReconciliationFile;
import cn.sdata.om.al.entity.BankReconciliationQuery;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailCommonInfo;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.BankReconciliationSQRpaJob;
import cn.sdata.om.al.job.BankReconciliationZZRpaJob;
import cn.sdata.om.al.mapper.BankReconciliationMapper;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.BankReconciliationService;
import cn.sdata.om.al.service.ProjectGroupService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;


@Service
@Slf4j
public class BankReconciliationServiceImpl implements BankReconciliationService {

    @Value("${file.dir}")
    private String filePath;

    private BankReconciliationMapper bankReconciliationMapper;

    private static volatile boolean isUpload = false;

    @Autowired
    public void setBankReconciliationMapper(BankReconciliationMapper bankReconciliationMapper) {
        this.bankReconciliationMapper = bankReconciliationMapper;
    }

    @Override
    public PageInfo<BankReconciliation> page(BankReconciliationQuery bankReconciliationQuery) {
        int pageNo = bankReconciliationQuery.getPageNo();
        int pageSize = bankReconciliationQuery.getPageSize();
        ProjectGroupService projectGroupService = SpringUtil.getBean(ProjectGroupService.class);
        List<CommonEntity> list = projectGroupService.list();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<BankReconciliation> bankReconciliations = bankReconciliationMapper.page(bankReconciliationQuery);
            for (BankReconciliation bankReconciliation : bankReconciliations) {
                String dataDate = bankReconciliation.getDataDate();
                String valuationPositionQuantity = bankReconciliation.getValuationPositionQuantity();
                String settlementCompanyPositionQuantity = bankReconciliation.getSettlementCompanyPositionQuantity();
                if (StringUtils.isBlank(valuationPositionQuantity)) {
                    valuationPositionQuantity = "0.0";
                }
                if (StringUtils.isBlank(settlementCompanyPositionQuantity)) {
                    settlementCompanyPositionQuantity = "0.0";
                }
                BigDecimal number = new BigDecimal(valuationPositionQuantity).subtract(new BigDecimal(settlementCompanyPositionQuantity));
                BigDecimal roundedNumber = number.setScale(1, RoundingMode.HALF_UP);
                bankReconciliation.setDifference(roundedNumber.toPlainString());
                String accountSetCode = bankReconciliation.getAccountSetCode();
                if (StringUtils.isNotBlank(dataDate)) {
                    DateTime dateTime = DateUtil.parseDate(dataDate);
                    bankReconciliation.setDataDate(DateUtil.format(dateTime, "yyyy年MM月dd日"));
                    for (CommonEntity commonEntity : list) {
                        String extra = commonEntity.getExtra();
                        if (StringUtils.isNotBlank(extra)) {
                            if (CollectionUtil.toList(extra.split(",")).contains(accountSetCode)) {
                                bankReconciliation.setAccountSetGroupName(commonEntity.getName());
                                break;
                            }
                        }
                    }
                }
            }
            return new PageInfo<>(bankReconciliations);
        }
    }

    @Override
    public List<CommonEntity> settlementLocationList() {
        return CommonUtil.stringToCommonEntityList(bankReconciliationMapper.settlementLocationList());
    }

    @Override
    public List<CommonEntity> securityNameList() {
        return CommonUtil.stringToCommonEntityList(bankReconciliationMapper.securityNameList());
    }

    @Override
    public List<CommonEntity> securitiesCodeList() {
        return CommonUtil.stringToCommonEntityList(bankReconciliationMapper.securitiesCodeList());
    }

    @Override
    public Boolean markDifference(JSONObject jsonObject) {
        String differenceReasons = jsonObject.getString("differenceReasons");
        List<String> ids = jsonObject.getList("ids", String.class);
        String userId = jsonObject.getString("userId");
        return bankReconciliationMapper.markDifference(ids, differenceReasons, userId) > 0;
    }

    @Override
    public List<File> downloadSettlementCompanyFiles(String beginDate, String endDate) {
        List<File> fileList = new ArrayList<>();
        List<BankReconciliationFile> bankReconciliationFiles = bankReconciliationMapper.getBankReconciliationFiles(beginDate, endDate);
        if (CollectionUtil.isNotEmpty(bankReconciliationFiles)) {
            for (BankReconciliationFile file : bankReconciliationFiles) {
                File file1 = new File(file.getFilePath());
                fileList.add(file1);
            }
        }
        return fileList;
    }

    @Override
    public String syncBankReconciliation(String beginDate, String endDate) {
        boolean useNewCode = false;
        String property = SpringUtil.getProperty("bank-reconciliation.use-new-code");
        if (StringUtils.isNotBlank(property)) {
            useNewCode = Boolean.parseBoolean(property);
        }
        if (useNewCode) {
            log.info("执行同步估值系统的重构代码...");
            return syncBankReconciliationV2(beginDate, endDate);
        }
        if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            // 如果2个日期有一个为空 则按照当天查询
            beginDate = DateUtil.today();
            endDate = DateUtil.today();
        }
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("dataDate", beginDate);
            params.put("params", beginDate + "-" + endDate);
            LogBRUtil.preSyncLog(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        BankReconciliationSyncConfig syncConfig = SpringUtil.getBean(BankReconciliationSyncConfig.class);
        // 按照时间范围检索对账表 获取此时间范围内的所有证券信息
        List<BankReconciliation> bankReconciliations = bankReconciliationMapper.selectBankReconciliationInfoByDate(beginDate, endDate);
        String status = syncConfig.getStatus();
        if ("executing".equals(status)) {
            // 如果任务在执行中 则直接返回状态
            log.info("同步任务正在执行中,不允许再次执行...");
            LogBRUtil.deleteSyncLog(params.getString("logId"));
            return "executing";
        } else if ("noStart".equals(status) || "completed".equals(status)) {
            synchronized (BankReconciliationServiceImpl.class) {
                log.info("同步任务准备开始执行...");
                syncConfig.setStatus("executing");
                // 查询在这个时间范围内所有估值库中的信息
                List<JSONObject> jsonObjectList = bankReconciliationMapper.queryValuationTableByDateScope(beginDate, endDate);
                List<MailCommonInfo> mailCommonInfos = SpringUtil.getBean(AccountSetMapper.class).queryAll();
                Map<String, List<MailCommonInfo>> commonInfoMap = mailCommonInfos.stream().collect(Collectors.groupingBy(MailCommonInfo::getId));
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        if (CollectionUtil.isEmpty(bankReconciliations)) {
                            // 只新增估值库中有的
                            if (CollectionUtil.isNotEmpty(jsonObjectList)) {
                                List<BankReconciliation> collect = jsonObjectList.stream().map(n -> {
                                    BankReconciliation bankReconciliation = new BankReconciliation();
                                    bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
                                    bankReconciliation.setDataDate(n.getString("D_DATE"));
                                    bankReconciliation.setSecurityName(n.getString("VC_ZQJC"));
                                    bankReconciliation.setSettlementLocation(n.getString("VC_LOCATION"));
                                    bankReconciliation.setSecurityCode(n.getString("VC_ZQDM"));
                                    bankReconciliation.setValuationPositionQuantity(n.getString("L_ZQCC"));
                                    bankReconciliation.setSourceCode(n.getString("VC_TRADEZQDM"));
                                    String lZtbh = n.getString("L_ZTBH");
                                    bankReconciliation.setAccountSetCode(lZtbh);
                                    List<MailCommonInfo> mailCommonInfos1 = commonInfoMap.get(lZtbh);
                                    if (CollectionUtil.isNotEmpty(mailCommonInfos1)) {
                                        MailCommonInfo mailCommonInfo = mailCommonInfos1.get(0);
                                        bankReconciliation.setAccountSetName(mailCommonInfo.getAccountName());
                                        bankReconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                                        bankReconciliation.setAccountNumber(mailCommonInfo.getHolderAccountNumber());
                                        bankReconciliation.setProductCode(mailCommonInfo.getProductCode());
                                        bankReconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                                        bankReconciliation.setValuationTime(mailCommonInfo.getValuationTime());
                                    }
                                    return bankReconciliation;
                                }).collect(Collectors.toList());
                                if (CollectionUtil.isNotEmpty(collect)) {
                                    bankReconciliationMapper.saveBatch(collect);
                                }
                            }
                            return;
                        }
                        for (BankReconciliation bankReconciliation : bankReconciliations) {
                            String id = bankReconciliation.getId();
                            String securityCode = bankReconciliation.getSecurityCode();
                            String sourceCode = bankReconciliation.getSourceCode();
                            String dataDate = bankReconciliation.getDataDate();
                            String accountSetCode = bankReconciliation.getAccountSetCode();
                            String valuationPositionQuantity = bankReconciliation.getValuationPositionQuantity();
                            log.info("需要查询的条件是: securityCode = {}, dataDate = {}, accountSetCode = {}", securityCode, dataDate, accountSetCode);
                            // 根据上述信息查询估值库 后更新对账表中的估值余额
                            List<JSONObject> jsonObjects = bankReconciliationMapper.queryValuationTableByConditions(securityCode, dataDate, accountSetCode);
                            if (CollectionUtil.isEmpty(jsonObjects)) {
                                // 尝试使用结算公司代码匹配
                                jsonObjects = bankReconciliationMapper.queryValuationTableByConditionsAndSourceCode(sourceCode, dataDate, accountSetCode);
                            }
                            log.info("查询出来的估值表中的数据为:{}", JSON.toJSONString(jsonObjects));
                            if (CollectionUtil.isNotEmpty(jsonObjects)) {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("id", id);
                                // 估值表中有对应条件的值
                                if (jsonObjects.size() > 1) {
                                    // 如果有多条 需要将余额进行累加
                                    Map<String, Double> collect = jsonObjects.stream().collect(Collectors.groupingBy(n -> n.getString("L_ZTBH"),
                                            Collectors.summingDouble(n -> n.getDouble("L_ZQCC"))));
                                    jsonObject.put("valuationPositionQuantity", collect.get(accountSetCode).toString());
                                } else {
                                    JSONObject res = jsonObjects.get(0);
                                    jsonObject.put("valuationPositionQuantity", res.getDouble("L_ZQCC").toString());
                                }
                                log.info("需要同步的id = {}, 估值持仓数为:{}", id, jsonObject.getDouble("valuationPositionQuantity"));
                                // 更新对账表
                                if (null != jsonObject.getDouble("valuationPositionQuantity")) {
                                    log.info("同步估值持仓数 由原{} -> {} 需要更新", valuationPositionQuantity, jsonObject.getDouble("valuationPositionQuantity").toString());
                                    bankReconciliationMapper.syncBankReconciliation(jsonObject);
                                }
                            }
                        }
                        // 两个集合做减法用来标识 估值库可能新增的
                        handleMoreData(jsonObjectList, bankReconciliations);
                        // 两个集合做减法用来标识 估值库可能减少的
                        handleLessData(jsonObjectList, bankReconciliations);
                        try {
                            params.put("endTime", DateUtil.now());
                            params.put("status", CommonStatus.SUCCESS.name());
                            LogBRUtil.postSyncLog(params);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            params.put("endTime", DateUtil.now());
                            params.put("exception", e.getMessage());
                            params.put("status", CommonStatus.FAIL.name());
                            LogBRUtil.postSyncLog(params);
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                    }
                });
                syncConfig.setCompletableFuture(completableFuture);
            }
        }
        return "noStart";
    }

    /**
     * 同步时需要可能将估值库中减少到同步到数据库中
     *
     * @param jsonObjectList      估值库数据
     * @param bankReconciliations 库中存在的
     */
    private void handleLessData(List<JSONObject> jsonObjectList, List<BankReconciliation> bankReconciliations) {
        if (CollectionUtil.isNotEmpty(jsonObjectList)) {
            List<BankReconciliation> collect = jsonObjectList.stream().map(n -> {
                BankReconciliation bankReconciliation = new BankReconciliation();
                bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
                bankReconciliation.setDataDate(n.getString("D_DATE"));
                bankReconciliation.setSecurityName(n.getString("VC_ZQJC"));
                bankReconciliation.setSettlementLocation(n.getString("VC_LOCATION"));
                bankReconciliation.setSecurityCode(n.getString("VC_ZQDM"));
                bankReconciliation.setSourceCode(n.getString("VC_TRADEZQDM"));
                bankReconciliation.setAccountSetCode(n.getString("L_ZTBH"));
                return bankReconciliation;
            }).collect(Collectors.toList());
            List<BankReconciliation> reconciliationList = CollectionUtil.subtractToList(bankReconciliations, collect);
            if (CollectionUtil.isNotEmpty(reconciliationList)) {
                log.info("同步时需要可能将估值库中减少到同步到数据库中:{}", JSON.toJSONString(reconciliationList));
                for (BankReconciliation bankReconciliation : reconciliationList) {
                    bankReconciliationMapper.updateQuantityToZero(bankReconciliation);
                }
            }
        }
    }

    /**
     * 同步时需要将可能估值库中新增的插入到数据库中
     *
     * @param jsonObjectList      估值库全量数据
     * @param bankReconciliations 库中已经存在的
     */
    private void handleMoreData(List<JSONObject> jsonObjectList, List<BankReconciliation> bankReconciliations) {
        // 查询所有账套信息
        List<MailCommonInfo> mailCommonInfos = SpringUtil.getBean(AccountSetMapper.class).queryAll();
        Map<String, List<MailCommonInfo>> commonInfoMap = mailCommonInfos.stream().collect(Collectors.groupingBy(MailCommonInfo::getId));
        if (CollectionUtil.isNotEmpty(jsonObjectList)) {
            List<BankReconciliation> collect = jsonObjectList.stream().map(n -> {
                BankReconciliation bankReconciliation = new BankReconciliation();
                bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
                bankReconciliation.setDataDate(n.getString("D_DATE"));
                bankReconciliation.setSecurityName(n.getString("VC_ZQJC"));
                bankReconciliation.setSettlementLocation(n.getString("VC_LOCATION"));
                bankReconciliation.setSecurityCode(n.getString("VC_ZQDM"));
                bankReconciliation.setSourceCode(n.getString("VC_TRADEZQDM"));
                bankReconciliation.setValuationPositionQuantity(n.getString("L_ZQCC"));
                String lZtbh = n.getString("L_ZTBH");
                bankReconciliation.setAccountSetCode(lZtbh);
                List<MailCommonInfo> mailCommonInfos1 = commonInfoMap.get(lZtbh);
                if (CollectionUtil.isNotEmpty(mailCommonInfos1)) {
                    MailCommonInfo mailCommonInfo = mailCommonInfos1.get(0);
                    bankReconciliation.setAccountSetName(mailCommonInfo.getAccountName());
                    bankReconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                    bankReconciliation.setAccountNumber(mailCommonInfo.getHolderAccountNumber());
                    bankReconciliation.setProductCode(mailCommonInfo.getProductCode());
                    bankReconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                    bankReconciliation.setValuationTime(mailCommonInfo.getValuationTime());
                }
                return bankReconciliation;
            }).collect(Collectors.toList());
            List<BankReconciliation> subtract = CollectionUtil.subtractToList(collect, bankReconciliations);
            log.info("T1估值库新增的对账信息为:{}", JSON.toJSONString(subtract));
            if (CollectionUtil.isNotEmpty(subtract)) {
                Set<BankReconciliation> reconciliations = new HashSet<>(subtract);
                if (CollectionUtil.isNotEmpty(reconciliations)) {
                    bankReconciliationMapper.saveBatch(ListUtil.toList(reconciliations));
                }
            }
        }
    }

    @Override
    public String upload(List<MultipartFile> files, String executeType, String selectedDate) {
        boolean useNewCode = false;
        String property = SpringUtil.getProperty("bank-reconciliation.use-new-code");
        if (StringUtils.isNotBlank(property)) {
            useNewCode = Boolean.parseBoolean(property);
        }
        if (useNewCode) {
            log.info("执行上传文件的重构代码...");
            return uploadV2(files, executeType, selectedDate);
        }
        if (!isUpload) {
            List<File> fileList = new ArrayList<>();
            for (MultipartFile file : files) {
                File uploadFile = new File(filePath + File.separator + file.getOriginalFilename());
                try {
                    file.transferTo(uploadFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                fileList.add(uploadFile);
            }
            String username = SecureUtil.currentUserName();
            CompletableFuture.runAsync(() -> {
                isUpload = true;
                log.info("开始导入银行对账文件...");
                if (CollectionUtil.isNotEmpty(fileList)) {
                    // 查询所有账套信息
                    List<MailCommonInfo> mailCommonInfos = SpringUtil.getBean(AccountSetMapper.class).queryAll();
                    for (File file : fileList) {
                        JSONObject params = new JSONObject();
                        try {
                            params.put("logId", IdUtil.getSnowflakeNextIdStr());
                            params.put("username", username);
                            params.put("beginTime", DateUtil.now());
                            params.put("fileUrl", file.getAbsolutePath());
                            params.put("type", executeType);
                            LogBRUtil.preUploadLog(params);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            // 上清文件
                            String date = null;
                            ExcelReader reader = ExcelUtil.getReader(file, 0);
                            // 读取第一行如果发现是 债券账户总对账单 则为中债文件
                            List<Object> objects = reader.readRow(0);
                            if (CollectionUtil.isEmpty(objects)) {
                                BusinessException.throwException("模板不正确");
                            }
                            Optional<String> first = objects.stream().map(String::valueOf).distinct().findFirst();
                            String type = "";
                            if (first.isPresent()) {
                                String firstRow = first.get();
                                if ("债券账户总对账单".equals(firstRow)) {
                                    type = "ZZ";
                                } else {
                                    type = "SQ";
                                }
                            }
                            if ("SQ".equals(type)) {
                                date = resolveSQExcel(file, mailCommonInfos, reader);
                            } else if ("ZZ".equals(type)) {
                                // 中债文件
                                date = resolveZZExcel(file, mailCommonInfos, reader);
                            }
                            // 记录文件
                            BankReconciliationFile bankReconciliationFile = new BankReconciliationFile();
                            bankReconciliationFile.setId(IdUtil.getSnowflakeNextIdStr());
                            bankReconciliationFile.setDataDate(date);
                            bankReconciliationFile.setType(type);
                            bankReconciliationFile.setFileName(file.getName());
                            bankReconciliationFile.setFilePath(file.getAbsolutePath());
                            bankReconciliationMapper.saveBankReconciliationFile(bankReconciliationFile);
                            JSONObject jsonObject1 = selectSyncTimeByEndDate(date);
                            if (jsonObject1 != null && !jsonObject1.isEmpty()) {
                                updateAllSyncTime(date, DateUtil.now());
                            } else {
                                insertAllSyncTime(date, DateUtil.now());
                            }
                            try {
                                params.put("endTime", DateUtil.now());
                                params.put("dataDate", date);
                                params.put("status", CommonStatus.SUCCESS.name());
                                LogBRUtil.postUploadLog(params);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            try {
                                params.put("endTime", DateUtil.now());
                                params.put("exception", e.getMessage());
                                params.put("status", CommonStatus.FAIL.name());
                                LogBRUtil.postUploadLog(params);
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                }
                isUpload = false;
                log.info("银行间缴费信息录入结束...");
            });
        }
        return isUpload ? "executing" : "completed";
    }

    @Override
    public List<CommonEntity> sourceCodeList() {
        return CommonUtil.stringToCommonEntityList(bankReconciliationMapper.sourceCodeList());
    }

    @Override
    public JSONObject selectSyncTimeByUserId(String id) {
        return bankReconciliationMapper.selectSyncTimeByUserId(id);
    }

    @Override
    public void updateSyncTime(String endDate, String now) {
        bankReconciliationMapper.updateSyncTime(endDate, now);
    }

    @Override
    public void insertSyncTime(String endDate, String now) {
        bankReconciliationMapper.insertSyncTime(endDate, now);
    }

    @Override
    public void updateSettlementSyncTime(String id, String now) {
        bankReconciliationMapper.updateSettlementSyncTime(id, now);
    }

    @Override
    public void insertSettlementSyncTime(String id, String now) {
        bankReconciliationMapper.insertSettlementSyncTime(id, now);
    }

    @Override
    public void updateAllSyncTime(String id, String now) {
        bankReconciliationMapper.updateAllSyncTime(id, now);
    }

    @Override
    public void insertAllSyncTime(String id, String now) {
        bankReconciliationMapper.insertAllSyncTime(id, now);
    }

    @Override
    public JSONObject selectSyncTimeByEndDate(String endDate) {
        return bankReconciliationMapper.selectSyncTimeByEndDate(endDate);
    }

    @Override
    public List<JSONObject> selectAllSyncTime() {
        return bankReconciliationMapper.selectAllSyncTime();
    }

    @Override
    public List<JSONObject> selectDataDateByIds(List<String> ids) {
        return bankReconciliationMapper.selectDataDateByIds(ids);
    }

    @Override
    public void executeSQRpa(JSONObject params) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> brSQRpaJobIds = cronService.getJobIdByClass(BankReconciliationSQRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", DEFAULT_USERNAME);
        jobDataMap.put("dataDate", params.getString("dataDate"));
        cronService.startJobNow(brSQRpaJobIds, jobDataMap);
    }

    @Override
    public void executeZZRpa(JSONObject params) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> brZZRpaJobIds = cronService.getJobIdByClass(BankReconciliationZZRpaJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("username", DEFAULT_USERNAME);
        jobDataMap.put("dataDate", params.getString("dataDate"));
        jobDataMap.put("accountNumber", params.getString("accountNumber"));
        cronService.startJobNow(brZZRpaJobIds, jobDataMap);
    }

    @Override
    public String syncBankReconciliationV2(String beginDate, String endDate) {
        if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            // 如果2个日期有一个为空 则按照当天查询
            beginDate = DateUtil.today();
            endDate = DateUtil.today();
        }
        BankReconciliationSyncConfig syncConfig = SpringUtil.getBean(BankReconciliationSyncConfig.class);
        String status = syncConfig.getStatus();
        if ("executing".equals(status)) {
            // 如果任务在执行中 则直接返回状态
            log.info("同步任务正在执行中,不允许再次执行...");
            return "executing";
        } else if ("noStart".equals(status) || "completed".equals(status)) {
            synchronized (BankReconciliationServiceImpl.class) {
                String beginDateParam = beginDate;
                String endDateParam = endDate;
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                    syncConfig.setStatus("executing");
                    log.info("同步任务准备开始执行...");
                    List<String> datesBetweenToStr = DateUtils.getDatesBetweenToStr(beginDateParam, endDateParam);
                    String sqFileBasePath = SpringUtil.getProperty("file.br-import-sq-file-path");
                    String zzFileBasePath = SpringUtil.getProperty("file.br-import-zz-file-path");
                    if (CollectionUtil.isNotEmpty(datesBetweenToStr)) {
                        JSONObject params = new JSONObject();
                        for (String date : datesBetweenToStr) {
                            try {
                                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                                params.put("username", SecureUtil.currentUserName());
                                params.put("beginTime", DateUtil.now());
                                params.put("dataDate", date);
                                params.put("params", date);
                                LogBRUtil.preSyncLog(params);
                            } catch (Exception e) {
                                e.printStackTrace();
                                e.printStackTrace();
                                params.put("endTime", DateUtil.now());
                                params.put("exception", e.getMessage());
                                params.put("status", CommonStatus.FAIL.name());
                                LogBRUtil.postSyncLog(params);
                            }
                            log.info("正在处理... {}", date);
                            // 存放上清文件的目录
                            File sqFileDir = new File(sqFileBasePath + File.separator + date + File.separator + "SQ");
                            // 存放中债文件的目录
                            File zzFileDir = new File(zzFileBasePath + File.separator + date + File.separator + "ZZ");
                            try {
                                doHandleBKDataInfo(CollectionUtil.newArrayList(date), sqFileDir, zzFileDir);
                                params.put("endTime", DateUtil.now());
                                params.put("status", CommonStatus.SUCCESS.name());
                                LogBRUtil.postSyncLog(params);
                            } catch (Exception e) {
                                e.printStackTrace();
                                params.put("endTime", DateUtil.now());
                                params.put("exception", e.getMessage());
                                params.put("status", CommonStatus.FAIL.name());
                                LogBRUtil.postSyncLog(params);
                            }
                        }
                    }
                });
                syncConfig.setCompletableFuture(completableFuture);
            }
        }
        return "noStart";
    }

    @Override
    public String uploadV2(List<MultipartFile> files, String executeType, String selectedDate) {
        if (!isUpload) {
            List<File> fileList = new ArrayList<>();
            for (MultipartFile file : files) {
                File uploadFile = FileUtil.createTempFile(file.getOriginalFilename(), "." + FileUtil.extName(file.getOriginalFilename()), true);
                try {
                    file.transferTo(uploadFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                fileList.add(uploadFile);
            }
            String username = SecureUtil.currentUserName();
            CompletableFuture.runAsync(() -> {
                isUpload = true;
                log.info("开始导入银行对账文件...选择的日期为:{}", selectedDate);
                if (CollectionUtil.isNotEmpty(fileList)) {
                    String sqFileBasePath = SpringUtil.getProperty("file.br-import-sq-file-path");
                    String zzFileBasePath = SpringUtil.getProperty("file.br-import-zz-file-path");
                    List<String> filesDate = doHandleBKFileInfo(fileList, selectedDate);
                    if (CollectionUtil.isNotEmpty(filesDate)) {
                        filesDate = filesDate.stream().distinct().collect(Collectors.toList());
                        for (String dataDate : filesDate) {
                            if (!dataDate.equals(selectedDate)) {
                                continue;
                            }
                            // 存放上清文件的目录
                            File sqFileDir = new File(sqFileBasePath + File.separator + dataDate + File.separator + "SQ");
                            // 存放中债文件的目录
                            File zzFileDir = new File(zzFileBasePath + File.separator + dataDate + File.separator + "ZZ");
                            JSONObject params = new JSONObject();
                            try {
                                JSONObject jsonObject1 = selectSyncTimeByEndDate(dataDate);
                                if (jsonObject1 != null && !jsonObject1.isEmpty()) {
                                    updateAllSyncTime(dataDate, DateUtil.now());
                                } else {
                                    insertAllSyncTime(dataDate, DateUtil.now());
                                }
                                params.put("logId", IdUtil.getSnowflakeNextIdStr());
                                params.put("username", username);
                                params.put("beginTime", DateUtil.now());
                                params.put("type", executeType);
                                params.put("dataDate", dataDate);
                                LogBRUtil.preUploadLog(params);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            // -------核心逻辑---------
                            doHandleBKDataInfo(filesDate, sqFileDir, zzFileDir);
                            // -------核心逻辑---------
                            try {
                                params.put("endTime", DateUtil.now());
                                params.put("status", CommonStatus.SUCCESS.name());
                                File file = FileUtil.createTempFile("银行对账", ".zip", true);
                                file = FileUtil.rename(file, "银行对账_" + dataDate + "." + FileUtil.extName(file), true);
                                OmFileUtil.zipDirectories(new String[]{sqFileDir.getAbsolutePath(), zzFileDir.getAbsolutePath()}, file);
                                String brUploadFilePath = SpringUtil.getProperty("file.br-upload-file-path");
                                File dest = new File(brUploadFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                                params.put("fileUrl", dest.getAbsolutePath());
                                LogBRUtil.postUploadLog(params);
                            } catch (Exception e) {
                                e.printStackTrace();
                                params.put("endTime", DateUtil.now());
                                params.put("exception", e.getMessage());
                                params.put("status", CommonStatus.FAIL.name());
                                LogBRUtil.postUploadLog(params);
                            }
                        }
                    }
                }
                isUpload = false;
                log.info("银行间缴费信息录入结束...");
            });
        }
        return isUpload ? "executing" : "completed";
    }

    /**
     * 处理银行对账文件 要保证 上清文件夹中每日只保留一个最新文件 中债只保留最新的一批数据(同一个账户账号只存最新的)
     * @param fileList 上传文件列表
     * @return 上传文件对应的时间
     */
    public List<String> doHandleBKFileInfo(List<File> fileList, String selectedDate) {
        List<String> filesDate = new ArrayList<>();
        String sqFileBasePath = SpringUtil.getProperty("file.br-import-sq-file-path");
        String zzFileBasePath = SpringUtil.getProperty("file.br-import-zz-file-path");
        for (File file : fileList) {
            JSONObject res = getDateByFile(file, selectedDate);
            String date = res.getString("date");
            String type = res.getString("type");
            String account = res.getString("account");
            try {
                if ("SQ".equals(type)) {
                    // 将此文件复制到上清目录
                    copyFile(file, date, type, sqFileBasePath, null);
                } else if ("ZZ".equals(type)) {
                    copyFile(file, date, type, zzFileBasePath, account);
                }
                filesDate.add(date);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                // 记录文件
                BankReconciliationFile bankReconciliationFile = new BankReconciliationFile();
                bankReconciliationFile.setId(IdUtil.getSnowflakeNextIdStr());
                bankReconciliationFile.setDataDate(date);
                bankReconciliationFile.setType(type);
                bankReconciliationFile.setFileName(file.getName());
                bankReconciliationFile.setFilePath(file.getAbsolutePath());
                bankReconciliationMapper.saveBankReconciliationFile(bankReconciliationFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return filesDate;
    }

    private void copyFile(File file, String date, String type, String path, String account) throws Exception {
        account = StringUtils.isNotBlank(account) ? account : DateUtil.format(new Date(), "yyyyMMDD_HHmmss");
        File dest = new File(path + File.separator + date + File.separator + type + File.separator + FileUtil.getPrefix(file) + "_" + account + "." + FileUtil.extName(file));
        if (!dest.getParentFile().exists()) {
            boolean mkdirs = dest.getParentFile().mkdirs();
            log.info("创建文件夹 {} 结果:{}", dest.getParentFile().getAbsolutePath(), mkdirs);
        }
        File file1 = FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
        System.out.println(file1);
    }

    public JSONObject getDateByFile(File file, String selectedDate) {
        JSONObject res = new JSONObject();
        ExcelReader reader = ExcelUtil.getReader(file, 0);
        String sqFileBasePath = SpringUtil.getProperty("file.br-import-sq-file-path");
        String zzFileBasePath = SpringUtil.getProperty("file.br-import-zz-file-path");
        // 读取第一行如果发现是 债券账户总对账单 则为中债文件
        List<Object> objects = reader.readRow(0);
        if (CollectionUtil.isEmpty(objects)) {
            BusinessException.throwException("模板不正确");
        }
        Optional<String> first = objects.stream().map(String::valueOf).distinct().findFirst();
        String type = "";
        if (first.isPresent()) {
            String firstRow = first.get();
            if ("债券账户总对账单".equals(firstRow)) {
                type = "ZZ";
            } else {
                type = "SQ";
            }
        }
        String date = null;
        String account = null;
        try {
            if ("SQ".equals(type)) {
                date = getSQDataDate(reader, selectedDate);
                if (StringUtils.isNotBlank(date) && new File(sqFileBasePath).exists()) {
                    // 上清文件夹清空
                    String sqFilePath = sqFileBasePath + File.separator + date + File.separator + "SQ";
                    delDataFile(null, sqFilePath);
                }
            } else if ("ZZ".equals(type)) {
                date = getZZDataDate(reader);
                account = getZZDataAccount(reader);
                if (StringUtils.isNotBlank(date) && StringUtils.isNotBlank(account) && new File(zzFileBasePath).exists()) {
                    String zzFilePath = zzFileBasePath + File.separator + date + File.separator + "ZZ";
                    // 中债文件夹删除对应账户账号文件
                    delDataFile(account, zzFilePath);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            reader.close();
        }
        // 根据类型拷贝文件
        res.put("date", StringUtils.isNotBlank(date) ? date : selectedDate);
        res.put("type", type);
        if (StringUtils.isNotBlank(account)) {
            res.put("account", account);
        }
        log.info("处理的日期为:{}, 文件类型为:{}", date, type);
        return res;
    }

    private void delDataFile(String account, String path) {
        if (StringUtils.isNotBlank(account)) {
            log.info("处理中债文件则需要覆盖相同账户账号的文件...");
            List<File> delFiles = new ArrayList<>();
            for (File loopFile : FileUtil.loopFiles(path)) {
                ExcelReader reader = new ExcelReader(loopFile, 0);
                String zzDataAccount = getZZDataAccount(reader);
                reader.close();
                if (account.equals(zzDataAccount)) {
                    delFiles.add(loopFile);
                }
            }
            if (CollectionUtil.isNotEmpty(delFiles)) {
                for (File delFile : delFiles) {
                    log.info("准备删除中债文件,账户账号为:{}", account);
                    boolean del = FileUtil.del(delFile);
                    log.info("删除{}文件结果为:{}", delFile.getName(), del);
                }
            }
        } else {
            log.info("处理上清文件则需要删除上清文件夹中所有文件...");
            // 清空所有文件 对于上清文件而言只需要保存最新的一个即可
            FileUtil.del(path);
        }
    }


    /**
     * 统一处理银行对账逻辑 只负责读取文件夹中的文件而后做处理
     */
    public void doHandleBKDataInfo(List<String> filesDate, File sqFileDir, File zzFileDir) {
        // 查询所有账套信息
        List<MailCommonInfo> mailCommonInfos = SpringUtil.getBean(AccountSetMapper.class).queryAll();
        Map<String, List<MailCommonInfo>> commonInfoMap = mailCommonInfos.stream().collect(Collectors.groupingBy(MailCommonInfo::getId));
        for (String dataDate : filesDate) {
            try {
                List<JSONObject> valueCurrData = bankReconciliationMapper.queryValuationTableByDateScope(dataDate, dataDate);
                List<JSONObject> sqValueCurrData = valueCurrData.stream().filter(n -> "上清".equals(n.getString("VC_LOCATION"))).collect(Collectors.toList());
                List<JSONObject> zzValueCurrData = valueCurrData.stream().filter(n -> "中债".equals(n.getString("VC_LOCATION"))).collect(Collectors.toList());
                handleSQData(sqValueCurrData, sqFileDir, dataDate, commonInfoMap);
                handleZZData(zzValueCurrData, zzFileDir, dataDate, commonInfoMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String getZZDataDate(ExcelReader reader) {
        String testDate;
        // 读取第二行 查询日期
        List<Object> objects = reader.readRow(1);
        if (CollectionUtil.isNotEmpty(objects)) {
            objects = objects.stream().map(String::valueOf).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        }
        log.info("数据日期是:{}", objects);
        testDate = String.valueOf(objects.get(1)).replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
        // 防止模板中的日期 不是 yyyy-MM-dd
        return DateUtil.format(DateUtil.parseDate(testDate), "yyyy-MM-dd");
    }

    private String getZZDataAccount(ExcelReader reader) {
        List<Object> accountInfoObj = reader.readRow(2);
        if (CollectionUtil.isNotEmpty(accountInfoObj)) {
            accountInfoObj = accountInfoObj.stream().map(String::valueOf).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(accountInfoObj)) {
            Object accountObj = accountInfoObj.get(accountInfoObj.size() - 1);
            log.info("账户账号是:{}", accountObj);
            return String.valueOf(accountObj);
        }
        return "";
    }

    private String getSQDataDate(ExcelReader reader, String selectedDate) {
        List<Object> headers = reader.readRow(0);
        Map<String, Integer> indexs = new HashMap<>();
        for (int i = 0; i < headers.size(); i++) {
            if (String.valueOf(headers.get(i)).equals("日期")) {
                indexs.put("RQ", i);
            }
        }
        List<List<Object>> read = reader.read(1);
        if (CollectionUtil.isNotEmpty(read)) {
            String testDate;
            List<Object> row = read.get(0);
            // 日期 如果对应没有此列 则按照当天日期
            Integer rq = indexs.get("RQ");
            Object o3;
            if (null == rq) {
                o3 = selectedDate;
            } else {
                o3 = row.get(indexs.get("RQ"));
            }
            testDate = String.valueOf(o3);
            // 防止模板中的日期 不是 yyyy-MM-dd
            return DateUtil.format(DateUtil.parseDate(testDate), "yyyy-MM-dd");
        }
        return null;
    }

    /**
     * 根据估值数据组装插入表中的集合
     * @param valueData 估值数据
     * @param commonInfoMap 账套信息
     * @return 插入表的对账数据
     */
    private List<BankReconciliation> generateBRDataByValueData(List<JSONObject> valueData, Map<String, List<MailCommonInfo>> commonInfoMap) {
        // 循环从估值中查询出来的并全部插入到表中
        List<BankReconciliation> collect = new ArrayList<>();
        if (CollectionUtil.isEmpty(valueData)) {
            return collect;
        }
        for (JSONObject jsonObject : valueData) {
            BankReconciliation bankReconciliation = new BankReconciliation();
            bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
            bankReconciliation.setDataDate(jsonObject.getString("D_DATE"));
            bankReconciliation.setSecurityName(jsonObject.getString("VC_ZQJC"));
            bankReconciliation.setSettlementLocation(jsonObject.getString("VC_LOCATION"));
            bankReconciliation.setSecurityCode(jsonObject.getString("VC_ZQDM"));
            bankReconciliation.setValuationPositionQuantity(jsonObject.getString("L_ZQCC"));
            bankReconciliation.setSourceCode(jsonObject.getString("VC_TRADEZQDM"));
            String lZtbh = jsonObject.getString("L_ZTBH");
            bankReconciliation.setAccountSetCode(lZtbh);
            List<MailCommonInfo> mailCommonInfos1 = commonInfoMap.get(lZtbh);
            // 如果账套可以跟当前系统匹配上则赋值账套信息
            if (CollectionUtil.isNotEmpty(mailCommonInfos1)) {
                MailCommonInfo mailCommonInfo = mailCommonInfos1.get(0);
                bankReconciliation.setAccountSetName(mailCommonInfo.getAccountName());
                bankReconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                bankReconciliation.setAccountNumber(mailCommonInfo.getHolderAccountNumber());
                bankReconciliation.setProductCode(mailCommonInfo.getProductCode());
                bankReconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                bankReconciliation.setValuationTime(mailCommonInfo.getValuationTime());
            }
            collect.add(bankReconciliation);
        }
        return collect;
    }

    /**
     * 处理每天的中债数据
     * @param zzValueCurrData 估值中债数据
     * @param zzFileDir 文件目录地址
     */
    private void handleZZData(List<JSONObject> zzValueCurrData, File zzFileDir, String date, Map<String, List<MailCommonInfo>> commonInfoMap) {
        // 先删除当前表里的中债数据
        bankReconciliationMapper.deleteByDate(CollectionUtil.newArrayList(date), "中债");
        log.info("开始处理中债数据...");
        if (!zzFileDir.exists() || !zzFileDir.isDirectory() || CollectionUtil.isEmpty(FileUtil.listFileNames(zzFileDir.getAbsolutePath()))) {
            // 如果目录不存在或者目录下没有任何文件 说明没有上传过文件 这样直接插入估值数据
            List<BankReconciliation> bankReconciliations = generateBRDataByValueData(zzValueCurrData, commonInfoMap);
            if (CollectionUtil.isNotEmpty(bankReconciliations)) {
                bankReconciliationMapper.saveBatch(bankReconciliations);
            }
        } else {
            // 目录下有文件
            List<File> sqFiles = FileUtil.loopFiles(zzFileDir);
            List<BankReconciliation> allFileData = new ArrayList<>();
            // 需要处理单个文件 将所有文件中的信息拿出来并进行去重
            for (File file : sqFiles) {
                List<BankReconciliation> singleFileData = handleZZSingleFile(file, date, commonInfoMap);
                allFileData.addAll(singleFileData);
            }
            // 整个去重文件中的数据
            List<BankReconciliation> distinct = CollectionUtil.distinct(allFileData, n -> String.format("%s-%s-%s-%s", n.getDataDate(), n.getAccountSetCode(), n.getSettlementLocation(), n.getSecurityName()), true);
            // 双向对比
            List<BankReconciliation> finalRes = doCompareData(distinct, zzValueCurrData, commonInfoMap);
            if (CollectionUtil.isNotEmpty(finalRes)) {
                bankReconciliationMapper.saveBatch(finalRes);
            }
        }
    }

    /**
     * 处理单个中债文件
     * @param file 文件
     * @param date 数据日期
     * @param commonInfoMap 账套信息
     * @return 单个文件中的数据
     */
    private List<BankReconciliation> handleZZSingleFile(File file, String date, Map<String, List<MailCommonInfo>> commonInfoMap) {
        // 检查文件模板
        ExcelReader reader = checkTemplate(file, "ZZ");
        if (ObjectUtil.isNull(reader)) {
            BusinessException.throwException("模板格式不正确");
        }
        List<BankReconciliation> bankReconciliations = resolveZZExcelV2(commonInfoMap, reader, date);
        if (CollectionUtil.isNotEmpty(bankReconciliations)) {
            reader.close();
            // 将读取文件中的相同的证券持仓进行累加
            return handleFileRepeatDataV2(bankReconciliations);
        }
        reader.close();
        return bankReconciliations;
    }

    /**
     * 解析中债模板
     * @param commonInfoMap 账套信息
     * @param reader reader
     * @param date 数据日期
     * @return 单个文件中的数据
     */
    private List<BankReconciliation> resolveZZExcelV2(Map<String, List<MailCommonInfo>> commonInfoMap, ExcelReader reader, String date) {
        List<BankReconciliation> bankReconciliations = new ArrayList<>();
        // 读取第二行 查询日期
        List<Object> objects = reader.readRow(1);
        if (CollectionUtil.isNotEmpty(objects)) {
            objects = objects.stream().map(String::valueOf).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        }
        log.info("数据日期是:{}", objects);
        date = String.valueOf(objects.get(1)).replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
        // 防止模板中的日期 不是 yyyy-MM-dd
        date = DateUtil.format(DateUtil.parseDate(date), "yyyy-MM-dd");
        // 读取第三行 账户名称和账号
        List<Object> accountNameAndNo = reader.readRow(2);
        if (CollectionUtil.isNotEmpty(accountNameAndNo)) {
            accountNameAndNo = accountNameAndNo.stream().map(String::valueOf).filter(StringUtils::isNotBlank).filter(n -> !"null".equals(n)).distinct().collect(Collectors.toList());
            accountNameAndNo = CollectionUtil.newArrayList(accountNameAndNo.get(1), accountNameAndNo.get(3));
        }
        log.info("账户名称和账号是:{}", accountNameAndNo);
        // 从第9行开始读取债券账户总对账单
        // 此处不能写死从第几行开始读取了 需要根据行标识来判断 以此行标识 债券账户总对账单（债权）往下开始读取 读取到 合计 结束
        int rowCount = reader.getRowCount();
        // 记录要读取2行内容的行索引
        List<Integer> resolveRowIndex = new ArrayList<>();
        for (int i = 0; i < rowCount; i++) {
            List<Object> rowContent = reader.readRow(i);
            if (CollectionUtil.isNotEmpty(rowContent)) {
                List<String> rowContentStr = rowContent.stream().map(String::valueOf).distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(rowContentStr)) {
                    String content = rowContentStr.get(0);
                    if ("债券账户总对账单（债权）".equals(content)) {
                        log.info("读取债券账户总对账单（债权）从第{}行开始", i);
                        resolveRowIndex.add(i);
                    } else if ("债券账户总对账单（额度）".equals(content)) {
                        log.info("读取债券账户总对账单（额度）从第{}行开始", i);
                        resolveRowIndex.add(i);
                    }
                }
            }
        }
        if (resolveRowIndex.size() != 2) {
            log.error("模板有误不能解析");
            // 处理当日模板
            return handleCurrentDateTemplateV2(commonInfoMap, reader, date, accountNameAndNo);
        }
        for (Integer rowIndex : resolveRowIndex) {
            int doRowIndex = rowIndex + 2;
            log.info("****从{}行开始解析债券账户总对账单", doRowIndex);
            for (int i = doRowIndex; i < rowCount; i++) {
                List<Object> rowData = reader.readRow(i);
                Object o = rowData.get(0);
                if ("合计".equals(o)) {
                    // 若出现 合计 说明 读取完毕
                    break;
                }
                // 债券代码
                Object o1 = rowData.get(1);
                // 债券名称
                Object o2 = rowData.get(2);
                // 持仓数量
                Object o3 = rowData.get(3);
                log.info("解析中债文件中每行的 code={}, name = {}, count={}", o1, o2, o3);
                // 如果代码或名称为空 则不解析当前行
                if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                    continue;
                }
                String code = StringUtil.delStartStr(String.valueOf(o1));
                String transformCode = transformCode(code);
                log.info("转换后的代码为 code={}", transformCode);
                // 中债持仓数量 * 100
                String zzCount = String.valueOf(o3);
                zzCount = zzCount.replaceAll(",", "");
                BigDecimal bigDecimal = new BigDecimal(zzCount);
                BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                String o3Str = multiply.toPlainString();
                BankReconciliation bankReconciliation = generateBankReconciliationV2(transformCode, String.valueOf(o2), o3Str, date, accountNameAndNo, code, "中债", commonInfoMap);
                bankReconciliations.add(bankReconciliation);
            }
        }
        return bankReconciliations;
    }

    /**
     * 处理新模板
     * @param commonInfoMap 账套
     * @param reader reader
     * @param date 日期
     * @param accountNameAndNo 账户信息
     * @return 文件中的数据
     */
    private List<BankReconciliation> handleCurrentDateTemplateV2(Map<String, List<MailCommonInfo>> commonInfoMap, ExcelReader reader, String date, List<Object> accountNameAndNo) {
        List<Object> objects = reader.readRow(4);
        if (CollectionUtil.isEmpty(objects)) {
            BusinessException.throwException("模板有误,第5行没内容");
        }
        List<String> collect = objects.stream().map(String::valueOf).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            if (collect.contains("债券认购") || collect.contains("债券持有")) {
                // 说明是新模板
                log.info("**********开始解析当天模板");
                int rowCount = reader.getRowCount();
                List<BankReconciliation> bankReconciliations = new ArrayList<>();
                for (int i = 6; i < rowCount; i++) {
                    List<Object> rowData = reader.readRow(i);
                    Object o = rowData.get(0);
                    if ("合计".equals(o)) {
                        // 若出现 合计 说明 读取完毕
                        break;
                    }
                    // 证券代码
                    Object o1 = rowData.get(1);
                    // 证券简称
                    Object o2 = rowData.get(2);
                    // 如果代码或名称为空 则不解析当前行
                    if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                        continue;
                    }
                    // 持仓数
                    Object o11 = rowData.get(11);
                    // 持仓数
                    Object o6 = rowData.get(6);
                    log.info("解析当日中债文件中每行的 code={}, name = {}, count={}", o1, o2, o11);
                    String code = StringUtil.delStartStr(String.valueOf(o1));
                    String transformCode = transformCode(code);
                    log.info("转换后的代码为 code={}", transformCode);
                    // 中债持仓数量 * 100
                    String zzCount = String.valueOf(o11);
                    String zzCount1 = String.valueOf(o6);
                    zzCount = zzCount.replaceAll(",", "");
                    zzCount1 = zzCount1.replaceAll(",", "");
                    BigDecimal bigDecimal = new BigDecimal(zzCount).add(new BigDecimal(zzCount1));
                    BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                    String o11Str = multiply.toPlainString();
                    // 将解析后的代码与估值表中的代码进行比对
                    BankReconciliation bankReconciliation = generateBankReconciliationV2(transformCode, String.valueOf(o2), o11Str, date, accountNameAndNo, code, "中债", commonInfoMap);
                    bankReconciliations.add(bankReconciliation);
                }
                return bankReconciliations;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 处理每天的上清数据
     * @param sqValueCurrData 估值上清数据
     * @param sqFileDir 文件目录地址
     */
    private void handleSQData(List<JSONObject> sqValueCurrData, File sqFileDir, String date, Map<String, List<MailCommonInfo>> commonInfoMap) {
        // 先删除当前表里的上清数据
        bankReconciliationMapper.deleteByDate(CollectionUtil.newArrayList(date), "上清");
        log.info("开始处理上清数据...");
        if (!sqFileDir.exists() || !sqFileDir.isDirectory() || CollectionUtil.isEmpty(FileUtil.listFileNames(sqFileDir.getAbsolutePath()))) {
            // 如果目录不存在或者目录下没有任何文件 说明没有上传过文件 这样直接插入估值数据
            List<BankReconciliation> bankReconciliations = generateBRDataByValueData(sqValueCurrData, commonInfoMap);
            if (CollectionUtil.isNotEmpty(bankReconciliations)) {
                bankReconciliationMapper.saveBatch(bankReconciliations);
            }
        } else {
            // 目录下有文件
            List<File> sqFiles = FileUtil.loopFiles(sqFileDir);
            List<BankReconciliation> allFileData = new ArrayList<>();
            // 需要处理单个文件 将所有文件中的信息拿出来并进行去重
            for (File file : sqFiles) {
                List<BankReconciliation> singleFileData = handleSQSingleFile(file, date, commonInfoMap);
                allFileData.addAll(singleFileData);
            }
            // 整个去重文件中的数据
            List<BankReconciliation> distinct = CollectionUtil.distinct(allFileData);
            // 双向对比
            List<BankReconciliation> finalRes = doCompareData(distinct, sqValueCurrData, commonInfoMap);
            if (CollectionUtil.isNotEmpty(finalRes)) {
                bankReconciliationMapper.saveBatch(finalRes);
            }
        }
    }

    /**
     * 统一的双向匹配逻辑
     * @param distinct 文件中的数据
     * @param sqValueCurrData 估值系统的数据
     * @return 双向匹配的结果
     */
    private List<BankReconciliation> doCompareData(List<BankReconciliation> distinct, List<JSONObject> sqValueCurrData, Map<String, List<MailCommonInfo>> commonInfoMap) {
        if (CollectionUtil.isNotEmpty(distinct) && CollectionUtil.isEmpty(sqValueCurrData)) {
            // 文件有数据 估值没有 则直接返回文件
            return distinct;
        }
        List<BankReconciliation> valueBRData = generateBRDataByValueData(sqValueCurrData, commonInfoMap);
        if (CollectionUtil.isNotEmpty(sqValueCurrData) && CollectionUtil.isEmpty(distinct)) {
            // 文件没有数据 估值有 则直接返回估值
            return valueBRData;
        }
        // 此循环完成后 仅能处理 文件中与估值中共有的以及 文件有估值没有的
        for (BankReconciliation bankReconciliation : distinct) {
            String accountSetCode = bankReconciliation.getAccountSetCode();
            String sourceCode = bankReconciliation.getSourceCode();
            String dataDate = bankReconciliation.getDataDate();
            String productId = bankReconciliation.getAccountSetCode();
            String settlementLocation = bankReconciliation.getSettlementLocation();
            // 如果有没有匹配上账套的则略过
            if (StringUtils.isEmpty(accountSetCode)) {
                continue;
            }
            for (BankReconciliation valueData : valueBRData) {
                String value_dDate = valueData.getDataDate();
                String value_sourceCode = valueData.getSourceCode();
                String value_location = valueData.getSettlementLocation();
                String value_productId = valueData.getAccountSetCode();
                String value_quantity = valueData.getValuationPositionQuantity();
                try {
                    if (sourceCode.equals(value_sourceCode)
                            && settlementLocation.equals(value_location)
                            && dataDate.equals(value_dDate)
                            && productId.equals(value_productId)) {
                        // 满足此条件说明完全匹配 设置估值持仓数即可
                        bankReconciliation.setValuationPositionQuantity(value_quantity);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        // 下面处理 估值有的 文件没有的
        List<BankReconciliation> subtract = CollectionUtil.subtractToList(valueBRData, distinct);
        if (CollectionUtil.isNotEmpty(subtract)) {
            distinct.addAll(subtract);
            return distinct;
        }
        return distinct;
    }

    /**
     * 处理单个上清文件
     * @param file 文件
     * @param date 数据日期
     * @param commonInfoMap 账套信息
     * @return 单独文件中的数据
     */
    private List<BankReconciliation> handleSQSingleFile(File file, String date, Map<String, List<MailCommonInfo>> commonInfoMap) {
        // 检查文件模板
        ExcelReader reader = checkTemplate(file, "SQ");
        if (ObjectUtil.isNull(reader)) {
            BusinessException.throwException("模板格式不正确");
        }
        List<BankReconciliation> bankReconciliations = resolveSQExcelV2(commonInfoMap, reader, date);
        if (CollectionUtil.isNotEmpty(bankReconciliations)) {
            // 将读取文件中的相同的证券持仓进行累加
            return handleFileRepeatDataV2(bankReconciliations);
        }
        reader.close();
        return bankReconciliations;
    }

    /**
     * 解析伤情文件
     * @param commonInfoMap 账套信息
     * @param dateDate 数据日期
     * @return 文件中的数据
     */
    private List<BankReconciliation> resolveSQExcelV2(Map<String, List<MailCommonInfo>> commonInfoMap, ExcelReader reader, String dateDate) {
        List<BankReconciliation> bankReconciliations = new ArrayList<>();
        // 记录列索引
        Map<String, Integer> indexMap = new HashMap<>();
        // 读取第一行表头
        List<Object> headers = reader.readRow(0);
        // 需要寻找列为 债券代码 债券简称 余额（元） 更新时间 持有人账号
        for (int i = 0; i < headers.size(); i++) {
            switch (String.valueOf(headers.get(i))) {
                case "债券代码":
                    indexMap.put("ZQDM", i);
                    break;
                case "债券简称":
                    indexMap.put("ZQJC", i);
                case "余额（元）":
                    indexMap.put("YE", i);
                    break;
                case "更新时间":
                    indexMap.put("GXSJ", i);
                    break;
                case "持有人账号":
                    indexMap.put("CYRZH", i);
                    break;
                case "持有人账户简称":
                    indexMap.put("CYRZHJC", i);
                    break;
                case "日期":
                    indexMap.put("RQ", i);
                    break;
            }
        }
        List<List<Object>> read = reader.read(1);
        for (List<Object> row : read) {
            // 债券代码
            Object o1 = row.get(indexMap.get("ZQDM"));
            // 债券简称 -> 证券名称
            Object o2 = row.get(indexMap.get("ZQJC"));
            // 如果代码或名称为空 则不解析当前行
            if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                continue;
            }
            // 持仓数
            Object o8 = row.get(indexMap.get("YE"));
            // 日期 如果对应没有此列 则按照当天日期
            Integer rq = indexMap.get("RQ");
            Object o3;
            if (null == rq) {
                o3 = DateUtil.today();
            } else {
                o3 = row.get(indexMap.get("RQ"));
            }
            // 持有人账户简称
            Object o4 = row.get(indexMap.get("CYRZHJC"));
            // 持有人账户
            Object o5 = row.get(indexMap.get("CYRZH"));
            String code = String.valueOf(o1);
            // 去掉开头字母
            code = StringUtil.delStartStr(code);
            String name = String.valueOf(o2);
            String updateTime = String.valueOf(o3);
            log.info("解析上清文件中每行的 code={}, name = {}, updateTime={}, count = {}, CYRZHJC = {}, CYRZH = {}", code, name, updateTime, o8, o4, o5);
            // 编码转换
            String resCode = transformCode(code);
            log.info("符合时间转换后的代码为:{}", resCode);
            // 有些可能存在逗号后的格式 统一删除逗号
            String o8Str = String.valueOf(o8);
            o8Str = o8Str.replaceAll(",", "");
            // 上清的值应该除以100后进行入库
            BigDecimal bigDecimal = new BigDecimal(o8Str);
            BigDecimal divide = bigDecimal.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
            o8Str = divide.toPlainString();
            BankReconciliation bankReconciliation = generateBankReconciliationV2(resCode,
                    name, o8Str, dateDate, CollectionUtil.newArrayList(o4, o5), code, "上清", commonInfoMap);
            bankReconciliations.add(bankReconciliation);
        }
        return bankReconciliations;
    }

    /**
     * 为每一行生成一个对象
     * @param securityCode 证券代码
     * @param securityName 证券名称
     * @param settlementCompanyPositionQuantity 结算中心持仓数
     * @param date 数据日期
     * @param accountNameAndNo 账号与账户信息
     * @param sourceCode 源代码
     * @return 单行对象
     */
    private BankReconciliation generateBankReconciliationV2(String securityCode,
                                                            String securityName,
                                                            String settlementCompanyPositionQuantity,
                                                            String date,
                                                            List<Object> accountNameAndNo,
                                                            String sourceCode,
                                                            String setSettlementLocation,
                                                            Map<String, List<MailCommonInfo>> commonInfoMap) {
        List<MailCommonInfo> allProductInfos = new ArrayList<>();
        commonInfoMap.forEach((k, v) -> {
            if (CollectionUtil.isNotEmpty(v)) {
                allProductInfos.addAll(v);
            }
        });
        securityName = StringUtils.deleteWhitespace(securityName.replaceAll("（", "(").replaceAll("）", ")"));
        BankReconciliation bankReconciliation = new BankReconciliation();
        bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
        bankReconciliation.setDataDate(date);
        bankReconciliation.setSettlementLocation(setSettlementLocation);
        bankReconciliation.setSourceCode(sourceCode);
        bankReconciliation.setSettlementCompanyPositionQuantity(settlementCompanyPositionQuantity);
        bankReconciliation.setSecurityCode(securityCode);
        bankReconciliation.setSecurityName(securityName);
        // 找到账套编码
        String accCode = String.valueOf(accountNameAndNo.get(1));
        bankReconciliation.setAccountNumber(accCode);
        // 根据转换后的代码查询估值表
        log.info("获取到的账户账号为:{}", accCode);
        // 根据账户账号查询对应的账套编号
        List<String> accountSetIds = new ArrayList<>();
        if ("中债".equals(setSettlementLocation)) {
            if (CollectionUtil.isNotEmpty(allProductInfos)) {
                accountSetIds = allProductInfos.stream()
                        .filter(n -> StringUtils.isNotBlank(n.getCentralDebtAccountNumber()))
                        .filter(n -> n.getCentralDebtAccountNumber().equals(accCode))
                        .map(MailCommonInfo::getId).collect(Collectors.toList());
            }
            // accountSetIds = SpringUtil.getBean(AccountSetMapper.class).getIdZZByAccount(accCode);
        } else {
            if (CollectionUtil.isNotEmpty(allProductInfos)) {
                accountSetIds = allProductInfos.stream()
                        .filter(n -> StringUtils.isNotBlank(n.getClearingHouseHolderAccount()))
                        .filter(n -> n.getClearingHouseHolderAccount().equals(accCode))
                        .map(MailCommonInfo::getId).collect(Collectors.toList());
            }
            // accountSetIds = SpringUtil.getBean(AccountSetMapper.class).getIdSQByAccount(accCode);
        }
        log.info("根据账号查询出来的账套编号为:{}", accountSetIds);
        String accountSetId = CollectionUtil.isNotEmpty(accountSetIds) ? accountSetIds.get(0) : null;
        if (StringUtils.isNotBlank(accountSetId)) {
            List<MailCommonInfo> commonInfos = commonInfoMap.get(accountSetId);
            if (CollectionUtil.isNotEmpty(commonInfos)) {
                MailCommonInfo mailCommonInfo = commonInfos.get(0);
                bankReconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                bankReconciliation.setAccountSetCode(mailCommonInfo.getId());
                bankReconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                bankReconciliation.setValuationTime(mailCommonInfo.getValuationTime());
                bankReconciliation.setProductCode(mailCommonInfo.getProductCode());
            }
        }
        return bankReconciliation;
    }


    /**
     * 校验模板
     * @param file 文件
     * @return 是否成功
     */
    private ExcelReader checkTemplate(File file, String type) {
        String extName = FileUtil.extName(file);
        if (!"xlsx".equals(extName) && !"xls".equals(extName)) {
            // 文件不是excel的
            return null;
        }
        ExcelReader reader = ExcelUtil.getReader(file, 0);
        // 读取第一行如果发现是 债券账户总对账单 则为中债文件
        List<Object> objects = reader.readRow(0);
        if (CollectionUtil.isEmpty(objects)) {
            return null;
        }
        Optional<String> first = objects.stream().map(String::valueOf).distinct().findFirst();
        if (first.isPresent()) {
            String firstRow = first.get();
            if ("债券账户总对账单".equals(firstRow) && "ZZ".equals(type)) {
                return reader;
            } else if ("SQ".equals(type)) {
                return reader;
            }
        }
        return null;
    }

    /**
     * 解析中债rpa文件
     */
    public String resolveZZExcel(File file, List<MailCommonInfo> mailCommonInfos, ExcelReader reader) {
        File handleFile = null;
        String testDate = null;
        String name = file.getName();
        if (!name.contains(".xlsx") && !name.contains(".xls")) {
            // 中债文件是个zip 需要先解压
            List<File> fileList = OmZipUtil.unzip7z(file);
            if (CollectionUtil.isNotEmpty(fileList)) {
                for (File tmpF : fileList) {
                    if (tmpF.getName().contains(".xlsx") || tmpF.getName().contains(".xls")) {
                        handleFile = tmpF;
                        break;
                    }
                }
            }
        } else {
            // 如果文件本身就是excel文件则不做解压处理
            handleFile = file;
        }
        if (null != handleFile) {
            log.info("需要解析的文件名是:{}", handleFile.getName());
        }
        if (handleFile != null && handleFile.exists()) {
            List<BankReconciliation> bankReconciliations = new ArrayList<>();
            List<BankReconciliation> repeatBankReconciliations = new ArrayList<>();
            // 读取第二行 查询日期
            List<Object> objects = reader.readRow(1);
            if (CollectionUtil.isNotEmpty(objects)) {
                objects = objects.stream().map(String::valueOf).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            }
            log.info("数据日期是:{}", objects);
            testDate = String.valueOf(objects.get(1)).replaceAll("年", "-").replaceAll("月", "-").replaceAll("日", "");
            // 防止模板中的日期 不是 yyyy-MM-dd
            testDate = DateUtil.format(DateUtil.parseDate(testDate), "yyyy-MM-dd");
            // 读取第三行 账户名称和账号
            List<Object> accountNameAndNo = reader.readRow(2);
            if (CollectionUtil.isNotEmpty(accountNameAndNo)) {
                accountNameAndNo = accountNameAndNo.stream().map(String::valueOf).filter(StringUtils::isNotBlank).filter(n -> !"null".equals(n)).distinct().collect(Collectors.toList());
                accountNameAndNo = CollectionUtil.newArrayList(accountNameAndNo.get(1), accountNameAndNo.get(3));
            }
            log.info("账户名称和账号是:{}", accountNameAndNo);
            /*
             此文件解析流程
              1、 读取第二行 查询日期
              2、 读取第三行 账户名称和账号   （账户名称：	安联增利6号			账户账号：	***********）
                （1） 通过此账号 在本系统的账套公共信息表(查询本系统库)中查找对应的 中债（托管账号）：*********** 确定账套基本信息（账套名称、账套编码、账套组等信息）
                （2） 从第九行开始读取债券对账单信息
                         序号	债券代码	   债券简称	          合计
                          1	    2128034	  21建设银行二级04	 1,000.000000
                          2	    2400006	  24特别国债06	     35,000.000000
                （3） 将每一行的债券代码按照业务提供的规则进行转换，形成估值库中对应的债券代码 对应字段为 VC_ZQDM
                （4） 查询估值持仓数量(查询估值库) 按照sql：select * from TACCOUNTZQJC where VC_ZQDM='DB12344' and d_date=date'2024-12-30'
                   注意：此sql可能会返回多条 并不唯一 可能会有 基金和理财产品 证券代码一样的情况，若查出同一账套多条数据，可以查tzqxx表找出对应证券内码，证券内码是唯一的
                --------------------
                页面表格字段对应关系
                  数据日期    账套组         账套        账套代码       结算场所       证券代码       证券名称          估值持仓数量        结算公司持仓数量          差异                       差异原因
                  每天日期  账套基本信息   账套基本信息   账套基本信息   根据文件类型   转换后的代码   文件中的债券简称   sql查询出的L_ZQCC      文件中的合计值     估值持仓数量-结算公司持仓数量    手动填写
             */
            // 从第9行开始读取债券账户总对账单
            // 此处不能写死从第几行开始读取了 需要根据行标识来判断 以此行标识 债券账户总对账单（债权）往下开始读取 读取到 合计 结束
            int rowCount = reader.getRowCount();
            // 记录要读取2行内容的行索引
            List<Integer> resolveRowIndex = new ArrayList<>();
            for (int i = 0; i < rowCount; i++) {
                List<Object> rowContent = reader.readRow(i);
                if (CollectionUtil.isNotEmpty(rowContent)) {
                    List<String> rowContentStr = rowContent.stream().map(String::valueOf).distinct().collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(rowContentStr)) {
                        String content = rowContentStr.get(0);
                        if ("债券账户总对账单（债权）".equals(content)) {
                            log.info("读取债券账户总对账单（债权）从第{}行开始", i);
                            resolveRowIndex.add(i);
                        } else if ("债券账户总对账单（额度）".equals(content)) {
                            log.info("读取债券账户总对账单（额度）从第{}行开始", i);
                            resolveRowIndex.add(i);
                        }
                    }
                }
            }
            if (resolveRowIndex.size() != 2) {
                log.error("模板有误不能解析");
                // 处理当日模板
                return handleCurrentDateTemplate(mailCommonInfos, reader, testDate, accountNameAndNo);
            }
            for (Integer rowIndex : resolveRowIndex) {
                int doRowIndex = rowIndex + 2;
                log.info("****从{}行开始解析债券账户总对账单", doRowIndex);
                for (int i = doRowIndex; i < rowCount; i++) {
                    List<Object> rowData = reader.readRow(i);
                    Object o = rowData.get(0);
                    if ("合计".equals(o)) {
                        // 若出现 合计 说明 读取完毕
                        break;
                    }
                    // 债券代码
                    Object o1 = rowData.get(1);
                    // 债券名称
                    Object o2 = rowData.get(2);
                    // 持仓数量
                    Object o3 = rowData.get(3);
                    log.info("解析中债文件中每行的 code={}, name = {}, count={}", o1, o2, o3);
                    // 如果代码或名称为空 则不解析当前行
                    if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                        continue;
                    }
                    // 去掉开头字母
                    String code = String.valueOf(o1);
                    char c = code.charAt(0);
                    if (Character.isLetter(c)) {
                        String tmp = code;
                        code = code.substring(1);
                        log.info("证券代码首字符是字母需要去掉:由 {} -> {}", tmp, code);
                    }
                    String transformCode = transformCode(code);
                    log.info("转换后的代码为 code={}", transformCode);
                    // 中债持仓数量 * 100
                    String zzCount = String.valueOf(o3);
                    zzCount = zzCount.replaceAll(",", "");
                    BigDecimal bigDecimal = new BigDecimal(zzCount);
                    BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                    String o3Str = multiply.toPlainString();
                    // 将解析后的代码与估值表中的代码进行比对
                    Tuple tuple = generateBankReconciliation(transformCode, "中债", String.valueOf(o2), o3Str, testDate, accountNameAndNo, code);
                    if ("1".equals(tuple.get(1))) {
                        Object o4 = tuple.get(0);
                        if (ObjectUtil.isNotNull(o4)) {
                            BankReconciliation bankReconciliation = (BankReconciliation) o4;
                            bankReconciliation.setSourceCode(code);
                            bankReconciliations.add(bankReconciliation);
                        }
                    } else {
                        Object o4 = tuple.get(0);
                        if (ObjectUtil.isNotNull(o4)) {
                            BankReconciliation bankReconciliation = (BankReconciliation) o4;
                            bankReconciliation.setSourceCode(code);
                            repeatBankReconciliations.add(bankReconciliation);
                        }
                    }
                }
            }
            log.info("银行对账需要入库的数据是(中债):{}", JSON.toJSONString(bankReconciliations));
            saveBatch(mailCommonInfos, testDate, bankReconciliations, "中债", String.valueOf(accountNameAndNo.get(1)), repeatBankReconciliations);
        }
        return testDate;
    }

    /**
     * 处理当天模板
     *
     * @param mailCommonInfos  账套信息
     * @param reader           读取对象
     * @param date             日期
     * @param accountNameAndNo 账户名称和编号
     * @return 日期
     */
    private String handleCurrentDateTemplate(List<MailCommonInfo> mailCommonInfos, ExcelReader reader, String date, List<Object> accountNameAndNo) {
        List<Object> objects = reader.readRow(4);
        if (CollectionUtil.isEmpty(objects)) {
            BusinessException.throwException("模板有误,第5行没内容");
        }
        List<String> collect = objects.stream().map(String::valueOf).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            if (collect.contains("债券认购") || collect.contains("债券持有")) {
                // 说明是新模板
                log.info("**********开始解析当天模板");
                int rowCount = reader.getRowCount();
                List<BankReconciliation> bankReconciliations = new ArrayList<>();
                List<BankReconciliation> repeatBankReconciliations = new ArrayList<>();
                for (int i = 6; i < rowCount; i++) {
                    List<Object> rowData = reader.readRow(i);
                    Object o = rowData.get(0);
                    if ("合计".equals(o)) {
                        // 若出现 合计 说明 读取完毕
                        break;
                    }
                    // 证券代码
                    Object o1 = rowData.get(1);
                    // 证券简称
                    Object o2 = rowData.get(2);
                    // 如果代码或名称为空 则不解析当前行
                    if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                        continue;
                    }
                    // 持仓数
                    Object o11 = rowData.get(11);
                    // 持仓数
                    Object o6 = rowData.get(6);
                    String code = String.valueOf(o1);
                    char c = code.charAt(0);
                    if (Character.isLetter(c)) {
                        String tmp = code;
                        code = code.substring(1);
                        log.info("证券代码首字符是字母需要去掉:由 {} -> {}", tmp, code);
                    }
                    log.info("解析当日中债文件中每行的 code={}, name = {}, count={}", o1, o2, o11);
                    String transformCode = transformCode(code);
                    log.info("转换后的代码为 code={}", transformCode);
                    // 中债持仓数量 * 100
                    String zzCount = String.valueOf(o11);
                    String zzCount1 = String.valueOf(o6);
                    zzCount = zzCount.replaceAll(",", "");
                    zzCount1 = zzCount1.replaceAll(",", "");
                    BigDecimal bigDecimal = new BigDecimal(zzCount).add(new BigDecimal(zzCount1));
                    BigDecimal multiply = bigDecimal.multiply(new BigDecimal(100));
                    String o11Str = multiply.toPlainString();
                    // 将解析后的代码与估值表中的代码进行比对
                    Tuple tuple = generateBankReconciliation(transformCode, "中债", String.valueOf(o2), o11Str, date, accountNameAndNo, code);
                    if ("1".equals(tuple.get(1))) {
                        Object o4 = tuple.get(0);
                        if (ObjectUtil.isNotNull(o4)) {
                            BankReconciliation bankReconciliation = (BankReconciliation) o4;
                            bankReconciliation.setSourceCode(code);
                            bankReconciliations.add(bankReconciliation);
                        }
                    } else {
                        Object o4 = tuple.get(0);
                        if (ObjectUtil.isNotNull(o4)) {
                            BankReconciliation bankReconciliation = (BankReconciliation) o4;
                            bankReconciliation.setSourceCode(code);
                            repeatBankReconciliations.add(bankReconciliation);
                        }
                    }
                }
                log.info("银行对账需要入库的数据是(当天中债):{}", JSON.toJSONString(bankReconciliations));
                saveBatch(mailCommonInfos, date, bankReconciliations, "中债", String.valueOf(accountNameAndNo.get(1)), repeatBankReconciliations);
            }
        }
        return date;
    }

    private void partSaveBatch(List<BankReconciliation> fileNotFoundBankReconciliations) {
        int total = fileNotFoundBankReconciliations.size();
        if (total > 1000) {
            // 分批插入
            List<List<BankReconciliation>> lists = splitListForChunkSize(fileNotFoundBankReconciliations, 1000);
            if (CollectionUtil.isNotEmpty(lists)) {
                for (List<BankReconciliation> list : lists) {
                    bankReconciliationMapper.saveBatch(list);
                }
            }
        } else {
            bankReconciliationMapper.saveBatch(fileNotFoundBankReconciliations);
        }
    }

    private static <T> List<List<T>> splitListForChunkSize(List<T> list, int chunkSize) {
        List<List<T>> res = new ArrayList<>();
        // 总数
        int total = list.size();
        // 如果每份的大小大于或等于总数则直接返回
        if (chunkSize >= total) {
            res.add(list);
            return res;
        }
        int page = 0;
        while (true) {
            int start = page * chunkSize;
            int end = (page + 1) * chunkSize;
            // 如果end大于total则说明是最后一页
            if (end > total) {
                if (start < total) {
                    res.add(list.subList(start, total));
                }
                break;
            }
            // 非最后一页正常获取
            res.add(list.subList(start, end));
            // 页码每次加1
            page += 1;
        }
        return res;
    }

    /**
     * 生成入库对象
     *
     * @param code                              证券编码
     * @param setSettlementLocation             文件类型
     * @param securityName                      证券名称
     * @param settlementCompanyPositionQuantity 结算中心持仓数
     * @param date                              日期
     * @return 入库对象
     */
    private Tuple generateBankReconciliation(String code,
                                             String setSettlementLocation,
                                             String securityName,
                                             String settlementCompanyPositionQuantity,
                                             String date, List<Object> accountNameAndNo, String sourceCode) {
        // 去掉文件中可能存在的空格
        securityName = StringUtils.deleteWhitespace(securityName.replaceAll("（", "(").replaceAll("）", ")"));
        BankReconciliation bankReconciliation = new BankReconciliation();
        bankReconciliation.setId(IdUtil.getSnowflakeNextIdStr());
        bankReconciliation.setDataDate(date);
        bankReconciliation.setSettlementLocation(setSettlementLocation);
        bankReconciliation.setSourceCode(sourceCode);
        bankReconciliation.setSettlementCompanyPositionQuantity(settlementCompanyPositionQuantity);
        // 找到账套编码
        String accCode = String.valueOf(accountNameAndNo.get(1));
        bankReconciliation.setAccountNumber(accCode);
        // 根据转换后的代码查询估值表
        log.info("获取到的账户账号为:{}", accCode);
        // 根据账户账号查询对应的账套编号
        List<String> accountSetIds;
        if ("中债".equals(setSettlementLocation)) {
            accountSetIds = SpringUtil.getBean(AccountSetMapper.class).getIdZZByAccount(accCode);
        } else {
            accountSetIds = SpringUtil.getBean(AccountSetMapper.class).getIdSQByAccount(accCode);
        }
        log.info("根据账号查询出来的账套编号为:{}", accountSetIds);
        String accountSetId = null;
        if (CollectionUtil.isNotEmpty(accountSetIds)) {
            accountSetId = accountSetIds.get(0);
        }
        List<JSONObject> zqdms = bankReconciliationMapper.queryValuationTableByZQDM(code, date, accountSetId);
        // 如果此处查不出来则进行手动校准, 说明估值系统没有数据
        if (CollectionUtil.isEmpty(zqdms)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("L_ZTBH", accountSetId);
            // 如果没有持仓数 则置空后续计算时按照0计算
            zqdms.add(jsonObject);
        } else {
            // 如果查询出的数量 > 1条 要将估值数想加
            if (zqdms.size() > 1) {
                Map<String, Double> collect = zqdms.stream().collect(Collectors.groupingBy(n -> n.getString("L_ZTBH"),
                        Collectors.summingDouble(n -> n.getDouble("L_ZQCC"))));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("L_ZTBH", accountSetId);
                jsonObject.put("L_ZQCC", collect.get(accountSetId));
                zqdms.clear();
                zqdms.add(jsonObject);
            }
        }
        log.info("查询估值表的账套编码为:{}", zqdms);
        // 查询自己系统的账套信息
        if (CollectionUtil.isNotEmpty(zqdms)) {
            List<String> zqdm_ids = zqdms.stream().map(n -> n.getString("L_ZTBH")).collect(Collectors.toList());
            List<MailCommonInfo> mailCommonInfos = SpringUtil.getBean(AccountSetMapper.class).queryAccountSetByIds(zqdm_ids);
            if (CollectionUtil.isNotEmpty(mailCommonInfos)) {
                log.info("查询的账套信息为:{}", mailCommonInfos.stream().map(MailCommonInfo::getAccountName).collect(Collectors.toList()));
                MailCommonInfo mailCommonInfo = mailCommonInfos.get(0);
                JSONObject jsonObject = zqdms.get(0);
                // 持仓数量
                Double lZqcc = jsonObject.getDouble("L_ZQCC");
                // 结算公司持仓数量
                double d = Double.parseDouble(settlementCompanyPositionQuantity);
                bankReconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                bankReconciliation.setAccountSetCode(mailCommonInfo.getId());
                bankReconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                bankReconciliation.setValuationTime(mailCommonInfo.getValuationTime());
                bankReconciliation.setProductCode(mailCommonInfo.getProductCode());
                bankReconciliation.setDifference(String.valueOf(Objects.requireNonNullElse(lZqcc, 0D) - d));
                bankReconciliation.setSecurityCode(code);
                bankReconciliation.setSecurityName(securityName);
                bankReconciliation.setValuationPositionQuantity(null == lZqcc ? null : String.valueOf(lZqcc));
            }
        } else {
            // 估值库中不存在 此证券代码的数据
            bankReconciliation.setSecurityCode(code);
            bankReconciliation.setSecurityName(securityName);
            bankReconciliation.setDifference(settlementCompanyPositionQuantity);
        }
        // 检查表中是否已经存在
        boolean res = checkData(date, securityName, accountSetId);
        if (res) {
            log.info("***数据重复不添加, date = {}, securityName = {}", date, securityName);
            // 如果原先表里就存在 则更新表中的结算公司持仓数
            syncExistBankReconciliation(bankReconciliation);
            return new Tuple(bankReconciliation, "0");
        } else {
            // 如果原先表里就存在 则更新表中的结算公司持仓数
            syncExistBankReconciliation(bankReconciliation);
        }
        return new Tuple(bankReconciliation, "1");
    }

    /**
     * 上传文件后需要同步表中已经存在的结算公司的持仓数
     *
     * @param bankReconciliation 银行对账对象
     */
    private void syncExistBankReconciliation(BankReconciliation bankReconciliation) {
        String accountSetCode = bankReconciliation.getAccountSetCode();
        String securityCode = bankReconciliation.getSecurityCode();
        String securityName = bankReconciliation.getSecurityName();
        String dataDate = bankReconciliation.getDataDate();
        String settlementLocation = bankReconciliation.getSettlementLocation();
        String settlementCompanyPositionQuantity = bankReconciliation.getSettlementCompanyPositionQuantity();
        if (StringUtils.isNotBlank(accountSetCode) &&
                StringUtils.isNotBlank(securityName) &&
                StringUtils.isNotBlank(dataDate) &&
                StringUtils.isNotBlank(settlementLocation)) {
            int res = bankReconciliationMapper.syncExistBankReconciliation(accountSetCode, securityName, dataDate, settlementLocation, settlementCompanyPositionQuantity);
            log.info("更新文件结算持仓修改的行数:{} - securityCode = {}, settlementCompanyPositionQuantity = {}", res, securityCode, settlementCompanyPositionQuantity);
        }
    }

    private boolean checkData(String date, String securityName, String accountSetId) {
        int count = bankReconciliationMapper.checkData(date, securityName, accountSetId);
        return count > 0;
    }

    /**
     * 解析上清rpa文件
     */
    public String resolveSQExcel(File file, List<MailCommonInfo> mailCommonInfos, ExcelReader reader) {
        String extName = FileUtil.extName(file);
        String testDate = null;
        if ("xlsx".equals(extName) || "xls".equals(extName)) {
            List<BankReconciliation> bankReconciliations = new ArrayList<>();
            List<BankReconciliation> needMerge = new ArrayList<>();
            // 记录列索引
            Map<String, Integer> indexs = new HashMap<>();
            // 读取第一行表头
            List<Object> headers = reader.readRow(0);
            // 需要寻找列为 债券代码 债券简称 余额（元） 更新时间 持有人账号
            for (int i = 0; i < headers.size(); i++) {
                switch (String.valueOf(headers.get(i))) {
                    case "债券代码":
                        indexs.put("ZQDM", i);
                        break;
                    case "债券简称":
                        indexs.put("ZQJC", i);
                    case "余额（元）":
                        indexs.put("YE", i);
                        break;
                    case "更新时间":
                        indexs.put("GXSJ", i);
                        break;
                    case "持有人账号":
                        indexs.put("CYRZH", i);
                        break;
                    case "持有人账户简称":
                        indexs.put("CYRZHJC", i);
                        break;
                    case "日期":
                        indexs.put("RQ", i);
                        break;
                }
            }
            List<List<Object>> read = reader.read(1);
            for (List<Object> row : read) {
                // 债券代码
                Object o1 = row.get(indexs.get("ZQDM"));
                // 债券简称 -> 证券名称
                Object o2 = row.get(indexs.get("ZQJC"));
                // 如果代码或名称为空 则不解析当前行
                if (StringUtils.isBlank(String.valueOf(o1)) || StringUtils.isBlank(String.valueOf(o2))) {
                    continue;
                }
                // 持仓数
                Object o8 = row.get(indexs.get("YE"));
                // 日期 如果对应没有此列 则按照当天日期
                Integer rq = indexs.get("RQ");
                Object o3;
                if (null == rq) {
                    o3 = DateUtil.today();
                } else {
                    o3 = row.get(indexs.get("RQ"));
                }
                testDate = String.valueOf(o3);
                // 防止模板中的日期 不是 yyyy-MM-dd
                testDate = DateUtil.format(DateUtil.parseDate(testDate), "yyyy-MM-dd");
                // 持有人账户简称
                Object o4 = row.get(indexs.get("CYRZHJC"));
                // 持有人账户
                Object o5 = row.get(indexs.get("CYRZH"));
                String code = String.valueOf(o1);
                String name = String.valueOf(o2);
                String updateTime = String.valueOf(o3);
                log.info("解析上清文件中每行的 code={}, name = {}, updateTime={}, count = {}, CYRZHJC = {}, CYRZH = {}", code, name, updateTime, o8, o4, o5);
                // 去掉开头字母
                char c = code.charAt(0);
                if (Character.isLetter(c)) {
                    String tmp = code;
                    code = code.substring(1);
                    log.info("证券代码首字符是字母需要去掉:由 {} -> {}", tmp, code);
                }
                // 编码转换
                String resCode = transformCode(code);
                log.info("符合时间转换后的代码为:{}", resCode);
                // 有些可能存在逗号后的格式 统一删除逗号
                String o8Str = String.valueOf(o8);
                o8Str = o8Str.replaceAll(",", "");
                // 上清的值应该除以100后进行入库
                BigDecimal bigDecimal = new BigDecimal(o8Str);
                BigDecimal divide = bigDecimal.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                o8Str = divide.toPlainString();
                // 将解析后的代码与估值表中的代码进行比对
                Tuple tuple = generateBankReconciliation(resCode, "上清", name, o8Str, testDate, CollectionUtil.newArrayList(o4, o5), code);
                if ("1".equals(tuple.get(1))) {
                    Object o = tuple.get(0);
                    if (ObjectUtil.isNotNull(o)) {
                        BankReconciliation bankReconciliation = (BankReconciliation) o;
                        bankReconciliation.setSourceCode(code);
                        bankReconciliations.add(bankReconciliation);
                    }
                } else {
                    Object o = tuple.get(0);
                    BankReconciliation bankReconciliation = (BankReconciliation) o;
                    bankReconciliation.setSourceCode(code);
                    needMerge.add(bankReconciliation);
                }
            }
            bankReconciliations = handleFileRepeatData(bankReconciliations);
            log.info("银行对账需要入库的数据是(上清):{}", JSON.toJSONString(bankReconciliations));
            saveBatch(mailCommonInfos, testDate, bankReconciliations, "上清", null, null);
            if (CollectionUtil.isNotEmpty(needMerge)) {
                log.info("需要处理合并更新的是:{}", JSON.toJSONString(needMerge));
                List<BankReconciliation> bankReconciliations1 = handleFileRepeatData(needMerge);
                if (CollectionUtil.isNotEmpty(bankReconciliations1)) {
                    for (BankReconciliation bankReconciliation : bankReconciliations1) {
                        bankReconciliationMapper.updateSettlementQuantity(bankReconciliation);
                    }
                }
            }
        }
        return testDate;
    }

    /**
     * 批量保存
     *
     * @param mailCommonInfos     账套信息
     * @param testDate            日期
     * @param bankReconciliations 银行对账数据
     */
    private void saveBatch(List<MailCommonInfo> mailCommonInfos, String testDate, List<BankReconciliation> bankReconciliations,
                           String location,
                           String accountNumber,
                           List<BankReconciliation> repeatBankReconciliations) {
        if (CollectionUtil.isNotEmpty(bankReconciliations)) {
            // 将读取文件中的相同的证券持仓进行累加
            bankReconciliations = handleFileRepeatData(bankReconciliations);
            // 先加文件中的
            Set<BankReconciliation> reconciliations = new HashSet<>(bankReconciliations);
            if (CollectionUtil.isNotEmpty(reconciliations)) {
                bankReconciliationMapper.saveBatch(ListUtil.toList(reconciliations));
            }
        }
        // bankReconciliations 得到的是所有文件中的证券 下面要对比估计系统中的 检索出 估值系统中有的 文件中没有的
        List<BankReconciliation> fileNotFoundBankReconciliations = notFoundBankReconciliationFromFile(bankReconciliations, testDate, mailCommonInfos);
        List<BankReconciliation> existData = bankReconciliationMapper.selectAlreadyExistInfo(testDate);
        if (CollectionUtil.isNotEmpty(fileNotFoundBankReconciliations)) {
            // 查询中债表里已经存在的 不允许重复添加
            try {
                for (BankReconciliation reconciliation : existData) {
                    for (BankReconciliation fileNotFind : fileNotFoundBankReconciliations) {
                        // 更新文件中存在且估值系统中也存在得
                        if (StringUtils.isNotBlank(reconciliation.getAccountSetCode()) && reconciliation.getAccountSetCode().equals(fileNotFind.getAccountSetCode())
                                && StringUtils.isNotBlank(reconciliation.getSecurityName()) && reconciliation.getSecurityName().equals(fileNotFind.getSecurityName())
                                && reconciliation.getDataDate().equals(fileNotFind.getDataDate())) {
                            reconciliation.setValuationPositionQuantity(fileNotFind.getValuationPositionQuantity());
                            reconciliation.setDifference(new BigDecimal(StringUtils.isNotBlank(reconciliation.getValuationPositionQuantity()) ? reconciliation.getValuationPositionQuantity() : "0.0")
                                    .subtract(new BigDecimal(StringUtils.isNotBlank(reconciliation.getSettlementCompanyPositionQuantity()) ? reconciliation.getSettlementCompanyPositionQuantity() : "0.0")).setScale(1, RoundingMode.HALF_UP).toPlainString());
                            bankReconciliationMapper.updateData(reconciliation);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<BankReconciliation> bankReconciliations1 = CollectionUtil.subtractToList(fileNotFoundBankReconciliations, existData);
            // 再进行一步去重
            Set<BankReconciliation> reconciliations = new HashSet<>(bankReconciliations1);
            if (CollectionUtil.isNotEmpty(reconciliations)) {
                this.partSaveBatch(ListUtil.toList(reconciliations));
            }
        }
        // 在最后判断 后面导入的文件可能比前一次少 则少的部分应该将结算公司持仓变为0
        List<BankReconciliation> finalExistData;
        if ("上清".equals(location)) {
            finalExistData = bankReconciliationMapper.selectAlreadyExistInfoByLocation(testDate, location);
        } else {
            log.info("中债需要判断文件中删除的逻辑 accountNumber = {}", accountNumber);
            finalExistData = bankReconciliationMapper.selectAlreadyExistInfoByLocationAndAccountNumber(testDate, location, accountNumber);
            //finalExistData = bankReconciliationMapper.selectAlreadyExistInfoByLocationAndAccountNumber(testDate, location, null);
        }
        if (CollectionUtil.isNotEmpty(finalExistData)) {
            // 表里多的 文件里少的
            log.info("表里存在accountNumber = {} 的数据:{}", accountNumber, JSON.toJSONString(finalExistData));
            if (CollectionUtil.isEmpty(bankReconciliations)) {
                bankReconciliations = repeatBankReconciliations;
            }
            log.info("文件中的数据:{}", JSON.toJSONString(bankReconciliations));
            List<BankReconciliation> reconciliationList = CollectionUtil.subtractToList(finalExistData, bankReconciliations);
            log.info("表里多的 文件里少的 需要修改结算公司持仓数为 reconciliationList = {}", JSONObject.toJSONString(finalExistData));
            if (CollectionUtil.isNotEmpty(reconciliationList)) {
                // 更新结算中心持仓数为 0
                for (BankReconciliation reconciliation : reconciliationList) {
                    reconciliation.setSettlementCompanyPositionQuantity("0");
                    bankReconciliationMapper.updateSettlementQuantity(reconciliation);
                }
            }
        }
    }


    /**
     * 处理单个文件中的重复数据将文件持仓相加
     * @param bankReconciliations 文件中的数据
     * @return 合并后的数据
     */
    private List<BankReconciliation> handleFileRepeatDataV2(List<BankReconciliation> bankReconciliations) {
        List<BankReconciliation> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bankReconciliations)) {
            Map<String, List<BankReconciliation>> collect = bankReconciliations.stream().collect(Collectors.groupingBy(n -> n.getDataDate() + "_" + n.getAccountSetCode() + "_" + n.getSourceCode()));
            for (Map.Entry<String, List<BankReconciliation>> next : collect.entrySet()) {
                List<BankReconciliation> values = next.getValue();
                if (CollectionUtil.isNotEmpty(values)) {
                    if (values.size() == 1) {
                        res.add(values.get(0));
                    } else {
                        double sum = values.stream().collect(Collectors.summarizingDouble(n -> Double.parseDouble(n.getSettlementCompanyPositionQuantity()))).getSum();
                        BankReconciliation bankReconciliation = values.get(0);
                        bankReconciliation.setSettlementCompanyPositionQuantity(String.valueOf(sum));
                        res.add(bankReconciliation);
                    }
                }
            }
        }
        return res;
    }

    /**
     * 处理单个文件中的重复数据将文件持仓相加
     * @param bankReconciliations 文件中的数据
     * @return 合并后的数据
     */
    private List<BankReconciliation> handleFileRepeatData(List<BankReconciliation> bankReconciliations) {
        List<BankReconciliation> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(bankReconciliations)) {
            Map<String, List<BankReconciliation>> collect = bankReconciliations.stream().collect(Collectors.groupingBy(n -> n.getDataDate() + "_" + n.getAccountSetCode() + "_" + n.getSecurityName()));
            for (Map.Entry<String, List<BankReconciliation>> next : collect.entrySet()) {
                List<BankReconciliation> values = next.getValue();
                if (CollectionUtil.isNotEmpty(values)) {
                    if (values.size() == 1) {
                        res.add(values.get(0));
                    } else {
                        double sum = values.stream().collect(Collectors.summarizingDouble(n -> Double.parseDouble(n.getSettlementCompanyPositionQuantity()))).getSum();
                        BankReconciliation bankReconciliation = values.get(0);
                        bankReconciliation.setSettlementCompanyPositionQuantity(String.valueOf(sum));
                        res.add(bankReconciliation);
                    }
                }
            }
            // 有合并的情况 重新计算差值
            for (BankReconciliation reconciliation : res) {
                String valuationPositionQuantity = reconciliation.getValuationPositionQuantity();
                String settlementCompanyPositionQuantity = reconciliation.getSettlementCompanyPositionQuantity();
                if (StringUtils.isBlank(valuationPositionQuantity)) {
                    valuationPositionQuantity = "0.0";
                }
                if (StringUtils.isBlank(settlementCompanyPositionQuantity)) {
                    settlementCompanyPositionQuantity = "0.0";
                }
                reconciliation.setDifference(new BigDecimal(valuationPositionQuantity).subtract(new BigDecimal(settlementCompanyPositionQuantity)).setScale(1, RoundingMode.HALF_UP).toPlainString());
            }
        }
        return res;
    }

    @Data
    static class Tmp {

        private String accountSetCode;
        private String securityCode;
        private BigDecimal valuationPositionQuantity;
        private String sourceCode;
        private String securityName;
        private String dataDate;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Tmp)) return false;
            Tmp tmp = (Tmp) o;
            return Objects.equals(accountSetCode, tmp.accountSetCode) && Objects.equals(securityName, tmp.securityName) && Objects.equals(dataDate, tmp.dataDate);
        }

        @Override
        public int hashCode() {
            return Objects.hash(accountSetCode, securityName, dataDate);
        }
    }

    /**
     * 查询估值系统中有的 文件中没有的
     *
     * @param bankReconciliations 文件中存在的
     * @param testDate            对比日期
     * @return 入库数据
     */
    private List<BankReconciliation> notFoundBankReconciliationFromFile(List<BankReconciliation> bankReconciliations,
                                                                        String testDate,
                                                                        List<MailCommonInfo> mailCommonInfos) {
        List<BankReconciliation> res = new ArrayList<>();
        Map<String, List<MailCommonInfo>> collect = null;
        if (CollectionUtil.isNotEmpty(mailCommonInfos)) {
            collect = mailCommonInfos.stream().collect(Collectors.groupingBy(MailCommonInfo::getId));
        }
        // 首先查询估值库中当天的所有证券信息 按照 证券代码、账套编号 分组后用 估值数量相加
        List<JSONObject> jsonObjectList = bankReconciliationMapper.queryValuationTableByDate(testDate);
        if (CollectionUtil.isNotEmpty(jsonObjectList)) {
            List<Tmp> collect1 = jsonObjectList.stream().map(n -> {
                Tmp tmp = new Tmp();
                tmp.setSecurityCode(n.getString("VC_ZQDM"));
                tmp.setAccountSetCode(n.getString("L_ZTBH"));
                tmp.setValuationPositionQuantity(n.getBigDecimal("L_ZQCC"));
                tmp.setSourceCode(n.getString("VC_TRADEZQDM"));
                tmp.setSecurityName(n.getString("VC_ZQJC"));
                tmp.setDataDate(n.getString("D_DATE"));
                return tmp;
            }).collect(Collectors.toList());
            List<Tmp> collect2 = bankReconciliations.stream().map(n -> {
                Tmp tmp = new Tmp();
                tmp.setSecurityCode(n.getSecurityCode());
                tmp.setAccountSetCode(n.getAccountSetCode());
                tmp.setSourceCode(n.getSourceCode());
                tmp.setSecurityName(n.getSecurityName());
                tmp.setDataDate(n.getDataDate());
                return tmp;
            }).collect(Collectors.toList());
            Collection<Tmp> subtract = CollectionUtil.subtract(collect1, collect2);
            if (CollectionUtil.isNotEmpty(subtract)) {
                // 查询匹配不到的估值系统中的 证券名称 结算场所 证券代码
                // List<String> collect3 = subtract.stream().map(Tmp::getSecurityCode).distinct().collect(Collectors.toList());
                List<Tmp> tmps = ListUtil.toList(subtract);
                List<List<Tmp>> lists = splitListForChunkSize(tmps, 500);
                List<JSONObject> objectList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(lists)) {
                    for (List<Tmp> list : lists) {
                        List<JSONObject> jsonObjectList1 = bankReconciliationMapper.selectValuationSettlementLocation(list.stream().map(Tmp::getSecurityCode).collect(Collectors.toList()));
                        if (CollectionUtil.isEmpty(jsonObjectList1)) {
                            // 如果证券代码查不出结算场所 则试图采用结算中心代码查询
                            objectList.addAll(bankReconciliationMapper.selectValuationSettlementLocationBySourceCode(list.stream().map(Tmp::getSourceCode).collect(Collectors.toList())));
                        } else {
                            // 常规按照证券代码进行查询
                            objectList.addAll(jsonObjectList1);
                        }
                    }
                }
                Map<String, List<JSONObject>> vcZqdm = new HashMap<>();
                if (CollectionUtil.isNotEmpty(objectList)) {
                    vcZqdm = objectList.stream().collect(Collectors.groupingBy(n -> n.getString("VC_ZQDM")));
                }
                log.info("有{}个账套不在文件中", subtract.size());
                for (Tmp tmp : subtract) {
                    try {
                        BankReconciliation reconciliation = new BankReconciliation();
                        reconciliation.setSecurityCode(tmp.getSecurityCode());
                        reconciliation.setAccountSetCode(tmp.getAccountSetCode());
                        reconciliation.setId(IdUtil.getSnowflakeNextIdStr());
                        reconciliation.setDataDate(testDate);
                        reconciliation.setValuationPositionQuantity(tmp.getValuationPositionQuantity() != null ? tmp.getValuationPositionQuantity().toPlainString() : "0.0");
                        // 此处说明估值有结算没有 则差异直接是 估值数量
                        // reconciliation.setDifference(tmp.getValuationPositionQuantity() != null ? tmp.getValuationPositionQuantity().toPlainString().equals("0") ? "0.0" : tmp.getValuationPositionQuantity().toPlainString() : null);
                        // 获取结算场所
                        List<JSONObject> objects = vcZqdm.get(tmp.getSecurityCode());
                        if (CollectionUtil.isNotEmpty(objects)) {
                            JSONObject jsonObject = objects.get(0);
                            log.info("匹配估值系统中的证券名称 结算场所 证券代码 = {}", jsonObject);
                            reconciliation.setSettlementLocation(jsonObject.getString("VC_LOCATION"));
                            reconciliation.setSecurityName(jsonObject.getString("VC_ZQJC"));
                            reconciliation.setSourceCode(jsonObject.getString("VC_TRADEZQDM"));
                        }
                        if (MapUtil.isNotEmpty(collect)) {
                            List<MailCommonInfo> list = collect.get(tmp.getAccountSetCode());
                            if (CollectionUtil.isNotEmpty(list)) {
                                // 账套id不会重复 则这里取第一个
                                MailCommonInfo mailCommonInfo = list.get(0);
                                reconciliation.setAccountSetGroupId(mailCommonInfo.getAccountSetGroupId());
                                reconciliation.setAccountSetName(mailCommonInfo.getAccountName());
                                reconciliation.setAccountNumber(mailCommonInfo.getHolderAccountNumber());
                                reconciliation.setProductCode(mailCommonInfo.getProductCode());
                                reconciliation.setProductCategory(mailCommonInfo.getProductCategory());
                                reconciliation.setValuationTime(mailCommonInfo.getValuationTime());
                            }
                        }
                        res.add(reconciliation);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return res;
    }

    /**
     * 债券代码转换逻辑
     * 9位代码，证券代码压缩规则如下：
     * 原代码为 2位序号＋2位年份＋2位机构编号＋3位顺序号，如
     * 11中石油SCP004 *********
     * 11志高CP001 *********
     * 对应缩编为 1位字母（01=A、02=B、03=C、04=D、……）＋1位字母（10=A、11=B、12=C、13=D、……）＋2位机构编号＋2位顺序号，如
     * 11中石油SCP004 AB0104
     * 11志高CP001 DB5401
     * 若顺序号超过100，小于360，则前两位用1位字母代替（10=A、11=B、12=C、13=D、……），最后一位用数字；
     * 若顺序号≥360，则(X-359)，用这个数去除26，倍数（0=A,1=B…），余数（1=A,2=B…）
     * 如101800498 则10=J，18=I，498-359=139,5倍余9，为FI，综合=JI00FI
     * 若是7位代码，前两位为年份，1位字母（10=A、11=B、12=C、13=D、……）
     * 1280260：12年=C C80260。
     */
    private String transformCode(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        // 去掉开头字母
        char c = code.charAt(0);
        if (Character.isLetter(c)) {
            String tmp = code;
            code = code.substring(1);
            log.info("证券代码首字符是字母需要去掉:由 {} -> {}", tmp, code);
        }
        // 查询此代码是否满足压缩规则，如果不满足压缩规则则直接返回原代码
       /* boolean noCompress = checkCode(code);
        if (noCompress) {
            return code;
        }*/
        // 6位不用转换
        if (code.length() == 6) {
            log.info("6位代码不需要替换:{}", code);
            return code;
        } else if (code.length() == 7) {
            // 若是7位代码，前两位为年份，1位字母（10=A、11=B、12=C、13=D、……）
            String before = code.substring(0, 2);
            log.info("7位代码前两位为:{}", before);
            String s = code7Map.get(before);
            if (StringUtils.isEmpty(s)) {
                s = before;
            }
            log.info("7位代码前两位需要替换:{} -> {}", before, s);
            String after = code.substring(2);
            log.info("7位代码后几位为:{}", after);
            return s.concat(after);
        } else if (code.length() == 9) {
            /*
              原代码为 2位序号＋2位年份＋2位机构编号＋3位顺序号
              若顺序号超过100，小于360，则前两位用1位字母代替（10=A、11=B、12=C、13=D、……），最后一位用数字；
              若顺序号≥360，则(X-359)，用这个数去除26，倍数（0=A,1=B…），余数（1=A,2=B…）
             */
            String str1 = code.substring(0, 2);
            log.info("9位代码前2位序号:{}", str1);
            String rStr1 = code9Map.get(str1);
            if (StringUtils.isEmpty(rStr1)) {
                rStr1 = str1;
            }
            String str2 = code.substring(2, 4);
            String rStr2 = code7Map.get(str2);
            if (StringUtils.isEmpty(rStr2)) {
                rStr2 = str2;
            }
            log.info("9位代码2位年份:{}", str2);
            String str3 = code.substring(4, 6);
            log.info("9位代码2位机构编号:{}", str3);
            String str4 = code.substring(6);
            log.info("9位代码最后3位顺序号:{}", str4);
            String before3Concat = rStr1.concat(rStr2).concat(str3);
            int int4 = Integer.parseInt(str4);
            // 若顺序号超过100，小于360，则前两位用1位字母代替（10=A、11=B、12=C、13=D、……），最后一位用数字；
            if (int4 >= 100 && int4 < 360) {
                String str4_2 = str4.substring(0, 2);
                log.info("9位代码最后3位顺序号超过100,小于360,前2位为:{}", str4_2);
                String after = str4.substring(2);
                String rStr4 = code7Map.get(str4_2).concat(after);
                if (StringUtils.isEmpty(rStr4)) {
                    rStr4 = str4_2;
                }
                return before3Concat.concat(rStr4);
            } else if (int4 >= 360) {
                // 防止后续出错还原 原排序号
                String temp = String.valueOf(int4);
                // 若顺序号≥360，则(X-359)，用这个数去除26，倍数（0=A,1=B…），余数（1=A,2=B…）
                int4 = int4 - 359;
                log.info("超过360减去359后的值为:{}", int4);
                int i1 = int4 / 26;
                int i2 = int4 % 26;
                String divide = code9DivMap.get(i1 + "");
                if (StringUtils.isEmpty(divide)) {
                    return before3Concat.concat(temp);
                }
                log.info("9位代码最后3位顺序号超过360求商, 要由{} -> {}", i1, divide);
                String mod = code9ModMap.get(i2 + "");
                if (StringUtils.isEmpty(mod)) {
                    return before3Concat.concat(temp);
                }
                log.info("9位代码最后3位顺序号超过360求余数, 要由{} -> {}", i2, mod);
                String rStr4 = divide.concat(mod);
                return before3Concat.concat(rStr4);
            } else {
                if (int4 < 10) {
                    String rStr4 = "0" + int4;
                    return before3Concat.concat(rStr4);
                }
                String rStr4 = String.valueOf(int4);
                return before3Concat.concat(rStr4);
            }
        }
        return code;
    }

    /**
     * 检查代码是否符合压缩规则
     *
     * @param code 长代码
     * @return 是否被压缩
     */
    private boolean checkCode(String code) {
        List<String> codes = bankReconciliationMapper.checkCode(code);
        if (CollectionUtil.isNotEmpty(codes)) {
            // 如果一致代表没有压缩, 没有压缩不能进行后续转换
            String s = codes.get(0);
            boolean res = s.equals(code);
            log.info("****查询出来的证券代码是:{}, 原代码是:{}, 是否被压缩:{}", s, code, res ? "不符合压缩规则" : "符合压缩规则");
            return res;
        }
        return true;
    }

    private static final Map<String, String> code7Map = new HashMap<>();

    private static final Map<String, String> code9Map = new HashMap<>();

    private static final Map<String, String> code9DivMap = new HashMap<>();

    private static final Map<String, String> code9ModMap = new HashMap<>();

    static {
        code7Map.put("10", "A");
        code7Map.put("11", "B");
        code7Map.put("12", "C");
        code7Map.put("13", "D");
        code7Map.put("14", "E");
        code7Map.put("15", "F");
        code7Map.put("16", "G");
        code7Map.put("17", "H");
        code7Map.put("18", "I");
        code7Map.put("19", "J");
        code7Map.put("20", "K");
        code7Map.put("21", "L");
        code7Map.put("22", "M");
        code7Map.put("23", "N");
        code7Map.put("24", "O");
        code7Map.put("25", "P");
        code7Map.put("26", "Q");
        code7Map.put("27", "R");
        code7Map.put("28", "S");
        code7Map.put("29", "T");
        code7Map.put("30", "U");
        code7Map.put("31", "V");
        code7Map.put("32", "W");
        code7Map.put("33", "X");
        code7Map.put("34", "Y");
        code7Map.put("35", "Z");
        code9Map.put("01", "A");
        code9Map.put("02", "B");
        code9Map.put("03", "C");
        code9Map.put("04", "D");
        code9Map.put("05", "E");
        code9Map.put("06", "F");
        code9Map.put("07", "G");
        code9Map.put("08", "H");
        code9Map.put("09", "I");
        code9Map.put("10", "J");
        code9Map.put("11", "K");
        code9Map.put("12", "L");
        code9Map.put("13", "M");
        code9Map.put("14", "N");
        code9Map.put("15", "O");
        code9Map.put("16", "P");
        code9Map.put("17", "Q");
        code9Map.put("18", "R");
        code9Map.put("19", "S");
        code9Map.put("20", "T");
        code9Map.put("21", "U");
        code9Map.put("22", "V");
        code9Map.put("23", "W");
        code9Map.put("24", "X");
        code9Map.put("25", "Y");
        code9Map.put("26", "Z");
        code9DivMap.put("0", "A");
        code9DivMap.put("1", "B");
        code9DivMap.put("2", "C");
        code9DivMap.put("3", "D");
        code9DivMap.put("4", "E");
        code9DivMap.put("5", "F");
        code9DivMap.put("6", "G");
        code9DivMap.put("7", "H");
        code9DivMap.put("8", "I");
        code9DivMap.put("9", "J");
        code9DivMap.put("10", "K");
        code9DivMap.put("11", "L");
        code9DivMap.put("12", "M");
        code9DivMap.put("13", "N");
        code9DivMap.put("14", "O");
        code9DivMap.put("15", "P");
        code9DivMap.put("16", "Q");
        code9DivMap.put("17", "R");
        code9DivMap.put("18", "S");
        code9DivMap.put("19", "T");
        code9DivMap.put("20", "U");
        code9DivMap.put("21", "V");
        code9DivMap.put("22", "W");
        code9DivMap.put("23", "X");
        code9DivMap.put("24", "Y");
        code9DivMap.put("25", "Z");
        code9ModMap.put("1", "A");
        code9ModMap.put("2", "B");
        code9ModMap.put("3", "C");
        code9ModMap.put("4", "D");
        code9ModMap.put("5", "E");
        code9ModMap.put("6", "F");
        code9ModMap.put("7", "G");
        code9ModMap.put("8", "H");
        code9ModMap.put("9", "I");
        code9ModMap.put("10", "J");
        code9ModMap.put("11", "K");
        code9ModMap.put("12", "L");
        code9ModMap.put("13", "M");
        code9ModMap.put("14", "N");
        code9ModMap.put("15", "O");
        code9ModMap.put("16", "P");
        code9ModMap.put("17", "Q");
        code9ModMap.put("18", "R");
        code9ModMap.put("19", "S");
        code9ModMap.put("20", "T");
        code9ModMap.put("21", "U");
        code9ModMap.put("22", "V");
        code9ModMap.put("23", "W");
        code9ModMap.put("24", "X");
        code9ModMap.put("25", "Y");
        code9ModMap.put("26", "Z");
    }

}
