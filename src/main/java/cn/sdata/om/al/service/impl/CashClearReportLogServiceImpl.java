package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.LogCCRExportRecord;
import cn.sdata.om.al.entity.LogCCRRPARecord;
import cn.sdata.om.al.entity.LogCCRSendMailRecord;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.mapper.CashClearReportLogMapper;
import cn.sdata.om.al.service.CashClearReportLogService;
import cn.sdata.om.al.service.cashClearReport.CashClearReportService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CashClearReportLogServiceImpl implements CashClearReportLogService {

    private CashClearReportLogMapper cashClearReportLogMapper;

    @Autowired
    public void setCashClearReportLogMapper(CashClearReportLogMapper cashClearReportLogMapper) {
        this.cashClearReportLogMapper = cashClearReportLogMapper;
    }

    @Override
    public PageInfo<LogCCRRPARecord> getLogCCRRPARecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogCCRRPARecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogCCRRPARecord> logCCRRPARecords = cashClearReportLogMapper.getLogCCRRPARecordList(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logCCRRPARecords)) {
                for (LogCCRRPARecord record : logCCRRPARecords) {
                    String status = record.getStatus();
                    if (StringUtils.isNotBlank(status)) {
                        if ("COMPLETE".equals(status)) {
                            record.setRpaResult("执行完成");
                        } else if ("FAILED".equals(status)) {
                            record.setRpaResult("执行失败");
                        } else {
                            record.setRpaResult("执行中");
                        }
                    }
                }
            }
            return new PageInfo<>(logCCRRPARecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogCCRExportRecord> getLogCCRExportRecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogCCRExportRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogCCRExportRecord> logCCRExportRecordList = cashClearReportLogMapper.getLogCCRExportRecordList(beginDataDate, endDataDate);
            return new PageInfo<>(logCCRExportRecordList);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogCCRSendMailRecord> getLogCCRSendMailRecordList(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogCCRSendMailRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogCCRSendMailRecord> logCCRSendMailRecordList = cashClearReportLogMapper.getLogCCRSendMailRecordList(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logCCRSendMailRecordList)) {
                for (LogCCRSendMailRecord record : logCCRSendMailRecordList) {
                    String sendStatus = record.getSendStatus();
                    if (StringUtils.isNotBlank(sendStatus)) {
                        if ("SUCCESS".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.SUCCESS.name());
                        } else if ("FAILED".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.FAILED.name());
                        }
                    }
                }
            }
            return new PageInfo<>(logCCRSendMailRecordList);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public Boolean reExecuteRpa(String rpaLogId) {
        LogCCRRPARecord logCCRRPARecord = cashClearReportLogMapper.getCCRRpaLogById(rpaLogId);
        if (logCCRRPARecord != null) {
            String dataDate = logCCRRPARecord.getDataDate();
            if (StringUtils.isNotBlank(dataDate)) {
                try {
                    SpringUtil.getBean(CashClearReportService.class).exeRpa(dataDate);
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }
}
