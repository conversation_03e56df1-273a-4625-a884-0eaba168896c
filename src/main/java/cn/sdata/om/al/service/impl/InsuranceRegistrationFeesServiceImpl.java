package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.*;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.InsuranceRegistrationFeesJob;
import cn.sdata.om.al.mapper.AccountInformationMapper;
import cn.sdata.om.al.mapper.InsuranceRegistrationFeesMapper;
import cn.sdata.om.al.mapper.InterbankFeesMapper;
import cn.sdata.om.al.ocr.OCRResult;
import cn.sdata.om.al.ocr.OCRUtil;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.InsuranceRegistrationFeesService;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.utils.CommonUtil;
import cn.sdata.om.al.utils.DateUtils;
import cn.sdata.om.al.utils.LogFYIUtils;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InsuranceRegistrationFeesServiceImpl implements InsuranceRegistrationFeesService {

    private static volatile boolean isUpload;

    private InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper;

    private OCRUtil ocrUtil;

    @Value("${file.insurance-registration-fees-dir}")
    private String filePath;

    private static volatile boolean isImportO32;

    @Autowired
    public void setInsuranceRegistrationFeesMapper(InsuranceRegistrationFeesMapper insuranceRegistrationFeesMapper) {
        this.insuranceRegistrationFeesMapper = insuranceRegistrationFeesMapper;
    }

    @Override
    public PageInfo<InsuranceRegistrationFees> page(InsuranceRegistrationFeesQuery insuranceRegistrationFeesQuery) {
        int pageNo = insuranceRegistrationFeesQuery.getPageNo();
        int pageSize = insuranceRegistrationFeesQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesMapper.page(insuranceRegistrationFeesQuery);
            if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                for (InsuranceRegistrationFees insuranceRegistrationFee : insuranceRegistrationFees) {
                    String amount = insuranceRegistrationFee.getAmount();
                    if (StringUtils.isNotBlank(amount)) {
                        insuranceRegistrationFee.setAmount(CommonUtil.handleNumberStr(amount));
                    }
                    if (!CommonStatus.SUCCESS.name().equals(insuranceRegistrationFee.getHandleResult())) {
                        insuranceRegistrationFee.setHandleResult(CommonStatus.FAIL.name());
                    }
                }
            }
            return new PageInfo<>(insuranceRegistrationFees);
        }
    }

    @Autowired
    public void setOcrUtil(OCRUtil ocrUtil) {
        this.ocrUtil = ocrUtil;
    }

    @Override
    public String upload(MultipartFile[] files, String mailId, String noticeDate) {
        if (StringUtils.isNotBlank(mailId)) {
            // 如果是邮件分拣过来的需要等待
            while (isUpload) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        if (!isUpload) {
            List<InsuranceRegistrationFeesFile> insuranceRegistrationFeesFiles = new ArrayList<>();
            List<JSONObject> fileList = new ArrayList<>();
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    try {
                        String fileId = IdUtil.getSnowflakeNextIdStr();
                        InsuranceRegistrationFeesFile insuranceRegistrationFeesFile = new InsuranceRegistrationFeesFile();
                        insuranceRegistrationFeesFile.setId(fileId);
                        insuranceRegistrationFeesFile.setFileName(file.getOriginalFilename());
                        File uploadFile = new File(filePath + file.getOriginalFilename());
                        file.transferTo(uploadFile);
                        insuranceRegistrationFeesFile.setFilePath(uploadFile.getAbsolutePath());
                        insuranceRegistrationFeesFiles.add(insuranceRegistrationFeesFile);
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("fileId", fileId);
                        jsonObject.put("file", uploadFile);
                        fileList.add(jsonObject);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            String username = SecureUtil.currentUserName();
            CompletableFuture.runAsync(() -> {
                isUpload = true;
                log.info("开始中保登缴费信息录入...");
                if (ArrayUtil.isNotEmpty(fileList)) {
                    List<InsuranceRegistrationFees> res = new ArrayList<>();
                    List<AccountInformation> accountInformationList = SpringUtil.getBean(AccountInformationMapper.class).selectList(null);
                    for (JSONObject obj : fileList) {
                        JSONObject params = new JSONObject();
                        try {
                            File file = obj.getObject("file", File.class);
                            params.put("logId", IdUtil.getSnowflakeNextIdStr());
                            params.put("username", username);
                            params.put("beginTime", DateUtil.now());
                            params.put("fileUrl", file.getAbsolutePath());
                            params.put("executeType", StringUtils.isNotBlank(mailId) ? "AUTO" : "MANUAL");
                            LogFYIUtils.preUploadFile(params);
                            String fileId = obj.getString("fileId");
                            // OCRResult ocrResult = ocrUtil.executeOCR(FileUtil.readBytes(file));
                            // todo 中保登统一2页
                            OCRResult ocrResult = ocrUtil.executeOCRV2(FileUtil.readBytes(file), 2);
                            List<InsuranceRegistrationFees> insuranceRegistrationFees = generateInsuranceRegistrationFees(ocrResult, accountInformationList);
                            if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                                insuranceRegistrationFees = insuranceRegistrationFees.stream().peek(n -> {
                                    n.setFileId(fileId);
                                    n.setMailId(mailId);
                                    n.setNoticeDate(noticeDate);
                                }).collect(Collectors.toList());
                                Iterator<InsuranceRegistrationFees> iterator = insuranceRegistrationFees.iterator();
                                while (iterator.hasNext()) {
                                    InsuranceRegistrationFees fees = iterator.next();
                                    int count = insuranceRegistrationFeesMapper.selectRepeatData(fees);
                                    if (count > 0) {
                                        // 重复数据不重复添加
                                        iterator.remove();
                                    }
                                }
                                if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
                                    res.addAll(insuranceRegistrationFees);
                                }
                            }
                            params.put("endTime", DateUtil.now());
                            params.put("status", CommonStatus.SUCCESS.name());
                            LogFYIUtils.postUploadFile(params);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            params.put("endTime", DateUtil.now());
                            params.put("status", CommonStatus.FAIL.name());
                            LogFYIUtils.postUploadFile(params);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(insuranceRegistrationFeesFiles)) {
                        insuranceRegistrationFeesMapper.batchSaveFile(insuranceRegistrationFeesFiles);
                    }
                    if (CollectionUtil.isNotEmpty(res)) {
                        insuranceRegistrationFeesMapper.batchSave(res);
                    }
                }
                isUpload = false;
                log.info("中保登缴费信息录入结束...");
            });
        }
        return isUpload ? "executing" : "completed";
    }

    private List<InsuranceRegistrationFees> generateInsuranceRegistrationFees(OCRResult ocrResult, List<AccountInformation> accountInformationList) {
        List<InsuranceRegistrationFees> res = new ArrayList<>();
        Map<String, String> commonData = ocrResult.getCommonData();
        List<Map<String, String>> tableData = ocrResult.getTableData();
        if (MapUtil.isEmpty(commonData)) {
            BusinessException.throwException("基础信息没有被识别");
        }
        if (CollectionUtil.isEmpty(tableData)) {
            BusinessException.throwException("表格信息没有被识别");
        }
        for (Map<String, String> data : tableData) {
            InsuranceRegistrationFees fees = new InsuranceRegistrationFees();
            fees.setId(IdUtil.getSnowflakeNextIdStr());
            String nameOfPayee = commonData.get("户名");
            fees.setNameOfPayee(nameOfPayee);
            String beneficiaryAccount = commonData.get("账号");
            fees.setBeneficiaryAccount(beneficiaryAccount);
            String bankAccount = commonData.get("开户行");
            fees.setBankAccount(bankAccount);
            String largePaymentNumber = commonData.get("大额支付系统号");
            fees.setLargePaymentNumber(largePaymentNumber);
            String remark = commonData.get("备注");
            fees.setRemark(remark);
            String date = data.get("缴费期间");
            if (StringUtils.isBlank(date)) {
                date = data.get("合计缴费期间");
            }
            if (StringUtils.isNotBlank(date)) {
                String[] split = date.split("-");
                if (split.length == 2) {
                    DateTime begin = DateUtil.parse(split[0], "yyyy.MM.dd");
                    DateTime end = DateUtil.parse(split[1], "yyyy.MM.dd");
                    fees.setBeginCostDate(DateUtil.format(begin, "yyyy-MM"));
                    fees.setEndCostDate(DateUtil.format(end, "yyyy-MM"));
                    fees.setBeginPaymentPeriodDate(DateUtil.format(begin, "yyyy-MM-dd"));
                    fees.setEndPaymentPeriodDate(DateUtil.format(end, "yyyy-MM-dd"));
                    fees.setPayMonth(DateUtils.getLastMonthOfPreviousQuarterFormatted(end));
                }
            }
            String amount = data.get("费用金额(元)");
            fees.setAmount(StringUtils.isNotBlank(amount) ? amount.replaceAll(",", "") : "0.00");
            String amountRes = fees.getAmount();
            Double amountDouble = Double.parseDouble(amountRes);
            if (Double.valueOf("0.00").equals(amountDouble)) {
                fees.setPaymentStatus(PaymentStatus.NO_PAID.name());
            } else {
                fees.setPaymentStatus(PaymentStatus.UNPAID.name());
            }
            String holderAccountNumber = data.get("账户号码");
            OCRProductInfo ocrProductInfo = getProductInfo(holderAccountNumber, accountInformationList);
            if (ocrProductInfo != null) {
                AccountInformation accountInformation = ocrProductInfo.getAccountInformation();
                if (accountInformation != null) {
                    fees.setProductId(accountInformation.getId());
                    fees.setProductName(accountInformation.getFullProductName());
                }
                fees.setPaymentMethod(ocrProductInfo.getPaymentMethod());
                fees.setFeeCollectionAgencies(ocrProductInfo.getFeeCollectionAgencies());
            }
            fees.setHolderAccountNumber(holderAccountNumber);
            String holderAccountName = data.get("持有人账户全称");
            fees.setHolderAccountName(holderAccountName);
            String orderNumber = data.get("序号");
            fees.setOrderNumber(orderNumber);
            fees.setImportO32Status(ImportO32Status.NO_GENERATE.name());
            fees.setOcrRecognizeStatus(OcrRecognizeStatusEnum.UNCONFIRMED.name());
            fees.setMailSendStatus(MailStatus.UNSENT.name());
            if (StringUtils.isEmpty(holderAccountName) && StringUtils.isEmpty(holderAccountNumber)) {
                log.info("没有识别到持有人名称与账号,忽略此数据...");
                continue;
            }
            res.add(fees);
        }
        return res;
    }

    private OCRProductInfo getProductInfo(String holderAccountNumber, List<AccountInformation> accountInformationList) {
        if (StringUtils.isNotBlank(holderAccountNumber)) {
            Optional<AccountInformation> first = accountInformationList.stream().filter(n -> holderAccountNumber.equals(n.getHolderAccountNumber())).findFirst();
            if (first.isPresent()) {
                OCRProductInfo ocrProductInfo = new OCRProductInfo();
                AccountInformation accountInformation = first.get();
            /*String clearingHouseHolderAccount = accountInformation.getClearingHouseHolderAccount();
            String foreignExchangeCenterMemberCode = accountInformation.getForeignExchangeCenterMemberCode();
            String centralDebtAccountNumber = accountInformation.getCentralDebtAccountNumber();
            if (StringUtils.isNotBlank(clearingHouseHolderAccount)) {
                ocrProductInfo.setAccountInformation(accountInformation);
                ocrProductInfo.setFeeCollectionAgencies("上清");
                ocrProductInfo.setPaymentMethod(accountInformation.getPaymentMethodSQ());
                return ocrProductInfo;
            }
            if (StringUtils.isNotBlank(centralDebtAccountNumber)) {
                ocrProductInfo.setAccountInformation(accountInformation);
                ocrProductInfo.setFeeCollectionAgencies("中债");
                ocrProductInfo.setPaymentMethod(accountInformation.getPaymentMethodZZ());
                return ocrProductInfo;
            }
            if (StringUtils.isNotBlank(foreignExchangeCenterMemberCode)) {
                ocrProductInfo.setAccountInformation(accountInformation);
                ocrProductInfo.setFeeCollectionAgencies("外汇");
                ocrProductInfo.setPaymentMethod(accountInformation.getPaymentMethodWH());
                return ocrProductInfo;
            }*/
                ocrProductInfo.setFeeCollectionAgencies("中保登");
                ocrProductInfo.setAccountInformation(accountInformation);
                return ocrProductInfo;
            }
        }
        return null;
    }

    @Override
    public String importO32(List<String> ids) {
        if (!isImportO32) {
            String username = SecureUtil.currentUserName();
            CompletableFuture.runAsync(() -> {
                isImportO32 = true;
                // 查询业务日切时间
                String initDate = null;
                try {
                    List<JSONObject> jsonObjects = SpringUtil.getBean(InterbankFeesMapper.class).testSelectO32Date();
                    if (CollectionUtil.isNotEmpty(jsonObjects)) {
                        JSONObject object = jsonObjects.get(0);
                        initDate = object.getString("L_INIT_DATE");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (StringUtils.isBlank(initDate)) {
                    initDate = DateUtil.format(new Date(), "yyyyMMdd");
                }
                List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesMapper.selectList(ids);
                if (CollectionUtil.isEmpty(insuranceRegistrationFees)) {
                    BusinessException.throwException("没有获取到费用信息");
                }
                // 生成o32文件
                InterbankFeesServiceImpl interbankFeesService = SpringUtil.getBean(InterbankFeesServiceImpl.class);
                List<InterBankFees> interbankFees = insuranceRegistrationFeesTransformToInterbankFees(insuranceRegistrationFees);
                List<FileAndIds> o32Files = interbankFeesService.generateO32File(interbankFees, 1, initDate);
                // 上传至共享文件夹
                if (CollectionUtil.isEmpty(o32Files)) {
                    BusinessException.throwException("没有生成任何o32文件");
                }
                interbankFeesService.uploadShareFolder(o32Files, 1, true, username, "MANUAL");
                isImportO32 = false;
            });
        }
        return isImportO32 ? "executing" : "completed";
    }

    private List<InterBankFees> insuranceRegistrationFeesTransformToInterbankFees(List<InsuranceRegistrationFees> insuranceRegistrationFees) {
        List<InterBankFees> interBankFees = new ArrayList<>();
        for (InsuranceRegistrationFees insuranceRegistrationFee : insuranceRegistrationFees) {
            InterBankFees fees = new InterBankFees();
            fees.setPaymentMethod(insuranceRegistrationFee.getPaymentMethod());
            fees.setId(insuranceRegistrationFee.getId());
            fees.setProductId(insuranceRegistrationFee.getProductId());
            fees.setAmount(insuranceRegistrationFee.getAmount());
            fees.setFeeCollectionAgencies(insuranceRegistrationFee.getFeeCollectionAgencies());
            fees.setBeginCostDate(insuranceRegistrationFee.getBeginCostDate());
            fees.setEndCostDate(insuranceRegistrationFee.getEndCostDate());
            interBankFees.add(fees);
        }
        return interBankFees;
    }

    @Override
    public List<JSONObject> getById(String beginDate, String endDate) {
        List<JSONObject> res = new ArrayList<>();
        List<InsuranceRegistrationFees> insuranceRegistrationFees = insuranceRegistrationFeesMapper.selectListByDate(beginDate, endDate);
        if (CollectionUtil.isNotEmpty(insuranceRegistrationFees)) {
            Map<String, List<InsuranceRegistrationFees>> collect = insuranceRegistrationFees.stream().collect(Collectors.groupingBy(InsuranceRegistrationFees::getFileId));
            if (MapUtil.isNotEmpty(collect)) {
                for (Map.Entry<String, List<InsuranceRegistrationFees>> entry : collect.entrySet()) {
                    JSONObject object = new JSONObject();
                    String fileId = entry.getKey();
                    List<InsuranceRegistrationFees> insuranceRegistrationFeesList = entry.getValue();
                    InsuranceRegistrationFeesFile feesFile = insuranceRegistrationFeesMapper.selectByFileId(fileId);
                    object.put("fileId", feesFile.getId());
                    object.put("fileName", feesFile.getFileName());
                    if (CollectionUtil.isNotEmpty(insuranceRegistrationFeesList)) {
                        InsuranceRegistrationFees fees = insuranceRegistrationFeesList.get(0);
                        object.put("nameOfPayee", fees.getNameOfPayee());
                        object.put("beneficiaryAccount", fees.getBeneficiaryAccount());
                        object.put("bankAccount", fees.getBankAccount());
                        object.put("largePaymentNumber", fees.getLargePaymentNumber());
                        List<JSONObject> list = new ArrayList<>();
                        insuranceRegistrationFeesList = insuranceRegistrationFeesList.stream().sorted(Comparator.comparing(InsuranceRegistrationFees::getOrderNumber)).collect(Collectors.toList());
                        for (InsuranceRegistrationFees registrationFees : insuranceRegistrationFeesList) {
                            JSONObject jsonObject = getJsonObject(registrationFees);
                            list.add(jsonObject);
                        }
                        object.put("list", list);
                    }
                    res.add(object);
                }
            }
        }
        return res;
    }

    private @NotNull JSONObject getJsonObject(InsuranceRegistrationFees registrationFees) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", registrationFees.getId());
        jsonObject.put("orderNumber", registrationFees.getOrderNumber());
        jsonObject.put("holderAccountNumber", registrationFees.getHolderAccountNumber());
        jsonObject.put("productId", registrationFees.getProductId());
        jsonObject.put("productName", registrationFees.getProductName());
        jsonObject.put("holderAccountName", registrationFees.getHolderAccountName());
        jsonObject.put("beginPaymentPeriodDate", registrationFees.getBeginPaymentPeriodDate());
        jsonObject.put("endPaymentPeriodDate", registrationFees.getEndPaymentPeriodDate());
        jsonObject.put("amount", registrationFees.getAmount());
        return jsonObject;
    }


    @Override
    public boolean update(List<JSONObject> jsonObjects) {
        try {
            for (JSONObject jsonObject : jsonObjects) {
                // 修改基本信息
                List<JSONObject> list = jsonObject.getList("list", JSONObject.class);
                if (CollectionUtil.isNotEmpty(list)) {
                    List<String> ids = list.stream().map(n -> n.getString("id")).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
                    insuranceRegistrationFeesMapper.updateBaseInfo(jsonObject, ids);
                    for (JSONObject object : list) {
                        // 修改表格中的内容
                        String amount = object.getString("amount");
                        if (StringUtils.isNotBlank(amount)) {
                            object.put("amount", amount.replaceAll(",", ""));
                            Double amountDouble = Double.parseDouble(object.getString("amount"));
                            if (Double.valueOf("0.00").equals(amountDouble)) {
                                object.put("paymentStatus", PaymentStatus.NO_PAID.name());
                            } else {
                                object.put("paymentStatus", PaymentStatus.UNPAID.name());
                            }
                        } else {
                            object.put("amount", "0.00");
                            object.put("paymentStatus", PaymentStatus.NO_PAID.name());
                        }
                        insuranceRegistrationFeesMapper.updateTableData(object);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public String previewFile(String fileId) {
        InsuranceRegistrationFeesFile insuranceRegistrationFeesFile = insuranceRegistrationFeesMapper.selectByFileId(fileId);
        String res = "";
        if (insuranceRegistrationFeesFile != null) {
            String path = insuranceRegistrationFeesFile.getFilePath();
            if (StringUtils.isNotBlank(path)) {
                File file = new File(path);
                try {
                    /*PDDocument document = PDDocument.load(file);
                    PDFRenderer renderer = new PDFRenderer(document);
                    for (int i = 0; i < document.getNumberOfPages(); i++) {
                        BufferedImage image = renderer.renderImageWithDPI(i, 100);
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        ImageIO.write(image, "png", baos);
                        byte[] bytes = baos.toByteArray();
                        Base64.Encoder encoder = Base64.getEncoder();
                        String base64String = encoder.encodeToString(bytes);
                        res.add(base64String);
                    }
                    document.close();*/
                    return cn.hutool.core.codec.Base64.encode(FileUtil.readBytes(file));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return res;
    }

    @Override
    public List<InsuranceRegistrationFeesFile> getFileByIds(List<String> ids) {
        return insuranceRegistrationFeesMapper.getFileByIds(ids);
    }

    @Override
    public String syncPaymentStatus(List<String> ids) {
        return SpringUtil.getBean(InterbankFeesService.class).syncPaymentStatus(ids, 1);
    }

    @Override
    public List<InsuranceRegistrationFees> selectList(List<String> ids) {
        return insuranceRegistrationFeesMapper.selectList(ids);
    }

    @Override
    public boolean sendEmail(List<String> ids) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> jobIds = cronService.getJobIdByClass(InsuranceRegistrationFeesJob.class);
        List<InsuranceRegistrationFees> registrationFees = insuranceRegistrationFeesMapper.selectList(ids);
        if (CollectionUtil.isNotEmpty(registrationFees)) {
            List<String> sendIds = registrationFees.stream().map(InsuranceRegistrationFees::getId).collect(Collectors.toList());
            Map<String, String> params = new HashMap<>();
            for (InsuranceRegistrationFees insuranceRegistrationFees : registrationFees) {
                params.putIfAbsent(insuranceRegistrationFees.getProductId(), insuranceRegistrationFees.getFileId());
            }
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put("ids", sendIds);
            jobDataMap.put("productIdAndFileIdMap", params);
            jobDataMap.put("username", SecureUtil.currentUser().getAccount());
            cronService.startJobNow(jobIds, jobDataMap);
            return true;
        }
        return false;
    }

    @Override
    public List<InsuranceRegistrationFees> selectLastQuarterData(String quarterMonth, String quarterMonth1) {
        return insuranceRegistrationFeesMapper.selectLastQuarterData(quarterMonth, quarterMonth1);
    }

    @Override
    public boolean deleteById(String id) {
        return insuranceRegistrationFeesMapper.deleteById(id) > 0;
    }
}
