package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.job.SQPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.job.ZZPaymentReceiptDownloadRpaJob;
import cn.sdata.om.al.mapper.LogFYBMapper;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.LogFYBService;
import cn.sdata.om.al.utils.ProductUtils;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.RPA_END_DATE_NAME;
import static cn.sdata.om.al.constant.BaseConstant.RPA_START_DATE_NAME;

@Service
public class LogFYBServiceImpl implements LogFYBService {

    private LogFYBMapper logFYBMapper;

    @Autowired
    public void setLogFYBMapper(LogFYBMapper logFYBMapper) {
        this.logFYBMapper = logFYBMapper;
    }

    @Override
    public PageInfo<LogFYBGenerateFileRecord> getLogFYBGenerateFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBGenerateFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBGenerateFileRecord> logFYBGenerateFileRecord = logFYBMapper.getLogFYBGenerateFileRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBGenerateFileRecord)) {
                logFYBGenerateFileRecord = logFYBGenerateFileRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductIdsStr())) {
                        List<String> productIds = JSON.parseArray(n.getProductIdsStr(), String.class);
                        List<String> names = SpringUtil.getBean(ProductUtils.class).getProductNamesByIds(productIds);
                        n.setProductNames(StringUtils.join(names, ","));
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYBGenerateFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBDownloadFileRecord> getLogFYBDownloadFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBDownloadFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBDownloadFileRecord> logFYBDownloadFileRecord = logFYBMapper.getLogFYBDownloadFileRecord(beginDataDate, endDataDate);
            return new PageInfo<>(logFYBDownloadFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBImportO32Record> getLogFYBImportO32Record(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBImportO32Record> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBImportO32Record> logFYBImportO32Record = logFYBMapper.getLogFYBImportO32Record(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBImportO32Record)) {
                ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                for (LogFYBImportO32Record o32Record : logFYBImportO32Record) {
                    String importResult = o32Record.getImportResult();
                    if (StringUtils.isNotBlank(importResult)) {
                        List<JSONObject> jsonObjectList = JSON.parseArray(importResult, JSONObject.class);
                        List<JSONObject> res = new ArrayList<>();
                        for (JSONObject jsonObject : jsonObjectList) {
                            JSONObject subRes = new JSONObject();
                            String productId = jsonObject.getString("productId");
                            if (StringUtils.isNotBlank(productId)) {
                                subRes.put("productId", productId);
                                List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                                if (CollectionUtil.isNotEmpty(names)) {
                                    subRes.put("productName", names.get(0));
                                } else {
                                    subRes.put("productName", "");
                                }
                            }
                            String importO32Status = jsonObject.getString("importO32Status");
                            if (StringUtils.isNotBlank(importO32Status)) {
                                subRes.put("importO32Status", importO32Status);
                            } else {
                                subRes.put("importO32Status", "");
                            }
                            String o32Message = jsonObject.getString("o32Message");
                            if (StringUtils.isNotBlank(o32Message)) {
                                subRes.put("o32Message", o32Message);
                            } else {
                                subRes.put("o32Message", "");
                            }
                            res.add(subRes);
                        }
                        o32Record.setImportResultList(res);
                    }
                    String productId = o32Record.getProductId();
                    if (StringUtils.isNotBlank(productId)) {
                        List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                        if (CollectionUtil.isNotEmpty(names)) {
                            o32Record.setProductName(names.get(0));
                        }
                    }
                }
            }
            return new PageInfo<>(logFYBImportO32Record);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBO32ConfirmRecord> getLogFYBO32ConfirmRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBO32ConfirmRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBO32ConfirmRecord> logFYBO32ConfirmRecord = logFYBMapper.getLogFYBO32ConfirmRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBO32ConfirmRecord)) {
                logFYBO32ConfirmRecord = logFYBO32ConfirmRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductIdsStr())) {
                        ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                        List<String> productIds = JSON.parseArray(n.getProductIdsStr(), String.class);
                        if (CollectionUtil.isNotEmpty(productIds)) {
                            n.setProductIds(productIds);
                            List<String> names = productUtils.getProductNamesByIds(productIds);
                            n.setProductNames(StringUtils.join(names, ","));
                        }
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYBO32ConfirmRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBSendMailRecord> getLogFYBSendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBSendMailRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBSendMailRecord> logFYBSendMailRecord = logFYBMapper.getLogFYBSendMailRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBSendMailRecord)) {
                for (LogFYBSendMailRecord record : logFYBSendMailRecord) {
                    String sendStatus = record.getSendStatus();
                    if (StringUtils.isNotBlank(sendStatus)) {
                        if ("SUCCESS".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.SUCCESS.name());
                        } else if ("FAILED".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.FAILED.name());
                        }
                    }
                }
            }
            return new PageInfo<>(logFYBSendMailRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBSyncPayStatusRecord> getLogFYBSyncPayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBSyncPayStatusRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBSyncPayStatusRecord> logFYBSyncPayStatusRecord = logFYBMapper.getLogFYBSyncPayStatusRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBSyncPayStatusRecord)) {
                ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                logFYBSyncPayStatusRecord = logFYBSyncPayStatusRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductInfosStr())) {
                        List<JSONObject> list = JSON.parseArray(n.getProductInfosStr(), JSONObject.class);
                        if (CollectionUtil.isNotEmpty(list)) {
                            for (JSONObject obj : list) {
                                String id = obj.getString("id");
                                if (StringUtils.isNotBlank(id)) {
                                    List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(id));
                                    if (CollectionUtil.isNotEmpty(names)) {
                                        obj.put("productName", names.get(0));
                                    } else {
                                        obj.put("productName", "");
                                    }
                                }
                            }
                        }
                        n.setProductInfos(list);
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYBSyncPayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBUpdatePayStatusRecord> getLogFYBUpdatePayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBUpdatePayStatusRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBUpdatePayStatusRecord> logFYBUpdatePayStatusRecord = logFYBMapper.getLogFYBUpdatePayStatusRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBUpdatePayStatusRecord)) {
                ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                for (LogFYBUpdatePayStatusRecord statusRecord : logFYBUpdatePayStatusRecord) {
                    String productId = statusRecord.getProductId();
                    if (StringUtils.isNotBlank(productId)) {
                        List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                        if (CollectionUtil.isNotEmpty(names)) {
                            statusRecord.setProductName(names.get(0));
                        }
                    }
                }
            }
            return new PageInfo<>(logFYBUpdatePayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYBRPARecord> getLogFYBRPARecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYBRPARecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYBRPARecord> logFYBRPARecord = logFYBMapper.getLogFYBRPARecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYBRPARecord)) {
                for (LogFYBRPARecord record : logFYBRPARecord) {
                    String status = record.getStatus();
                    if (StringUtils.isNotBlank(status)) {
                        if ("COMPLETE".equals(status)) {
                            record.setRpaResult("执行完成");
                        } else if ("FAILED".equals(status)) {
                            record.setRpaResult("执行失败");
                        } else {
                            record.setRpaResult("执行中");
                        }
                    }
                }
            }
            return new PageInfo<>(logFYBRPARecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public Boolean reExecuteRpa(String rpaLogId) {
        LogFYBRPARecord logFYBRPARecord = logFYBMapper.getFYBRpaLogById(rpaLogId);
        if (logFYBRPARecord != null) {
            String rpaParam = logFYBRPARecord.getRpaParam();
            if (StringUtils.isNotBlank(rpaParam)) {
                JSONObject rpaParamJson = JSONObject.parseObject(rpaParam);
                if (rpaParamJson != null && rpaParamJson.containsKey("flowExtendParams") && rpaParamJson.containsKey("flow")) {
                    JSONObject extendParams = rpaParamJson.getJSONObject("flowExtendParams");
                    FlowList flow = rpaParamJson.getObject("flow", FlowList.class);
                    if (flow != null) {
                        String beginDate = extendParams.getString(RPA_START_DATE_NAME);
                        String endDate = extendParams.getString(RPA_END_DATE_NAME);
                        CronService cronService = SpringUtil.getBean(CronService.class);
                        if (flow.getShowName().contains("上清")) {
                            List<String> fySQRpaJobIds = cronService.getJobIdByClass(SQPaymentReceiptDownloadRpaJob.class);
                            if (CollectionUtil.isNotEmpty(fySQRpaJobIds)) {
                                JobDataMap sqJobDataMap = new JobDataMap();
                                // 上清 执行季度的最后一个月 月初 月末 2025-03-01 2025-03-31
                                sqJobDataMap.put("beginDate", beginDate);
                                sqJobDataMap.put("endDate", endDate);
                                sqJobDataMap.put("username", SecureUtil.currentUser().getAccount());
                                cronService.startJobNow(fySQRpaJobIds, sqJobDataMap);
                            }
                        } else if (flow.getShowName().contains("中债")) {
                            List<String> fyZZRpaJobIds = cronService.getJobIdByClass(ZZPaymentReceiptDownloadRpaJob.class);
                            if (CollectionUtil.isNotEmpty(fyZZRpaJobIds)) {
                                JobDataMap zzJobDataMap = new JobDataMap();
                                // 中债 执行季度的第一个月 最后一个月  2025-1  2025-3
                                zzJobDataMap.put("username", SecureUtil.currentUser().getAccount());
                                zzJobDataMap.put("beginDate", beginDate);
                                zzJobDataMap.put("endDate", endDate);
                                cronService.startJobNow(fyZZRpaJobIds, zzJobDataMap);
                            }
                        }
                    }
                    return true;
                }
            }
        }
        return false;
    }
}
