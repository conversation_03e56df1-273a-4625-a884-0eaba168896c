package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.mapper.LogFYIMapper;
import cn.sdata.om.al.service.LogFYIService;
import cn.sdata.om.al.utils.ProductUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LogFYIServiceImpl implements LogFYIService {

    private LogFYIMapper logFYIMapper;

    @Autowired
    public void setLogFYIMapper(LogFYIMapper logFYIMapper) {
        this.logFYIMapper = logFYIMapper;
    }

    @Override
    public PageInfo<LogFYIGenerateFileRecord> getLogFYIGenerateFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIGenerateFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIGenerateFileRecord> logFYIGenerateFileRecord = logFYIMapper.getLogFYIGenerateFileRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYIGenerateFileRecord)) {
                logFYIGenerateFileRecord = logFYIGenerateFileRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductIdsStr())) {
                        ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                        List<String> productIds = JSON.parseArray(n.getProductIdsStr(), String.class);
                        if (CollectionUtil.isNotEmpty(productIds)) {
                            List<String> names = productUtils.getProductNamesByIds(productIds);
                            if (CollectionUtil.isNotEmpty(names)) {
                                n.setProductNames(StringUtils.join(names, ","));
                            }
                        }
                        n.setProductIds(productIds);
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYIGenerateFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYIDownloadFileRecord> getLogFYIDownloadFileRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIDownloadFileRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIDownloadFileRecord> logFYIDownloadFileRecord = logFYIMapper.getLogFYIDownloadFileRecord(beginDataDate, endDataDate);
            return new PageInfo<>(logFYIDownloadFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYIImportO32Record> getLogFYIImportO32Record(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIImportO32Record> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIImportO32Record> logFYIImportO32Record = logFYIMapper.getLogFYIImportO32Record(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYIImportO32Record)) {
                ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                for (LogFYIImportO32Record o32Record : logFYIImportO32Record) {
                    String importResult = o32Record.getImportResult();
                    if (StringUtils.isNotBlank(importResult)) {
                        List<JSONObject> jsonObjectList = JSON.parseArray(importResult, JSONObject.class);
                        List<JSONObject> res = new ArrayList<>();
                        for (JSONObject jsonObject : jsonObjectList) {
                            JSONObject subRes = new JSONObject();
                            String productId = jsonObject.getString("productId");
                            if (StringUtils.isNotBlank(productId)) {
                                subRes.put("productId", productId);
                                List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                                if (CollectionUtil.isNotEmpty(names)) {
                                    subRes.put("productNames", names.get(0));
                                } else {
                                    subRes.put("productNames", "");
                                }
                            }
                            String importO32Status = jsonObject.getString("importO32Status");
                            if (StringUtils.isNotBlank(importO32Status)) {
                                subRes.put("importO32Status", importO32Status);
                            } else {
                                subRes.put("importO32Status", "");
                            }
                            String o32Message = jsonObject.getString("o32Message");
                            if (StringUtils.isNotBlank(o32Message)) {
                                subRes.put("o32Message", o32Message);
                            } else {
                                subRes.put("o32Message", "");
                            }
                            res.add(subRes);
                        }
                        o32Record.setImportResultList(res);
                    }
                }
            }
            return new PageInfo<>(logFYIImportO32Record);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYIO32ConfirmRecord> getLogFYIO32ConfirmRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIO32ConfirmRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIO32ConfirmRecord> logFYIO32ConfirmRecord = logFYIMapper.getLogFYIO32ConfirmRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYIO32ConfirmRecord)) {
                logFYIO32ConfirmRecord = logFYIO32ConfirmRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductIdsStr())) {
                        ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                        List<String> productIds = JSON.parseArray(n.getProductIdsStr(), String.class);
                        if (CollectionUtil.isNotEmpty(productIds)) {
                            List<String> names = productUtils.getProductNamesByIds(productIds);
                            if (CollectionUtil.isNotEmpty(names)) {
                                n.setProductNames(StringUtils.join(names, ","));
                            }
                        }
                        n.setProductIds(productIds);
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYIO32ConfirmRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYISendMailRecord> getLogFYISendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYISendMailRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYISendMailRecord> logFYISendMailRecord = logFYIMapper.getLogFYISendMailRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYISendMailRecord)) {
                for (LogFYISendMailRecord record : logFYISendMailRecord) {
                    String sendStatus = record.getSendStatus();
                    if (StringUtils.isNotBlank(sendStatus)) {
                        if ("SUCCESS".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.SUCCESS.name());
                        } else if ("FAILED".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.FAILED.name());
                        }
                    }
                }
            }
            return new PageInfo<>(logFYISendMailRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYISyncPayStatusRecord> getLogFYISyncPayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYISyncPayStatusRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYISyncPayStatusRecord> logFYISyncPayStatusRecord = logFYIMapper.getLogFYISyncPayStatusRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYISyncPayStatusRecord)) {
                logFYISyncPayStatusRecord = logFYISyncPayStatusRecord.stream().peek(n -> {
                    if (StringUtils.isNotBlank(n.getProductInfosStr())) {
                        List<JSONObject> list = JSON.parseArray(n.getProductInfosStr(), JSONObject.class);
                        if (CollectionUtil.isNotEmpty(list)) {
                            ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                            for (JSONObject jsonObject : list) {
                                String productId = jsonObject.getString("id");
                                List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                                if (CollectionUtil.isNotEmpty(names)) {
                                    jsonObject.put("productName", names.get(0));
                                } else {
                                    jsonObject.put("productName", "");
                                }
                            }
                        }
                        n.setProductInfos(list);
                    }
                }).collect(Collectors.toList());
            }
            return new PageInfo<>(logFYISyncPayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYIUpdatePayStatusRecord> getLogFYIUpdatePayStatusRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIUpdatePayStatusRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIUpdatePayStatusRecord> logFYIUpdatePayStatusRecord = logFYIMapper.getLogFYIUpdatePayStatusRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logFYIUpdatePayStatusRecord)) {
                ProductUtils productUtils = SpringUtil.getBean(ProductUtils.class);
                for (LogFYIUpdatePayStatusRecord payStatusRecord : logFYIUpdatePayStatusRecord) {
                    String productId = payStatusRecord.getProductId();
                    if (StringUtils.isNotBlank(productId)) {
                        List<String> names = productUtils.getProductNamesByIds(CollectionUtil.newArrayList(productId));
                        if (CollectionUtil.isNotEmpty(names)) {
                            payStatusRecord.setProductName(names.get(0));
                        }
                    }
                }
            }
            return new PageInfo<>(logFYIUpdatePayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogFYIUploadRecord> getLogFYIUploadRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogFYIUploadRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogFYIUploadRecord> logFYIUploadRecords = logFYIMapper.getLogFYIUploadRecord(beginDataDate, endDataDate);
            return new PageInfo<>(logFYIUploadRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }
}
