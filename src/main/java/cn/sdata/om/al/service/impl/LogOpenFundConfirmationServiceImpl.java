package cn.sdata.om.al.service.impl;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.service.LogOpenFundConfirmationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 开基确认单日志服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class LogOpenFundConfirmationServiceImpl implements LogOpenFundConfirmationService {

    private final LogOpenFundConfirmationUploadMapper uploadMapper;
    private final LogOpenFundConfirmationDownloadMapper downloadMapper;
    private final LogOpenFundConfirmationMailMapper mailMapper;
    private final LogOpenFundConfirmationOcrMapper ocrMapper;
    private final LogOpenFundConfirmationDeleteMapper deleteMapper;

    @Override
    public void logUpload(String fileName, String filePath, String fileId, String confirmationId, String operator, OperationStatus status) {
        try {
            LogOpenFundConfirmationUpload log = new LogOpenFundConfirmationUpload();
            log.setFileName(fileName);
            log.setFilePath(filePath);
            log.setFileId(fileId);
            log.setConfirmationId(confirmationId);
            log.setUploadTime(new Date());
            log.setOperator(operator);
            log.setOperationStatus(status);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            uploadMapper.insert(log);
        } catch (Exception e) {
            log.error("记录上传日志失败", e);
        }
    }

    @Override
    public void logDownload(String fileName, String filePath, String fileId, String confirmationId, String operator, OperationStatus status) {
        try {
            LogOpenFundConfirmationDownload log = new LogOpenFundConfirmationDownload();
            log.setFileName(fileName);
            log.setFilePath(filePath);
            log.setFileId(fileId);
            log.setConfirmationId(confirmationId);
            log.setDownloadTime(new Date());
            log.setOperator(operator);
            log.setOperationStatus(status);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            downloadMapper.insert(log);
        } catch (Exception e) {
            log.error("记录下载日志失败", e);
        }
    }

    @Override
    public void logMailSend(String confirmationId, String taskName, SendMethod sendMethod, String mailStatus, String operator, String mailLogId) {
        try {
            LogOpenFundConfirmationMail log = new LogOpenFundConfirmationMail();
            log.setConfirmationId(confirmationId);
            log.setTaskName(taskName);
            log.setSendMethod(sendMethod);
            // 直接使用MailStatus.valueOf()转换，因为传入的就是枚举名称
            MailStatus status = MailStatus.valueOf(mailStatus.toUpperCase());
            log.setMailStatus(status);
            log.setSendTime(new Date());
            log.setOperator(operator);
            log.setMailLogId(mailLogId);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            mailMapper.insert(log);
        } catch (Exception e) {
            log.error("记录邮件发送日志失败", e);
        }
    }

    @Override
    public void logOcrConfirm(String confirmationId, String fileName, String accountSetName, String transactionChannel, String operator) {
        try {
            LogOpenFundConfirmationOcr log = new LogOpenFundConfirmationOcr();
            log.setConfirmationId(confirmationId);
            log.setFileName(fileName);
            log.setAccountSetName(accountSetName);
            log.setTransactionChannel(transactionChannel);
            log.setConfirmTime(new Date());
            log.setOperator(operator);
            log.setConfirmStatus("已确认");
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            ocrMapper.insert(log);
        } catch (Exception e) {
            log.error("记录OCR确认日志失败", e);
        }
    }

    @Override
    public void logDelete(String confirmationId, String fileName, String accountSetName, String transactionChannel, String operator) {
        try {
            LogOpenFundConfirmationDelete log = new LogOpenFundConfirmationDelete();
            log.setConfirmationId(confirmationId);
            log.setFileName(fileName);
            log.setAccountSetName(accountSetName);
            log.setTransactionChannel(transactionChannel);
            log.setDeleteTime(new Date());
            log.setOperator(operator);
            log.setDeleteStatus("删除记录");
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            deleteMapper.insert(log);
        } catch (Exception e) {
            log.error("记录删除日志失败", e);
        }
    }

    @Override
    public void deleteLogsByConfirmationId(String confirmationId) {
        try {
            // 删除上传日志
            LambdaQueryWrapper<LogOpenFundConfirmationUpload> uploadWrapper = new LambdaQueryWrapper<>();
            uploadWrapper.eq(LogOpenFundConfirmationUpload::getConfirmationId, confirmationId);
            uploadMapper.delete(uploadWrapper);

            // 删除下载日志
            LambdaQueryWrapper<LogOpenFundConfirmationDownload> downloadWrapper = new LambdaQueryWrapper<>();
            downloadWrapper.eq(LogOpenFundConfirmationDownload::getConfirmationId, confirmationId);
            downloadMapper.delete(downloadWrapper);

            // 删除邮件发送日志
            LambdaQueryWrapper<LogOpenFundConfirmationMail> mailWrapper = new LambdaQueryWrapper<>();
            mailWrapper.eq(LogOpenFundConfirmationMail::getConfirmationId, confirmationId);
            mailMapper.delete(mailWrapper);

            // 删除OCR确认日志
            LambdaQueryWrapper<LogOpenFundConfirmationOcr> ocrWrapper = new LambdaQueryWrapper<>();
            ocrWrapper.eq(LogOpenFundConfirmationOcr::getConfirmationId, confirmationId);
            ocrMapper.delete(ocrWrapper);

            log.info("已删除确认单 {} 相关的所有操作日志（上传/下载/邮件/OCR确认）", confirmationId);
        } catch (Exception e) {
            log.error("删除确认单相关日志失败", e);
        }
    }

    @Override
    public Page<LogOpenFundConfirmationUpload> getUploadLogs(Page<LogOpenFundConfirmationUpload> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundConfirmationUpload> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundConfirmationUpload::getUploadTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundConfirmationUpload::getUploadTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundConfirmationUpload::getUploadTime);
        return uploadMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundConfirmationDownload> getDownloadLogs(Page<LogOpenFundConfirmationDownload> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundConfirmationDownload> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundConfirmationDownload::getDownloadTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundConfirmationDownload::getDownloadTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundConfirmationDownload::getDownloadTime);
        return downloadMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundConfirmationMail> getMailLogs(Page<LogOpenFundConfirmationMail> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundConfirmationMail> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundConfirmationMail::getSendTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundConfirmationMail::getSendTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundConfirmationMail::getSendTime);
        return mailMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundConfirmationOcr> getOcrLogs(Page<LogOpenFundConfirmationOcr> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundConfirmationOcr> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundConfirmationOcr::getConfirmTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundConfirmationOcr::getConfirmTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundConfirmationOcr::getConfirmTime);
        return ocrMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundConfirmationDelete> getDeleteLogs(Page<LogOpenFundConfirmationDelete> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundConfirmationDelete> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundConfirmationDelete::getDeleteTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundConfirmationDelete::getDeleteTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundConfirmationDelete::getDeleteTime);
        return deleteMapper.selectPage(page, wrapper);
    }
}
