package cn.sdata.om.al.service.impl;

import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OperationStatus;
import cn.sdata.om.al.enums.SendMethod;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.service.LogOpenFundReconciliationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 开基对账单日志服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class LogOpenFundReconciliationServiceImpl implements LogOpenFundReconciliationService {

    private final LogOpenFundReconciliationUploadMapper uploadMapper;
    private final LogOpenFundReconciliationDownloadMapper downloadMapper;
    private final LogOpenFundReconciliationMailMapper mailMapper;
    private final LogOpenFundReconciliationOcrMapper ocrMapper;
    private final LogOpenFundReconciliationDeleteMapper deleteMapper;

    @Override
    public void logUpload(String fileName, String filePath, String fileId, String reconciliationId, String operator, OperationStatus status) {
        try {
            LogOpenFundReconciliationUpload log = new LogOpenFundReconciliationUpload();
            log.setFileName(fileName);
            log.setFilePath(filePath);
            log.setFileId(fileId);
            log.setReconciliationId(reconciliationId);
            log.setUploadTime(new Date());
            log.setOperator(operator);
            log.setOperationStatus(status);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            uploadMapper.insert(log);
        } catch (Exception e) {
            log.error("记录上传日志失败", e);
        }
    }

    @Override
    public void logDownload(String fileName, String filePath, String fileId, String reconciliationId, String operator, OperationStatus status) {
        try {
            LogOpenFundReconciliationDownload log = new LogOpenFundReconciliationDownload();
            log.setFileName(fileName);
            log.setFilePath(filePath);
            log.setFileId(fileId);
            log.setReconciliationId(reconciliationId);
            log.setDownloadTime(new Date());
            log.setOperator(operator);
            log.setOperationStatus(status);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            downloadMapper.insert(log);
        } catch (Exception e) {
            log.error("记录下载日志失败", e);
        }
    }

    @Override
    public void logMailSend(String reconciliationId, String taskName, SendMethod sendMethod, String mailStatus, String operator, String mailLogId) {
        try {
            LogOpenFundReconciliationMail log = new LogOpenFundReconciliationMail();
            log.setReconciliationId(reconciliationId);
            log.setTaskName(taskName);
            log.setSendMethod(sendMethod);
            // 直接使用MailStatus.valueOf()转换，因为传入的就是枚举名称
            MailStatus status = MailStatus.valueOf(mailStatus.toUpperCase());
            log.setMailStatus(status);
            log.setSendTime(new Date());
            log.setOperator(operator);
            log.setMailLogId(mailLogId);
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            mailMapper.insert(log);
        } catch (Exception e) {
            log.error("记录邮件发送日志失败", e);
        }
    }

    @Override
    public void logOcrConfirm(String reconciliationId, String fileName, String accountSetName, String transactionChannel, String operator) {
        try {
            LogOpenFundReconciliationOcr log = new LogOpenFundReconciliationOcr();
            log.setReconciliationId(reconciliationId);
            log.setFileName(fileName);
            log.setAccountSetName(accountSetName);
            log.setTransactionChannel(transactionChannel);
            log.setConfirmTime(new Date());
            log.setOperator(operator);
            log.setConfirmStatus("已确认");
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            ocrMapper.insert(log);
        } catch (Exception e) {
            log.error("记录OCR确认日志失败", e);
        }
    }

    @Override
    public void logDelete(String reconciliationId, String fileName, String accountSetName, String transactionChannel, String operator) {
        try {
            LogOpenFundReconciliationDelete log = new LogOpenFundReconciliationDelete();
            log.setReconciliationId(reconciliationId);
            log.setFileName(fileName);
            log.setAccountSetName(accountSetName);
            log.setTransactionChannel(transactionChannel);
            log.setDeleteTime(new Date());
            log.setOperator(operator);
            log.setDeleteStatus("删除记录");
            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());
            deleteMapper.insert(log);
        } catch (Exception e) {
            log.error("记录删除日志失败", e);
        }
    }

    @Override
    public void deleteLogsByReconciliationId(String reconciliationId) {
        try {
            // 删除上传日志
            LambdaQueryWrapper<LogOpenFundReconciliationUpload> uploadWrapper = new LambdaQueryWrapper<>();
            uploadWrapper.eq(LogOpenFundReconciliationUpload::getReconciliationId, reconciliationId);
            uploadMapper.delete(uploadWrapper);

            // 删除下载日志
            LambdaQueryWrapper<LogOpenFundReconciliationDownload> downloadWrapper = new LambdaQueryWrapper<>();
            downloadWrapper.eq(LogOpenFundReconciliationDownload::getReconciliationId, reconciliationId);
            downloadMapper.delete(downloadWrapper);

            // 删除邮件发送日志
            LambdaQueryWrapper<LogOpenFundReconciliationMail> mailWrapper = new LambdaQueryWrapper<>();
            mailWrapper.eq(LogOpenFundReconciliationMail::getReconciliationId, reconciliationId);
            mailMapper.delete(mailWrapper);

            // 删除OCR确认日志
            LambdaQueryWrapper<LogOpenFundReconciliationOcr> ocrWrapper = new LambdaQueryWrapper<>();
            ocrWrapper.eq(LogOpenFundReconciliationOcr::getReconciliationId, reconciliationId);
            ocrMapper.delete(ocrWrapper);

            // 删除删除记录日志
            LambdaQueryWrapper<LogOpenFundReconciliationDelete> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(LogOpenFundReconciliationDelete::getReconciliationId, reconciliationId);
            deleteMapper.delete(deleteWrapper);
        } catch (Exception e) {
            log.error("删除对账单相关日志失败", e);
        }
    }

    @Override
    public Page<LogOpenFundReconciliationUpload> getUploadLogs(Page<LogOpenFundReconciliationUpload> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundReconciliationUpload> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundReconciliationUpload::getUploadTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundReconciliationUpload::getUploadTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundReconciliationUpload::getUploadTime);
        return uploadMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundReconciliationDownload> getDownloadLogs(Page<LogOpenFundReconciliationDownload> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundReconciliationDownload> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundReconciliationDownload::getDownloadTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundReconciliationDownload::getDownloadTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundReconciliationDownload::getDownloadTime);
        return downloadMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundReconciliationMail> getMailLogs(Page<LogOpenFundReconciliationMail> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundReconciliationMail> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundReconciliationMail::getSendTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundReconciliationMail::getSendTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundReconciliationMail::getSendTime);
        return mailMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundReconciliationOcr> getOcrLogs(Page<LogOpenFundReconciliationOcr> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundReconciliationOcr> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundReconciliationOcr::getConfirmTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundReconciliationOcr::getConfirmTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundReconciliationOcr::getConfirmTime);
        return ocrMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<LogOpenFundReconciliationDelete> getDeleteLogs(Page<LogOpenFundReconciliationDelete> page, Date startDate, Date endDate) {
        LambdaQueryWrapper<LogOpenFundReconciliationDelete> wrapper = new LambdaQueryWrapper<>();
        if (startDate != null) {
            wrapper.ge(LogOpenFundReconciliationDelete::getDeleteTime, startDate);
        }
        if (endDate != null) {
            wrapper.le(LogOpenFundReconciliationDelete::getDeleteTime, endDate);
        }
        wrapper.orderByDesc(LogOpenFundReconciliationDelete::getDeleteTime);
        return deleteMapper.selectPage(page, wrapper);
    }
}
