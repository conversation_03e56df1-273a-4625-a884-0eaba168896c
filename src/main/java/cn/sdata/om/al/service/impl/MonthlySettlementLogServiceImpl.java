package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.controller.MonthlySettlementController;
import cn.sdata.om.al.entity.LogMSExportRecord;
import cn.sdata.om.al.entity.LogMSFileOptRecord;
import cn.sdata.om.al.entity.LogMSRPARecord;
import cn.sdata.om.al.entity.LogMSSendMailRecord;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.mapper.MonthlySettlementLogMapper;
import cn.sdata.om.al.service.MonthlySettlementLogService;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class MonthlySettlementLogServiceImpl implements MonthlySettlementLogService {

    private MonthlySettlementLogMapper monthlySettlementLogMapper;

    @Autowired
    public void setMonthlySettlementLogMapper(MonthlySettlementLogMapper monthlySettlementLogMapper) {
        this.monthlySettlementLogMapper = monthlySettlementLogMapper;
    }

    @Override
    public PageInfo<LogMSExportRecord> getLogMSExportRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogMSExportRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogMSExportRecord> logMSExportRecords = monthlySettlementLogMapper.getLogMSExportRecord(beginDataDate, endDataDate);
            return new PageInfo<>(logMSExportRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogMSFileOptRecord> getLogMSFileOptRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogMSFileOptRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogMSFileOptRecord> logMSFileOptRecords = monthlySettlementLogMapper.getLogMSFileOptRecord(beginDataDate, endDataDate);
            return new PageInfo<>(logMSFileOptRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogMSRPARecord> getLogMSRPARecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogMSRPARecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogMSRPARecord> logMSRPARecords = monthlySettlementLogMapper.getLogMSRPARecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logMSRPARecords)) {
                for (LogMSRPARecord record : logMSRPARecords) {
                    String status = record.getStatus();
                    if (StringUtils.isNotBlank(status)) {
                        if ("COMPLETE".equals(status)) {
                            record.setRpaResult("执行完成");
                        } else if ("FAILED".equals(status)) {
                            record.setRpaResult("执行失败");
                        } else {
                            record.setRpaResult("执行中");
                        }
                    }
                }
            }
            return new PageInfo<>(logMSRPARecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public PageInfo<LogMSSendMailRecord> getLogMSSendMailRecord(Integer pageNo, Integer pageSize, String beginDataDate, String endDataDate) {
        try (Page<LogMSSendMailRecord> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<LogMSSendMailRecord> logMSSendMailRecords = monthlySettlementLogMapper.getLogMSSendMailRecord(beginDataDate, endDataDate);
            if (CollectionUtil.isNotEmpty(logMSSendMailRecords)) {
                for (LogMSSendMailRecord record : logMSSendMailRecords) {
                    String sendStatus = record.getSendStatus();
                    if (StringUtils.isNotBlank(sendStatus)) {
                        if ("SUCCESS".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.SUCCESS.name());
                        } else if ("FAILED".equals(sendStatus)) {
                            record.setSendStatus(MailStatus.FAILED.name());
                        }
                    }
                }
            }
            return new PageInfo<>(logMSSendMailRecords);
        } catch (Exception e) {
            e.printStackTrace();
            return new PageInfo<>(new ArrayList<>());
        }
    }

    @Override
    public Boolean reExecuteRpa(String rpaLogId) {
        LogMSRPARecord logMSRPARecord = monthlySettlementLogMapper.getById(rpaLogId);
        if (logMSRPARecord != null) {
            String params = logMSRPARecord.getParams();
            if (StringUtils.isNotBlank(params)) {
                JSONObject jsonObject = new JSONObject();
                JSONObject rpaParams = JSONObject.parseObject(params);
                if (rpaParams != null && rpaParams.containsKey("dataDate") && rpaParams.containsKey("orders")) {
                    String dataDate = rpaParams.getString("dataDate");
                    List<String> orders = rpaParams.getList("orders", String.class);
                    jsonObject.put("date", dataDate);
                    jsonObject.put("orders", orders);
                    jsonObject.put("type", "MANUAL");
                    jsonObject.put("username", SecureUtil.currentUserName());
                    SpringUtil.getBean(MonthlySettlementController.class).generateFiles(jsonObject);
                    return true;
                }
            }
        }
        return false;
    }
}
