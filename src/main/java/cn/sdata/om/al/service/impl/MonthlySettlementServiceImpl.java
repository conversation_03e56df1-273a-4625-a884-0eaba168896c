package cn.sdata.om.al.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.ShareDownloadFile;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyExcelFileEnum;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.job.MonthlySettlementSendMailJob;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.MarketTradeDayService;
import cn.sdata.om.al.service.MonthlySettlementService;
import cn.sdata.om.al.service.RpaExecuteService;
import cn.sdata.om.al.service.SMBService;
import cn.sdata.om.al.utils.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.sdata.om.al.constant.BaseConstant.*;

@Service
@Slf4j
public class MonthlySettlementServiceImpl implements MonthlySettlementService {

    private MonthlySettlementMapper monthlySettlementMapper;

    private SMBService smbService;

    private ExcelDealCashFlowUtil excelDealCashFlowUtil;

    /**
     * 默认数据缓存
     */
    private static final List<MonthlySettlementList> monthlySettlementLists = new ArrayList<>();


    private OrdersAndFlowConfig ordersAndFlowConfig;

    @Autowired
    public void setOrdersAndFlowConfig(OrdersAndFlowConfig ordersAndFlowConfig) {
        this.ordersAndFlowConfig = ordersAndFlowConfig;
    }

    @Autowired
    public void setMonthlySettlementMapper(MonthlySettlementMapper monthlySettlementMapper) {
        this.monthlySettlementMapper = monthlySettlementMapper;
    }

    @Autowired
    public void setSmbService(SMBService smbService) {
        this.smbService = smbService;
    }

    @Autowired
    public void setExcelDealCashFlowUtil(ExcelDealCashFlowUtil excelDealCashFlowUtil) {
        this.excelDealCashFlowUtil = excelDealCashFlowUtil;
    }

    @Override
    public PageInfo<MonthlySettlementList> page(MonthlySettlementListQuery monthlySettlementListQuery) {
        int pageNo = monthlySettlementListQuery.getPageNo();
        int pageSize = monthlySettlementListQuery.getPageSize();
        String dataDate = monthlySettlementListQuery.getDataDate();
        // 先查询此月份有没有数据 若没有先新增 有直接查询
        int res = monthlySettlementMapper.selectCountByDate(dataDate);
        if (res == 0) {
            // 没数据 先新增后查询
            log.info("没有此{}月份的数据,需要新增", dataDate);
            generateDefaultData(dataDate);
            monthlySettlementMapper.batchSave(monthlySettlementLists);
        }
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<MonthlySettlementList> monthlySettlementLists = monthlySettlementMapper.page(monthlySettlementListQuery);
            if (CollectionUtil.isNotEmpty(monthlySettlementLists)) {
                for (MonthlySettlementList list : monthlySettlementLists) {
                    String replaceFileName = list.getReplaceFileName();
                    if (StringUtils.isNotBlank(replaceFileName)) {
                        list.setFileName(replaceFileName);
                    }
                }
            }
            return new PageInfo<>(monthlySettlementLists);
        }
    }

    @Override
    public List<MonthlySettlementList> selectMonthlySettlementByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return List.of();
        }
        return monthlySettlementMapper.selectMonthlySettlementByIds(ids);
    }

    @Override
    public String generateFiles(String date, List<String> orders) {
        executeGenerateFiles(date, orders);
        return "noStart";
    }

    /**
     * 执行生成文件
     *
     * @param date   月份
     * @param orders 文件夹序号
     */
    private void executeGenerateFiles(String date, List<String> orders) {
        // 从RPA根据日期从各个场景目录中下载文件
        /*List<String> flowIds = getFlowByOrders(orders);
        if (CollectionUtil.isEmpty(flowIds)) {
            BusinessException.throwException("流程id不存在");
        }
        String account = SecureUtil.currentUser().getAccount();
        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            // 先更新此文件目录中的所有文件为下载中
            monthlySettlementMapper.updateFileDownloadStatus(date, orders, "2");
            File shareDiskFile;
            log.info("读取配置文件中应该查询的流程id为:{}", flowIds);
            List<FlowList> flowLists = SpringUtil.getBean(FlowListMapper.class).selectBatchIds(flowIds);
            // 取 沪深 交易日
            MarketTradeDayService marketTradeDayService = SpringUtil.getBean(MarketTradeDayService.class);
            Set<Date> monthLastDay = marketTradeDayService.getMonthLastDay("00");
            if (CollectionUtil.isEmpty(flowLists)) {
                return;
            }
            try {
                for (FlowList flowList : flowLists) {
                    // 异步执行处理任务
                    // 1、执行RPA下载文件任务
                    synchronized (MonthlySettlementServiceImpl.class) {
                        executeRPATask(date, CollectionUtil.newArrayList(flowList), monthLastDay, account);
                    }
                    Integer id = flowList.getId();
                    String order;
                    Map<String, String> config = ordersAndFlowConfig.getConfig();
                    Map<String, String> reverse = MapUtil.reverse(config);
                    order = reverse.get(id + "");
                    // 2、从共享文件夹中下载文件 最后形成一个目录
                    shareDiskFile = executeShareDiskDownload(date, CollectionUtil.newArrayList(flowList.getBasePath()), flowList.getShowName(), order);
                    if (shareDiskFile != null && shareDiskFile.exists()) {
                        // 3、处理需要修改的文件 处理文件后依然返回处理后的目录地址(在原rpa下载的目录上进行修改)
                        // 4、创建目标文件夹并将文件拷贝到目标文件夹下 目标文件夹统一创建到配置的目标地址下 目录结构为 baseDir/date/业务文件夹/业务文件
                        String absolutePath = shareDiskFile.getAbsolutePath();
                        log.info("需要处理的文件夹为:{}", absolutePath);
                        // 处理文件
                        List<FolderFileInfo> files = traverseFile(absolutePath);
                        // 执行数据库更新操作 此月份匹配上的文件变更状态为 已下载、文件路径
                        if (CollectionUtil.isNotEmpty(files)) {
                            for (FolderFileInfo folderFileInfo : files) {
                                File file = folderFileInfo.getFile();
                                if (file != null && file.exists()) {
                                    String name = file.getName();
                                    String path = file.getAbsolutePath();
                                    log.info("文件名和路径为:{} - {}", name, path);
                                    String newName = handleFileName(name, String.valueOf(folderFileInfo.getFolderOrder()));
                                    log.info("要处理的原文件名 = {}, 处理后的文件名 = {}", name, newName);
                                    monthlySettlementMapper.updateFileInfo(date, FileUtil.getPrefix(newName), path, name);
                                }
                            }
                            // 把后续没有匹配上文件的修改为未下载
                            int folderOrder = files.get(0).getFolderOrder();
                            monthlySettlementMapper.updateNoMatchFileNameStatus(String.valueOf(folderOrder), date, "0");
                        } else {
                            // 说明没有需要下载的文件
                            for (Map.Entry<String, String> entry : config.entrySet()) {
                                String value = entry.getValue();
                                if (value.equals(String.valueOf(flowList.getId()))) {
                                    order = entry.getKey();
                                    break;
                                }
                            }
                            // rpa 没有下载回来文件 则改为未下载
                            monthlySettlementMapper.updateFileDownloadStatus(date, CollectionUtil.newArrayList(order), "0");
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });*/
    }

    /**
     * 处理文件名
     *
     * @param fileName 原文件名
     * @param order    顺序号
     * @return 处理后的文件名
     */
    private String handleFileName(String fileName, String order) {
        String regex;
        Pattern pattern;
        Matcher matcher;
        switch (order) {
            case "7":
            case "11":
            case "12":
                regex = "\\d{4}-\\d{2}-\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "yyyy-MM-dd");
                }
                return fileName;
            case "8":
                if (fileName.contains("duration model") || fileName.contains("可供出售国债公允价值变动")) {
                    regex = "\\d{6}";
                    pattern = Pattern.compile(regex);
                    matcher = pattern.matcher(fileName);
                    while (matcher.find()) {
                        String group = matcher.group();
                        fileName = fileName.replaceAll(group, "yyyyMM");
                    }
                } else if (fileName.contains("导出TA")) {
                    regex = "\\d{8}";
                    pattern = Pattern.compile(regex);
                    matcher = pattern.matcher(fileName);
                    while (matcher.find()) {
                        String group = matcher.group();
                        fileName = fileName.replaceAll(group, "yyyyMMdd");
                    }
                }
                return fileName;
            case "9":
                regex = "\\d{4}年\\d{2}月";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "yyyy年MM月");
                }
                return fileName;
            case "4":
                regex = "\\d{4}\\.\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "yyyy.MM");
                }
                return fileName;
            case "2":
                regex = ".*凭证浏览_\\s";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "");
                }
                fileName = fileName.replaceAll("\\(", "").replaceAll("\\)", "_");
                regex = "-\\d{4}-\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "");
                }
                return fileName;
            case "1":
            case "3":
            case "5":
            case "6":
            case "10":
            case "13":
                regex = "-\\d{4}-\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                while (matcher.find()) {
                    String group = matcher.group();
                    fileName = fileName.replaceAll(group, "");
                }
                return fileName;
        }
        return fileName;
    }

    /**
     * 根据顺序号获取流程信息
     *
     * @param orders 顺序号
     * @return 流程id
     */
    private List<String> getFlowByOrders(List<String> orders) {
        List<String> strings = new ArrayList<>();
        for (String order : orders) {
            Map<String, String> config = ordersAndFlowConfig.getConfig();
            if (MapUtil.isNotEmpty(config)) {
                String s = config.get(order);
                if (StringUtils.isNotBlank(s)) {
                    strings.add(s);
                }
            }
        }
        return strings;
    }

    /**
     * 执行rpa任务
     *
     * @param date 月份
     */
    private String executeRPATask(String date, List<FlowList> flowLists, Set<Date> monthLastDay, String account, String type) throws Exception {
        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
        RpaExecuteService rpaExecuteService = SpringUtil.getBean(RpaExecuteService.class);
        List<CommonEntity> list = SpringUtil.getBean(AccountSetMapper.class).list();
        CronMapper cronMapper = SpringUtil.getBean(CronMapper.class);
        LambdaQueryWrapper<Cron> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // lambdaQueryWrapper.eq(Cron::getStatus, 0);
        // 获取所有手动执行的任务
        List<Cron> manJobs = cronMapper.selectList(lambdaQueryWrapper);
        String logId = null;
        // 获取月结 rpa流程信息
        for (FlowList flowList : flowLists) {
            // 组装参数
            Tuple dateParam = getRpaParams(flowList.getShowName(), date, monthLastDay, list);
            Object o = dateParam.get(0);
            Object o1 = dateParam.get(1);
            log.info("传给rpa的参数是 - 开始时间:{}, 结束时间:{}", o, o1);
            String beginDateLog = String.valueOf(o);
            String endDateLog = String.valueOf(o1);
            logId = IdUtil.getSnowflakeNextIdStr();
            // 预写日志
            BaseCronLog baseCronLog = new BaseCronLog();
            baseCronLog.setId(logId);
            baseCronLog.setStartDateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            baseCronLog.setDataDate(DateUtil.today());
            baseCronLog.setStatus(JobStatus.RUNNING);
            if (CollectionUtil.isNotEmpty(manJobs)) {
                Optional<Cron> first = manJobs.stream().filter(n -> null != n.getFlowId()).filter(n -> n.getFlowId().equals(flowList.getId())).findFirst();
                if (first.isPresent()) {
                    baseCronLog.setTaskId(first.get().getJobId());
                } else {
                    baseCronLog.setTaskId(IdUtil.getSnowflakeNextIdStr());
                }
            } else {
                baseCronLog.setTaskId(IdUtil.getSnowflakeNextIdStr());
            }
            baseCronLog.setExecutor(account);
            baseCronLog.setExecuteMethod("AUTO".equals(type) ? "AUTO" : "MANUAL");
            String name = flowList.getName();
            log.info("流程全路径为:{}", name);
            baseCronLog.setDataDate(
                    DateUtil.format(DateUtil.parseDate(beginDateLog), "yyyy年MM月dd日")
                            + "-" +
                            DateUtil.format(DateUtil.parseDate(endDateLog), "yyyy年MM月dd日"));
            baseCronLogMapper.insert(baseCronLog);
            Map<String, Object> flowExtendParams = new HashMap<>();
            flowExtendParams.put(RPA_START_DATE_NAME, dateParam.get(0));
            flowExtendParams.put(RPA_END_DATE_NAME, dateParam.get(1));
            if (dateParam.size() == 3) {
                flowExtendParams.put(RPA_PRODUCT_IDS, dateParam.get(2));
            }
            try {
                // 开启流程
                RpaExecLog rpaExecLog = rpaExecuteService.executeRPA(flowList, flowExtendParams, logId);
                log.info("rpa执行日志为:{}", JSON.toJSONString(rpaExecLog));
                rpaExecuteService.startTimer(rpaExecLog, flowList, flowExtendParams, logId);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
            while (true) {
                BaseCronLog cronLog = baseCronLogMapper.selectById(logId);
                JobStatus status = cronLog.getStatus();
                JobStatus rpaStatus = cronLog.getRpaStatus();
                if (status == JobStatus.COMPLETE) {
                    log.info("此 {} RPA 任务已经完成", flowList.getShowName());
                    break;
                } else if (status == JobStatus.FAILED || rpaStatus == JobStatus.FAILED || rpaStatus == JobStatus.EXCEPTION
                        || rpaStatus == JobStatus.TIMEOUT || rpaStatus == JobStatus.STOP) {
                    log.info("此 {} RPA 任务异常结束", flowList.getShowName());
                    Map<String, String> config = ordersAndFlowConfig.getConfig();
                    Map<String, String> reverse = MapUtil.reverse(config);
                    String order = reverse.get(flowList.getId() + "");
                    monthlySettlementMapper.updateFileDownloadStatus(date, CollectionUtil.newArrayList(order), "0");
                    BusinessException.throwException("RPA 任务异常结束");
                    break;
                }
                TimeUnit.SECONDS.sleep(5);
                log.info("正在检查 {} RPA 任务状态", flowList.getShowName());
            }
        }
        return logId;
    }

    /**
     * 根据任务名确定调用rpa时的时间参数
     *
     * @param showName 任务名
     * @return 时间2元组
     */
    private Tuple getRpaParams(String showName, String date, Set<Date> monthLastDay, List<CommonEntity> list) {
        DateTime preMonth = DateUtil.parse(date + "-01");
        DateTime beginOfMonth;
        DateTime endOfMonth;
        String productIdsStr;
        switch (showName) {
            case "现金流量预测":
                // 属于 现金流量预测 传的时间为上个自然月
                beginOfMonth = DateUtil.beginOfMonth(preMonth);
                endOfMonth = DateUtil.endOfMonth(preMonth);
                return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"));
            case "质押券信息":
                // 属于 质押券信息 传的时间为上个月的最后一个自然日
                // String format = DateUtil.format(preMonth, "yyyy-MM");
                /*Optional<String> first = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(format)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (first.isPresent()) {
                    String s = first.get();
                    return new Tuple(s, s);
                }*/
                String dateStr = getMonthLastDay(preMonth);
                log.info("上个月的最后一个自然日:{}", dateStr);
                return new Tuple(dateStr, dateStr);
            case "UL申购赎回明细":
                // 需要多传账套编号 以 5 开头的代码
                beginOfMonth = DateUtil.beginOfMonth(preMonth);
                endOfMonth = DateUtil.endOfMonth(preMonth);
                List<String> collect = list.stream().map(CommonEntity::getId)
                        .filter(n -> n.startsWith("5"))
                        .distinct()
                        .collect(Collectors.toList());
                productIdsStr = String.join(",", collect);

                if (StringUtils.isNotBlank(productIdsStr)) {
                    return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"),
                            DateUtil.format(endOfMonth, "yyyy-MM-dd"), productIdsStr);
                }
                return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"),
                        DateUtil.format(endOfMonth, "yyyy-MM-dd"));
            case "回购流水":
            case "增值税计算表":
                // 上个自然月
                beginOfMonth = DateUtil.beginOfMonth(preMonth);
                endOfMonth = DateUtil.endOfMonth(preMonth);
                productIdsStr = getProductIdsStr(list);
                if (StringUtils.isNotBlank(productIdsStr)) {
                    return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"), productIdsStr);
                } else {
                    return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"));
                }
            case "财务凭证数据":
                // 上个自然月
                beginOfMonth = DateUtil.beginOfMonth(preMonth);
                endOfMonth = DateUtil.endOfMonth(preMonth);
                productIdsStr = SpringUtil.getProperty("monthly-settlement.financial_voucher_data");
                if (StringUtils.isBlank(productIdsStr)) {
                    productIdsStr = "1101,1304,2101,2102,2103,2104,2106,2107,2301,2302,2303,2304,2305,4102,4105,4110";
                }
                if (StringUtils.isNotBlank(productIdsStr)) {
                    return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"), productIdsStr);
                }
                return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"));
            case "财务凭证数据-投连":
                // 上上个月最后一个交易所交易日的下一个自然日到上个月的最后一个交易所交易日
                DateTime prePreMonth = DateUtil.offsetMonth(preMonth, -1);
                String prePreFormat = DateUtil.format(prePreMonth, "yyyy-MM");
                String beginDate = null;
                String endDate1 = null;
                Optional<String> beginDateFirst = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(prePreFormat)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (beginDateFirst.isPresent()) {
                    beginDate = beginDateFirst.get();
                    // 开始日期的下一个自然日
                    DateTime dateTime = DateUtil.parseDate(beginDate);
                    dateTime.offset(DateField.DAY_OF_WEEK, 1);
                    beginDate = DateUtil.format(dateTime, "yyyy-MM-dd");
                    log.info("上上个月最后一个交易所交易日的下一个自然日为:{}", beginDate);
                }
                String preMonthFormat = DateUtil.format(preMonth, "yyyy-MM");
                Optional<String> endDateFirst = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(preMonthFormat)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (endDateFirst.isPresent()) {
                    endDate1 = endDateFirst.get();
                    log.info("上个月的最后一个交易所交易日:{}", endDate1);
                }
                productIdsStr = "5001,5002,5003,5004,5005,5006,5007,5008,5010,5101,5102,5103,5104,5105,6001";
                if (StringUtils.isNotBlank(productIdsStr)) {
                    return new Tuple(beginDate, endDate1, productIdsStr);
                }
                return new Tuple(beginDate, endDate1);
            case "证券买卖盈亏表":
                // 上个自然月
                beginOfMonth = DateUtil.beginOfMonth(preMonth);
                endOfMonth = DateUtil.endOfMonth(preMonth);
                return new Tuple(DateUtil.format(beginOfMonth, "yyyy-MM-dd"), DateUtil.format(endOfMonth, "yyyy-MM-dd"));
            case "证券基本信息":
                // 当日
                return new Tuple(DateUtil.today(), DateUtil.today());
            case "证券投资基金估值表非投连I9":
                // 上个月的最后一个自然日
                /*String format1 = DateUtil.format(preMonth, "yyyy-MM");
                Optional<String> first1 = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(format1)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (first1.isPresent()) {
                    String s = first1.get();
                    return new Tuple(s, s);
                }*/
                String dateStr1 = getMonthLastDay(preMonth);
                log.info("上个月的最后一个自然日:{}", dateStr1);
                return new Tuple(dateStr1, dateStr1);
            case "非投连估值表":
                // 上个月的最后一个自然日
                /*String format2 = DateUtil.format(preMonth, "yyyy-MM");
                Optional<String> endDateFirst1 = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(format2)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (endDateFirst1.isPresent()) {
                    String endDate = endDateFirst1.get();
                    log.info("上个月的最后一个交易所交易日:{}", endDate);
                    return new Tuple(endDate, endDate);
                }*/
                String dateStr2 = getMonthLastDay(preMonth);
                log.info("上个月的最后一个自然日:{}", dateStr2);
                return new Tuple(dateStr2, dateStr2);
            case "持仓汇总":
                // 上个月的最后一个自然日
                /*String format3 = DateUtil.format(preMonth, "yyyy-MM");
                Optional<String> endDateFirst2 = monthLastDay.stream().filter(n -> DateUtil.format(n, "yyyy-MM").equals(format3)).filter(Objects::nonNull).map(n -> DateUtil.format(n, "yyyy-MM-dd")).findFirst();
                if (endDateFirst2.isPresent()) {
                    String endDate = endDateFirst2.get();
                    log.info("上个月的最后一个交易所交易日:{}", endDate);
                    return new Tuple(endDate, endDate);
                }*/
                String dateStr3 = getMonthLastDay(preMonth);
                log.info("上个月的最后一个自然日:{}", dateStr3);
                return new Tuple(dateStr3, dateStr3);
        }
        return new Tuple();
    }


    /**
     * 获取上个月的最后一个自然日
     * @param preMonth  dateTime对象
     * @return 上个月的最后一个自然日
     */
    private String getMonthLastDay(DateTime preMonth) {
        Calendar calendar = preMonth.toCalendar();
        // 月份减 1
        // 设为该月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date finalDate = calendar.getTime();
        return DateUtil.format(finalDate, "yyyy-MM-dd");
    }

    private String getProductIdsStr(List<CommonEntity> list) {
        String productIdsStr = null;
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> collect = list.stream().map(CommonEntity::getId)
                    .filter(n -> n.startsWith("1") || n.startsWith("2") || n.startsWith("4") || n.startsWith("5"))
                    .distinct()
                    .collect(Collectors.toList());
            productIdsStr = String.join(",", collect);
        }
        return productIdsStr;
    }

    @Data
    private static class FolderFileInfo {

        /**
         * 文件夹排序
         */
        private int folderOrder;

        /**
         * 文件夹名称
         */
        private String folderName;

        /**
         * 文件夹分类
         */
        private String folderCategory;

        /**
         * 文件对象
         */
        private File file;
    }

    /**
     * 从共享文件夹中下载文件到本地
     *
     * @param date 月份
     *             isTL 指的是 下载为 财务浏览数据的投连 需要进行文件匹配
     * @return 文件夹对象
     */
    private File executeShareDiskDownload(String date, List<String> basePaths,
                                          String folderName, String order, Date executeDate, boolean isTL) throws Exception {
        // 从共享文件夹中下载文件到本地
        String baseDir = SpringUtil.getProperty("file.monthly-settlement-dir");
        // 创建每月的总文件夹
        // 注意此时创建本地文件夹时 应该创建为上月
        String preMonthDate = DateUtil.format(DateUtil.parseDate(date + "-01"), "yyyy-MM");
        // 每次在创建文件夹时都要先删除根据最新任务进行重新加载文件
        String parentPath = baseDir + File.separator + preMonthDate + File.separator + order + "." + folderName;
        if (!isTL) {
            boolean delRes = FileUtil.del(parentPath);
            if (delRes) {
                log.info("删除 {} 文件夹成功", parentPath);
            } else {
                log.info("删除 {} 文件夹失败", parentPath);
            }
        }
        File res = FileUtil.mkdir(parentPath);
        for (String basePath : basePaths) {
            String downloadPath;
            // 如果配置中存在特殊标识 则需要替换
            if (basePath.matches(".*\\$\\{.*}.*")) {
                PropertyPlaceholderHelper propertyPlaceholderHelper = new PropertyPlaceholderHelper("${", "}");
                Properties properties = new Properties();
                properties.put("yyyyMMdd", DateUtil.format(executeDate, "yyyyMMdd"));
                downloadPath = propertyPlaceholderHelper.replacePlaceholders(basePath, properties);
            } else {
                downloadPath = basePath + File.separator + DateUtil.format(executeDate, "yyyyMMdd");
            }
            log.info("需要从共享文件夹下载的文件根路径为:{}", downloadPath);
            try {
                List<String> filePaths = smbService.listDir(downloadPath);
                log.info("共享文件夹中的文件路径为:{}", JSON.toJSONString(filePaths));
                if (CollectionUtil.isNotEmpty(filePaths)) {
                    for (String filePath : filePaths) {
                        filePath = filePath.substring(filePath.indexOf(SpringUtil.getProperty("smb.base")) + SpringUtil.getProperty("smb.base").length());
                        log.info("需要从远程文件夹下载的路径是:{}", filePath);
                        ShareDownloadFile shareDownloadFile = smbService.downloadReturnFile(filePath);
                        if (shareDownloadFile != null) {
                            String fileName = shareDownloadFile.getFileName();
                            fileName = fileName.replaceAll("（", "(").replaceAll("）", ")");
                            if (isTL) {
                                log.info("处理凭证浏览 - 投连");
                                String productIdsStr = "5001,5002,5003,5004,5005,5006,5007,5008,5010,5101,5102,5103,5104,5105,6001";
                                List<String> list = CollectionUtil.toList(productIdsStr.split(","));
                                for (String pId : list) {
                                    log.info("pId = {}, fileName = {}", pId, fileName);
                                    if (fileName.contains(pId)) {
                                        log.info("---匹配前的文件名为:{}", fileName);
                                        // 注意 远程文件夹会放置所有带有日期的文件 这里要根据数据日期进行匹配
                                        boolean matchRes = findFile(preMonthDate, FileUtil.getPrefix(fileName), order, FileUtil.extName(fileName));
                                        if (matchRes) {
                                            log.info("---匹配时间的文件名为:{}", fileName);
                                            String newName = handleFileName(FileUtil.getPrefix(fileName), order);
                                            int count = monthlySettlementMapper.countMatchFileName(date, newName);
                                            if (count > 0) {
                                                log.info("---已匹配模板文件名称,需要上传");
                                                File subFile = new File(res, File.separator + ("2".equals(order) ? newName + "." + FileUtil.extName(fileName) : fileName));
                                                log.info("子文件夹路径为:{}", subFile.getAbsolutePath());
                                                FileUtil.copyFile(shareDownloadFile.getInputStream(), subFile, StandardCopyOption.REPLACE_EXISTING);
                                            }
                                        }
                                        break;
                                    }
                                }
                            } else {
                                log.info("---匹配前的文件名为:{}", fileName);
                                // 注意 远程文件夹会放置所有带有日期的文件 这里要根据数据日期进行匹配
                                boolean matchRes = findFile(preMonthDate, FileUtil.getPrefix(fileName), order, FileUtil.extName(fileName));
                                if (matchRes) {
                                    log.info("---匹配时间的文件名为:{}", fileName);
                                    String newName = handleFileName(FileUtil.getPrefix(fileName), order);
                                    int count = monthlySettlementMapper.countMatchFileName(date, newName);
                                    if (count > 0) {
                                        log.info("---已匹配模板文件名称,需要上传");
                                        File subFile = new File(res, File.separator + ("2".equals(order) ? newName + "." + FileUtil.extName(fileName) : fileName));
                                        log.info("子文件夹路径为:{}", subFile.getAbsolutePath());
                                        FileUtil.copyFile(shareDownloadFile.getInputStream(), subFile, StandardCopyOption.REPLACE_EXISTING);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return res;
    }


    private boolean findFile(String date, String fileName, String order, String extName) {
        String regex;
        Pattern pattern;
        Matcher matcher;
        switch (order) {
            case "7":
            case "11":
            case "12":
                regex = "\\d{4}-\\d{2}-\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                if (matcher.find()) {
                    String group = matcher.group();
                    return group.contains(date);
                }
                return false;
            case "8":
                if (fileName.contains("duration model") || fileName.contains("可供出售国债公允价值变动")) {
                    regex = "\\d{6}";
                    pattern = Pattern.compile(regex);
                    matcher = pattern.matcher(fileName);
                    if (matcher.find()) {
                        String group = matcher.group();
                        return group.contains(date.replaceAll("-", ""));
                    }
                } else if (fileName.contains("导出TA")) {
                    regex = "\\d{8}";
                    pattern = Pattern.compile(regex);
                    matcher = pattern.matcher(fileName);
                    if (matcher.find()) {
                        String group = matcher.group();
                        return group.contains(date.replaceAll("-", ""));
                    }
                }
            case "9":
                regex = "\\d{4}年\\d{2}月";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                if (matcher.find()) {
                    String group = matcher.group();
                    return group.contains(date.replaceAll("-", "年").concat("月"));
                }
            case "4":
                regex = "\\d{4}\\.\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                if (matcher.find()) {
                    String group = matcher.group();
                    return group.contains(date.replaceAll("-", "."));
                }
            case "1":
            case "2":
            case "5":
            case "6":
            case "10":
            case "13":
                regex = "-\\d{4}-\\d{2}";
                pattern = Pattern.compile(regex);
                matcher = pattern.matcher(fileName);
                if (matcher.find()) {
                    String group = matcher.group();
                    return group.contains(date);
                }
            case "3":
                return "xlsx".equals(extName);
        }
        return true;
    }

    /**
     * 遍历本地的月结相关文件夹(即下载到本地的整个文件夹)
     *
     * @param folderPath
     */
    private List<FolderFileInfo> traverseFile(String folderPath) {
        List<FolderFileInfo> folderFileInfos = new ArrayList<>();
        Path startDir = Paths.get(folderPath);
        if (!Files.exists(startDir) || !Files.isDirectory(startDir)) {
            return List.of();
        }
        try {
            Files.walkFileTree(startDir, new SimpleFileVisitor<>() {
                @Override
                public FileVisitResult preVisitDirectory(Path path, BasicFileAttributes basicFileAttributes) throws IOException {
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFile(Path path, BasicFileAttributes basicFileAttributes) throws IOException {
                    // 生成文件信息
                    FolderFileInfo folderFileInfo = generateFolderFileInfo(path.toFile());
                    if (folderFileInfo != null) {
                        folderFileInfos.add(folderFileInfo);
                    }
                    //处理文件
                    excelContentHandleLogic(path.toAbsolutePath().normalize().toString());
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (Exception e) {
            log.error("traverseFile_error:{}", e);
        }
        return folderFileInfos;
    }


    private FolderFileInfo generateFolderFileInfo(File file) {
        FolderFileInfo folderFileInfo = new FolderFileInfo();
        // 根据父文件夹进行分类
        if (file != null) {
            folderFileInfo.setFile(file);
            String name = file.getParentFile().getName();
            folderFileInfo.setFolderName(name);
            Tuple tuple = getFolderOrderAndCategory(name);
            if (tuple != null) {
                folderFileInfo.setFolderOrder(tuple.get(0));
                folderFileInfo.setFolderCategory(tuple.get(1));
                return folderFileInfo;
            }
        }
        return null;
    }


    private Tuple getFolderOrderAndCategory(String name) {
        if (name.contains("UL申购赎回明细")) {
            return new Tuple(1, "I39");
        } else if (name.contains("财务凭证数据")) {
            return new Tuple(2, "I39");
        } else if (name.contains("证券基本信息")) {
            return new Tuple(3, "I39");
        } else if (name.contains("股票行业分类")) {
            return new Tuple(4, "I39");
        } else if (name.contains("回购流水")) {
            return new Tuple(5, "I39");
        } else if (name.contains("增值税计算表")) {
            return new Tuple(6, "I39");
        } else if (name.contains("非投连估值表")) {
            return new Tuple(7, "I39");
        } else if (name.contains("可供出售&duration model")) {
            return new Tuple(8, "I39");
        } else if (name.contains("现金流量预测")) {
            return new Tuple(9, "I39");
        } else if (name.contains("质押券信息")) {
            return new Tuple(10, "I39");
        } else if (name.contains("证券买卖盈亏表")) {
            return new Tuple(11, "I39");
        } else if (name.contains("估值表非投连I9")) {
            return new Tuple(12, "国际I9");
        } else if (name.contains("持仓汇总")) {
            return new Tuple(13, "国际I9");
        }
        return null;
    }

    /**
     * 平台加工文件逻辑入口
     *
     * @param filePath 文件路径
     */
    @Override
    public void excelContentHandleLogic(String filePath) {
        File f = new File(filePath);
        log.info("平台加工文件开始,加工的路径为:{}", filePath);
        if (f.exists() && isExcelFile(f)) {
            String parentDirName = f.getParentFile().getName(), fileName = getFileNameNoExtension(f);
            if (parentDirName.contains(MonthlyExcelFileEnum.FUND.getParentDirName())
                    && MonthlyExcelFileEnum.FUND.getFileName().equals(fileName)) {
                log.info("****** 开始处理 {} 文件", fileName);
                excelCVHandleLogic(f, "发行机构", fileName);

            } else if (parentDirName.contains(MonthlyExcelFileEnum.TRUST.getParentDirName())
                    && MonthlyExcelFileEnum.TRUST.getFileName().equals(fileName)) {
                log.info("****** 开始处理 {} 文件", fileName);
                excelCVHandleLogic(f, "发行机构", fileName);

            } else if (parentDirName.contains(MonthlyExcelFileEnum.INSURANCE.getParentDirName())
                    && MonthlyExcelFileEnum.INSURANCE.getFileName().equals(fileName)) {
                log.info("****** 开始处理 {} 文件", fileName);
                excelCVHandleLogic(f, "发行机构", fileName);

            } else if (parentDirName.contains(MonthlyExcelFileEnum.BANK.getParentDirName())
                    && MonthlyExcelFileEnum.BANK.getFileName().equals(fileName)) {
                log.info("****** 开始处理 {} 文件", fileName);
                excelCVHandleLogic(f, "发行机构", fileName);

            } else if (parentDirName.contains(MonthlyExcelFileEnum.FINANCIALBONDS.getParentDirName())
                    && MonthlyExcelFileEnum.FINANCIALBONDS.getFileName().equals(fileName)) {
                log.info("****** 开始处理 {} 文件", fileName);
                financialBondsHandleLogic(f);

            } else if (parentDirName.contains(MonthlyExcelFileEnum.CASHFLOWFORECAST.getParentDirName())) {
                String tmp_fileName = fileName.replaceAll("\\d{4}年\\d{2}月", "YYYY年MM月");
                if (tmp_fileName.contains(MonthlyExcelFileEnum.CASHFLOWFORECAST.getFileName())
                        && !tmp_fileName.contains("(2)")) {
                    String parentPath = f.getParent(),
                            extension = f.getName().substring(f.getName().lastIndexOf(".") + 1),
                            path2 = parentPath + File.separator + fileName + "(2)" + "." + extension;
                    log.info("****** 开始处理 {} 文件", fileName);
                    //cashFlowHandleLogic(new File(path2), f);
                    try {
                        excelDealCashFlowUtil.transferAndCalculate(path2, f.getAbsolutePath());
                    } catch (IOException e) {
                        log.error("excelDealCashFlowUtil_transferAndCalculate_error:{},{}", e, e.getMessage());
                    }
                }
            }
        }
    }

    @Override
    public List<String> upload(MultipartFile[] files, String date) {
        JSONObject params = new JSONObject();
        try {
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("username", SecureUtil.currentUserName());
            params.put("beginTime", DateUtil.now());
            params.put("params", date);
            params.put("dataDate", date);
            params.put("type", "UPLOAD");
            LogMSUtil.preFileOptLog(params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<String> res = new ArrayList<>();
        /*
           判断每个文件名是否包含
             1、 文件夹：股票行业分类  文件名：《股票行业分类YYYY.MM》
             2、 文件夹：可供出售&duration model  文件名： 《duration model-YYYYMM》
                                                        《导出TA-(YYYYMMDD-YYYYMMDD)交易日》
                                                        《导出TA-(YYYYMMDD-YYYYMMDD)自然日》
                                                        《可供出售国债公允价值变动YYYYMM》
         */
        String baseDir = SpringUtil.getProperty("file.monthly-settlement-dir");
        String parentPath = baseDir + File.separator + date;
        File parentFile = FileUtil.mkdir(parentPath);
        List<String> names = monthlySettlementMapper.selectFileNameByDate(date, "3");
        List<File> resFiles = new ArrayList<>();
        List<String> errorMsgs = new ArrayList<>();
        // 匹配前4位为数字
        // 定义正则表达式
        String regex = "^\\d{4}";
        // 创建Pattern对象
        Pattern pattern = Pattern.compile(regex);
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                // 修改了在上传 账套基本信息的文件时 排除文件后缀干扰的问题
                String name = Objects.requireNonNull(FileUtil.getPrefix(file.getOriginalFilename())).replaceAll("（", "(").replaceAll("）", ")");
                try {
                    String folderName = "";
                    if (StringUtils.isNotBlank(name)) {
                        String order = "";
                        if (name.contains("股票行业分类")) {
                            // 放到 /date/股票行业分类
                            order = "4";
                            folderName = order + "." + "股票行业分类";
                        } else if (name.contains("导出TA") || name.contains("可供出售国债公允价值变动") || name.contains("duration model")) {
                            // 放到 /date/可供出售&duration model
                            order = "8";
                            folderName = order + "." + "可供出售&duration model";
                        } else if (name.contains("TA申购业务") || name.contains("TA赎回业务")) {
                            order = "1";
                            folderName = order + "." + "UL申购赎回明细";
                        } else if (pattern.matcher(name).find()) {
                            order = "2";
                            folderName = order + "." + "财务凭证数据";
                        } else if (name.contains("交易所质押式回购") || name.contains("银行间质押式回购")) {
                            order = "5";
                            folderName = order + "." + "回购流水";
                        } else if (name.contains("贷款服务台账表") || name.contains("金融商品转让台账表")) {
                            order = "6";
                            folderName = order + "." + "增值税计算表";
                        } else if (name.contains("证券投资基金估值表")) {
                            order = "7";
                            folderName = order + "." + "非投连估值表";
                        } else if (name.contains("多账套科目发生及余额表")) {
                            order = "9";
                            folderName = order + "." + "现金流量预测";
                        } else if (name.contains("质押券信息浏览表")) {
                            order = "10";
                            folderName = order + "." + "质押券信息";
                        } else if (name.contains("证券买卖盈亏表")) {
                            order = "11";
                            folderName = order + "." + "证券买卖盈亏表";
                        } else if (CollectionUtil.isNotEmpty(names) && names.contains(name)) {
                            order = "3";
                            folderName = order + "." + "证券基本信息";
                        }
                        String newName = handleFileName(name, order);
                        String prefix = FileUtil.getPrefix(newName);
                        if ("4".equals(order)) {
                            prefix = newName;
                        }
                        int count = monthlySettlementMapper.countMatchFileName(date, prefix);
                        // 文件名匹配时才上传
                        if (count > 0) {
                            File dest = new File(parentFile, File.separator + folderName + File.separator + file.getOriginalFilename());
                            FileUtil.mkdir(dest.getParentFile());
                            file.transferTo(dest);
                            monthlySettlementMapper.updateFileInfo(date, prefix, dest.getAbsolutePath(), name, "1");
                            resFiles.add(dest);
                        } else {
                            res.add(name);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    errorMsgs.add(e.getMessage() + "--");
                }
            }
        }
        if (CollectionUtil.isNotEmpty(res)) {
            try {
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.FAIL.name());
                params.put("exception", JSON.toJSONString(res) + "文件名不匹配");
                LogMSUtil.postFileOptLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (CollectionUtil.isNotEmpty(errorMsgs)) {
            try {
                params.put("endTime", DateUtil.now());
                params.put("status", CommonStatus.FAIL.name());
                params.put("exception", JSON.toJSONString(errorMsgs));
                LogMSUtil.postFileOptLog(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            if (CollectionUtil.isNotEmpty(resFiles)) {
                File file = FileUtil.createTempFile("月结压缩文件", ".zip", true);
                OmFileUtil.zipFiles(resFiles, file);
                String importMSFilePath = SpringUtil.getProperty("file.ms-import-file-path");
                File dest = new File(importMSFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                params.put("fileUrl", dest.getAbsolutePath());
            }
            params.put("endTime", DateUtil.now());
            params.put("status", CommonStatus.SUCCESS.name());
            LogMSUtil.postFileOptLog(params);
        }
        return res;
    }

    @Override
    public void sendMail(File attachmentFile, String date, List<String> orders) throws Exception {
        String otherContactsId = SpringUtil.getProperty("monthly-settlement.other-contacts-id");
        if (StringUtils.isBlank(otherContactsId)) {
            BusinessException.throwException("投资人信息为null");
        }
        OtherContacts otherContacts = SpringUtil.getBean(OtherContactsMapper.class).selectById(otherContactsId);
        if (null == otherContacts) {
            BusinessException.throwException("投资人信息为null");
        }
        String recipient = otherContacts.getRecipient();
        String recipientCc = otherContacts.getRecipientCc();
        List<String> carbonCopyList = new ArrayList<>();
        List<String> receivers = new ArrayList<>();
        if (StringUtils.isNotBlank(recipient)) {
            receivers = CollectionUtil.newArrayList(recipient.split(";"));
        }
        if (StringUtils.isNotBlank(recipientCc)) {
            carbonCopyList = CollectionUtil.newArrayList(recipientCc.split(";"));
        }
        String title = "I39月度结账数据-" + date.replaceAll("-", ".");
        String content = "";
        MailAttachment mailAttachment = new MailAttachment();
        mailAttachment.setFileName(attachmentFile.getName());
        mailAttachment.setInputStream(FileUtil.getInputStream(attachmentFile));
        try {
            SpringUtil.getBean(MailUtil.class).sendEmailFileFromShareFolder(receivers, carbonCopyList, CollectionUtil.newArrayList(mailAttachment), title, content);
            monthlySettlementMapper.updateSendStatus(date, orders, MailStatus.SUCCESS.name());
        } catch (Exception e) {
            e.printStackTrace();
            monthlySettlementMapper.updateSendStatus(date, orders, MailStatus.FAILED.name());
        }
    }

    @Override
    public void generateFilesSync(String date, List<String> orders, String username, String type) {
        try {
            JSONObject params = new JSONObject();
            params.put("username", username);
            params.put("logId", IdUtil.getSnowflakeNextIdStr());
            params.put("dataDate", date);
            JSONObject param = new JSONObject();
            param.put("dataDate", date);
            param.put("orders", orders);
            params.put("params", JSON.toJSONString(param));
            params.put("type", type);
            params.put("beginTime", DateUtil.now());
            LogMSUtil.preRpaLog(params);
            // 从RPA根据日期从各个场景目录中下载文件
            List<String> flowIds = getFlowByOrders(orders);
            if (CollectionUtil.isEmpty(flowIds)) {
                BusinessException.throwException("流程id不存在");
            }
            // 队列开始消费时修改状态为下载中
            monthlySettlementMapper.updateFileDownloadStatus(date, orders, "2");
            // 先更新此文件目录中的所有文件为下载中
            File shareDiskFile;
            log.info("读取配置文件中应该查询的流程id为:{}", flowIds);
            List<FlowList> flowLists = SpringUtil.getBean(FlowListMapper.class).selectBatchIds(flowIds);
            // 取 沪深 交易日
            MarketTradeDayService marketTradeDayService = SpringUtil.getBean(MarketTradeDayService.class);
            Set<Date> monthLastDay = marketTradeDayService.getMonthLastDay("00");
            if (CollectionUtil.isEmpty(flowLists)) {
                return;
            }
            // 如果存在凭证浏览任务 应该执行2次 一次是投连的 另一次是非投连的
            if (flowIds.contains("11")) {
                // 包含凭证浏览的流程
                Optional<FlowList> first = flowLists.stream().filter(n -> n.getId() == 11).findFirst();
                if (first.isPresent()) {
                    FlowList addList = new FlowList();
                    FlowList flowList = first.get();
                    BeanUtil.copyProperties(flowList, addList);
                    addList.setShowName("财务凭证数据-投连");
                    flowLists.add(addList);
                }
            }
            for (FlowList flowList : flowLists) {
                String logId = null;
                try {
                    // 异步执行处理任务
                    // 1、执行RPA下载文件任务
                    String order;
                    Map<String, String> config = ordersAndFlowConfig.getConfig();
                    Map<String, String> reverse = MapUtil.reverse(config);
                    Integer id = flowList.getId();
                    order = reverse.get(id + "");
                    Date executeDate = new Date();
                    boolean res = "2".equals(order) && flowList.getShowName().contains("投连");
                    if (res) {
                        // 更新这些文件为下载中
                        String productIdsStr = "5001,5002,5003,5004,5005,5006,5007,5008,5010,5101,5102,5103,5104,5105,6001";
                        for (String pId : productIdsStr.split(",")) {
                            monthlySettlementMapper.updateFileDownloadStatusMatchProductId(pId, "2", date, order);
                        }
                    }
                    logId = executeRPATask(date, CollectionUtil.newArrayList(flowList), monthLastDay, username, type);
                    // 2、从共享文件夹中下载文件 最后形成一个目录
                    shareDiskFile = executeShareDiskDownload(date, CollectionUtil.newArrayList(flowList.getBasePath()),
                            res ? "财务凭证数据" : flowList.getShowName(), order, executeDate, res);
                    if (shareDiskFile != null && shareDiskFile.exists()) {
                        // 3、处理需要修改的文件 处理文件后依然返回处理后的目录地址(在原rpa下载的目录上进行修改)
                        // 4、创建目标文件夹并将文件拷贝到目标文件夹下 目标文件夹统一创建到配置的目标地址下 目录结构为 baseDir/date/业务文件夹/业务文件
                        String absolutePath = shareDiskFile.getAbsolutePath();
                        log.info("需要处理的文件夹为:{}", absolutePath);
                        // 处理文件
                        List<FolderFileInfo> files = traverseFile(absolutePath);
                        // 执行数据库更新操作 此月份匹配上的文件变更状态为 已下载、文件路径
                        if (CollectionUtil.isNotEmpty(files)) {
                            for (FolderFileInfo folderFileInfo : files) {
                                File file = folderFileInfo.getFile();
                                if (file != null && file.exists()) {
                                    String name = file.getName();
                                    String path = file.getAbsolutePath();
                                    log.info("文件名和路径为:{} - {}", name, path);
                                    String newName = handleFileName(name, String.valueOf(folderFileInfo.getFolderOrder()));
                                    log.info("要处理的原文件名 = {}, 处理后的文件名 = {}", name, newName);
                                    monthlySettlementMapper.updateFileInfo(date, FileUtil.getPrefix(newName), path, name, "0");
                                }
                            }
                            // 把后续没有匹配上文件的修改为未下载
                            int folderOrder = files.get(0).getFolderOrder();
                            monthlySettlementMapper.updateNoMatchFileNameStatus(String.valueOf(folderOrder), date, "0");
                        } else {
                            // 说明没有需要下载的文件
                            for (Map.Entry<String, String> entry : config.entrySet()) {
                                String value = entry.getValue();
                                if (value.equals(String.valueOf(flowList.getId()))) {
                                    order = entry.getKey();
                                    break;
                                }
                            }
                            // rpa 没有下载回来文件 则改为未下载
                            monthlySettlementMapper.updateFileDownloadStatus(date, CollectionUtil.newArrayList(order), "0");
                        }
                    }
                    params.put("rpaLogId", logId);
                    List<File> fileList = FileUtil.loopFiles((shareDiskFile != null && shareDiskFile.exists()) ? shareDiskFile.getAbsolutePath() : null);
                    if (CollectionUtil.isNotEmpty(fileList)) {
                        List<File> files = new ArrayList<>();
                        for (File subFile : fileList) {
                            if (subFile != null) {
                                files.add(subFile);
                            }
                        }
                        File file = FileUtil.createTempFile("月结数据", ".zip", true);
                        file = FileUtil.rename(file, "月结数据" + "." + FileUtil.extName(file), true);
                        OmFileUtil.zipFiles(files, file);
                        String msRPAFilePath = SpringUtil.getProperty("file.ms-rpa-file-path");
                        File dest = new File(msRPAFilePath + File.separator + FileUtil.getPrefix(file) + "_" + DateUtil.current() + "." + FileUtil.extName(file));
                        FileUtil.copyFile(file, dest, StandardCopyOption.REPLACE_EXISTING);
                        params.put("fileUrl", dest.getAbsolutePath());
                    }
                    params.put("status", CommonStatus.SUCCESS.name());
                    LogMSUtil.postRpaLog(params);
                } catch (Exception e) {
                    e.printStackTrace();
                    if (StringUtils.isNotBlank(logId)) {
                        BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
                        BaseCronLog baseCronLog = baseCronLogMapper.selectById(logId);
                        if (baseCronLog != null) {
                            baseCronLog.setTaskInfo(e.getMessage());
                            baseCronLogMapper.updateById(baseCronLog);
                        }
                    }
                    params.put("status", CommonStatus.FAIL.name());
                    params.put("errorMsg", e.getMessage());
                    LogMSUtil.postRpaLog(params);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int selectDownloadStatus(String date, String order) {
        return monthlySettlementMapper.selectDownloadStatus(date, order);
    }

    @Override
    public void sendMailV1(String date, List<String> orders, List<MonthlySettlementList> settlementLists) {
        CronService cronService = SpringUtil.getBean(CronService.class);
        List<String> jobIds = cronService.getJobIdByClass(MonthlySettlementSendMailJob.class);
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("orders", orders);
        jobDataMap.put("date", date);
        jobDataMap.put("username", SecureUtil.currentUser().getAccount());
        jobDataMap.put("settlementLists", settlementLists);
        cronService.startJobNow(jobIds, jobDataMap);
    }

    /**
     * 现金流量预测文件加工逻辑
     *
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     */
    public void cashFlowHandleLogic(File sourceFile, File targetFile) {
        log.info("cashFlow_start:{},{}", sourceFile.getName(), targetFile.getName());
        if (sourceFile == null || targetFile == null) {
            return;
        }
        try (FileInputStream file1 = new FileInputStream(sourceFile)) {
            log.info("cashFlow_sourceFile:{}", sourceFile.getName());
            Workbook workbook1 = WorkbookFactory.create(file1);
            Sheet sheet1 = workbook1.getSheetAt(0);
            try (FileInputStream file2 = new FileInputStream(targetFile)) {
                log.info("cashFlow_targetFile:{}", targetFile.getName());
                Workbook workbook2 = WorkbookFactory.create(file2);
                Sheet sheet2 = workbook2.getSheetAt(0);
                List<Integer> rowsToDelete = new ArrayList<>();
                List<Row> rowsFromFile1 = new ArrayList<>();
                for (int i = 5; i <= sheet1.getLastRowNum(); i++) {
                    Row row = sheet1.getRow(i);
                    if (row == null) continue;
                    Cell firstCell = row.getCell(0);
                    if (firstCell == null) continue;
                    String firstCellValue = firstCell.getStringCellValue();
                    if (StringUtils.isNotBlank(firstCellValue)
                            && (firstCellValue.startsWith("1") || firstCellValue.startsWith("2") || firstCellValue.startsWith("4"))) {
                        log.info("cashFlow_firstCell_firstCellValue:{}", firstCellValue);
                        rowsFromFile1.add(row);
                        rowsToDelete.add(i);
                    }
                }
                insertRowsToSheet2(sheet2, rowsFromFile1);
                removeRowFromSheet1(sheet1, rowsToDelete);
                removeEmptyRows(sheet1);
                removeEmptyRows(sheet2);
                insertTotalRow(sheet1, 2, calculateTotalAmount(sheet1, 5, sheet1.getLastRowNum() - 1, 2), 1, (sheet1.getLastRowNum() - 5));
                insertTotalRow(sheet2, 2, calculateTotalAmount(sheet2, 5, sheet2.getLastRowNum() - 1, 2), 1, (sheet2.getLastRowNum() - 5));
                try (FileOutputStream outFile1 = new FileOutputStream(sourceFile)) {
                    log.info("cashFlow_outFile1");
                    workbook1.write(outFile1);
                }
                try (FileOutputStream outFile2 = new FileOutputStream(targetFile)) {
                    log.info("cashFlow_outFile2");
                    workbook2.write(outFile2);
                }
                workbook2.close();
            }
            workbook1.close();
        } catch (Exception e) {
            log.error("cashFlowHandleLogic_error:{},{}", e, e.getMessage());
        }
    }

    /**
     * 删除数据行
     *
     * @param sheet
     * @param rowsToDelete 要删除的数据行索引
     */
    private static void removeRowFromSheet1(Sheet sheet, List<Integer> rowsToDelete) {
        log.info("cashFlow_removeRowFromSheet1");
        for (int i = rowsToDelete.size() - 1; i >= 0; i--) {
            int rowIndex = rowsToDelete.get(i);
            sheet.removeRow(sheet.getRow(rowIndex));
        }
    }


    /**
     * 计算“金额”列的总和
     *
     * @param sheet
     * @param firstRowIndex     开始行索引
     * @param lastRowIndex      结束行索引
     * @param amountColumnIndex 被计算列索引
     * @return
     */
    private static double calculateTotalAmount(Sheet sheet, int firstRowIndex, int lastRowIndex, int amountColumnIndex) {
        log.info("cashFlow_calculateTotalAmount_start");
        double sum = 0.0;
        NumberFormat format = NumberFormat.getInstance(Locale.US); // 使用US locale处理千分位
        if (firstRowIndex > lastRowIndex) {
            return sum;
        }
        for (int i = firstRowIndex; i <= lastRowIndex; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            Cell cell = row.getCell(amountColumnIndex);
            if (cell == null) continue;
            try {
                switch (cell.getCellType()) {
                    case NUMERIC:
                        // 直接获取数值
                        sum += cell.getNumericCellValue();
                        break;
                    case STRING:
                        // 处理带千分位的文本数字
                        String value = cell.getStringCellValue().replaceAll("[^\\d.-]", "");
                        if (!value.isEmpty()) {
                            sum += Double.parseDouble(value);
                        }
                        break;
                    case FORMULA:
                        // 处理公式单元格
                        switch (cell.getCachedFormulaResultType()) {
                            case NUMERIC:
                                sum += cell.getNumericCellValue();
                                break;
                            case STRING:
                                String formulaValue = cell.getStringCellValue().replaceAll("[^\\d.-]", "");
                                if (!formulaValue.isEmpty()) {
                                    sum += Double.parseDouble(formulaValue);
                                }
                                break;
                        }
                        break;
                }
            } catch (Exception e) {
                log.error("cashFlow_calculateTotalAmount_error:{},{},{}", row.getRowNum(), e, e.getMessage());
            }
        }
        return sum;
    }


    /**
     * 插入合计行
     *
     * @param sheet
     * @param amountColumnIndex 被计算列索引
     * @param totalAmount       总和
     * @param countIndex        个数列索引
     * @param count             个数
     */
    private static void insertTotalRow(Sheet sheet, int amountColumnIndex, double totalAmount, int countIndex, int count) {
        log.info("cashFlow_insertTotalRow");
        Row totalRow = sheet.getRow(sheet.getLastRowNum());
        if (totalRow == null) return;
        Cell amountCell = totalRow.getCell(amountColumnIndex);
        if (amountCell == null) return;
        amountCell.setCellValue(totalAmount);
        Cell countCell = totalRow.getCell(countIndex);
        if (countCell == null) return;
        countCell.setCellValue(count);
    }


    /**
     * 将文件 1 的数据插入到文件 2 的上边
     *
     * @param sheet2
     * @param rowsFromFile1 要插入的数据行列表
     */
    private static void insertRowsToSheet2(Sheet sheet2, List<Row> rowsFromFile1) {
        log.info("cashFlow_insertRowsToSheet2");
        int headerRowIndex = 4;
        int existingRowCount = sheet2.getLastRowNum() + 1;
        if (existingRowCount > headerRowIndex + 1) {
            sheet2.shiftRows(headerRowIndex + 1, existingRowCount - 1, rowsFromFile1.size());
        }
        for (int i = 0; i < rowsFromFile1.size(); i++) {
            Row sourceRow = rowsFromFile1.get(i);
            Row newRow = sheet2.createRow(headerRowIndex + 1 + i);
            copyRow(sourceRow, newRow);
        }
    }


    /**
     * 删除空行
     *
     * @param sheet
     */
    private static void removeEmptyRows(Sheet sheet) {
        log.info("cashFlow_removeEmptyRows");
        int lastRowNum = sheet.getLastRowNum();

        // 从最后一行开始向前遍历
        for (int i = lastRowNum; i >= 0; i--) {
            Row row = sheet.getRow(i);
            if (row == null || isRowEmpty(row)) {
                // 如果行为空，删除该行
                if (i < lastRowNum) {
                    // 将下面的行上移
                    sheet.shiftRows(i + 1, lastRowNum, -1);
                }
                // 更新最后一行索引
                lastRowNum--;
            }
        }
    }


    /**
     * 判断行是否为空
     *
     * @param row
     * @return
     */
    private static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }


    /**
     * 复制行数据
     *
     * @param sourceRow 原行数据
     * @param targetRow 目标行数据
     */
    private static void copyRow(Row sourceRow, Row targetRow) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell == null) continue;

            Cell targetCell = targetRow.createCell(i);
            switch (sourceCell.getCellType()) {
                case STRING:
                    targetCell.setCellValue(sourceCell.getStringCellValue());
                    break;
                case NUMERIC:
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                    break;
                default:
                    targetCell.setCellValue(sourceCell.toString());
            }
        }
    }

    /**
     * 基金、信托、保险理财产品文件加工逻辑
     *
     * @param excelFile
     * @param headerCellValue
     * @param type
     */
    public void excelCVHandleLogic(File excelFile, String headerCellValue, String type) {
        try (FileInputStream f = new FileInputStream(excelFile)) {
            Workbook workbook = WorkbookFactory.create(f);
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            int targetColumnIndex = -1, codeColumnIndex = -1;
            log.info("excelCVHandleLogic_start_fileName_headerCellValue:{},{}", type, headerCellValue);
            if (StringUtils.isBlank(headerCellValue)) {
                return;
            }
            for (Cell cell : headerRow) {
                if (headerCellValue.equals(cell.getStringCellValue())) {
                    targetColumnIndex = cell.getColumnIndex();
                }
                if ("证券代码".equals(cell.getStringCellValue())) {
                    codeColumnIndex = cell.getColumnIndex();
                }
            }
            log.info("excelCVHandleLogic_targetColumnIndex_codeColumnIndex:{},{}", targetColumnIndex, codeColumnIndex);
            if (targetColumnIndex == -1 || codeColumnIndex == -1) {
                return;
            }
            for (int i = 1; i < sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                Cell codeCell = row.getCell(codeColumnIndex);
                if (codeCell == null) continue;
                log.info("excelCVHandleLogic_codeCell_value:{}", codeCell.getStringCellValue());
                if (StringUtils.isBlank(codeCell.getStringCellValue()) || "证券代码".equals(codeCell.getStringCellValue().trim()))
                    continue;
                Cell cell = row.getCell(targetColumnIndex);
                if (cell == null) continue;
                log.info("excelCVHandleLogic_cell_value:{}", cell.getStringCellValue());
                if (MonthlyExcelFileEnum.FUND.getFileName().equals(type)) {
                    //基金理财产品
                    String cv = cell.getStringCellValue();
                    log.info("excelCVHandleLogic_cell_FUND_value1:{}", cell.getStringCellValue());
                    if (StringUtils.isNotBlank(cv) && cv.contains("lccp")) {
                        String tmp_v = cv.replaceAll(".*lccp", "");
                        log.info("excelCVHandleLogic_cell_FUND_value2:{}", cell.getStringCellValue());
                        cell.setCellValue(tmp_v);
                    }
                } else if (MonthlyExcelFileEnum.TRUST.getFileName().equals(type) || MonthlyExcelFileEnum.INSURANCE.getFileName().equals(type)) {
                    log.info("excelCVHandleLogic_cell_TRUST_INSURANCE_clear");
                    //信托理财产品、保险理财产品
                    cell.setCellValue("");
                } else if (MonthlyExcelFileEnum.BANK.getFileName().equals(type)) {
                    log.info("excelCVHandleLogic_cell_BANK_value1:{}", cell.getStringCellValue());
                    //银行理财产品
                    cell.setCellValue("理财公司理财产品");
                    log.info("excelCVHandleLogic_cell_BANK_value2:{}", cell.getStringCellValue());
                }
            }
            try (FileOutputStream outFile = new FileOutputStream(excelFile)) {
                log.info("excelCVHandleLogic_end_fileName_headerCellValue:{},{}", type, headerCellValue);
                workbook.write(outFile);
            }
            workbook.close();
        } catch (Exception e) {
            log.error("excelCVHandleLogic_end_fileName_headerCellValue_error:{},{},{},{}", type, headerCellValue, e, e.getMessage());
        }

    }

    /**
     * 债券-金融债-文件加工逻辑
     *
     * @param excelFile
     */
    public void financialBondsHandleLogic(File excelFile) {
        try (FileInputStream fis = new FileInputStream(excelFile);
             Workbook workbook = WorkbookFactory.create(fis)) {
            log.info("financialBondsHandleLogic_start");
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            if (sheet.getPhysicalNumberOfRows() == 0) return;

            // 1. 获取标题行，确定列索引
            Row headerRow = sheet.getRow(0);
            Map<String, Integer> columnIndexMap = new HashMap<>();

            for (Cell cell : headerRow) {
                String headerName = getCellStringValue(cell);
                if (headerName != null) {
                    columnIndexMap.put(headerName.trim(), cell.getColumnIndex());
                }
            }

            // 2. 检查所需列是否存在
            Integer nameColumnIndex = columnIndexMap.get("证券名称");
            Integer categoryColumnIndex = columnIndexMap.get("债券类别");

            if (nameColumnIndex == null || categoryColumnIndex == null) {
                throw new RuntimeException("Excel中缺少'证券名称'或'债券类别'列");
            }

            // 3. 遍历数据行并修改符合条件的单元格
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Cell nameCell = row.getCell(nameColumnIndex);
                Cell categoryCell = row.getCell(categoryColumnIndex);

                if (nameCell != null && categoryCell != null) {
                    String nameValue = getCellStringValue(nameCell);

                    // 检查名称是否包含"银行"或"绿色"
                    if (nameValue != null && (nameValue.contains("银行") || nameValue.contains("绿色"))) {
                        // 修改类别列的值为"ABC"
                        categoryCell.setCellValue("商业银行金融债券");
                    }
                }
            }

            // 4. 保存修改后的文件
            try (FileOutputStream fos = new FileOutputStream(excelFile)) {
                log.info("financialBondsHandleLogic_end");
                workbook.write(fos);
            }
            workbook.close();
        } catch (Exception e) {
            log.error("financialBondsHandleLogic_error:{},{}", e, e.getMessage());
        }
    }

    /**
     * 获取单元格值
     *
     * @param cell
     * @return
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    /**
     * 判断文件是否为excel文件
     *
     * @param file
     * @return
     */
    private boolean isExcelFile(File file) {
        String fName = file.getName().toLowerCase();
        return fName.endsWith(".xlsx") || fName.endsWith(".xls");
    }

    /**
     * 获取不包含后缀名的文件名称
     *
     * @param file
     * @return
     */
    private String getFileNameNoExtension(File file) {
        return file.getName().replaceFirst("[.][^.]+$", "");
    }

    /**
     * 读取模板文件生成默认数据
     *
     * @param dataDate 月份
     */
    private synchronized void generateDefaultData(String dataDate) {
        monthlySettlementLists.clear();
        if (CollectionUtil.isEmpty(monthlySettlementLists)) {
            synchronized (MonthlySettlementServiceImpl.class) {
                if (CollectionUtil.isEmpty(monthlySettlementLists)) {
                    InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("monthly_template/I39月结文件清单-2025.03.19(1).xlsx");
                    ExcelReader reader = ExcelUtil.getReader(resourceAsStream);
                    int rowCount = reader.getRowCount();
                    for (int i = 1; i < rowCount; i++) {
                        List<Object> objects = reader.readRow(i);
                        if (CollectionUtil.isNotEmpty(objects)) {
                            // 就取2列
                            Object o = objects.get(0);
                            Object o1 = objects.get(1);
                            String orderAndFolderName = String.valueOf(o);
                            String fileName = String.valueOf(o1);
                            String[] split = orderAndFolderName.split("\\.");
                            String order = split[0];
                            String folder = split[1];
                            MonthlySettlementList settlementList = new MonthlySettlementList();
                            settlementList.setId(IdUtil.getSnowflakeNextIdStr());
                            settlementList.setDataDate(dataDate);
                            settlementList.setFolderOrder(Integer.parseInt(order));
                            settlementList.setFileName(FileUtil.getPrefix(fileName));
                            settlementList.setDownloadStatus(0);
                            settlementList.setSendStatus(MailStatus.UNSENT.name());
                            settlementList.setType("-1");
                            if ("12".equals(order)) {
                                settlementList.setFolderName("非投连I9估值表");
                                settlementList.setFolderCategory(folder);
                            } else if ("13".equals(order)) {
                                settlementList.setFolderName("提取持仓数据");
                                settlementList.setFolderCategory(folder);
                            } else {
                                settlementList.setFolderName(folder);
                                settlementList.setFolderCategory("I39");
                            }
                            monthlySettlementLists.add(settlementList);
                        }
                    }
                }
            }
        }
    }

    @Component
    @ConfigurationProperties(prefix = "monthly-settlement.order-flow")
    @Data
    public static class OrdersAndFlowConfig {
        private Map<String, String> config;
    }
}
