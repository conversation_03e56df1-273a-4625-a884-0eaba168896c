package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.O32AssetUnit;
import cn.sdata.om.al.entity.O32AssetUnitQuery;
import cn.sdata.om.al.mapper.O32AssetUnitMapper;
import cn.sdata.om.al.service.O32AssetUnitService;
import cn.sdata.om.al.utils.CommonUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class O32AssetUnitServiceImpl implements O32AssetUnitService {

    private O32AssetUnitMapper o32AssetUnitMapper;

    @Autowired
    public void setO32AssetUnitMapper(O32AssetUnitMapper o32AssetUnitMapper) {
        this.o32AssetUnitMapper = o32AssetUnitMapper;
    }

    @Override
    public boolean importO32AssetUnit(MultipartFile file) {
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), 0);
            List<List<Object>> list = excelReader.read(1);
            List<O32AssetUnit> o32AssetUnits = new ArrayList<>();
            for (List<Object> row : list) {
                O32AssetUnit o32AssetUnit = new O32AssetUnit();
                o32AssetUnit.setId(IdUtil.getSnowflakeNextIdStr());
                // 序号
                Object o = row.get(0);
                o32AssetUnit.setOrderNo(Integer.parseInt(String.valueOf(o)));
                // 账套名称
                Object o1 = row.get(1);
                o32AssetUnit.setProductName(String.valueOf(o1));
                // 账套编号
                Object o2 = row.get(2);
                o32AssetUnit.setProductId(String.valueOf(o2));
                // O32 基金名称
                Object o3 = row.get(3);
                o32AssetUnit.setO32FundName(String.valueOf(o3));
                // O32 资产单元名称
                Object o4 = row.get(4);
                o32AssetUnit.setO32AssetUnitName(String.valueOf(o4));
                // O32 资产单元编码
                Object o5 = row.get(5);
                o32AssetUnit.setO32AssetUnitCode(String.valueOf(o5));
                o32AssetUnits.add(o32AssetUnit);
            }
            if (CollectionUtil.isNotEmpty(o32AssetUnits)) {
                o32AssetUnitMapper.truncateTable();
                o32AssetUnitMapper.saveBatch(o32AssetUnits);
                return true;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return false;
    }

    @Override
    public PageInfo<O32AssetUnit> page(O32AssetUnitQuery o32AssetUnitQuery) {
        int pageNo = o32AssetUnitQuery.getPageNo();
        int pageSize = o32AssetUnitQuery.getPageSize();
        List<String> productIds = o32AssetUnitQuery.getProductIds();
        if (CollectionUtil.isNotEmpty(productIds)) {
            productIds = productIds.stream().distinct().collect(Collectors.toList());
            o32AssetUnitQuery.setProductIds(productIds);
        }
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<O32AssetUnit> o32AssetUnits = o32AssetUnitMapper.page(o32AssetUnitQuery);
            return new PageInfo<>(o32AssetUnits);
        }
    }

    @Override
    public O32AssetUnit getById(String id) {
        return o32AssetUnitMapper.getById(id);
    }

    @Override
    public boolean saveOrUpdate(O32AssetUnit o32AssetUnit) {
        o32AssetUnit.setUpdateTime(DateUtil.today());
        o32AssetUnit.setUpdateByName(SecureUtil.currentUser().getAccount());
        String id = o32AssetUnit.getId();
        if (StringUtils.isNotBlank(id)) {
            return o32AssetUnitMapper.update(o32AssetUnit) > 0;
        } else {
            o32AssetUnit.setId(IdUtil.getSnowflakeNextIdStr());
            int order = o32AssetUnitMapper.getMaxOrderNumber();
            o32AssetUnit.setOrderNo(order + 1);
            return o32AssetUnitMapper.insert(o32AssetUnit) > 0;
        }
    }

    @Override
    public List<CommonEntity> codeList() {
        List<String> commonEntities = o32AssetUnitMapper.codeList();
        return CommonUtil.stringToCommonEntityList(commonEntities);
    }

    @Override
    public List<CommonEntity> nameList() {
        List<String> commonEntities = o32AssetUnitMapper.nameList();
        return CommonUtil.stringToCommonEntityList(commonEntities);
    }
}
