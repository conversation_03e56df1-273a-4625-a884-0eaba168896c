package cn.sdata.om.al.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSetGroup;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.ProjectGroupMapper;
import cn.sdata.om.al.mapper.mail.MailCommonInfoMapper;
import cn.sdata.om.al.result.R;
import cn.sdata.om.al.service.ProjectGroupService;
import cn.sdata.om.al.utils.PageUtil;
import cn.sdata.om.al.utils.SecureUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectGroupServiceImpl implements ProjectGroupService {

    private ProjectGroupMapper projectGroupMapper;

    private MailCommonInfoMapper mailCommonInfoMapper;

    @Autowired
    public void setProjectGroupMapper(ProjectGroupMapper projectGroupMapper) {
        this.projectGroupMapper = projectGroupMapper;
    }

    @Autowired
    public void setMailCommonInfoMapper(MailCommonInfoMapper mailCommonInfoMapper) {
        this.mailCommonInfoMapper = mailCommonInfoMapper;
    }

    @Override
    public PageInfo<AccountSetGroup> page(Integer pageNo, Integer pageSize, List<String> accountSetGroupIds, List<String> productIds) {
        int newPageNo = (pageNo - 1) * pageSize;
        List<AccountSetGroup> accountSetGroups = projectGroupMapper.page(accountSetGroupIds);
        List<CommonEntity> list = mailCommonInfoMapper.list();
        if (CollectionUtil.isNotEmpty(accountSetGroups)) {
            Iterator<AccountSetGroup> iterator = accountSetGroups.iterator();
            while (iterator.hasNext()) {
                AccountSetGroup accountSetGroup = iterator.next();
                fillAccountSetName(accountSetGroup, list);
                if (CollectionUtil.isNotEmpty(productIds)) {
                    String accountCodesStr = accountSetGroup.getAccountCodesStr();
                    if (StringUtils.isNotBlank(accountCodesStr)) {
                        List<String> codeList = ListUtil.toList(accountCodesStr.split(","));
                        Collection<String> intersection = CollectionUtil.intersection(codeList, productIds);
                        if (CollectionUtil.isEmpty(intersection)) {
                            iterator.remove();
                        }
                    }
                }
            }
        }
        return PageUtil.handleRightPage(ListUtil.sub(accountSetGroups, newPageNo, newPageNo + pageSize), accountSetGroups.size(), pageNo, pageSize);
    }

    @Override
    public R<Boolean> saveOrUpdate(AccountSetGroup accountSetGroup) {
        String id = accountSetGroup.getId();
        List<String> accountCodes = accountSetGroup.getAccountSetCodes();
        // 判断如果有账套属于某个组时则不允许新增
        List<CommonEntity> list = list();
        List<String> existAccountCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (CommonEntity commonEntity : list) {
                String extra = commonEntity.getExtra();
                if (StringUtils.isNotBlank(extra)) {
                    List<String> split = ListUtil.toList(extra.split(","));
                    existAccountCodes.addAll(split);
                }
            }
        }
        accountSetGroup.setAccountCodesStr(String.join(",", accountCodes));
        int res;
        if (StringUtils.isNotBlank(id)) {
            // 修改
            // 查询除了自己已经绑定组得所有账套编码
            List<String> excludeSelfAccountCodes = projectGroupMapper.selectAccountCodesStrExcludeSelf(id);
            if (CollectionUtil.isNotEmpty(excludeSelfAccountCodes)) {
                List<String> split = excludeSelfAccountCodes.stream().distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(accountCodes)) {
                    // 如果除了自己的其他已经绑定组的账套编号 跟马上要修改的有重叠 则判定重复绑定
                    Collection<String> intersection = CollectionUtil.intersection(split, accountCodes);
                    if (CollectionUtil.isNotEmpty(intersection)) {
                        // 两个集合存在交集则说明有重复的
                        return R.restResult(false, 400, "账套不能重复绑定账套组");
                    }
                }
            }
            accountSetGroup.setUpdateTime(DateUtil.now());
            accountSetGroup.setUpdateBy(SecureUtil.currentUser().getAccount());
            res = projectGroupMapper.update(accountSetGroup);
        } else {
            // 新增
            // 所有已经配置 新增时检查所有
            existAccountCodes = existAccountCodes.stream().distinct().collect(Collectors.toList());
            for (String accountCode : accountCodes) {
                if (existAccountCodes.contains(accountCode)) {
                    return R.restResult(false, 400, "账套不能重复绑定账套组");
                }
            }
            id = IdUtil.getSnowflakeNextIdStr();
            accountSetGroup.setId(id);
            accountSetGroup.setCreateTime(DateUtil.now());
            accountSetGroup.setUpdateTime(DateUtil.now());
            accountSetGroup.setUpdateBy(SecureUtil.currentUser().getAccount());
            accountSetGroup.setCreateBy(SecureUtil.currentUser().getAccount());
            res = projectGroupMapper.save(accountSetGroup);
        }
        return res > 0 ? R.ok() : R.failed();
    }

    @Override
    public AccountSetGroup getById(String id) {
        AccountSetGroup accountSetGroup = projectGroupMapper.getById(id);
        List<CommonEntity> list = mailCommonInfoMapper.list();
        fillAccountSetName(accountSetGroup, list);
        return accountSetGroup;
    }

    @Override
    public List<CommonEntity> list() {
        return projectGroupMapper.list();
    }

    @Override
    public boolean delete(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            BusinessException.throwException("ids为null");
        }
        return projectGroupMapper.batchDelete(ids) > 0;
    }

    @Override
    public List<CommonEntity> accountSetListNoGroup() {
        PageInfo<AccountSetGroup> page = page(1, Integer.MAX_VALUE, null, null);
        List<AccountSetGroup> list = page.getList();
        List<String> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (AccountSetGroup accountSetGroup : list) {
                String accountCodesStr = accountSetGroup.getAccountCodesStr();
                if (StringUtils.isNotBlank(accountCodesStr)) {
                    res.addAll(CollectionUtil.newArrayList(accountCodesStr.split(",")));
                }
            }
            if (CollectionUtil.isNotEmpty(res)) {
                return mailCommonInfoMapper.accountSetListNoGroup(res.stream().distinct().collect(Collectors.toList()));
            }
        }
        return mailCommonInfoMapper.list();
    }

    /**
     * 填充账套名称
     *
     * @param accountSetGroup 账套组对象
     * @param list            全部账套列表
     */
    private void fillAccountSetName(AccountSetGroup accountSetGroup, List<CommonEntity> list) {
        if (ObjectUtil.isNotNull(accountSetGroup)) {
            String accountCode = accountSetGroup.getAccountCodesStr();
            if (StringUtils.isNotBlank(accountCode) && CollectionUtil.isNotEmpty(list)) {
                if (StringUtils.isNotBlank(accountCode)) {
                    accountSetGroup.setAccountSetCodes(CollectionUtil.newArrayList(accountCode.split(",")));
                } else {
                    accountSetGroup.setAccountSetCodes(new ArrayList<>());
                }
                Map<String, String> collect = list.stream().collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
                StringBuilder sb = new StringBuilder();
                for (String code : accountCode.split(",")) {
                    sb.append(collect.get(code)).append(",");
                }
                accountSetGroup.setAccountSetName(sb.deleteCharAt(sb.length() - 1).toString());
            }
        }
    }
}
