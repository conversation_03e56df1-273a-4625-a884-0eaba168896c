package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSummarizeListQuery;
import cn.sdata.om.al.entity.mail.AccountFundInformation;
import cn.sdata.om.al.entity.mail.MailCommonInfo;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface MailCommonInfoService {
    PageInfo<MailCommonInfo> page(AccountSummarizeListQuery accountSummarizeListQuery);

    boolean saveOrUpdate(MailCommonInfo mailCommonInfo);

    MailCommonInfo getById(String id);

    List<CommonEntity> list();

    PageInfo<AccountFundInformation> fundInfoPage(int pageNo, int pageSize, List<String> productIds, List<String> admins, String type);

    boolean fundInfoSaveOrUpdate(AccountFundInformation accountFundInformation);

    boolean fundInfoBatchDelete(List<String> ids);

    AccountFundInformation fundInfoGetById(String fundInfoId);

    Boolean importAccountSetInfo(MultipartFile file);

    Boolean importFundInfo(MultipartFile file);

    List<CommonEntity> fundAdminList();

}
