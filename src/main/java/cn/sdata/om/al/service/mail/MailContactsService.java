package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailContactsListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailContacts;
import cn.sdata.om.al.entity.mail.vo.MailContactsVo;
import cn.sdata.om.al.entity.mail.vo.MailContactsListVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface MailContactsService {
    PageInfo<MailContactsListVo> page(MailContactsListQuery mailContactsListQuery);

    List<CommonEntity> list();

    boolean delete(List<String> ids);

    boolean saveOrUpdate(SaveOrUpdateMailContacts saveOrUpdateMailContacts);

    MailContactsVo getById(String id);

    List<CommonEntity> getAccountSetByTemplateId(String templateId);

    List<CommonEntity> getAllRecipient();

    List<CommonEntity> getMailContactsType();

    List<CommonEntity> getMailContactsCategoryByType(String typeId);

    List<CommonEntity> getAllThreePartyOrganization();

    List<CommonEntity> getAllInvestor();

    List<CommonEntity> getAccountSetByIds(List<String> accountSetIds);

    List<String> getMailContactsByAccountSetIdsAndTemplateId(List<String> accountSetIds, String templateId);

    List<String> getTypeByTaskId(String taskId);
}
