package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SendMailInfo;
import cn.sdata.om.al.qrtz.entity.Cron;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface MailInfoService {

    @Deprecated
    List<SendMailInfo> doSendMailInfo(Cron cron, String dataDate, Set<String> paramProductIds, Map<String, RemoteFileInfo> files);

    List<SendMailInfo> doSendMailInfo(List<SendMailInfo> sendMailInfos);

    List<SendMailInfo> composeByConfig(Cron cron, String dataDate, Set<String> paramProductIds, Map<String, RemoteFileInfo> files);

    File downloadAttachments(String mailId, String fileId);

    boolean testSendMail(String url, String filePath);

}
