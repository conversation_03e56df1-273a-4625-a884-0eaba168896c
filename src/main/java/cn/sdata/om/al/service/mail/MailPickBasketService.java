package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickBasket;
import cn.sdata.om.al.entity.mail.vo.MailPickBasketVo;

import java.util.List;

public interface MailPickBasketService {
    Boolean saveOrUpdate(SaveOrUpdateMailPickBasket saveOrUpdateMailPickBasket);

    List<MailPickBasketVo> list();

    Boolean delete(String id);

    int selectPickBasketCount(String id);

    MailPickBasketVo getById(String id);

    int selectPickBasketCountByConditions(String id, MailContentListQuery contentListQuery);

}
