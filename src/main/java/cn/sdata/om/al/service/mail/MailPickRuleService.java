package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.MailFilterRuleDTO;
import cn.sdata.om.al.entity.mail.params.MailPickRuleListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickRule;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleListVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface MailPickRuleService {
    PageInfo<MailPickRuleListVo> page(MailPickRuleListQuery mailPickRuleListQuery);

    boolean saveOrUpdate(SaveOrUpdateMailPickRule saveOrUpdateMailPickRule);

    MailPickRuleDetail getById(String id);

    List<CommonEntity> list();

    boolean delete(List<String> ids);

    List<MailPickRuleDetail> doFilter(MailContent mailContent, List<MailPickRuleDetail> mailPickRules);

    String ruleToExpression(List<MailFilterRuleDTO> ruleList, String condition);

    List<CommonEntity> unbindRuleList();

}
