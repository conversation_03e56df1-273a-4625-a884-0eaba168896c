package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.ManualPickParam;
import cn.sdata.om.al.entity.mail.vo.MailContentListVo;

import java.util.List;

public interface MailPickService {

    void readFromMail(Integer messageNumber);

    /**
     * 只读取指定messageNumber的那一封邮件
     * @param messageNumber 邮件编号（从1开始）
     */
    void readSingleMail(Integer messageNumber);

    List<MailContentListVo> list(MailContentListQuery mailContentListQuery);

    MailContent getById(String id);

    Boolean move(String boxId, String contentId);

    Boolean doPick(ManualPickParam manualPickParam);

    void pickMail(List<MailContent> mailContents);

    void pickMailSimple(List<MailContent> mailContents);

    Integer getMaxNumber();
}
