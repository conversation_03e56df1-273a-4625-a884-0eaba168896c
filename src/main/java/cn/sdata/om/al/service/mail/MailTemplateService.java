package cn.sdata.om.al.service.mail;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.params.MailTemplateListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailTemplate;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateListVo;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface MailTemplateService {
    PageInfo<MailTemplateListVo> page(MailTemplateListQuery mailTemplateListQuery);

    List<CommonEntity> list();

    boolean delete(List<String> ids);

    boolean saveOrUpdate(SaveOrUpdateMailTemplate saveOrUpdateMailTemplate);

    MailTemplateDetailVo getById(String id);

    List<CommonEntity> getAccountSetList(String fieldTypeId);

    List<CommonEntity> getFieldTypeList();

    JSONObject getCategoryByTemplateId(String templateId);
}
