package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.mapper.ProjectGroupMapper;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.service.mail.AccountSetService;
import cn.sdata.om.al.utils.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AccountSetServiceImpl implements AccountSetService {

    private AccountSetMapper accountSetMapper;

    @Autowired
    public void setAccountSetMapper(AccountSetMapper accountSetMapper) {
        this.accountSetMapper = accountSetMapper;
    }

    @Override
    public List<CommonEntity> list() {
        List<CommonEntity> allProduct = accountSetMapper.list();
        if (CollectionUtil.isEmpty(allProduct)) {
            return List.of();
        }
        ProjectGroupMapper projectGroupMapper = SpringUtil.getBean(ProjectGroupMapper.class);
        List<CommonEntity> list = projectGroupMapper.list();
        List<String> bindGroupProduct = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (CommonEntity commonEntity : list) {
                String extra = commonEntity.getExtra();
                if (StringUtils.isNotBlank(extra)) {
                    bindGroupProduct.addAll(ListUtil.toList(extra.split(",")));
                }
            }
            bindGroupProduct = bindGroupProduct.stream().distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(bindGroupProduct)) {
                Iterator<CommonEntity> iterator = allProduct.iterator();
                while (iterator.hasNext()) {
                    CommonEntity commonEntity = iterator.next();
                    String id = commonEntity.getId();
                    if (bindGroupProduct.contains(id)) {
                        iterator.remove();
                    }
                }
            }
        }
        return allProduct;
    }

    @Override
    public List<CommonEntity> listAll() {
        return accountSetMapper.list();
    }

    @Override
    public List<CommonEntity> productCodeList() {
        List<String> productCodes = accountSetMapper.productCodeList();
        return CommonUtil.stringToCommonEntityList(productCodes);
    }

    @Override
    public List<CommonEntity> accountSetCodeList() {
        List<String> accountSetCodes = accountSetMapper.accountSetCodeList();
        return CommonUtil.stringToCommonEntityList(accountSetCodes);
    }

    @Override
    public List<CommonEntity> accountSetNameList() {
        List<String> accountSetNameList = accountSetMapper.accountSetNameList();
        return CommonUtil.stringToCommonEntityList(accountSetNameList);
    }

    @Override
    public List<CommonEntity> bindGroupList() {
        List<CommonEntity> res = new ArrayList<>();
        List<CommonEntity> allProduct = accountSetMapper.list();
        if (CollectionUtil.isEmpty(allProduct)) {
            return List.of();
        }
        ProjectGroupMapper projectGroupMapper = SpringUtil.getBean(ProjectGroupMapper.class);
        List<CommonEntity> list = projectGroupMapper.list();
        List<String> bindGroupProduct = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (CommonEntity commonEntity : list) {
                String extra = commonEntity.getExtra();
                if (StringUtils.isNotBlank(extra)) {
                    bindGroupProduct.addAll(ListUtil.toList(extra.split(",")));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(bindGroupProduct)) {
            bindGroupProduct = bindGroupProduct.stream().distinct().collect(Collectors.toList());
            Map<String, List<CommonEntity>> collect =
                    allProduct.stream().collect(Collectors.groupingBy(CommonEntity::getId));
            for (String id : bindGroupProduct) {
                CommonEntity commonEntity = new CommonEntity();
                List<CommonEntity> commonEntities = collect.get(id);
                if (CollectionUtil.isNotEmpty(commonEntities)) {
                    CommonEntity commonEntity1 = commonEntities.get(0);
                    commonEntity.setId(id);
                    commonEntity.setName(commonEntity1.getName());
                    res.add(commonEntity);
                }
            }
        }
        return res;
    }
}
