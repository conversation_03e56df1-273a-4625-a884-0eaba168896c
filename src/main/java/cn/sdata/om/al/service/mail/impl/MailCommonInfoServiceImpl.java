package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.account.AccountSummarizeListQuery;
import cn.sdata.om.al.entity.mail.AccountFundInformation;
import cn.sdata.om.al.entity.mail.MailCommonInfo;
import cn.sdata.om.al.enums.PaymentMethod;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.ProjectGroupMapper;
import cn.sdata.om.al.mapper.mail.MailCommonInfoMapper;
import cn.sdata.om.al.service.mail.MailCommonInfoService;
import cn.sdata.om.al.service.mail.MailContactsService;
import cn.sdata.om.al.utils.CommonUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MailCommonInfoServiceImpl implements MailCommonInfoService {

    private MailCommonInfoMapper mailCommonInfoMapper;

    private MailContactsService mailContactsService;

    @Autowired
    public void setMailCommonInfoMapper(MailCommonInfoMapper mailCommonInfoMapper) {
        this.mailCommonInfoMapper = mailCommonInfoMapper;
    }

    @Autowired
    public void setMailContactsService(MailContactsService mailContactsService) {
        this.mailContactsService = mailContactsService;
    }

    @Override
    public PageInfo<MailCommonInfo> page(AccountSummarizeListQuery accountSummarizeListQuery) {
        int pageNo = accountSummarizeListQuery.getPageNo();
        int pageSize = accountSummarizeListQuery.getPageSize();
        List<CommonEntity> allInvestor = mailContactsService.getAllInvestor();
        List<CommonEntity> allThreePartyOrganization = mailContactsService.getAllThreePartyOrganization();
        ProjectGroupMapper projectGroupMapper = SpringUtil.getBean(ProjectGroupMapper.class);
        List<CommonEntity> groupInfo = projectGroupMapper.list();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<MailCommonInfo> page = mailCommonInfoMapper.page(accountSummarizeListQuery);
            if (CollectionUtil.isNotEmpty(page)) {
                for (MailCommonInfo mailCommonInfo : page) {
                    MailCommonInfo commonInfo = mailCommonInfoMapper.getById(mailCommonInfo.getId());
                    String investor = commonInfo.getInvestor();
                    String threePartyOrganization = commonInfo.getThreePartyOrganization();
                    if (StringUtils.isNotBlank(threePartyOrganization)) {
                        if (CollectionUtil.isNotEmpty(allThreePartyOrganization)) {
                            StringBuilder sb = combineInfo(allThreePartyOrganization, threePartyOrganization);
                            mailCommonInfo.setThreePartyOrganization(sb.deleteCharAt(sb.length() - 1).toString());
                        }
                    }
                    if (StringUtils.isNotBlank(investor)) {
                        StringBuilder sb = combineInfo(allInvestor, investor);
                        mailCommonInfo.setInvestor(sb.deleteCharAt(sb.length() - 1).toString());
                    }
                    String id = mailCommonInfo.getId();
                    if (CollectionUtil.isNotEmpty(groupInfo)) {
                        Optional<CommonEntity> first = groupInfo.stream().filter(n -> {
                            String extra = n.getExtra();
                            if (StringUtils.isNotBlank(extra)) {
                                List<String> list = ListUtil.toList(extra.split(","));
                                return list.contains(id);
                            }
                            return false;
                        }).findFirst();
                        if (first.isPresent()) {
                            CommonEntity commonEntity = first.get();
                            mailCommonInfo.setGroupName(commonEntity.getName());
                        }
                    }
                }
            }
            return new PageInfo<>(page);
        }
    }

    private StringBuilder combineInfo(List<CommonEntity> allInvestor, String investor) {
        StringBuilder sb = new StringBuilder();
        Map<String, String> allInvestorMap =
                allInvestor.stream().collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
        for (String oId : investor.split(",")) {
            sb.append(allInvestorMap.get(oId)).append(",");
        }
        return sb;
    }

    @Override
    public boolean saveOrUpdate(MailCommonInfo mailCommonInfo) {
        String id = mailCommonInfo.getId();
        // MailCommonInfo exist = mailCommonInfoMapper.getById(id);
        int addFlag = mailCommonInfo.getAddFlag();
        int res = 0;
        try {
            if (addFlag == 0) {
                // 修改
                res = mailCommonInfoMapper.update(mailCommonInfo);
                List<String> investorIds = mailCommonInfo.getInvestorIds();
                List<String> threePartyOrganizationIds = mailCommonInfo.getThreePartyOrganizationIds();
                mailCommonInfoMapper.deleteProductAndInvestorIdsRelation(id);
                mailCommonInfoMapper.deleteProductAndThreePartyOrganizationIdsRelation(id);
                if (CollectionUtil.isNotEmpty(investorIds)) {
                    List<CommonEntity> commonEntities = new ArrayList<>();
                    for (String investorId : investorIds) {
                        CommonEntity commonEntity = new CommonEntity();
                        commonEntity.setId(IdUtil.getSnowflakeNextIdStr());
                        commonEntity.setName(id);
                        commonEntity.setExtra(investorId);
                        commonEntities.add(commonEntity);
                    }
                    if (CollectionUtil.isNotEmpty(commonEntities)) {
                        mailCommonInfoMapper.saveProductAndInvestorIdsRelation(commonEntities);
                    }
                }
                if (CollectionUtil.isNotEmpty(threePartyOrganizationIds)) {
                    List<CommonEntity> commonEntities = new ArrayList<>();
                    for (String threePartyOrganizationId : threePartyOrganizationIds) {
                        CommonEntity commonEntity = new CommonEntity();
                        commonEntity.setId(IdUtil.getSnowflakeNextIdStr());
                        commonEntity.setName(id);
                        commonEntity.setExtra(threePartyOrganizationId);
                        commonEntities.add(commonEntity);
                    }
                    if (CollectionUtil.isNotEmpty(commonEntities)) {
                        mailCommonInfoMapper.saveThreePartyOrganizationIdsRelation(commonEntities);
                    }
                }
            } else {
                // 新增
                res = mailCommonInfoMapper.save(mailCommonInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            String message = e.getMessage();
            if (message.contains("Duplicate entry")) {
                if (message.contains("'PRIMARY'")) {
                    BusinessException.throwException(400, "账套编号不能重复");
                } else {
                    BusinessException.throwException(400, "账套名称不能重复");
                }
            }
        }
        return res > 0;
    }

    @Override
    public MailCommonInfo getById(String id) {
        MailCommonInfo mailCommonInfo = mailCommonInfoMapper.getById(id);
        String investor = mailCommonInfo.getInvestor();
        String threePartyOrganization = mailCommonInfo.getThreePartyOrganization();
        if (StringUtils.isNotBlank(investor)) {
            mailCommonInfo.setInvestorIds(CollectionUtil.newArrayList(investor.split(",")));
        }
        if (StringUtils.isNotBlank(threePartyOrganization)) {
            mailCommonInfo.setThreePartyOrganizationIds(CollectionUtil.newArrayList(threePartyOrganization.split(",")));
        }
        return mailCommonInfo;
    }

    @Override
    public List<CommonEntity> list() {
        return mailCommonInfoMapper.list();
    }

    @Override
    public PageInfo<AccountFundInformation> fundInfoPage(int pageNo, int pageSize, List<String> productIds, List<String> admins, String type) {
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            return new PageInfo<>(mailCommonInfoMapper.fundInfoPage(productIds, admins, type));
        }
    }

    @Override
    public boolean fundInfoSaveOrUpdate(AccountFundInformation accountFundInformation) {
        String id = accountFundInformation.getId();
        int res;
        if (StringUtils.isNotBlank(id)) {
            // 修改
            res = mailCommonInfoMapper.funInfoUpdate(accountFundInformation);
        } else {
            // 新增
            id = IdUtil.getSnowflakeNextIdStr();
            accountFundInformation.setId(id);
            res = mailCommonInfoMapper.fundInfoSave(accountFundInformation);
        }
        return res > 0;
    }

    @Override
    public boolean fundInfoBatchDelete(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            BusinessException.throwException("ids为null");
        }
        return mailCommonInfoMapper.fundInfoBatchDelete(ids) > 0;
    }

    @Override
    public AccountFundInformation fundInfoGetById(String fundInfoId) {
        return mailCommonInfoMapper.fundInfoGetById(fundInfoId);
    }

    @Override
    public Boolean importAccountSetInfo(MultipartFile file) {
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), 0);
            List<List<Object>> list = excelReader.read(3);
            List<MailCommonInfo> mailCommonInfos = new ArrayList<>();
            for (List<Object> row : list) {
                MailCommonInfo commonInfo = new MailCommonInfo();
                // 序号
                Object o = row.get(0);
                // 账套信息（产品全称）
                Object o1 = row.get(1);
                commonInfo.setFullProductName(String.valueOf(o1));
                // 账套编号
                Object o2 = row.get(2);
                commonInfo.setId(String.valueOf(o2));
                // 产品代码
                Object o3 = row.get(3);
                commonInfo.setProductCode(String.valueOf(o3));
                // 账套类型
                Object o4 = row.get(4);
                String o4Str = String.valueOf(o4);
                int o4Int = 0;
                if ("委受托".equals(o4Str)) {
                    o4Int = 1;
                } else if ("债权投资计划".equals(o4Str)) {
                    o4Int = 2;
                } else if ("组合产品".equals(o4Str)) {
                    // 组合产品
                    o4Int = 3;
                }
                commonInfo.setProductCategory(o4Int);
                // 估值时间
                Object o5 = row.get(5);
                if (o5 != null) {
                    String o5Str = String.valueOf(o5);
                    commonInfo.setValuationTime(o5Str.substring(0, 2));
                }
                // 是否结构化
                Object o6 = row.get(6);
                if (o6 != null) {
                    String o6Str = String.valueOf(o6);
                    commonInfo.setStructuredIs("是".equals(o6Str) ? 1 : 0);
                }
                // 是否电子直连
                Object o7 = row.get(7);
                if (o7 != null) {
                    String o7Str = String.valueOf(o7);
                    commonInfo.setElectronicDirectConnectionIs("是".equals(o7Str) ? 1 : 0);
                }
                // 是否为货币
                Object o8 = row.get(8);
                if (o8 != null) {
                    String o8Str = String.valueOf(o8);
                    commonInfo.setCurrencyIs("是".equals(o8Str) ? 1 : 0);
                }
                // 账套状态
                Object o9 = row.get(9);
                if (o9 != null) {
                    String o9Str = String.valueOf(o9);
                    commonInfo.setProductStatus(null);
                }
                // 成立日期
                Object o10 = row.get(10);
                if (o10 != null) {
                    String o10Str = String.valueOf(o10);
                    commonInfo.setEstablishmentDate(o10Str);
                }
                // 托管行
                Object o11 = row.get(11);
                commonInfo.setCustodianBank(String.valueOf(o11));
                // 二次估值的证券类型
                Object o12 = row.get(12);
                commonInfo.setSecondaryValuationSecurityType(String.valueOf(o12));
                // 账户名称
                Object o13 = row.get(13);
                commonInfo.setAccountName(String.valueOf(o13));
                // 账号
                Object o14 = row.get(14);
                commonInfo.setAccountNumberDetail(String.valueOf(o14));
                // 开户行
                Object o15 = row.get(15);
                commonInfo.setOpeningBank(String.valueOf(o15));
                // 大额支付号
                Object o16 = row.get(16);
                commonInfo.setLargePaymentNumber(String.valueOf(o16));
                // 中债扣收方式
                Object o17 = row.get(17);
                if (o17 != null) {
                    String o17Str = String.valueOf(o17);
                    if ("自动扣收".equals(o17Str.trim())) {
                        commonInfo.setPaymentMethodZZ(PaymentMethod.AUTO_PAYMENT.name());
                    } else if ("手工支付".equals(o17Str.trim())) {
                        commonInfo.setPaymentMethodZZ(PaymentMethod.MANUAL_PAYMENT.name());
                    }
                }
                // 中债（托管账号）
                Object o18 = row.get(18);
                commonInfo.setCentralDebtAccountNumber(String.valueOf(o18));
                // 外汇扣收方式
                Object o19 = row.get(19);
                if (o19 != null) {
                    String o19Str = String.valueOf(o19);
                    if ("自动扣收".equals(o19Str.trim())) {
                        commonInfo.setPaymentMethodWH(PaymentMethod.AUTO_PAYMENT.name());
                    } else if ("手工支付".equals(o19Str.trim())) {
                        commonInfo.setPaymentMethodWH(PaymentMethod.MANUAL_PAYMENT.name());
                    }
                }
                // 外汇交易中心（会员代码）
                Object o20 = row.get(20);
                commonInfo.setForeignExchangeCenterMemberCode(String.valueOf(o20));
                // 上清扣收方式
                Object o21 = row.get(21);
                if (o21 != null) {
                    String o21Str = String.valueOf(o21);
                    if ("自动扣收".equals(o21Str.trim())) {
                        commonInfo.setPaymentMethodSQ(PaymentMethod.AUTO_PAYMENT.name());
                    } else if ("手工支付".equals(o21Str.trim())) {
                        commonInfo.setPaymentMethodSQ(PaymentMethod.MANUAL_PAYMENT.name());
                    }
                }
                // 上清（持有人账号）
                Object o22 = row.get(22);
                commonInfo.setClearingHouseHolderAccount(String.valueOf(o22));
                // 持有人账号
                Object o23 = row.get(23);
                commonInfo.setHolderAccountNumber(String.valueOf(o23));
                // 持有人账户全称
                Object o24 = row.get(24);
                commonInfo.setHolderAccountFullName(String.valueOf(o24));
                mailCommonInfos.add(commonInfo);
            }
            if (CollectionUtil.isNotEmpty(mailCommonInfos)) {
                mailCommonInfoMapper.saveBatch(mailCommonInfos);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public Boolean importFundInfo(MultipartFile file) {
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream(), 0);
            List<List<Object>> list = excelReader.read(1);
            List<AccountFundInformation> accountFundInformations = new ArrayList<>();
            for (List<Object> row : list) {
                AccountFundInformation accountFundInformation = new AccountFundInformation();
                accountFundInformation.setId(IdUtil.getSnowflakeNextIdStr());
                // 渠道类型
                Object o1 = row.get(1);
                if (o1 != null) {
                    String o1Str = (String) o1;
                    accountFundInformation.setChannelType(o1Str.equals("直销") ? 1 : 0);
                }
                // 管理人
                Object o2 = row.get(2);
                accountFundInformation.setAdministrator(String.valueOf(o2));
                // 户名
                Object o3 = row.get(3);
                accountFundInformation.setAccountName(String.valueOf(o3));
                // 账号
                Object o4 = row.get(4);
                accountFundInformation.setAccountNumber(String.valueOf(o4));
                // 邮件后缀
                Object o5 = row.get(5);
                accountFundInformation.setEmailSuffix(String.valueOf(o5));
                // 账套编号
                Object o7 = row.get(7);
                accountFundInformation.setProductId(String.valueOf(o7));
                accountFundInformations.add(accountFundInformation);
            }
            if (CollectionUtil.isNotEmpty(accountFundInformations)) {
                mailCommonInfoMapper.saveBatchFundInfo(accountFundInformations);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public List<CommonEntity> fundAdminList() {
        List<String> admins = mailCommonInfoMapper.fundAdminList();
        return CommonUtil.stringToCommonEntityList(admins);
    }
}
