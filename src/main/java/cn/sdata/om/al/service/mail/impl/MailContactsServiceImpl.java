package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.MailContactsDetail;
import cn.sdata.om.al.entity.mail.params.ContactsAndAccountSetRelation;
import cn.sdata.om.al.entity.mail.params.MailContactsListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailContacts;
import cn.sdata.om.al.entity.mail.vo.MailContactsListVo;
import cn.sdata.om.al.entity.mail.vo.MailContactsVo;
import cn.sdata.om.al.enums.MailContactsType;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.mapper.mail.MailContactsMapper;
import cn.sdata.om.al.qrtz.entity.Cron;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.CustodianBankContactsService;
import cn.sdata.om.al.service.MailContactsDetailService;
import cn.sdata.om.al.service.mail.MailContactsService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class MailContactsServiceImpl implements MailContactsService {

    private MailContactsMapper mailContactsMapper;

    private AccountSetMapper accountSetMapper;

    private MailContactsDetailService mailContactsDetailService;

    private CronService cronService;

    private CustodianBankContactsService custodianBankContactsService;

    @Autowired
    public void setMailContactsMapper(MailContactsMapper mailContactsMapper) {
        this.mailContactsMapper = mailContactsMapper;
    }

    @Autowired
    public void setAccountSetMapper(AccountSetMapper accountSetMapper) {
        this.accountSetMapper = accountSetMapper;
    }

    @Autowired
    public void setMailContactsDetailService(MailContactsDetailService mailContactsDetailService) {
        this.mailContactsDetailService = mailContactsDetailService;
    }

    @Autowired
    public void setCronService(CronService cronService) {
        this.cronService = cronService;
    }

    @Override
    public PageInfo<MailContactsListVo> page(MailContactsListQuery mailContactsListQuery) {
        int pageNo = mailContactsListQuery.getPageNo();
        int pageSize = mailContactsListQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            return new PageInfo<>(mailContactsMapper.page(mailContactsListQuery));
        }
    }

    @Override
    public List<CommonEntity> list() {
        return mailContactsMapper.list();
    }

    @Override
    public boolean delete(List<String> ids) {
        // 删除 通讯录与任务关系
        mailContactsMapper.batchDeleteContactsAndTaskRelation(ids);
        // 删除 通讯录与账套关系
        mailContactsMapper.batchDeleteContactsAndAccountSetRelation(ids);
        return mailContactsMapper.delete(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(SaveOrUpdateMailContacts saveOrUpdateMailContacts) {
        String id = saveOrUpdateMailContacts.getId();
        int res;
        if (StringUtils.isNotBlank(id)) {
            // 修改
            saveOrUpdateMailContacts.setUpdateByName(SecureUtil.currentUser().getAccount());
            res = mailContactsMapper.update(saveOrUpdateMailContacts);
            saveDetail(saveOrUpdateMailContacts.getValues(), saveOrUpdateMailContacts.getId(), saveOrUpdateMailContacts.getType());
        } else {
            // 新增
            id = IdUtil.getSnowflakeNextIdStr();
            saveOrUpdateMailContacts.setId(id);
            saveOrUpdateMailContacts.setCreateByName(SecureUtil.currentUser().getAccount());
            res = mailContactsMapper.save(saveOrUpdateMailContacts);
            saveDetail(saveOrUpdateMailContacts.getValues(), saveOrUpdateMailContacts.getId(), saveOrUpdateMailContacts.getType());
        }
        return res > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveDetail(List<String> values, String contactId, String type) {
        List<MailContactsDetail> mailContactsDetails = new ArrayList<>();
        if (values != null && !values.isEmpty()) {
            for (String value : values) {
                MailContactsDetail mailContactsDetail = new MailContactsDetail();
                mailContactsDetail.setContactsId(contactId);
                mailContactsDetail.setSplitType(type);
                mailContactsDetail.setValue(value);
                mailContactsDetails.add(mailContactsDetail);
            }
        }
        if (!mailContactsDetails.isEmpty()) {
            LambdaQueryWrapper<MailContactsDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MailContactsDetail::getContactsId, contactId);
            mailContactsDetailService.remove(queryWrapper);
            mailContactsDetailService.saveBatch(mailContactsDetails);
        }
    }

    /**
     * 组装新增对象
     *
     * @param accountSetNames 账套对象
     * @param id              通讯录id
     * @return 新增对象
     */
    private static List<ContactsAndAccountSetRelation> getContactsAndAccountSetRelations(List<String> accountSetNames, String id) {
        List<ContactsAndAccountSetRelation> contactsAndAccountSetRelations = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(accountSetNames)) {
            for (String accountSetName : accountSetNames) {
                ContactsAndAccountSetRelation contactsAndAccountSetRelation = new ContactsAndAccountSetRelation();
                contactsAndAccountSetRelation.setId(IdUtil.getSnowflakeNextIdStr());
                contactsAndAccountSetRelation.setContactsId(id);
                contactsAndAccountSetRelation.setAccountSetId(accountSetName);
                contactsAndAccountSetRelations.add(contactsAndAccountSetRelation);
            }
            return contactsAndAccountSetRelations;
        }
        return null;
    }

    @Override
    public MailContactsVo getById(String id) {
        return mailContactsMapper.getById(id);
    }

    @Override
    public List<CommonEntity> getAccountSetByTemplateId(String templateId) {
        List<CommonEntity> commonEntities = accountSetMapper.list();
        List<String> allAccountSetIds = mailContactsMapper.getAccountSetByTemplateId(templateId);
        allAccountSetIds = allAccountSetIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(allAccountSetIds)) {
            Map<String, String> collect =
                    commonEntities.stream().collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
            List<CommonEntity> res = new ArrayList<>();
            for (String accountSetId : allAccountSetIds) {
                CommonEntity commonEntity = new CommonEntity();
                commonEntity.setId(accountSetId);
                commonEntity.setName(collect.get(accountSetId));
                res.add(commonEntity);
            }
            return res;
        }
        return List.of();
    }

    @Override
    public List<CommonEntity> getAllRecipient() {
        List<String> list = mailContactsMapper.getAllRecipient();
        List<String> recipientList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (String str : list) {
                String[] split = str.split(";");
                recipientList.addAll(Arrays.asList(split));
            }
        }
        recipientList = recipientList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(recipientList)) {
            List<CommonEntity> res = new ArrayList<>();
            for (String str : recipientList) {
                CommonEntity commonEntity = new CommonEntity();
                commonEntity.setId(str);
                commonEntity.setName(str);
                res.add(commonEntity);
            }
            return res;
        }
        return List.of();
    }

    @Override
    public List<CommonEntity> getMailContactsType() {
        return mailContactsMapper.getMailContactsType();
    }

    @Override
    public List<CommonEntity> getMailContactsCategoryByType(String typeId) {
        return mailContactsMapper.getMailContactsCategoryByType(typeId);
    }

    @Override
    public List<CommonEntity> getAllThreePartyOrganization() {
        return mailContactsMapper.getMailContactsCategoryByType("3");
    }

    @Override
    public List<CommonEntity> getAllInvestor() {
        return mailContactsMapper.getMailContactsCategoryByType("2");
    }

    @Override
    public List<CommonEntity> getAccountSetByIds(List<String> accountSetIds) {
        return mailContactsMapper.getAccountSetByIds(accountSetIds);
    }

    @Override
    public List<String> getMailContactsByAccountSetIdsAndTemplateId(List<String> accountSetIds, String templateId) {
        return mailContactsMapper.getMailContactsByAccountSetIdsAndTemplateId(accountSetIds, templateId);
    }

    @Override
    public List<String> getTypeByTaskId(String taskId) {
        Cron cron = cronService.getById(taskId);
        Objects.requireNonNull(cron, "任务不存在");
        String contactType = cron.getContactType();
        if (contactType == null || contactType.isEmpty()) {
            return List.of();
        }
        MailContactsType mailContactsType = MailContactsType.valueOf(contactType);
        Objects.requireNonNull(mailContactsType, "任务缺少通讯录类型,需要手动配置");
        return mailContactsType.getMailSplitTypes().stream().map(Enum::name).collect(Collectors.toList());
    }


    /**
     * 组装分页信息
     *
     * @param queryList 查询的结果
     * @param total     总数
     * @param pageNo    第几页
     * @param pageSize  每页几条
     * @param <T>       泛型
     * @return 分页对象
     */
    private <T> PageInfo<T> handlePage(List<T> queryList, int total, int pageNo, int pageSize) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setTotal(total);
        pageInfo.setList(CollectionUtil.isNotEmpty(queryList) ? queryList : new ArrayList<>());
        if (total % pageSize == 0) {
            pageInfo.setPages(total / pageSize);
        } else {
            pageInfo.setPages(total / pageSize + 1);
        }
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);
        int pages = pageInfo.getPages();
        pageInfo.setIsLastPage(pageNo == pages || pages == 0);
        return pageInfo;
    }
}
