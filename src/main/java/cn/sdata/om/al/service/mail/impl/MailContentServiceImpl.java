package cn.sdata.om.al.service.mail.impl;

import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.service.mail.MailContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MailContentServiceImpl implements MailContentService {

    private MailContentMapper mailContentMapper;

    @Autowired
    public void setMailContentMapper(MailContentMapper mailContentMapper) {
        this.mailContentMapper = mailContentMapper;
    }

    @Override
    public int getMaxNumber() {
        return mailContentMapper.getMaxNumber();
    }

    @Override
    public void save(MailContent mailContent) {
        mailContentMapper.save(mailContent);
    }
}
