package cn.sdata.om.al.service.mail.impl;

import cn.sdata.om.al.entity.mail.params.MailLogListQuery;
import cn.sdata.om.al.entity.mail.vo.MailDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailLogListVo;
import cn.sdata.om.al.mapper.mail.MailInfoMapper;
import cn.sdata.om.al.mapper.mail.MailLogMapper;
import cn.sdata.om.al.service.mail.MailLogService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MailLogServiceImpl implements MailLogService {

    private MailLogMapper mailLogMapper;

    private MailInfoMapper mailInfoMapper;

    @Autowired
    public void setMailLogMapper(MailLogMapper mailLogMapper) {
        this.mailLogMapper = mailLogMapper;
    }

    @Autowired
    public void setMailInfoMapper(MailInfoMapper mailInfoMapper) {
        this.mailInfoMapper = mailInfoMapper;
    }

    @Override
    public PageInfo<MailLogListVo> page(MailLogListQuery mailLogListQuery) {
        int pageNo = mailLogListQuery.getPageNo();
        int pageSize = mailLogListQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            return new PageInfo<>(mailLogMapper.page(mailLogListQuery));
        }
    }

    @Override
    public MailDetailVo detail(String mailId) {
        return mailInfoMapper.getById(mailId);
    }
}
