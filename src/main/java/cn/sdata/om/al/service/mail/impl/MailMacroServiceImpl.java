package cn.sdata.om.al.service.mail.impl;

import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailMacroMap;
import cn.sdata.om.al.mapper.mail.MailMacroMapper;
import cn.sdata.om.al.service.mail.MailMacroService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MailMacroServiceImpl implements MailMacroService {

    private MailMacroMapper mailMacroMapper;

    @Autowired
    public void setMailMacroMapper(MailMacroMapper mailMacroMapper) {
        this.mailMacroMapper = mailMacroMapper;
    }

    @Override
    public List<CommonEntity> list(String moduleName) {
        return mailMacroMapper.list(moduleName);
    }

    @Override
    public List<MailMacroMap> page(String moduleName) {
        return mailMacroMapper.page(moduleName);
    }
}
