package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.util.IdUtil;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickBasket;
import cn.sdata.om.al.entity.mail.vo.MailPickBasketVo;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.mail.MailPickBasketMapper;
import cn.sdata.om.al.service.mail.MailPickBasketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class MailPickBasketServiceImpl implements MailPickBasketService {

    private MailPickBasketMapper mailPickBasketMapper;

    @Autowired
    public void setMailPickBasketMapper(MailPickBasketMapper mailPickBasketMapper) {
        this.mailPickBasketMapper = mailPickBasketMapper;
    }

    @Override
    public Boolean saveOrUpdate(SaveOrUpdateMailPickBasket saveOrUpdateMailPickBasket) {
        // 新增邮件筐
        String ruleId = saveOrUpdateMailPickBasket.getRuleId();
        String basketId = saveOrUpdateMailPickBasket.getId();
        /*if (StringUtils.isEmpty(ruleId)) {
            BusinessException.throwException("没有选择规则");
        }*/
        if (StringUtils.isEmpty(ruleId)) {
            saveOrUpdateMailPickBasket.setRuleId(null);
        }
        try {
            if (StringUtils.isNotBlank(basketId)) {
                return mailPickBasketMapper.update(saveOrUpdateMailPickBasket) > 0;
            } else {
                int orderBy = mailPickBasketMapper.getMaxOrderBy();
                basketId = IdUtil.getSnowflakeNextIdStr();
                saveOrUpdateMailPickBasket.setId(basketId);
                saveOrUpdateMailPickBasket.setBasketOrderBy(orderBy + 1);
                return mailPickBasketMapper.save(saveOrUpdateMailPickBasket) > 0;
            }
        } catch (Exception e) {
            String message = e.getMessage();
            if (message.contains("Duplicate entry") && message.contains("for key 'name'")) {
                log.error(message);
                BusinessException.throwException("邮件筐名称重复");
            }
            if (message.contains("Duplicate entry") && message.contains("for key 'rule_id'")) {
                log.error(message);
                BusinessException.throwException("规则已经绑定了");
            }
        }
        return false;
    }

    @Override
    public List<MailPickBasketVo> list() {
        return mailPickBasketMapper.list();
    }

    @Override
    public Boolean delete(String id) {
        int delete = mailPickBasketMapper.delete(id);
        if (delete > 0) {
            // 分拣筐删除后，在这个分拣筐下的邮件变为未分拣状态
            mailPickBasketMapper.updateMailToUnPick(id);
        }
        return delete > 0;
    }

    @Override
    public int selectPickBasketCount(String id) {
        return mailPickBasketMapper.selectPickBasketCount(id);
    }

    @Override
    public MailPickBasketVo getById(String id) {
        return mailPickBasketMapper.getById(id);
    }

    @Override
    public int selectPickBasketCountByConditions(String id, MailContentListQuery contentListQuery) {
        return mailPickBasketMapper.selectPickBasketCountByConditions(id, contentListQuery);
    }
}
