package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.constant.MailFilterConditionEnum;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.MailFilterRuleDTO;
import cn.sdata.om.al.entity.mail.MailPickRuleCondition;
import cn.sdata.om.al.entity.mail.params.MailPickRuleListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailPickRule;
import cn.sdata.om.al.entity.mail.vo.MailPickBasketVo;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleListVo;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.mapper.mail.MailPickBasketMapper;
import cn.sdata.om.al.mapper.mail.MailPickRuleMapper;
import cn.sdata.om.al.service.mail.MailPickRuleService;
import cn.sdata.om.al.service.mail.MailPickService;
import cn.sdata.om.al.utils.SecureUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MailPickRuleServiceImpl implements MailPickRuleService {

    private MailPickRuleMapper mailPickRuleMapper;

    private static volatile boolean isSyncMailRule;

    @Autowired
    public void setMailPickRuleMapper(MailPickRuleMapper mailPickRuleMapper) {
        this.mailPickRuleMapper = mailPickRuleMapper;
    }

    @Override
    public PageInfo<MailPickRuleListVo> page(MailPickRuleListQuery mailPickRuleListQuery) {
        int pageNo = mailPickRuleListQuery.getPageNo();
        int pageSize = mailPickRuleListQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            return new PageInfo<>(mailPickRuleMapper.page(mailPickRuleListQuery));
        }
    }

    @Override
    public boolean saveOrUpdate(SaveOrUpdateMailPickRule saveOrUpdateMailPickRule) {
        MailPickBasketMapper mailPickBasketMapper = SpringUtil.getBean(MailPickBasketMapper.class);
        String id = saveOrUpdateMailPickRule.getId();
        String pickBasket = saveOrUpdateMailPickRule.getPickBasket();
        if (StringUtils.isNotBlank(pickBasket)) {
            MailPickBasketVo mailPickBasketVo = mailPickBasketMapper.getById(pickBasket);
            if (mailPickBasketVo != null){
                String ruleId = mailPickBasketVo.getRuleId();
                if (StringUtils.isNotBlank(ruleId) && !ruleId.equals(id)){
                    BusinessException.throwException("规则已经绑定了");
                }
            }
        }
        String executeType = saveOrUpdateMailPickRule.getExecuteType();
        saveOrUpdateMailPickRule.setCreateBy(SecureUtil.currentUser().getAccount());
        saveOrUpdateMailPickRule.setUpdateBy(SecureUtil.currentUser().getAccount());
        int res = 0;
        List<MailFilterRuleDTO> executeConditions = saveOrUpdateMailPickRule.getExecuteConditions();
        if (CollectionUtil.isNotEmpty(executeConditions)) {
            String expression = this.ruleToExpression(executeConditions, executeType);
            log.info("规则表达式为:{}", expression);
            saveOrUpdateMailPickRule.setRuleExpression(expression);
        }
        // 重新计算规则表达式
        try {
            if (StringUtils.isNotBlank(id)) {
                // 删除条件
                mailPickRuleMapper.deleteConditionsByRuleId(id);
                res = mailPickRuleMapper.update(saveOrUpdateMailPickRule);
            } else {
                id = IdUtil.getSnowflakeNextIdStr();
                saveOrUpdateMailPickRule.setId(id);
                res = mailPickRuleMapper.save(saveOrUpdateMailPickRule);
            }
            // 修改此分拣筐对应的规则id
            if (StringUtils.isBlank(pickBasket)) {
                // 说明此规则不绑定任何分拣筐
                mailPickBasketMapper.updateRuleIdToNull(id);
            } else {
                // 需要将此分拣筐绑定此规则
                mailPickBasketMapper.bindRule(id, pickBasket);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            log.error(message);
            if (message.contains("Duplicate entry")) {
                BusinessException.throwException("邮件分拣规则名称重复");
            }
        }
        if (CollectionUtil.isNotEmpty(executeConditions)) {
            List<MailPickRuleCondition> mailPickRuleConditions = new ArrayList<>();
            for (MailFilterRuleDTO mailFilterRuleDTO : executeConditions) {
                MailPickRuleCondition condition = new MailPickRuleCondition();
                condition.setRuleId(id);
                condition.setId(IdUtil.getSnowflakeNextIdStr());
                condition.setConditionKey(mailFilterRuleDTO.getTarget());
                condition.setConditionOperate(mailFilterRuleDTO.getCondition());
                condition.setConditionValue(String.join(",", mailFilterRuleDTO.getContentList()));
                condition.setType(mailFilterRuleDTO.getType());
                mailPickRuleConditions.add(condition);
            }
            mailPickRuleMapper.batchSaveConditions(mailPickRuleConditions);
        }
        synchronized (MailPickRuleServiceImpl.class) {
            if (!isSyncMailRule) {
                CompletableFuture.runAsync(() -> {
                    log.info("******开始邮件分拣规则执行所有邮件规则的重新匹配...");
                    isSyncMailRule = true;
                    // 规则新增后 重新匹配规则
                    // 此处需要查询所有邮件进行重新规则匹配
                    List<MailContent> mailContents = SpringUtil.getBean(MailContentMapper.class).selectAllPickMail();
                    SpringUtil.getBean(MailPickService.class).pickMail(mailContents);
                    log.info("------结束邮件分拣规则执行所有邮件规则的重新匹配...");
                    isSyncMailRule = false;
                });
            }
        }
        return res > 0;
    }

    @Override
    public MailPickRuleDetail getById(String id) {
        MailPickRuleDetail pickRuleDetail = mailPickRuleMapper.getById(id);
        List<MailFilterRuleDTO> executeConditions = pickRuleDetail.getExecuteConditions();
        if (CollectionUtil.isNotEmpty(executeConditions)) {
            executeConditions = executeConditions.stream().peek(n -> {
                String contentListStr = n.getContentListStr();
                if (StringUtils.isNotBlank(contentListStr)) {
                    n.setContentList(CollectionUtil.newArrayList(contentListStr.split(",")));
                }
            }).collect(Collectors.toList());
            pickRuleDetail.setExecuteConditions(executeConditions);
        }
        return pickRuleDetail;
    }

    @Override
    public List<CommonEntity> list() {
        return mailPickRuleMapper.list();
    }

    @Override
    public boolean delete(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return false;
        }
        // 如果此规则已经有分拣筐绑定 则不允许删除
        List<MailPickBasketVo> list = SpringUtil.getBean(MailPickBasketMapper.class).list();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> bindRuleIds = list.stream().map(MailPickBasketVo::getRuleId).filter(StringUtils::isNoneBlank).collect(Collectors.toList());
            // 如果已绑定的规则id与要删除的规则id有重合的话不能删除
            Collection<String> intersection = CollectionUtil.intersection(bindRuleIds, ids);
            if (CollectionUtil.isNotEmpty(intersection)) {
                BusinessException.throwException("规则已经绑定了分拣筐不能删除");
            }
        }
        mailPickRuleMapper.batchDeleteConditionsByRuleId(ids);
        // 将分拣筐的ruleId置为null
        mailPickRuleMapper.updateBasketRuleToNull(ids);
        return mailPickRuleMapper.delete(ids);
    }

    @Override
    public List<MailPickRuleDetail> doFilter(MailContent mailContent, List<MailPickRuleDetail> mailPickRules) {
        List<MailPickRuleDetail> result = new ArrayList<>();
        Map<String, Object> contentMap = contentToMap(mailContent);
        for (MailPickRuleDetail mailPickRuleDetail : mailPickRules) {
            String expression = mailPickRuleDetail.getRuleExpression();
            List<MailFilterRuleDTO> list = mailPickRuleDetail.getExecuteConditions();
            // 执行类型 0 满足所有条件 1 满足任意条件
            DefaultContext<String, Object> context = new DefaultContext<>();
            int i = 0;
            for (MailFilterRuleDTO dto : list) {
                Object o = contentMap.get(dto.getTarget());
                String value = String.valueOf(o);
                context.put(dto.getTarget(), value);
                List<String> contentList = CollectionUtil.newArrayList(dto.getContentListStr().split(","));
                for (String c : contentList) {
                    context.put("param".concat(String.valueOf(i)), c);
                    i = i + 1;
                }
            }
            ExpressRunner runner = new ExpressRunner();
            // 执行规则引擎
            Object execute;
            try {
                if (!context.isEmpty()) {
                    execute = runner.execute(expression, context, null, true, true);
                    if (Boolean.TRUE.equals(execute)) {
                        result.add(mailPickRuleDetail);
                    }
                }
            } catch (Exception e) {
                log.error("规则引擎执行错误,异常信息为:{}", e.getMessage());
            }
        }
        return result;
    }

    private Map<String, Object> contentToMap(MailContent mailContent) {
        Map<String, Object> objectMap = new HashMap<>();
        // 主题
        objectMap.put("subject", mailContent.getSubject());
        // 内容
        objectMap.put("content", mailContent.getContent());
        // 发送人
        objectMap.put("sender", mailContent.getMailFromStr());
        // 接收人
        objectMap.put("receiver", mailContent.getReceiveStr());
        // 附件名称
        objectMap.put("attachmentName", mailContent.getAttachmentStr());
        return objectMap;
    }

    /**
     * 将配置的规则转换成QLExpression的表达式
     *
     * @return 表达式
     */
    public String ruleToExpression(List<MailFilterRuleDTO> ruleList, String condition) {
        String expression = "";
        if (CollUtil.isEmpty(ruleList)) {
            return expression;
        }
        // 运算关系拼接符
        String conditionStr = MailFilterConditionEnum.getValue(condition);
        // 用来记录参数param参数下标
        int temp = 0;
        for (int i = 0; i < ruleList.size(); i++) {
            String s = "";
            MailFilterRuleDTO rule = ruleList.get(i);
            String target = rule.getTarget().trim();
            String type = rule.getType().trim();
            String ruleCondition = MailFilterConditionEnum.getValue(rule.getCondition());
            int size = rule.getContentList().size();
            for (int j = 0; j < size; j++) {
                String tempExpression;
                if ("include".equals(type) || "exclude".equals(type)) {
                    tempExpression = target.concat(".contains(").concat("param" + temp).concat(")");
                    if ("exclude".equals(type)) {
                        tempExpression = "!".concat(tempExpression);
                    }
                } else {
                    tempExpression = "(".concat(target).concat(type).concat("param" + temp).concat(")");
                }
                temp += 1;
                if (j < size - 1) {
                    tempExpression = tempExpression.concat(ruleCondition);
                }
                s = s.concat(tempExpression);
            }
            if (size > 1) {
                s = "(".concat(s).concat(")");
            }
            if (i < ruleList.size() - 1) {
                s = s.concat(conditionStr);
            }
            expression = expression.concat(s);
        }
        expression = "(".concat(expression).concat(")");
        return expression;
    }

    @Override
    public List<CommonEntity> unbindRuleList() {
        return mailPickRuleMapper.unbindRuleList();
    }
}
