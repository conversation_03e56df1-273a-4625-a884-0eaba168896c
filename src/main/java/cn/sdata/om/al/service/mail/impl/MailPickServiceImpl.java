package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.sdata.om.al.config.MailConfiguration;
import cn.sdata.om.al.entity.OpenFundBasket;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.entity.mail.MailContent;
import cn.sdata.om.al.entity.mail.MailUser;
import cn.sdata.om.al.entity.mail.params.MailContentListQuery;
import cn.sdata.om.al.entity.mail.params.ManualPickParam;
import cn.sdata.om.al.entity.mail.vo.MailContentListVo;
import cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.mail.MailContentMapper;
import cn.sdata.om.al.mapper.mail.MailPickRuleMapper;
import cn.sdata.om.al.service.InsuranceRegistrationFeesService;
import cn.sdata.om.al.service.InterbankFeesService;
import cn.sdata.om.al.open.BaseBoxHandler;
import cn.sdata.om.al.service.OpenFundBasketService;
import cn.sdata.om.al.service.mail.MailContentService;
import cn.sdata.om.al.service.mail.MailPickRuleService;
import cn.sdata.om.al.service.mail.MailPickService;
import cn.sdata.om.al.utils.JavaMailUtils;
import cn.sdata.om.al.utils.OmFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Store;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MailPickServiceImpl implements MailPickService {

    private MailConfiguration mailConfiguration;

    private JavaMailUtils javaMailUtils;

    private MailContentService mailContentService;

    private MailContentMapper mailContentMapper;

    private MailPickRuleMapper mailPickRuleMapper;

    private MailPickRuleService mailPickRuleService;

    private OpenFundBasketService openFundBasketService;

    private ApplicationContext applicationContext;

    @Autowired
    public void setMailPickRuleService(MailPickRuleService mailPickRuleService) {
        this.mailPickRuleService = mailPickRuleService;
    }

    @Autowired
    public void setMailPickRuleMapper(MailPickRuleMapper mailPickRuleMapper) {
        this.mailPickRuleMapper = mailPickRuleMapper;
    }

    @Autowired
    public void setMailContentMapper(MailContentMapper mailContentMapper) {
        this.mailContentMapper = mailContentMapper;
    }

    @Autowired
    public void setMailConfiguration(MailConfiguration mailConfiguration) {
        this.mailConfiguration = mailConfiguration;
    }

    @Autowired
    public void setJavaMailUtils(JavaMailUtils javaMailUtils) {
        this.javaMailUtils = javaMailUtils;
    }

    @Autowired
    public void setMailContentService(MailContentService mailContentService) {
        this.mailContentService = mailContentService;
    }

    @Autowired
    public void setOpenFundBasketService(OpenFundBasketService openFundBasketService) {
        this.openFundBasketService = openFundBasketService;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public Integer getMaxNumber() {
        Store store = null;
        Folder folder = null;
        try {
            // 初始化邮箱连接
            store = mailConfiguration.initEmailConnect();
            // 打开收件箱
            folder = store.getFolder("INBOX");
            folder.open(Folder.READ_ONLY);
            return folder.getMessages().length;
        } catch (MessagingException e) {
            log.error("初始化邮箱失败:", e);
        } finally {
            try {
                if (folder != null) {
                    folder.close();
                }
                if (store != null) {
                    store.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public void readFromMail(Integer messageNumber) {
        log.info("[START]同步读取邮件定时任务开始.邮件读取的时间为:{}", DateUtil.now());
        Store store = null;
        Folder folder = null;
        Message[] messageList = new Message[0];
        try {
            // 初始化邮箱连接
            store = mailConfiguration.initEmailConnect();
            // 打开收件箱
            folder = store.getFolder("INBOX");
            folder.open(Folder.READ_ONLY);
            messageList = folder.getMessages();
        } catch (MessagingException e) {
            log.error("初始化邮箱失败:", e);
        }
        int length = messageList.length;
        int messageNumberFromDb = mailContentService.getMaxNumber();
        if (messageNumberFromDb == 0) {
            messageNumberFromDb = messageNumber;
        }
        log.info("库里最大的messageNumber为{}", messageNumberFromDb);
        if (length <= messageNumberFromDb) {
            log.info("没有新的增量邮件!");
            return;
        }
        // 从邮箱中读取邮件
        synEmail(messageNumberFromDb, length, messageList);
        try {
            // 关闭收件箱和存储
            assert folder != null;
            folder.close(false);
            store.close();
        } catch (MessagingException e) {
            log.error("邮箱关闭失败", e);
        }
        log.info("[END]同步读取邮件定时任务结束.结束时间为:{}", DateUtil.now());
    }

    @Override
    public List<MailContentListVo> list(MailContentListQuery mailContentListQuery) {
        // 0 今天 1 昨天 2 上周 3 两周前 4 上个月 5 更早
        String pickBasket = mailContentListQuery.getPickBasket();
        Integer type = mailContentListQuery.getType();
        String beginReceiveDateParam = mailContentListQuery.getBeginReceiveDate();
        String endReceiveDateParam = mailContentListQuery.getEndReceiveDate();
        boolean isQueryDate = StringUtils.isNotBlank(beginReceiveDateParam) || StringUtils.isNotBlank(endReceiveDateParam);
        if (StringUtils.isBlank(pickBasket)) {
            mailContentListQuery.setPickBasket(null);
        }
        // 获取每一个日期区间
        Tuple date0 = getDate(mailContentListQuery, "0");
        Tuple date1 = getDate(mailContentListQuery, "1");
        //Tuple date2 = getDate(mailContentListQuery, "2");
        //Tuple date3 = getDate(mailContentListQuery, "3");
        //Tuple date4 = getDate(mailContentListQuery, "4");
        Tuple date5 = getDate(mailContentListQuery, "5");
        if (isQueryDate) {
            // 如果页面传了时间范围，则按照时间范围进行查询
            List<MailContentListVo> res = new ArrayList<>();
            mailContentListQuery.setBeginReceiveDate(beginReceiveDateParam);
            mailContentListQuery.setEndReceiveDate(endReceiveDateParam);
            List<MailContentListVo> mailContentListVos = mailContentMapper.list(mailContentListQuery);
            if (CollectionUtil.isNotEmpty(mailContentListVos)) {
                return mailContentListVos.stream().peek(n -> {
                    String receiveDate = n.getReceiveDate();
                    if (DateUtil.parseDate(receiveDate).equals(DateUtil.parseDate(date0.get(0)))) {
                        // 当天
                        n.setType(0);
                    } else if (DateUtil.parseDate(receiveDate).equals(DateUtil.parseDate(date1.get(0)))) {
                        // 昨天
                        n.setType(1);
                    } else if (DateUtil.parseDate(receiveDate).isAfterOrEquals(DateUtil.parseDate(date5.get(0))) && DateUtil.parseDate(receiveDate).isBeforeOrEquals(DateUtil.parseDate(date5.get(1)))) {
                        // 更早
                        n.setType(5);
                    }
                }).collect(Collectors.toList());
            }
            return res;
        }
        // 查询邮件列表 不区分 type 日期类型
        if (ObjectUtil.isNull(type)) {
            List<MailContentListVo> res = new ArrayList<>(getDayList(mailContentListQuery, "0", date0));
            res.addAll(getDayList(mailContentListQuery, "1", date1));
            /*res.addAll(getDayList(mailContentListQuery, "2", date2));
            res.addAll(getDayList(mailContentListQuery, "3", date3));
            res.addAll(getDayList(mailContentListQuery, "4", date4));*/
            res.addAll(getDayList(mailContentListQuery, "5", date5));
            return res;
        }
        // 按照日期类型进行查询, 在手工分拣页面使用
        Tuple tuple;
        switch (type) {
            case 1:
                tuple = date1;
                break;
            /*case 2:
                tuple = date2;
                break;
            case 3:
                tuple = date3;
                break;
            case 4:
                tuple = date4;
                break;*/
            case 5:
                tuple = date5;
                break;
            default:
                tuple = date0;
        }
        return getDayList(mailContentListQuery, String.valueOf(type), tuple);
    }

    /**
     * 获取邮件列表
     *
     * @param mailContentListQuery 查询参数
     * @param type                 类型
     * @return 邮件列表
     */
    private List<MailContentListVo> getDayList(MailContentListQuery mailContentListQuery, String type, Tuple data) {
        mailContentListQuery.setBeginReceiveDate(data.get(0));
        mailContentListQuery.setEndReceiveDate(data.get(1));
        switch (type) {
            case "0":
                List<MailContentListVo> type0List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type0List)) {
                    return type0List.stream().peek(n -> n.setType(0)).collect(Collectors.toList());
                }
                break;
            case "1":
                List<MailContentListVo> type1List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type1List)) {
                    return type1List.stream().peek(n -> n.setType(1)).collect(Collectors.toList());
                }
                break;
            case "2":
                List<MailContentListVo> type2List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type2List)) {
                    return type2List.stream().peek(n -> n.setType(2)).collect(Collectors.toList());
                }
                break;
            case "3":
                List<MailContentListVo> type3List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type3List)) {
                    return type3List.stream().peek(n -> n.setType(3)).collect(Collectors.toList());
                }
                break;
            case "4":
                List<MailContentListVo> type4List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type4List)) {
                    return type4List.stream().peek(n -> n.setType(4)).collect(Collectors.toList());
                }
                break;
            case "5":
                List<MailContentListVo> type5List = mailContentMapper.list(mailContentListQuery);
                if (CollectionUtil.isNotEmpty(type5List)) {
                    return type5List.stream().peek(n -> n.setType(5)).collect(Collectors.toList());
                }
                break;
        }
        return new ArrayList<>();
    }

    /**
     * 获取日期范围
     *
     * @param mailContentListQuery 参数
     * @param type                 类型
     * @return 日期范围
     */
    private Tuple getDate(MailContentListQuery mailContentListQuery, String type) {
        switch (type) {
            case "0":
                String nowDateStr = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
                mailContentListQuery.setBeginReceiveDate(nowDateStr);
                mailContentListQuery.setEndReceiveDate(nowDateStr);
                break;
            case "1":
                String yesterdayStr = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN);
                mailContentListQuery.setBeginReceiveDate(yesterdayStr);
                mailContentListQuery.setEndReceiveDate(yesterdayStr);
                break;
            case "2":
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.WEEK_OF_MONTH, -1);
                String beginWeekDay = DateUtil.format(DateUtil.beginOfWeek(calendar.getTime()), DatePattern.NORM_DATE_PATTERN);
                String endWeekDay = DateUtil.format(DateUtil.endOfWeek(calendar.getTime()), DatePattern.NORM_DATE_PATTERN);
                mailContentListQuery.setBeginReceiveDate(beginWeekDay);
                mailContentListQuery.setEndReceiveDate(endWeekDay);
                break;
            case "3":
                Calendar week2Before = Calendar.getInstance();
                week2Before.add(Calendar.WEEK_OF_YEAR, -2);
                String beginWeek2BeforeDay = DateUtil.format(DateUtil.beginOfWeek(week2Before.getTime()), DatePattern.NORM_DATE_PATTERN);
                String endWeek2BeforeDay = DateUtil.format(DateUtil.endOfWeek(week2Before.getTime()), DatePattern.NORM_DATE_PATTERN);
                mailContentListQuery.setBeginReceiveDate(beginWeek2BeforeDay);
                mailContentListQuery.setEndReceiveDate(endWeek2BeforeDay);
                break;
            case "4":
                Calendar week2Month = Calendar.getInstance();
                week2Month.add(Calendar.MONTH, -1);
                String beginMonth2BeforeDay = DateUtil.format(DateUtil.beginOfMonth(week2Month.getTime()), DatePattern.NORM_DATE_PATTERN);
                String endMonth2BeforeDay = DateUtil.format(DateUtil.endOfMonth(week2Month.getTime()), DatePattern.NORM_DATE_PATTERN);
                mailContentListQuery.setBeginReceiveDate(beginMonth2BeforeDay);
                mailContentListQuery.setEndReceiveDate(endMonth2BeforeDay);
                break;
            case "5":
                // 更早改为 昨天以前
                // Calendar monthBefore = Calendar.getInstance();
                // monthBefore.add(Calendar.MONTH, -1);
                // 更早就是从最小时间开始
                String endReceiveDate = DateUtil.format(DateUtil.offsetDay(new Date(), -2), DatePattern.NORM_DATE_PATTERN);
                LocalDate date = LocalDate.of(1900, 1, 1);
                LocalTime time = LocalTime.of(0, 0, 0);
                LocalDateTime dateTime = LocalDateTime.of(date, time);
                mailContentListQuery.setBeginReceiveDate(DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN));
                mailContentListQuery.setEndReceiveDate(endReceiveDate);
                break;
        }
        log.info("查询邮件的时间范围为:开始时间: {} - 结束时间: {}", mailContentListQuery.getBeginReceiveDate(), mailContentListQuery.getEndReceiveDate());
        return new Tuple(mailContentListQuery.getBeginReceiveDate(), mailContentListQuery.getEndReceiveDate());
    }

    @Override
    public MailContent getById(String id) {
        MailContent mailContent = mailContentMapper.getById(id);
        if (ObjectUtil.isNull(mailContent)) {
            BusinessException.throwException("未找到此收件信息");
        }
        String attachmentStr = mailContent.getAttachmentStr();
        String receiveStr = mailContent.getReceiveStr();
        String mailFromStr = mailContent.getMailFromStr();
        if (StringUtils.isNotBlank(receiveStr)) {
            mailContent.setReceive(JSONUtil.toList(receiveStr, MailUser.class));
        }
        if (StringUtils.isNotBlank(mailFromStr)) {
            mailContent.setMailFrom(JSONUtil.toBean(mailFromStr, MailUser.class));
        }
        if (StringUtils.isNotBlank(attachmentStr)) {
            mailContent.setAttachments(JSONUtil.toList(attachmentStr, MailAttachment.class));
        }
        return mailContent;
    }

    @Override
    public Boolean move(String boxId, String contentId) {
        boolean result = mailContentMapper.move(boxId, contentId) > 0;
        MailContent mailContent = mailContentMapper.getById(contentId);
        dealHandler(boxId, mailContent);
        return result;
    }

    @Override
    public Boolean doPick(ManualPickParam manualPickParam) {
        List<String> contentIds = manualPickParam.getContentIds();
        String boxId = manualPickParam.getBoxId();
        if (CollectionUtil.isEmpty(contentIds)) {
            BusinessException.throwException("没有选择的邮件");
        }
        if (StringUtils.isBlank(boxId)) {
            manualPickParam.setBoxId("1");
        }
        return mailContentMapper.doPick(manualPickParam) > 0;
    }

    @Override
    public void pickMail(List<MailContent> mailContents) {
        log.info("[START]执行邮件分拣逻辑.开始时间为:{}", DateUtil.now());
        // List<MailContent> mailContents = mailContentMapper.selectUnPickMail();
        if (CollectionUtil.isEmpty(mailContents)) {
            log.info("未找到合适的邮件进行分拣");
        }
        List<MailPickRuleDetail> mailPickRules = mailPickRuleMapper.selectAllRule();
        if (CollectionUtil.isEmpty(mailPickRules)) {
            log.info("没有配置邮件分拣规则");
        }
        // 需要处理附件在循环外统一处理
        // 执行外汇对附件操作 - 费用模块
        List<MailContent> needHandleAttachmentWH = new ArrayList<>();
        // 执行中保登对附件操作 - 费用模块
        List<MailContent> needHandleAttachmentZBD = new ArrayList<>();
        for (MailContent mailContent : mailContents) {
            String sourceBoxId = mailContent.getBoxId();
            boolean picked = StringUtils.isNotBlank(sourceBoxId) && !sourceBoxId.equals("1");
            // 每个邮件进行规则匹配
            List<MailPickRuleDetail> matchedRules = mailPickRuleService.doFilter(mailContent, mailPickRules);
            if (CollectionUtil.isNotEmpty(matchedRules)) {
                if (matchedRules.size() > 1) {
                    // 匹配到了多个规则
                    String boxId = matchedRules.get(0).getPickBasket();
                    mailContent.setBoxId(boxId);
                    mailContent.setPickStatus(2);
                } else if (matchedRules.size() == 1) {
                    // 只匹配了一个规则
                    String boxId = matchedRules.get(0).getPickBasket();
                    mailContent.setBoxId(boxId);
                    mailContent.setPickStatus(-1);
                }
            } else {
                mailContent.setBoxId("1");
                mailContent.setPickStatus(1);
            }
            mailContentMapper.move(mailContent.getBoxId(), mailContent.getId());
            if (!picked) {
                // 对调用ocr的不重新识别
                // 根据分拣筐查询规则名称
                String boxId = mailContent.getBoxId();
                String ruleValue = mailContentMapper.selectRuleNameByBoxId(boxId);
                if (StringUtils.isNotBlank(ruleValue) && StringUtils.isNotBlank(boxId)) {
                    if (ruleValue.contains("缴费通知单") && ruleValue.contains("外汇")) {
                        // 外汇
                        log.info("规则name = {}, 需要加到外汇附件处理中", ruleValue);
                        needHandleAttachmentWH.add(mailContent);
                    } else if (ruleValue.contains("缴费通知单") && ruleValue.contains("中保登")) {
                        // 中保登
                        log.info("规则name = {}, 需要加到中保登附件处理中", ruleValue);
                        needHandleAttachmentZBD.add(mailContent);
                    }
                }
            }
            String boxId = mailContent.getBoxId();
            if (!picked && !sourceBoxId.equals(boxId)) {
                mailContentMapper.move(boxId, mailContent.getId());
                dealHandler(boxId, mailContent);
            }
        }
        if (CollectionUtil.isNotEmpty(needHandleAttachmentWH) || CollectionUtil.isNotEmpty(needHandleAttachmentZBD)) {
            // 开一个线程处理附件 不阻塞当前处理分拣线程
            log.info("开始处理费用模块相关的附件...");
            CompletableFuture.runAsync(() -> {
                InterbankFeesService interbankFeesService = SpringUtil.getBean(InterbankFeesService.class);
                InsuranceRegistrationFeesService insuranceRegistrationFeesService = SpringUtil.getBean(InsuranceRegistrationFeesService.class);
                log.info("需要处理外汇的文件数量为:{}", needHandleAttachmentWH.size());
                log.info("需要处理中保登的文件数量为:{}", needHandleAttachmentZBD.size());
                if (CollectionUtil.isNotEmpty(needHandleAttachmentWH)) {
                    for (MailContent mailContent : needHandleAttachmentWH) {
                        String attachmentStr = mailContent.getAttachmentStr();
                        if (StringUtils.isNotBlank(attachmentStr)) {
                            String mailId = mailContent.getId();
                            String sentDate = mailContent.getSentDate();
                            List<String> collect = CollectionUtil.newArrayList(attachmentStr);
                            List<MultipartFile> multipartFiles = new ArrayList<>();
                            log.info("需要处理外汇的文件信息为:{}", collect);
                            if (CollectionUtil.isNotEmpty(collect)) {
                                getMultipartFiles(collect, multipartFiles);
                                if (CollectionUtil.isNotEmpty(multipartFiles)) {
                                    interbankFeesService.upload(multipartFiles.toArray(MultipartFile[]::new), mailId, sentDate);
                                }
                            }
                        }
                    }
                }
                if (CollectionUtil.isNotEmpty(needHandleAttachmentZBD)) {
                    for (MailContent mailContent : needHandleAttachmentZBD) {
                        String attachmentStr = mailContent.getAttachmentStr();
                        if (StringUtils.isNotBlank(attachmentStr)) {
                            String mailId = mailContent.getId();
                            String sentDate = mailContent.getSentDate();
                            List<String> collect = CollectionUtil.newArrayList(attachmentStr);
                            List<MultipartFile> multipartFiles = new ArrayList<>();
                            log.info("需要处理中保登的文件信息为:{}", collect);
                            if (CollectionUtil.isNotEmpty(collect)) {
                                getMultipartFiles(collect, multipartFiles);
                                if (CollectionUtil.isNotEmpty(multipartFiles)) {
                                    insuranceRegistrationFeesService.upload(multipartFiles.toArray(MultipartFile[]::new), mailId, sentDate);
                                }
                            }
                        }
                    }
                }
            });
        }
        log.info("[END]执行邮件分拣逻辑结束.结束时间为:{}", DateUtil.now());
    }

    @Override
    public void pickMailSimple(List<MailContent> mailContents) {
        log.info("[START]执行邮件分拣逻辑.开始时间为:{}", DateUtil.now());
        // List<MailContent> mailContents = mailContentMapper.selectUnPickMail();
        if (CollectionUtil.isEmpty(mailContents)) {
            log.info("未找到合适的邮件进行分拣");
        }
        List<MailPickRuleDetail> mailPickRules = mailPickRuleMapper.selectAllRule();
        if (CollectionUtil.isEmpty(mailPickRules)) {
            log.info("没有配置邮件分拣规则");
        }
        for (MailContent mailContent : mailContents) {
            // 每个邮件进行规则匹配
            List<MailPickRuleDetail> matchedRules = mailPickRuleService.doFilter(mailContent, mailPickRules);
            if (CollectionUtil.isNotEmpty(matchedRules)) {
                if (matchedRules.size() > 1) {
                    // 匹配到了多个规则
                    String boxId = matchedRules.get(0).getPickBasket();
                    mailContent.setBoxId(boxId);
                    mailContent.setPickStatus(2);
                } else if (matchedRules.size() == 1) {
                    // 只匹配了一个规则
                    String boxId = matchedRules.get(0).getPickBasket();
                    mailContent.setBoxId(boxId);
                    mailContent.setPickStatus(-1);
                }
            } else {
                mailContent.setBoxId("1");
                mailContent.setPickStatus(1);
            }
            String boxId = mailContent.getBoxId();
            mailContentMapper.move(boxId, mailContent.getId());
        }
        log.info("[END]执行邮件分拣逻辑结束.结束时间为:{}", DateUtil.now());
    }

    private void getMultipartFiles(List<String> collect, List<MultipartFile> multipartFiles) {
        for (String fileStr : collect) {
            List<MailAttachment> list = JSONUtil.toList(fileStr, MailAttachment.class);
            if (CollectionUtil.isEmpty(list)) {
                continue;
            }
            for (MailAttachment mailAttachment : list) {
                String filePath = mailAttachment.getFilePath();
                log.info("附件地址为: filePath = {}", fileStr);
                if (StringUtils.isNotBlank(filePath)) {
                    File file = new File(filePath);
                    if (file.exists()) {
                        try {
                            log.info("文件扩展名为:{}", FileUtil.extName(file));
                            if ("zip".equals(FileUtil.extName(file))) {
                                // 压缩文件需要解压
                                File unzipDir = ZipUtil.unzip(file);
                                log.info("将压缩文件解压到路径为:{}", unzipDir.getAbsolutePath());
                                List<File> fileList = FileUtil.loopFiles(unzipDir);
                                if (CollectionUtil.isNotEmpty(fileList)) {
                                    for (File subFile : fileList) {
                                        if ("pdf".equals(FileUtil.extName(subFile))) {
                                            log.info("需要处理的文件为:{}", subFile.getAbsolutePath());
                                            MultipartFile multipartFile = OmFileUtil.fileToMultipartFile(subFile);
                                            multipartFiles.add(multipartFile);
                                        }
                                    }
                                }
                            } else {
                                log.info("文件不是压缩格式的...");
                                if ("pdf".equals(FileUtil.extName(file))) {
                                    // 正常文件
                                    MultipartFile multipartFile = OmFileUtil.fileToMultipartFile(file);
                                    multipartFiles.add(multipartFile);
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    private void dealHandler(String boxId, MailContent mailContent) {
        try {
            OpenFundBasket openFundBasket = openFundBasketService.getById(boxId);
            if (openFundBasket == null) {
                throw new RuntimeException("对应分拣框没有设置handler");
            } else {
                String handler = openFundBasket.getHandler();
                Map<String, BaseBoxHandler> beansOfType = applicationContext.getBeansOfType(BaseBoxHandler.class);
                BaseBoxHandler baseBoxHandler = beansOfType.get(handler);
                if (baseBoxHandler == null) {
                    throw new RuntimeException("对应handler没有实现");
                }
                baseBoxHandler.execute(mailContent);
            }
        } catch (Exception e) {
            log.error("执行分拣后处理出现异常", e);
        }
    }

    /**
     * 同步邮件信息
     *
     * @param messageNumber 信息数量
     * @param length        偏移量
     * @param messageList   消息列表
     */
    private void synEmail(Integer messageNumber, int length, Message[] messageList) {
        // 统计读取邮件的数量
        int count = 0;
        for (int i = messageNumber; i < length; i++) {
            try {
                Message msg = messageList[i];
                // 读取邮箱中的内容并落库
                MailContent mailContent = javaMailUtils.getMailContent(msg);
                mailContentService.save(mailContent);
                count++;
            } catch (Exception e) {
                log.error("{}号邮件解析失败", i);
                log.error("解析失败的原因为:", e);
            }
        }
        log.info("邮件读取完成,共从邮件中读取{}条邮件", count);
    }

    /**
     * 只读取指定messageNumber的那一封邮件
     * @param messageNumber 邮件编号（从1开始）
     */
    public void readSingleMail(Integer messageNumber) {
        log.info("[START]读取单封邮件，邮件编号:{}，时间:{}", messageNumber, DateUtil.now());
        Store store = null;
        Folder folder = null;
        try {
            // 初始化邮箱连接
            store = mailConfiguration.initEmailConnect();
            // 打开收件箱
            folder = store.getFolder("INBOX");
            folder.open(Folder.READ_ONLY);
            Message[] messageList = folder.getMessages();
            int length = messageList.length;
            if (messageNumber == null || messageNumber < 1 || messageNumber > length) {
                log.warn("messageNumber参数不合法，当前收件箱邮件总数:{}", length);
                return;
            }
            Message msg = messageList[messageNumber - 1]; // JavaMail Message数组下标从0开始
            try {
                MailContent mailContent = javaMailUtils.getMailContent(msg);
                mailContentService.save(mailContent);
                log.info("成功读取并保存第{}封邮件", messageNumber);
            } catch (Exception e) {
                log.error("第{}号邮件解析失败", messageNumber);
                log.error("解析失败的原因为:", e);
            }
        } catch (MessagingException e) {
            log.error("初始化邮箱失败:", e);
        } finally {
            try {
                if (folder != null) {
                    folder.close(false);
                }
                if (store != null) {
                    store.close();
                }
            } catch (Exception e) {
                log.error("邮箱关闭失败", e);
            }
        }
        log.info("[END]读取单封邮件结束，邮件编号:{}，时间:{}", messageNumber, DateUtil.now());
    }
}
