package cn.sdata.om.al.service.mail.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.entity.mail.MailTemplateExt;
import cn.sdata.om.al.entity.mail.params.MailTemplateListQuery;
import cn.sdata.om.al.entity.mail.params.SaveOrUpdateMailTemplate;
import cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo;
import cn.sdata.om.al.entity.mail.vo.MailTemplateListVo;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.mapper.mail.AccountSetMapper;
import cn.sdata.om.al.mapper.mail.MailTemplateMapper;
import cn.sdata.om.al.service.mail.MailTemplateService;
import cn.sdata.om.al.utils.SecureUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class MailTemplateServiceImpl implements MailTemplateService {

    private MailTemplateMapper mailTemplateMapper;

    @Autowired
    public void setMailTemplateMapper(MailTemplateMapper mailTemplateMapper) {
        this.mailTemplateMapper = mailTemplateMapper;
    }

    @Override
    public PageInfo<MailTemplateListVo> page(MailTemplateListQuery mailTemplateListQuery) {
        int pageNo = mailTemplateListQuery.getPageNo();
        int pageSize = mailTemplateListQuery.getPageSize();
        try (Page<Object> ignored = PageHelper.startPage(pageNo, pageSize)) {
            List<MailTemplateListVo> page = mailTemplateMapper.page(mailTemplateListQuery);
            if (CollectionUtil.isNotEmpty(page)) {
                for (MailTemplateListVo mailTemplateListVo : page) {
                    String extInfoStr = mailTemplateListVo.getExtInfoStr();
                    if (StringUtils.isNotBlank(extInfoStr)) {
                        mailTemplateListVo.setBindTaskCount(1);
                    } else {
                        mailTemplateListVo.setBindTaskCount(0);
                    }
                }
                return new PageInfo<>(page);
            }
        }
        return new PageInfo<>();
    }

    @Override
    public List<CommonEntity> list() {
        return mailTemplateMapper.list();
    }

    @Override
    public boolean delete(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            BusinessException.throwException("删除模板失败,id列表为空");
        }
        return mailTemplateMapper.batchDeleteByIds(ids) > 0;
    }

    @Override
    public boolean saveOrUpdate(SaveOrUpdateMailTemplate saveOrUpdateMailTemplate) {
        String id = saveOrUpdateMailTemplate.getId();
        MailTemplateExt mailTemplateExt = saveOrUpdateMailTemplate.getMailTemplateExt();
        if (ObjectUtil.isNotNull(mailTemplateExt)) {
            saveOrUpdateMailTemplate.setMailTemplateExtStr(JSON.toJSONString(mailTemplateExt));
        }
        int res;
        if (StringUtils.isNotBlank(id)) {
            // 修改
            saveOrUpdateMailTemplate.setUpdateByName(SecureUtil.currentUser().getAccount());
            res = mailTemplateMapper.update(saveOrUpdateMailTemplate);
        } else {
            // 新增
            id = IdUtil.getSnowflakeNextIdStr();
            saveOrUpdateMailTemplate.setId(id);
            saveOrUpdateMailTemplate.setCreateByName(SecureUtil.currentUser().getAccount());
            saveOrUpdateMailTemplate.setUpdateByName(SecureUtil.currentUser().getAccount());
            saveOrUpdateMailTemplate.setTemplateId(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 10));
            res = mailTemplateMapper.save(saveOrUpdateMailTemplate);
        }
        return res > 0;
    }

    @Override
    public MailTemplateDetailVo getById(String id) {
        MailTemplateDetailVo mailTemplateDetailVo = mailTemplateMapper.getById(id);
        if (ObjectUtil.isNotNull(mailTemplateDetailVo)) {
            String mailTemplateExtStr = mailTemplateDetailVo.getMailTemplateExtStr();
            if (StringUtils.isNotBlank(mailTemplateExtStr)) {
                mailTemplateDetailVo.setMailTemplateExt(JSON.parseObject(mailTemplateExtStr, MailTemplateExt.class));
            }
            return mailTemplateDetailVo;
        }
        return null;
    }

    @Override
    public List<CommonEntity> getAccountSetList(String fieldTypeId) {
        if ("1".equals(fieldTypeId)) {
            // TODO 这块先写死 以后进表维护
            return CollectionUtil.newArrayList(new CommonEntity("1", "账套名称"));
        }
        return CollectionUtil.newArrayList();
    }

    @Override
    public List<CommonEntity> getFieldTypeList() {
        return mailTemplateMapper.getFieldTypeList();
    }

    @Override
    public JSONObject getCategoryByTemplateId(String templateId) {
        JSONObject jsonObject = new JSONObject();
        MailTemplateDetailVo mailTemplateDetailVo = getById(templateId);
        if (ObjectUtil.isNotNull(mailTemplateDetailVo)) {
            MailTemplateExt mailTemplateExt = mailTemplateDetailVo.getMailTemplateExt();
            String category = mailTemplateExt.getCategory();
            if ("1".equals(category)) {
                // 账套信息
                jsonObject.put("key", category);
                jsonObject.put("name", "账套名称");
                jsonObject.put("data", SpringUtil.getBean(AccountSetMapper.class).list());
            } else {
                return null;
            }
            return jsonObject;
        }
        BusinessException.throwException("邮件模板不存在");
        return null;
    }
}
