package cn.sdata.om.al.service.monthlyData;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.dto.MonthlyDataLogDto;
import cn.sdata.om.al.entity.FuncDataToMailEntity;
import cn.sdata.om.al.entity.monthlyData.LogCallRpaMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.LogFileOperMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.LogMailSendMonthlyDataEntity;
import cn.sdata.om.al.entity.monthlyData.MonthlyDataEntity;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import cn.sdata.om.al.job.MonthlyDataZQMailJob;
import cn.sdata.om.al.job.MonthlyDataZQTZJHJZRpaJob;
import cn.sdata.om.al.job.MonthlyDataZZSMailJob;
import cn.sdata.om.al.job.MonthlyDataZZSTZRpaJob;
import cn.sdata.om.al.mapper.monthlyData.LogCallRpaMonthlyDataMapper;
import cn.sdata.om.al.mapper.monthlyData.LogFileOperMonthlyDataMapper;
import cn.sdata.om.al.mapper.monthlyData.LogMailSendMonthlyDataMapper;
import cn.sdata.om.al.mapper.monthlyData.MonthlyDataMapper;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.enums.JobStatus;
import cn.sdata.om.al.qrtz.service.BaseCronLogService;
import cn.sdata.om.al.qrtz.service.CronService;
import cn.sdata.om.al.service.ExecutionLockService;
import cn.sdata.om.al.service.FuncDataToMailService;
import cn.sdata.om.al.utils.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.quartz.JobDataMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.sdata.om.al.constant.JobConstant.*;

/**
 * 月度数据service
 *
 * <AUTHOR>
 * @Date 2025/4/7 11:27
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonthlyDataService extends ServiceImpl<MonthlyDataMapper, MonthlyDataEntity> {

    private final CronService cronService;

    private final ExecutionLockService lock;

    private final BaseCronLogService baseCronLogService;

    private final FuncDataToMailService funcDataToMailService;

    private final LogFileOperMonthlyDataMapper logFileOperMonthlyDataMapper;

    private final LogCallRpaMonthlyDataMapper logCallRpaMonthlyDataMapper;

    private final LogMailSendMonthlyDataMapper logMailSendMonthlyDataMapper;

    /**
     * 基础目录-来自配置
     */
    @Value("${file.dir}")
    private String baseDir;

    /**
     * 初始化月度数据
     *
     * @param dataDate  yyyy-MM 数据日期
     * @param startDate yyyy-MM-dd 开始日期
     * @param endDate   yyyy-MM-dd 结束日期
     */
    public synchronized void initDefaultData(String dataDate, String startDate, String endDate) throws Exception {
        checkDataDate(dataDate);
        int defaultCount = Math.toIntExact(this.count(Wrappers.lambdaQuery(MonthlyDataEntity.class).eq(MonthlyDataEntity::getDataDate, dataDate)));
        if (defaultCount > 0)
            return;
        String pureEndDate = dateNorm2Pure(endDate);
        List<MonthlyDataEntity> list = Lists.newArrayList();
        for (MonthlyDataFileEnum anEnum : MonthlyDataFileEnum.values()) {
            MonthlyDataEntity entity = new MonthlyDataEntity();
            entity.setId(IdWorker.getIdStr())
                    .setTaskName(anEnum.getTaskName())
                    .setTaskType(anEnum.getTaskType())
                    .setFileName(anEnum.getFileName().replaceAll("YYYYMMDD", pureEndDate))
                    .setFileTypeNo(anEnum.getTypeNo())
                    .setDataDate(dataDate)
                    .setEndDate(endDate)
                    .setDownloadStatus(0)
                    .setMailSendStatus(MailStatus.UNSENT.name())
                    .setCreateTime(new Date())
                    .setUserId(SecureUtil.currentUserName())
                    .setStep("initDefaultData");
            if ("CLAIM_INVEST_PLAN".equals(anEnum.getTaskType())) {
                entity.setStartDate(endDate);
            } else {
                entity.setStartDate(startDate);
            }
            list.add(entity);
        }
        if (CollUtil.isNotEmpty(list))
            this.saveBatch(list);
    }

    /**
     * 分页接口
     *
     * @param dataDate       yyyy-MM
     * @param taskName       任务名称
     * @param downloadStatus 下载状态
     * @param mailSendStatus 邮件发送状态
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<MonthlyDataEntity> page(String dataDate,
                                        String taskName,
                                        Integer downloadStatus,
                                        String mailSendStatus,
                                        Integer pageNo,
                                        Integer pageSize) throws Exception {

        YearMonth yearMonth = YearMonth.parse(dataDate);
        String startDate = yearMonth.atDay(1).toString(), endDate = yearMonth.atEndOfMonth().toString();
        initDefaultData(dataDate, startDate, endDate);
        Page<MonthlyDataEntity> page = this.page(new Page<>(pageNo, pageSize), Wrappers.lambdaQuery(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(StringUtils.isNotBlank(taskName), MonthlyDataEntity::getTaskName, taskName)
                .eq(downloadStatus != null, MonthlyDataEntity::getDownloadStatus, downloadStatus)
                .eq(StringUtils.isNotBlank(mailSendStatus), MonthlyDataEntity::getMailSendStatus, mailSendStatus));

        List<FuncDataToMailEntity> dataToMailList = funcDataToMailService.getLatestMailByDataDateAndFuncType(dataDate, "monthlyData");
        if (CollUtil.isNotEmpty(dataToMailList)) {
            Map<String, FuncDataToMailEntity> dtmMap = dataToMailList.stream().collect(Collectors.toMap(FuncDataToMailEntity::getDataType, Function.identity(), (n1, n2) -> n2));
            for (MonthlyDataEntity monthlyData : page.getRecords()) {
                if (CollUtil.isNotEmpty(dtmMap)
                        && dtmMap.containsKey(monthlyData.getTaskType())) {
                    if (ObjectUtil.isNotNull(dtmMap.get(monthlyData.getTaskType()))
                            && StringUtils.isNotBlank(dtmMap.get(monthlyData.getTaskType()).getMailId())) {
                        monthlyData.setMailId(dtmMap.get(monthlyData.getTaskType()).getMailId());
                    }
                }
            }
        }
        return page;
    }

    /**
     * 增值税台账邮件发送
     *
     * @param dataDate yyyy-MM
     * @return boolean
     */
    public synchronized Boolean sendZZSTZMail(String dataDate) throws Exception {
        try {
            YearMonth yearMonth = YearMonth.parse(dataDate);
            String startDate = yearMonth.atDay(1).toString(),
                    endDate = yearMonth.atEndOfMonth().toString();
            List<String> filePaths = this.list(
                    Wrappers.lambdaQuery(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                            .isNotNull(MonthlyDataEntity::getLocalFilePath))
                    .stream().map(MonthlyDataEntity::getLocalFilePath).collect(Collectors.toList());
            if (CollUtil.isEmpty(filePaths) || filePaths.size() < 4) {
                this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                        .eq(MonthlyDataEntity::getDataDate, dataDate)
                        .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                        .set(MonthlyDataEntity::getMailSendStatus, MailStatus.FAILED.name())
                        .set(MonthlyDataEntity::getUpdateTime, new Date()));
                log.error("MonthlyDataService_sendZZSTZMail_文件缺失_error:{}", filePaths.size());
                throw new Exception("文件缺失");
            }
            sendZZSTZMailV1(dataDate, filePaths, startDate, endDate);
            return this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.SENDING.name())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getMailSendTime, new Date()));
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.FAILED.name())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getMailSendTime, new Date()));
            log.error("MonthlyDataService_sendZZSTZMail_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 债权投资计划净值邮件发送
     *
     * @param dataDate yyyy-MM
     * @return boolean
     */
    public synchronized Boolean sendZQTZJHJZMail(String dataDate) throws Exception {
        try {
            YearMonth yearMonth = YearMonth.parse(dataDate);
            String startDate = yearMonth.atDay(1).toString(),
                    endDate = yearMonth.atEndOfMonth().toString();
            List<String> filePaths = this.list(
                    Wrappers.lambdaQuery(MonthlyDataEntity.class)
                            .eq(MonthlyDataEntity::getDataDate, dataDate)
                            .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                            .isNotNull(MonthlyDataEntity::getLocalFilePath))
                    .stream().map(MonthlyDataEntity::getLocalFilePath).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(filePaths)) {
                this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                        .eq(MonthlyDataEntity::getDataDate, dataDate)
                        .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                        .set(MonthlyDataEntity::getMailSendStatus, MailStatus.FAILED.name())
                        .set(MonthlyDataEntity::getUpdateTime, new Date()));
                log.error("MonthlyDataService_sendZQTZJHJZMail_文件缺失_error:{}", filePaths.size());
                throw new Exception("文件缺失");
            }
            sendZQTZJHJZMailV1(dataDate, filePaths, endDate, endDate);
            return this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.SENDING.name())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getMailSendTime, new Date()));
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.FAILED.name())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getMailSendTime, new Date()));
            log.error("MonthlyDataService_sendZQTZJHJZMail_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 发邮件
     *
     * @param dataDate  数据日期
     * @param filePaths 文件路径列表
     */
    public void sendZQTZJHJZMailV1(String dataDate, List<String> filePaths, String startDate, String endDate) {
        try {
            List<String> jobIds = cronService.getJobIdByClass(MonthlyDataZQMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(FILE_PATHS, String.join(", ", filePaths));
            jobDataMap.put(CronConstant.START_DATE, startDate);
            jobDataMap.put(CronConstant.END_DATE, endDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            log.info("MonthlyDataService_sendZQTZJHJZMailV1_param:{},{}", jobIds, jobDataMap.toString());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            log.error("MonthlyDataService_sendZQTZJHJZMailV1_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    public void sendZZSTZMailV1(String dataDate, List<String> filePaths, String startDate, String endDate) {
        try {
            List<String> jobIds = cronService.getJobIdByClass(MonthlyDataZZSMailJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(DATA_DATE, dataDate);
            jobDataMap.put(FILE_PATHS, String.join(", ", filePaths));
            jobDataMap.put(CronConstant.START_DATE, startDate);
            jobDataMap.put(CronConstant.END_DATE, endDate);
            jobDataMap.put(CronConstant.SYNC, true);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            log.info("MonthlyDataService_sendZZSTZMailV1_param:{},{}", jobIds, jobDataMap.toString());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            log.error("MonthlyDataService_sendZZSTZMailV1_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 校验格式 yyyy-MM
     *
     * @param input
     * @return
     */
    public static boolean isValidYearMonth(String input) {
        try {
            YearMonth.parse(input);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 处理数据日期
     *
     * @param dataDate yyyy-MM
     * @return
     */
    public static void checkDataDate(String dataDate) throws Exception {
        if (StringUtils.isBlank(dataDate))
            throw new Exception("数据日期为空");
        if (!isValidYearMonth(dataDate))
            throw new Exception("数据日期格式异常");
        if (YearMonth.parse(dataDate).compareTo(YearMonth.parse(DateUtil.format(new Date(), DatePattern.NORM_MONTH_PATTERN))) >= 0)
            throw new Exception("数据日期必须小于当月");
    }

    /**
     * 增值税台账rpa调用
     *
     * @param dataDate yyyy-MM
     */
    public synchronized void dealZZSTZRpa(String dataDate, String startDate, String endDate) {
        try {
            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, null)
                    .set(MonthlyDataEntity::getDownloadStatus, 3)
                    .set(MonthlyDataEntity::getDataStatus, null)
                    .set(MonthlyDataEntity::getStep, "dealZZSTZRpa_start")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, SecureUtil.currentUserName())
            );

            List<String> jobIds = cronService.getJobIdByClass(MonthlyDataZZSTZRpaJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(CronConstant.START_DATE, startDate);
            jobDataMap.put(CronConstant.END_DATE, endDate);
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put(BaseConstant.SYSTEM_DATE_NAME, dataDate);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, null)
                    .set(MonthlyDataEntity::getDownloadStatus, 2)
                    .set(MonthlyDataEntity::getDataStatus, null)
                    .set(MonthlyDataEntity::getStep, "MonthlyDataService_dealZZSTZRpa_error")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, SecureUtil.currentUserName())
            );
            log.error("MonthlyDataService_dealZZSTZRpa_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * 债权投资计划净值rpa调用
     *
     * @param dataDate yyyy-MM
     */
    public synchronized void dealZQTZJHJZRpa(String dataDate, String startDate, String endDate) {
        try {

            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, null)
                    .set(MonthlyDataEntity::getDownloadStatus, 3)
                    .set(MonthlyDataEntity::getDataStatus, null)
                    .set(MonthlyDataEntity::getStep, "dealZQTZJHJZRpa_start")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, SecureUtil.currentUserName())
            );

            List<String> jobIds = cronService.getJobIdByClass(MonthlyDataZQTZJHJZRpaJob.class);
            JobDataMap jobDataMap = new JobDataMap();
            jobDataMap.put(CronConstant.START_DATE, startDate);
            jobDataMap.put(CronConstant.END_DATE, endDate);
            jobDataMap.put(CronConstant.EXECUTOR, SecureUtil.currentUserName());
            jobDataMap.put(BaseConstant.SYSTEM_DATE_NAME, dataDate);
            jobDataMap.put(CronConstant.MANUAL, "MANUAL");
            jobDataMap.put("userId", SecureUtil.currentUserId());
            jobDataMap.put("userName", SecureUtil.currentUserName());
            cronService.startJobNow(jobIds, jobDataMap);
        } catch (Exception e) {
            this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                    .eq(MonthlyDataEntity::getDataDate, dataDate)
                    .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                    .set(MonthlyDataEntity::getUpdateTime, new Date())
                    .set(MonthlyDataEntity::getFileUpdateTime, null)
                    .set(MonthlyDataEntity::getDownloadStatus, 2)
                    .set(MonthlyDataEntity::getDataStatus, null)
                    .set(MonthlyDataEntity::getStep, "MonthlyDataService_dealZQTZJHJZRpa_error")
                    .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                    .set(MonthlyDataEntity::getMailSendTime, null)
                    .set(MonthlyDataEntity::getLocalFilePath, null)
                    .set(MonthlyDataEntity::getUserId, SecureUtil.currentUserName())
            );
            log.error("MonthlyDataService_dealZQTZJHJZRpa_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    /**
     * yyyy-MM-dd转yyyy年MM月dd日
     *
     * @param dataDate yyyy-MM-dd
     * @return
     */
    public String toLocalDateStr(String dataDate) {
        return LocalDate.parse(dataDate).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.CHINA));
    }

    /**
     * 校验rpa是否正在执行
     *
     * @param startDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param jobClass
     * @throws Exception
     */
    public void checkRunning(String startDate, String endDate, Class<?> jobClass) throws Exception {
        List<String> jobIds = cronService.getJobIdByClass(jobClass);
        String newStartDate = toLocalDateStr(startDate), newEndDate = toLocalDateStr(endDate);
        log.info("MonthlyDataService_checkRunning_newStartDate:{},newEndDate:{},jobClass:{}", newStartDate, newEndDate, jobClass.getName());
        if (CollUtil.isEmpty(jobIds)) {
            log.error("MonthlyDataService_checkRunning_无对应流程配置信息");
            throw new Exception("无对应流程配置信息");
        }
        BaseCronLog latestLog = baseCronLogService.getLatestLog(jobIds.get(0), (newStartDate + "-" + newEndDate));
        log.info("MonthlyDataService_checkRunning_latestLog:{}", latestLog);
        if (latestLog != null && latestLog.getRpaStatus() != null && latestLog.getRpaStatus().equals(JobStatus.RUNNING)) {
            log.error("MonthlyDataService_checkRunning_该数据日期对应的rpa任务正在运行中");
            throw new Exception("该数据日期对应的rpa任务正在运行中");
        }
        /*if (latestLog != null && (latestLog.getRpaStatus() == null || latestLog.getStatus().equals(JobStatus.RUNNING))) {
            log.error("MonthlyDataService_checkRunning_该数据日期对应的下载任务正在运行中");
            throw new Exception("该数据日期对应的下载任务正在运行中");
        }*/
    }

    /**
     * 下载-调用rpa
     *
     * @param dataDate yyyy-MM
     * @throws Exception
     */
    public void callRpa(String dataDate, List<String> taskTypes) throws Exception {
        if (StringUtils.isBlank(dataDate) || CollUtil.isEmpty(taskTypes)) {
            throw new Exception("参数不能为空");
        }
        YearMonth yearMonth = YearMonth.parse(dataDate);
        String startDate = yearMonth.atDay(1).toString(), endDate = yearMonth.atEndOfMonth().toString();
        log.info("callRpa_yearMonth:{},startDate:{},endDate:{}", yearMonth, startDate, endDate);
        for (String taskType : taskTypes) {
            String lockName = "monthlyData_lock_" + dataDate + taskType;
            log.info("MonthlyDataService_exeRpa_run:{},{},{}", dataDate, taskType, lockName);
            if (taskType.equals(MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType())) {
                if (lock.isLocked(lockName)) {
                    log.info("MonthlyDataService_exeRpa_增值税任务正在下载中...");
                    throw new Exception("增值税任务正在下载中...");
                }
                //增值税rpa处理
                if (lock.tryLock(lockName)) {
                    try {
                        checkRunning(startDate, endDate, MonthlyDataZZSTZRpaJob.class);
                    } catch (Exception e) {
                        log.error("MonthlyDataService_checkRunning_zzs_error:{},{}", e, e.getMessage());
                        throw e;
                    } finally {
                        lock.unlock(lockName);
                    }
                }
                //增值税rpa处理
                if (lock.tryLock(lockName)) {
                    try {
                        dealZZSTZRpa(dataDate, startDate, endDate);
                    } catch (Exception e) {
                        log.error("MonthlyDataService_dealZZSTZRpa_error:{},{}", e, e.getMessage());
                        throw e;
                    } finally {
                        lock.unlock(lockName);
                    }
                }
            }

            if (taskType.equals(MonthlyDataFileEnum.ZQJHJZ.getTaskType())) {
                //债权rpa处理
                if (lock.isLocked(lockName)) {
                    log.info("MonthlyDataService_exeRpa_债权任务正在下载中...");
                    throw new Exception("债权任务正在下载中...");
                }
                if (lock.tryLock(lockName)) {
                    try {
                        checkRunning(endDate, endDate, MonthlyDataZQTZJHJZRpaJob.class);
                    } catch (Exception e) {
                        log.error("MonthlyDataService_checkRunning_zq_error:{},{}", e, e.getMessage());
                        throw e;
                    } finally {
                        lock.unlock(lockName);
                    }
                }
                if (lock.tryLock(lockName)) {
                    try {
                        dealZQTZJHJZRpa(dataDate, endDate, endDate);
                    } catch (Exception e) {
                        log.error("MonthlyDataService_dealZQTZJHJZRpa_error:{},{}", e, e.getMessage());
                        throw e;
                    } finally {
                        lock.unlock(lockName);
                    }
                }
            }

        }
    }

    /**
     * 从rpa操作日志处操作rpa调用
     *
     * @param id
     * @throws Exception
     */
    public void logCallRpa(String id) throws Exception {
        Assert.notNull(id, "参数不能为空");
        LogCallRpaMonthlyDataEntity rpaLog = logCallRpaMonthlyDataMapper.getCallRpaLogById(id);
        if (ObjectUtil.isNull(rpaLog)) {
            log.error("MonthlyDataService_logCallRpa_error:{}", "错误日志数据");
            throw new Exception("错误日志数据");
        }
        String dataDate = rpaLog.getDataDate(), taskName = rpaLog.getTaskName();
        if (StringUtils.isBlank(dataDate)) {
            log.error("MonthlyDataService_logCallRpa_error:{}", "缺失数据日期");
            throw new Exception("缺失数据日期");
        }
        if (StringUtils.isBlank(taskName)) {
            log.error("MonthlyDataService_logCallRpa_error:{}", "无法识别任务");
            throw new Exception("无法识别任务");
        }
        if (taskName.contains("增值税") && taskName.contains("月度")) {
            callRpa(dataDate, Lists.newArrayList(MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType()));
        }
        if (taskName.contains("净值") && taskName.contains("月度")) {
            callRpa(dataDate, Lists.newArrayList(MonthlyDataFileEnum.ZQJHJZ.getTaskType()));
        }
    }

    /**
     * 下载到本地
     *
     * @param dataDate  yyyy-MM
     * @param taskTypes
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadToLocal(String dataDate, List<String> taskTypes) throws Exception {
        if (StringUtils.isBlank(dataDate) || CollUtil.isEmpty(taskTypes)) {
            throw new Exception("参数不能为空");
        }
        LambdaQueryWrapper<MonthlyDataEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(MonthlyDataEntity::getDataDate, dataDate)
                .isNotNull(MonthlyDataEntity::getLocalFilePath);
        if (taskTypes.size() == 1 && MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType().equals(taskTypes.get(0))) {
            qw.eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZZSBB_CPHZ.getTaskType());
        }
        if (taskTypes.size() == 1 && MonthlyDataFileEnum.ZQJHJZ.getTaskType().equals(taskTypes.get(0))) {
            qw.eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType());
        }
        List<String> filePaths = this.list(qw).stream().map(MonthlyDataEntity::getLocalFilePath).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(filePaths))
            throw new Exception("数据日期无对应文件");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
            Map<String, Integer> fileNameCountMap = new HashMap<>();
            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (ObjectUtil.isNotNull(file) && file.exists()) {
                    String fileName = file.getName();
                    int count = fileNameCountMap.getOrDefault(fileName, 0);
                    fileNameCountMap.put(fileName, count + 1);
                    String entryName = count == 0 ? fileName : fileName.replace(".", "_" + count + ".");
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipOut.putNextEntry(zipEntry);
                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = fis.read(buffer)) > 0) {
                            zipOut.write(buffer, 0, len);
                        }
                    }
                    zipOut.closeEntry();
                }
            }
        }
        byte[] zipBytes = baos.toByteArray();
        byte[] destBytes = Arrays.copyOf(zipBytes, zipBytes.length);
        try {
            InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(zipBytes));
            //下载到本地日志记录
            String id = IdWorker.getIdStr(),
                    fileName = "月度数据" + dataDate + ".zip",
                    targetFilePath = ensureTrailingSeparator(baseDir) + "monthlyData" + File.separator + dataDate + File.separator + id + File.separator + fileName;
            log.info("downloadToLocal_writeResourceToFile_fileName:{},targetFilePath:{}", fileName, targetFilePath);
            writeResourceToFile(resource, targetFilePath);
            logFileOperMonthlyDataMapper.insert(new LogFileOperMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setSource("下载到本地")
                    .setDataDate(dataDate)
                    .setFileName(fileName)
                    .setFilePath(targetFilePath)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setLogType("fileOper")
                    .setStatus("成功")
            );
        } catch (Exception e) {
            log.error("downloadToLocal_writeResourceToFile_error:{},{}", e, e.getMessage());
        }
        InputStreamResource resource1 = new InputStreamResource(new ByteArrayInputStream(destBytes));
        HttpHeaders headers = new HttpHeaders();
        String filename = "月度数据yyyy-MM".replaceAll("yyyy-MM", dataDate);
        String contentDisposition = "attachment; filename=" + URLEncoder.encode(new String(filename.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + ".zip";
        headers.add(HttpHeaders.CONTENT_DISPOSITION, contentDisposition);
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(destBytes.length)
                .body(resource1);
    }

    /**
     * 写文件
     *
     * @param resource
     * @param targetFilePath
     * @throws Exception
     */
    public void writeResourceToFile(InputStreamResource resource, String targetFilePath) throws Exception {
        Path targetPath = Paths.get(targetFilePath),
                parentDir = targetPath.getParent();
        if (parentDir != null) Files.createDirectories(parentDir);
        try (InputStream inputStream = resource.getInputStream()) {
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        }
    }

    /**
     * 下载具体的单文件
     *
     * @param id
     * @return
     * @throws IOException
     */
    public ResponseEntity<InputStreamResource> downloadById(String id) throws Exception {
        MonthlyDataEntity reportEntity = this.getOne(Wrappers.lambdaQuery(MonthlyDataEntity.class).eq(MonthlyDataEntity::getId, id));
        if (reportEntity == null || StringUtils.isBlank(reportEntity.getLocalFilePath()))
            throw new Exception("目标文件不存在");
        File file = new File(reportEntity.getLocalFilePath());
        if (ObjectUtil.isNull(file) || !file.exists())
            throw new Exception("目标文件不存在");
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));

        //下载到本地日志记录
        logFileOperMonthlyDataMapper.insert(new LogFileOperMonthlyDataEntity()
                .setId(IdWorker.getIdStr())
                .setSource("单文件下载")
                .setDataDate(reportEntity.getDataDate())
                .setFileTypeNo(reportEntity.getFileTypeNo())
                .setFileName(reportEntity.getFileName())
                .setFilePath(reportEntity.getLocalFilePath())
                .setUserId(SecureUtil.currentUserId())
                .setUserName(SecureUtil.currentUserName())
                .setCreateTime(new Date())
                .setLogType("fileOper")
                .setStatus("成功")
        );

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }

    /**
     * 获取不包含后缀名的文件名称
     *
     * @param fileName a.xlsx
     * @return a
     */
    private String getFileNameNoExtension(String fileName) {
        return fileName.replaceFirst("[.][^.]+$", "");
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     * @return
     */
    public String getFileExt(String fileName) {
        return com.google.common.io.Files.getFileExtension(fileName);
    }

    /**
     * 上传文件
     *
     * @param file     文件
     * @param dataDate 数据日期
     * @throws Exception
     */
    public void uploadZQFile(MultipartFile file, String dataDate) throws Exception {
        Assert.notNull(dataDate, "数据日期参数为空");
        MonthlyDataEntity zqEntity = this.getOne(Wrappers.lambdaQuery(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType()));
        if (zqEntity == null) {
            //文件上传日志记录
            logFileOperMonthlyDataMapper.insert(new LogFileOperMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setSource("手工上传")
                    .setFileTypeNo(MonthlyDataFileEnum.ZQJHJZ.getTypeNo())
                    .setDataDate(dataDate)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setLogType("fileOper")
                    .setStatus("失败")
                    .setStep("该数据日期无对应的初始化数据")
            );
            throw new Exception("该数据日期无对应的初始化数据");
        }
        String uploadFileNameNoExt = getFileNameNoExtension(Objects.requireNonNull(file.getOriginalFilename())),
                targetFilePath = genFilePath(dataDate, file.getOriginalFilename());
        log.info("uploadZQFile_uploadFileNameNoExt_targetFilePath:{},{}", uploadFileNameNoExt, targetFilePath);
        if (zqEntity.getFileName().equals(uploadFileNameNoExt)) {
            Path parentDir = Paths.get(targetFilePath).getParent();
            if (parentDir != null)
                Files.createDirectories(parentDir);
            dealCopyAndUpdate(file, targetFilePath, dataDate, "upload_replace_" + zqEntity.getFileName());

            //文件上传日志记录
            logFileOperMonthlyDataMapper.insert(new LogFileOperMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setSource("手工上传")
                    .setFileTypeNo(MonthlyDataFileEnum.ZQJHJZ.getTypeNo())
                    .setDataDate(dataDate)
                    .setFileName(uploadFileNameNoExt)
                    .setFilePath(targetFilePath)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setLogType("fileOper")
                    .setStatus("成功")
            );
        } else {
            //文件上传日志记录
            logFileOperMonthlyDataMapper.insert(new LogFileOperMonthlyDataEntity()
                    .setId(IdWorker.getIdStr())
                    .setSource("手工上传")
                    .setFileTypeNo(MonthlyDataFileEnum.ZQJHJZ.getTypeNo())
                    .setDataDate(dataDate)
                    .setFileName(uploadFileNameNoExt)
                    .setFilePath(targetFilePath)
                    .setUserId(SecureUtil.currentUserId())
                    .setUserName(SecureUtil.currentUserName())
                    .setCreateTime(new Date())
                    .setLogType("fileOper")
                    .setStatus("失败")
                    .setStep("文件匹配错误")
            );
            log.error("uploadZQFile_文件匹配错误");
            throw new Exception("文件匹配错误");
        }
    }

    /**
     * @param file           上传的文件
     * @param targetFilePath 目标文件路径
     * @param dataDate       数据日期
     * @param step           步骤
     * @throws Exception
     */
    public void dealCopyAndUpdate(MultipartFile file,
                                  String targetFilePath,
                                  String dataDate,
                                  String step) throws Exception {
        Files.copy(file.getInputStream(), Paths.get(targetFilePath), StandardCopyOption.REPLACE_EXISTING);
        Integer error = checkNetValue(targetFilePath);
        log.info("dealCopyAndUpdate_error:{}", error);
        if (error == -1) {
            log.error("dealCopyAndUpdate_未找到基金单位净值列");
            throw new Exception("未找到基金单位净值列");
        }
        this.update(Wrappers.lambdaUpdate(MonthlyDataEntity.class)
                .eq(MonthlyDataEntity::getDataDate, dataDate)
                .eq(MonthlyDataEntity::getTaskType, MonthlyDataFileEnum.ZQJHJZ.getTaskType())
                .set(MonthlyDataEntity::getFileUpdateTime, new Date())
                .set(MonthlyDataEntity::getUpdateTime, new Date())
                .set(error > 0, MonthlyDataEntity::getDataStatus, "WR")
                .set(error == 0, MonthlyDataEntity::getDataStatus, "R")
                .set(MonthlyDataEntity::getLocalFilePath, targetFilePath)
                .set(MonthlyDataEntity::getStep, step)
                .set(MonthlyDataEntity::getDownloadStatus, 1)
                .set(MonthlyDataEntity::getMailSendStatus, MailStatus.UNSENT.name())
                .set(MonthlyDataEntity::getMailSendTime, null)
                .set(MonthlyDataEntity::getUserId, SecureUtil.currentUserName())
        );
    }

    /**
     * 生成文件全路径
     *
     * @param dataDate 数据日期
     * @param fileName 文件名称
     * @return
     */
    public String genFilePath(String dataDate, String fileName) {
        /**
         * 月度数据-本级目录名称
         */
        String activeDirName = "monthlyData";
        return ensureTrailingSeparator(baseDir) + activeDirName + File.separator + dataDate + File.separator + fileName;
    }

    /**
     * 确保文件夹路径以 \或者/ 结尾
     *
     * @param pathStr 父目录
     * @return
     */
    private static String ensureTrailingSeparator(String pathStr) {
        String separator = Paths.get(pathStr).getFileSystem().getSeparator();
        if (!pathStr.endsWith(separator)) return pathStr + separator;
        return pathStr;
    }

    /**
     * 校验单位净值
     *
     * @param filePath 文件路径
     * @return
     * @throws Exception
     */
    public static Integer checkNetValue(String filePath) throws Exception {
        int error = 0;
        ZipSecureFile.setMinInflateRatio(0.001);
        try (Workbook sourceWorkbook = WorkbookFactory.create(new FileInputStream(filePath))) {
            Sheet sourceSheet = sourceWorkbook.getSheet("Sheet1");
            Row headerRow = sourceSheet.getRow(0);
            if (headerRow == null) {
                sourceWorkbook.close();
                throw new Exception("表头不存在");
            }
            int dwjzColumnIndex = -1;
            for (Cell cell : headerRow) {
                if (cell.getCellType() == CellType.STRING && "基金单位净值".equalsIgnoreCase(cell.getStringCellValue().trim())) {
                    dwjzColumnIndex = cell.getColumnIndex();
                    break;
                }
            }
            if (dwjzColumnIndex == -1) {
                log.error("未找到基金单位净值列");
                return -1;
            }
            for (int srcRowNum = 1; srcRowNum <= sourceSheet.getLastRowNum(); srcRowNum++) {
                Row sourceRow = sourceSheet.getRow(srcRowNum);
                if (sourceRow == null)
                    continue;
                Cell sourceCell = sourceRow.getCell(dwjzColumnIndex);
                if (sourceCell == null)
                    continue;
                if (sourceCell.getCellType() == CellType.NUMERIC) {
                    double value = sourceCell.getNumericCellValue();
                    if (value <= 95.0) {
                        error++;
                    }
                }
                if (sourceCell.getCellType() == CellType.STRING
                        && StringUtils.isNotBlank(sourceCell.getStringCellValue())) {
                    double value = Double.parseDouble(sourceCell.getStringCellValue());
                    if (value <= 95.0) {
                        error++;
                    }
                }
            }
        }
        return error;
    }

    /**
     * yyyy-mm-dd转yyyymmdd
     *
     * @param normDateStr yyyy-mm-dd
     * @return yyyymmdd
     */
    public static String dateNorm2Pure(String normDateStr) {
        return LocalDate.parse(normDateStr, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN))
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 获取月度日志记录
     *
     * @param dataDate
     * @return
     */
    public MonthlyDataLogDto getMonthlyDataLogs(String dataDate) {
        MonthlyDataLogDto dto = new MonthlyDataLogDto();
        dto.setLogCallRpas(logCallRpaMonthlyDataMapper.getCallRpaLogsByDataDate(dataDate));
        dto.setLogFileUploads(logFileOperMonthlyDataMapper.selectList(Wrappers.lambdaQuery(LogFileOperMonthlyDataEntity.class)
                .eq(LogFileOperMonthlyDataEntity::getDataDate, dataDate)
                .eq(LogFileOperMonthlyDataEntity::getSource, "手工上传")
                .orderByDesc(LogFileOperMonthlyDataEntity::getCreateTime)
        ));
        dto.setLogFileDownloads(logFileOperMonthlyDataMapper.selectList(Wrappers.lambdaQuery(LogFileOperMonthlyDataEntity.class)
                .eq(LogFileOperMonthlyDataEntity::getDataDate, dataDate)
                .ne(LogFileOperMonthlyDataEntity::getSource, "手工上传")
                .orderByDesc(LogFileOperMonthlyDataEntity::getCreateTime)
        ));
        dto.setLogMailSend(logMailSendMonthlyDataMapper.selectList(Wrappers.lambdaQuery(LogMailSendMonthlyDataEntity.class)
                .eq(LogMailSendMonthlyDataEntity::getDataDate, dataDate)
                .orderByDesc(LogMailSendMonthlyDataEntity::getCreateTime)
        ));
        return dto;
    }

    /**
     * 下载日志数据对应的文件
     *
     * @param id
     * @param logType
     * @return
     * @throws Exception
     */
    public ResponseEntity<InputStreamResource> downloadByLogId(String id, String logType) throws Exception {
        String filePath = "";
        switch (logType) {
            case "fileOper":
                LogFileOperMonthlyDataEntity fileOper = logFileOperMonthlyDataMapper.selectById(id);
                if (ObjectUtil.isNotNull(fileOper)) {
                    filePath = fileOper.getFilePath();
                }
                break;
            case "callRpa":
                LogCallRpaMonthlyDataEntity callRpa = logCallRpaMonthlyDataMapper.selectById(id);
                if (ObjectUtil.isNotNull(callRpa)) {
                    filePath = callRpa.getFilePath();
                }
                break;
        }
        if (StringUtils.isBlank(filePath)) {
            throw new Exception("文件不存在");
        }
        File file = new File(filePath);
        if (ObjectUtil.isNull(file) || !file.exists()) {
            throw new Exception("目标文件不存在");
        }
        String prefix = getFileNameNoExtension(file.getName()),
                postfix = getFileExt(file.getName());
        InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(new String(prefix.getBytes(), StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("+", "%20") + "." + postfix);
        headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_OCTET_STREAM_VALUE));
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(resource);
    }
}
