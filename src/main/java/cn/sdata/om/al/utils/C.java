/**
 * Alipay.com Inc. Copyright (c) 2004-2014 All Rights Reserved.
 */
package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Stream;

/**
 * 参数默认值工具类
 *
 * <AUTHOR>
 */
public class C {

    /**
     * @param <T>    the type parameter
     * @param source the source
     * @return the stream
     */
    public static <T> Stream<T> stream(Collection<T> source) {
        return notNul(source).stream();
    }

    /**
     * @param <T>       the type parameter
     * @param condition the condition
     * @param trueFunc  the true func
     * @param falseFunc the false func
     * @return the t
     */
    public static <T> T condition(boolean condition, Supplier<T> trueFunc, Supplier<T> falseFunc) {
        if (condition) {
            return trueFunc.get();
        } else {
            return falseFunc.get();
        }
    }

    /**
     * 获取默认值, 如果为空这返回空列表(可编辑)
     *
     * @param source 当前值
     * @param <T>    列表类型
     * @return 参数值
     */
    public static <T> Collection<T> notNul(final Collection<T> source) {
        return condition(Objects.isNull(source), ArrayList::new, () -> source);
    }

    /**
     * 获取默认值, 如果为空这返回空列表(可编辑)
     *
     * @param value 当前值
     * @param <T>   列表类型
     * @return 参数值
     */
    public static <T> List<T> notNul(final List<T> value) {
        return condition(Objects.isNull(value), ArrayList::new, () -> value);
    }

    /**
     * 获取默认值,如果为空则返回空Map(可编辑)
     *
     * @param map 当前值
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 非null的Map
     */
    public static <K, V> Map<K, V> notNul(final Map<K, V> map) {
        return condition(Objects.isNull(map), HashMap::new, () -> map);
    }

    /**
     * @param value        当前值
     * @param defaultValue 默认值
     * @return int
     */
    public static int defaultValue(final int value, final int defaultValue) {
        return condition(value <= 0, () -> defaultValue, () -> value);
    }

    /**
     * @param value        当前值
     * @param defaultValue 默认值
     * @return 参数值
     */
    public static String defaultValue(final String value, final String defaultValue) {
        return condition(StrUtil.isBlank(value), () -> defaultValue, () -> value);
    }

    /**
     * @param object       当前值
     * @param defaultValue 默认值
     * @return 参数值
     */
    public static <T> T defaultValue(final T object, final T defaultValue) {
        return condition(Objects.isNull(object), () -> defaultValue, () -> object);
    }

    /**
     * @param value        当前值
     * @param defaultValue 默认值
     * @return 参数值
     */
    public static <T> List<T> defaultValue(final List<T> value, final List<T> defaultValue) {
        return condition(CollUtil.isEmpty(value), () -> defaultValue, () -> value);
    }
}
