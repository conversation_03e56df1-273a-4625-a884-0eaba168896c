package cn.sdata.om.al.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Random;

/**
 * <AUTHOR>
 * @Date 2025/2/18 14:12
 * @Version 1.0
 */
public class CaptchaUtil {
    // 验证码字符集
    private static final String CHARACTERS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final int WIDTH = 120; // 图片宽度
    private static final int HEIGHT = 40; // 图片高度
    private static final int FONT_SIZE = 20; // 字体大小
    private static final int CODE_LENGTH = 4; // 验证码长度

    // 生成验证码图片
    public static Captcha generateCaptcha() {
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 设置背景颜色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, WIDTH, HEIGHT);

        // 绘制干扰线
        Random random = new Random();
        for (int i = 0; i < 10; i++) {
            g.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g.drawLine(random.nextInt(WIDTH), random.nextInt(HEIGHT), random.nextInt(WIDTH), random.nextInt(HEIGHT));
        }

        // 绘制验证码
        StringBuilder captchaText = new StringBuilder();
        for (int i = 0; i < CODE_LENGTH; i++) {
            String ch = String.valueOf(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
            captchaText.append(ch);
            g.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g.setFont(new Font("Arial", Font.BOLD, FONT_SIZE));
            g.drawString(ch, 20 + i * 25, 30);
        }

        g.dispose();

        // 将图片转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "JPEG", outputStream);
        } catch (IOException e) {
            throw new RuntimeException("生成验证码失败", e);
        }

        return new Captcha(captchaText.toString(), outputStream.toByteArray());
    }

    // 验证码对象
    public static class Captcha {
        private final String text;
        private final byte[] imageBytes;

        public Captcha(String text, byte[] imageBytes) {
            this.text = text;
            this.imageBytes = imageBytes;
        }

        public String getText() {
            return text;
        }

        public byte[] getImageBytes() {
            return imageBytes;
        }
    }
}
