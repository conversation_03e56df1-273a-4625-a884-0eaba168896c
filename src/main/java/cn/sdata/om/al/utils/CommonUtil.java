package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.sdata.om.al.entity.CommonEntity;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

public class CommonUtil {

    public static List<CommonEntity> stringToCommonEntityList(List<String> list) {
        List<CommonEntity> resList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (String s : list) {
                CommonEntity commonEntity = new CommonEntity();
                commonEntity.setId(s);
                commonEntity.setName(s);
                resList.add(commonEntity);
            }
            return resList;
        }
        return List.of();
    }

    /**
     * 下载文件
     *
     * @param response 响应对象
     * @param file     文件
     * @throws Exception 异常
     */
    public static void downloadFile(HttpServletResponse response, File file) throws Exception {
        InputStream inputStream = FileUtil.getInputStream(file);
        response.reset();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(file.getName(), StandardCharsets.UTF_8));
        ServletOutputStream outputStream = response.getOutputStream();
        byte[] b = new byte[1024];
        int len;
        //从输入流中读取一定数量的字节，并将其存储在缓冲区字节数组中，读到末尾返回-1
        while ((len = inputStream.read(b)) > 0) {
            outputStream.write(b, 0, len);
        }
        inputStream.close();
    }

    /**
     * 处理数字3位格式化
     *
     * @param fieldValue 值
     * @return 格式化后数据
     */
    public static String handleNumberStr(Object fieldValue) {
        if (fieldValue != null) {
            double parseDouble = Double.parseDouble(String.valueOf(fieldValue));
            DecimalFormat decimalFormat = new DecimalFormat("#,###.##");
            return decimalFormat.format(parseDouble);
        }
        return "";
    }

}
