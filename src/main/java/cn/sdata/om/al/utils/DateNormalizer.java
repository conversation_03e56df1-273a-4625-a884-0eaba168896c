package cn.sdata.om.al.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DateNormalizer {

    // 内部类，用于封装 Formatter 和其快速检查逻辑
    private static class FormatCheck {
        final DateTimeFormatter formatter;
        final int expectedLength; // 预期长度，-1 表示不检查或长度可变
        final Character requiredSeparator; // 检查是否包含此分隔符，null 表示不检查

        FormatCheck(DateTimeFormatter formatter, int expectedLength, Character requiredSeparator) {
            this.formatter = formatter;
            this.expectedLength = expectedLength;
            this.requiredSeparator = requiredSeparator;
        }

        // 快速检查方法
        boolean mightMatch(String input) {
            // 1. 检查长度 (如果定义了预期长度)
            if (expectedLength != -1 && input.length() != expectedLength) {
                return false;
            }
            // 2. 检查分隔符 (如果定义了分隔符)
            //    注意: indexOf 比 contains 稍快一点点，对于单个字符检查足够
            return requiredSeparator == null || input.indexOf(requiredSeparator) >= 0;
            // 3. (可以添加其他快速检查，例如检查是否只包含数字和特定分隔符)
            // ...

            // 通过所有快速检查
        }
    }

    // 1. 定义可能的输入格式列表，并按最常用到最少用的顺序排列
    //    同时关联快速检查条件
    private static final List<FormatCheck> INPUT_FORMAT_CHECKS = List.of(
            // 假设 yyyy-MM-dd 最常见
            new FormatCheck(DateTimeFormatter.ofPattern("yyyy-MM-dd"), 10, '-'),
            // 其次是 yyyy/MM/dd
            new FormatCheck(DateTimeFormatter.ofPattern("yyyy/MM/dd"), 10, '/'),
            // 然后是 yyyyMMdd
            new FormatCheck(DateTimeFormatter.ofPattern("yyyyMMdd"), 8, null), // 长度为8，无特定分隔符
            // 最后是中文格式
            new FormatCheck(DateTimeFormatter.ofPattern("yyyy年MM月dd日", Locale.CHINA), -1, '年') // 长度可变(如2025年5月5日 vs 2025年05月05日)，检查'年'
            // 继续添加其他格式和检查...
    );

    // 2. 定义目标输出格式
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 3. 定义日期范围的正则表达式模式
    private static final List<Pattern> DATE_RANGE_PATTERNS = List.of(
            // 匹配 "自2025年04月25日至2025年04月25日" 格式
            Pattern.compile("自(\\d{4}年\\d{1,2}月\\d{1,2}日)至(\\d{4}年\\d{1,2}月\\d{1,2}日)"),
            // 匹配 "2025-04-25至2025-04-25" 格式
            Pattern.compile("(\\d{4}-\\d{1,2}-\\d{1,2})至(\\d{4}-\\d{1,2}-\\d{1,2})"),
            // 匹配 "2025/04/25至2025/04/25" 格式
            Pattern.compile("(\\d{4}/\\d{1,2}/\\d{1,2})至(\\d{4}/\\d{1,2}/\\d{1,2})"),
            // 匹配 "从2025年04月25日到2025年04月25日" 格式
            Pattern.compile("从(\\d{4}年\\d{1,2}月\\d{1,2}日)到(\\d{4}年\\d{1,2}月\\d{1,2}日)")
    );

    /**
     * 优化后的标准化方法
     *
     * @param dateString 输入的日期字符串
     * @return 统一格式后的日期字符串，如果无法解析则返回 null
     */
    public static String normalizeDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        String trimmedDateString = dateString.trim();

        // 首先尝试解析日期范围格式
        String dateFromRange = tryParseDateRange(trimmedDateString);
        if (dateFromRange != null) {
            return dateFromRange;
        }

        // 如果不是日期范围，则按原有逻辑处理单个日期
        // 遍历格式检查列表 (已按常用顺序排列)
        for (FormatCheck check : INPUT_FORMAT_CHECKS) {
            // 先执行快速的启发式检查
            if (check.mightMatch(trimmedDateString)) {
                // 如果快速检查通过，再尝试调用 parse (相对较慢的操作)
                try {
                    LocalDate parsedDate = LocalDate.parse(trimmedDateString, check.formatter);
                    // 解析成功，格式化并返回
                    return parsedDate.format(OUTPUT_FORMATTER);
                } catch (DateTimeParseException ignored) {
                    // 即使快速检查通过了，严格的 parse 仍可能失败，继续下一个格式
                }
            }
            // else: 快速检查未通过，直接跳过这个格式的 parse 尝试
        }

        // 所有格式尝试完毕仍失败
        log.error("无法解析日期字符串: '{}'", dateString);
        return null;
    }

    /**
     * 尝试解析日期范围格式
     *
     * @param dateString 输入的日期字符串
     * @return 解析成功返回起始日期，失败返回 null
     */
    private static String tryParseDateRange(String dateString) {
        for (Pattern pattern : DATE_RANGE_PATTERNS) {
            Matcher matcher = pattern.matcher(dateString);
            if (matcher.find()) {
                // 提取第一个日期（起始日期）
                String startDateStr = matcher.group(1);

                // 递归调用 normalizeDate 来解析提取出的日期字符串
                // 注意：这里需要避免无限递归，所以我们直接调用单日期解析逻辑
                String normalizedDate = normalizeSingleDate(startDateStr);
                if (normalizedDate != null) {
                    log.debug("从日期范围 '{}' 中提取起始日期: '{}'", dateString, normalizedDate);
                    return normalizedDate;
                }
            }
        }
        return null;
    }

    /**
     * 解析单个日期（避免递归调用）
     *
     * @param dateString 单个日期字符串
     * @return 标准化后的日期字符串，失败返回 null
     */
    private static String normalizeSingleDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        String trimmedDateString = dateString.trim();

        for (FormatCheck check : INPUT_FORMAT_CHECKS) {
            if (check.mightMatch(trimmedDateString)) {
                try {
                    LocalDate parsedDate = LocalDate.parse(trimmedDateString, check.formatter);
                    return parsedDate.format(OUTPUT_FORMATTER);
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
        }
        return null;
    }
}
