package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import cn.sdata.om.al.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DateUtils extends DateUtil {

    /**
     * 获取两个日期之间的所有日期（包含起始日期和结束日期）
     *
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 包含起始日期和结束日期在内的所有日期列表
     */
    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        // 使用一个循环从起始日期开始，逐日递增，直到结束日期
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }


    /**
     * 获取两个日期之间的所有日期（包含起始日期和结束日期）
     *
     * @param startDateStr 起始日期
     * @param endDateStr   结束日期
     * @return 包含起始日期和结束日期在内的所有日期列表
     */
    public static List<String> getDatesBetweenToStr(String startDateStr, String endDateStr) {
        List<String> dates = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        // 使用一个循环从起始日期开始，逐日递增，直到结束日期
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(LocalDateTimeUtil.format(currentDate, "yyyy-MM-dd"));
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }


    /**
     * 获取当前日期上个季度的最后一个月
     * @param date 日期
     * @return yyyy-MM
     */
    public static String getLastMonthOfPreviousQuarterFormatted(Date date) {
        LocalDateTime localDateTime = LocalDateTimeUtil.of(date);
        int currentYear = localDateTime.getYear();
        int currentMonthValue = localDateTime.getMonthValue();
        // 计算当前季度索引（0-3对应Q1-Q4）
        int currentQuarter = (currentMonthValue - 1) / 3;
        int previousQuarter = currentQuarter - 1;
        // 处理跨年逻辑
        int adjustedYear = currentYear;
        if (previousQuarter < 0) {
            previousQuarter = 3; // 上一季度为去年的第四季度
            adjustedYear -= 1;   // 年份减1
        }
        // 计算上一季度的最后一个月（3、6、9、12月）
        int lastMonthOfPreviousQuarter = (previousQuarter + 1) * 3;
        // 构造格式化字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return LocalDate.of(adjustedYear, lastMonthOfPreviousQuarter, 1)
                .format(formatter);
    }


    public static List<String> getAllHolidays(int todayYear) {
        try (InputStream resourceAsStream = DateUtils.class.getClassLoader().getResourceAsStream("holidays/" + todayYear + "/days.txt")) {
            assert resourceAsStream != null;
            byte[] bytes = resourceAsStream.readAllBytes();
            String jsonStr = new String(bytes);
            return JSONUtil.toList(jsonStr, String.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return List.of();
    }

    // 获取当月的第7个工作日
    public static LocalDate findSeventhWorkday(LocalDate inputDate) {
        LocalDate date = inputDate.withDayOfMonth(1); // 从当月1号开始
        int workdayCount = 0;
        while (workdayCount < 7) {
            if (isWorkday(date)) {
                workdayCount++;
                if (workdayCount == 7) return date; // 找到第7个工作日
            }
            date = date.plusDays(1); // 下一天
        }
        return null; // 理论上不会执行
    }


    // 获取当月的第7个工作日
    public static LocalDate findEighthWorkday(LocalDate inputDate) {
        LocalDate date = inputDate.withDayOfMonth(1); // 从当月1号开始
        int workdayCount = 0;
        while (workdayCount < 8) {
            if (isWorkday(date)) {
                workdayCount++;
                if (workdayCount == 8) return date; // 找到第7个工作日
            }
            date = date.plusDays(1); // 下一天
        }
        return null; // 理论上不会执行
    }


    // 获取当月的第10个工作日
    public static LocalDate findTenthWorkday(LocalDate inputDate) {
        LocalDate date = inputDate.withDayOfMonth(1); // 从当月1号开始
        int workdayCount = 0;
        while (workdayCount < 10) {
            if (isWorkday(date)) {
                workdayCount++;
                if (workdayCount == 10) return date; // 找到第7个工作日
            }
            date = date.plusDays(1); // 下一天
        }
        return null; // 理论上不会执行
    }

    // 判断是否为工作日（排除周末和节假日）
    public static boolean isWorkday(LocalDate date) {
        if (CollectionUtil.isEmpty(DateUtils.getAllHolidays(date.getYear()))) {
            BusinessException.throwException("找不到节假日信息");
        }
        return !(date.getDayOfWeek().getValue() >= 6) // 非周六(6)、周日(7)
                && !DateUtils.getAllHolidays(date.getYear()).contains(LocalDateTimeUtil.format(date, "yyyy-MM-dd"));
    }

    public static String transformDate(String currDate, String transferTime) {
        if (StringUtils.isNotBlank(currDate) && !"0".equals(transferTime) && StringUtils.isNotBlank(transferTime)) {
            if (transferTime.length() < 6) {
                StringBuilder res = new StringBuilder();
                for (int i = 0; i < 6 - transferTime.length(); i++) {
                    res.insert(0, "0");
                }
                transferTime = res + transferTime;
            }
            String finalTransferTime = currDate + transferTime;
            try {
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 解析为LocalDateTime对象 → 直接格式化为目标字符串
                return LocalDateTime.parse(finalTransferTime, inputFormatter)
                        .format(outputFormatter);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return DateUtil.parse(currDate, "yyyy-MM-dd").toString();
        }
        return "";
    }

    public static String[] getQuarterMonths() {
        // 获取当前日期（示例：2025-06-26）
        LocalDate currentDate = LocalDate.now();
        // 计算上个季度的日期（当前日期减去3个月）
        LocalDate date = currentDate.minusMonths(3);
        int year = date.getYear();
        int monthValue = date.getMonthValue();
        int startMonth, endMonth;
        // 根据月份确定季度范围
        if (monthValue >= 1 && monthValue <= 3) {
            startMonth = 1;
            endMonth = 3;
        } else if (monthValue >= 4 && monthValue <= 6) {
            startMonth = 4;
            endMonth = 6;
        } else if (monthValue >= 7 && monthValue <= 9) {
            startMonth = 7;
            endMonth = 9;
        } else {
            startMonth = 10;
            endMonth = 12;
        }
        // 格式化为 yyyy-MM
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String startMonthStr = LocalDate.of(year, startMonth, 1).format(formatter);
        String endMonthStr = LocalDate.of(year, endMonth, 1).format(formatter);
        return new String[]{startMonthStr, endMonthStr};
    }
}
