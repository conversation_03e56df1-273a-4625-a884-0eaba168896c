package cn.sdata.om.al.utils;

import cn.sdata.om.al.config.EncryptionConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Date 2025/2/17 15:57
 * @Version 1.0
 */
@Slf4j
@Component
@Data
@AllArgsConstructor
public class EncryptionUtil {

    private final EncryptionConfig encryptionConfig;

    // 加密
    public String encrypt(String plainText) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(encryptionConfig.getKey().getBytes(), encryptionConfig.getAlgorithm());
        IvParameterSpec ivParameterSpec = new IvParameterSpec(encryptionConfig.getIv().getBytes());
        Cipher cipher = Cipher.getInstance(encryptionConfig.getTransformation());
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    // 解密
    public String decrypt(String encryptedText) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(encryptionConfig.getKey().getBytes(), encryptionConfig.getAlgorithm());
        IvParameterSpec ivParameterSpec = new IvParameterSpec(encryptionConfig.getIv().getBytes());
        Cipher cipher = Cipher.getInstance(encryptionConfig.getTransformation());
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes);
    }

}
