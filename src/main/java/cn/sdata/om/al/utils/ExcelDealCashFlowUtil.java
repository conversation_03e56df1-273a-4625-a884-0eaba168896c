package cn.sdata.om.al.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;

/**
 * 现金流量预测-excel处理工具
 *
 * <AUTHOR>
 * @Date 2025/3/27 23:30
 * @Version 1.0
 */
@Slf4j
@Component
public class ExcelDealCashFlowUtil {

    private static final DecimalFormat DECIMAL_FORMAT = (DecimalFormat) DecimalFormat.getInstance(Locale.getDefault());

    static {
        DECIMAL_FORMAT.setGroupingUsed(true);
        DECIMAL_FORMAT.setGroupingSize(3);
    }

    /**
     * @param sourceFilePath 源文件路径
     * @param targetFilePath 目标文件路径
     * @throws IOException
     */
    public void transferAndCalculate(String sourceFilePath, String targetFilePath) throws IOException {
        log.info("ExcelDealCashFlowUtil_start:{},{}", sourceFilePath, targetFilePath);
        // 1. 从源文件提取符合条件的行
        List<Object[]> extractedRows = extractRows(sourceFilePath);
        // 2. 将行插入到目标文件
        insertRows(targetFilePath, extractedRows);
        // 3. 从源文件删除已移动的行
        removeRows(sourceFilePath, extractedRows);
        // 4. 重新计算两个文件的合计
        recalculateTotal(targetFilePath);
        recalculateTotal(sourceFilePath);
        log.info("ExcelDealCashFlowUtil_end:{},{}", sourceFilePath, targetFilePath);
    }

    /**
     * 从源文件提取符合条件的行
     *
     * @param filePath
     * @return
     * @throws IOException
     */
    private List<Object[]> extractRows(String filePath) throws IOException {
        List<Object[]> rows = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            // 从第6行开始（索引5），跳过表头
            for (int i = 5; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                Cell codeCell = row.getCell(0);
                if (codeCell == null) continue;
                String code = getCellStringValue(codeCell);
                if (code == null || code.isEmpty()) continue;
                // 检查是否以1,2,4开头
                if (code.startsWith("1") || code.startsWith("2") || code.startsWith("4")) {
                    Object[] rowData = new Object[3];
                    for (int j = 0; j < 3; j++) {
                        Cell cell = row.getCell(j);
                        rowData[j] = getCellStringValue(cell);
                    }
                    rows.add(rowData);
                }
            }
        }
        return rows;
    }

    /**
     * 将行插入到目标文件
     *
     * @param filePath
     * @param rowsToInsert
     * @throws IOException
     */
    private void insertRows(String filePath, List<Object[]> rowsToInsert) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            int insertPos = 5; // 在第6行位置插入（索引5）
            // 向下移动现有行，腾出空间
            if (sheet.getLastRowNum() >= insertPos) {
                sheet.shiftRows(insertPos, sheet.getLastRowNum(), rowsToInsert.size(), true, false);
            }
            // 插入新行
            for (int i = 0; i < rowsToInsert.size(); i++) {
                Row newRow = sheet.createRow(insertPos + i);
                Object[] rowData = rowsToInsert.get(i);

                for (int j = 0; j < rowData.length; j++) {
                    Cell cell = newRow.createCell(j);
                    setCellValue(cell, rowData[j]);
                }
            }
            saveWorkbook(workbook, filePath);
        }
    }

    /**
     * 从源文件删除已移动的行
     *
     * @param filePath
     * @param movedRows
     * @throws IOException
     */
    private void removeRows(String filePath, List<Object[]> movedRows) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            // 创建一个集合来存储被移动行的标识（第一列的值）
            Set<String> movedCodes = new HashSet<>();
            for (Object[] row : movedRows) {
                if (row != null && row.length > 0 && row[0] != null) {
                    movedCodes.add(row[0].toString());
                }
            }
            // 从最后一行开始检查
            for (int i = lastRowNum; i >= 5; i--) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                Cell codeCell = row.getCell(0);
                if (codeCell == null) continue;
                String code = getCellStringValue(codeCell);
                if (code == null || code.isEmpty()) continue;
                // 只有当行的代码在movedCodes集合中时才删除
                if (movedCodes.contains(code)) {
                    sheet.removeRow(row);
                }
            }
            // 压缩空行
            compressSheet(sheet);
            saveWorkbook(workbook, filePath);
        }
    }

    /**
     * 压缩空格
     *
     * @param sheet
     */
    private void compressSheet(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();
        // 从最后一行开始向前遍历
        for (int i = lastRowNum; i >= 0; i--) {
            Row row = sheet.getRow(i);
            if (row == null || isRowEmpty(row)) {
                // 如果行为空，删除该行
                if (i < lastRowNum) {
                    // 将下面的行上移
                    sheet.shiftRows(i + 1, lastRowNum, -1);
                }
                // 更新最后一行索引
                lastRowNum--;
            }
        }
    }

    /**
     * 判断行是否为空
     *
     * @param row
     * @return
     */
    private static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 重新计算两个文件的合计
     *
     * @param filePath
     * @throws IOException
     */
    private void recalculateTotal(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            double totalAmount = 0;
            int itemCount = 0;
            // 从第6行开始计算
            for (int i = 5; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                Cell firstCell = row.getCell(0);
                if (firstCell == null || null == getCellStringValue(firstCell) || getCellStringValue(firstCell).contains("合计")) {
                    continue;
                } else {
                    itemCount++;
                }
                Cell amountCell = row.getCell(2);
                if (amountCell != null) {
                    try {
                        String amountStr = getCellStringValue(amountCell);
                        double amount = parseNumber(amountStr);
                        totalAmount += amount;
                        //itemCount++;
                    } catch (ParseException e) {
                        // 忽略非数字值
                        log.error("recalculateTotal_error:{},{}", e, e.getMessage());
                    }
                }
            }

            // 查找或创建合计行
            Row totalRow = findOrCreateTotalRow(sheet);
            // 更新合计行的值
            Cell countCell = totalRow.getCell(1) != null ? totalRow.getCell(1) : totalRow.createCell(1);
            Cell amountCell = totalRow.getCell(2) != null ? totalRow.getCell(2) : totalRow.createCell(2);
            countCell.setCellValue(itemCount + "个账套");
            // 设置金额格式（带千分位）
            CellStyle amountStyle = workbook.createCellStyle();
            DataFormat format = workbook.createDataFormat();
            amountStyle.setDataFormat(format.getFormat("#,##0.00"));
            amountCell.setCellValue(totalAmount);
            amountCell.setCellStyle(amountStyle);
            saveWorkbook(workbook, filePath);
        }
    }

    /**
     * 获取总计行
     *
     * @param sheet
     * @return
     */
    private Row findOrCreateTotalRow(Sheet sheet) {
        // 先查找现有的合计行
        for (int i = sheet.getLastRowNum(); i >= 5; i--) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Cell firstCell = row.getCell(0);
                if (firstCell != null && "合计".equals(getCellStringValue(firstCell))) {
                    return row;
                }
            }
        }
        // 没有找到，创建新行
        Row newRow = sheet.createRow(sheet.getLastRowNum() + 1);
        newRow.createCell(0).setCellValue("合计");
        return newRow;
    }

    /**
     * 获取单元格值
     *
     * @param cell
     * @return
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    DataFormatter formatter = new DataFormatter();
                    return formatter.formatCellValue(cell);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                switch (cell.getCachedFormulaResultType()) {
                    case NUMERIC:
                        return String.valueOf(cell.getNumericCellValue());
                    case STRING:
                        return cell.getStringCellValue();
                    default:
                        return cell.getCellFormula();
                }
            default:
                return null;
        }
    }

    /**
     * 设置单元格值
     *
     * @param cell
     * @param value
     */
    private void setCellValue(Cell cell, Object value) {
        if (cell == null) return;
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        }
    }

    /**
     * 解析金额类字符-数字
     *
     * @param value
     * @return
     * @throws ParseException
     */
    private double parseNumber(String value) throws ParseException {
        if (value == null || value.isEmpty()) return 0;
        // 移除可能存在的千分位和货币符号
        String cleaned = value.replaceAll("[^\\d.-]", "");
        return DECIMAL_FORMAT.parse(cleaned).doubleValue();
    }

    /**
     * 保存文件
     *
     * @param workbook
     * @param filePath
     * @throws IOException
     */
    private void saveWorkbook(Workbook workbook, String filePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            workbook.write(fos);
        }
    }
}
