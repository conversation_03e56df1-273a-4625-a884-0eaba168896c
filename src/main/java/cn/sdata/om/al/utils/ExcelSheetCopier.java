package cn.sdata.om.al.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

public class ExcelSheetCopier {

    /**
     * 复制工作表到同一个工作簿
     * @param workbook 工作簿
     * @param sourceSheetIndex 源工作表索引
     * @param targetSheetName 目标工作表名称
     * @return 新工作表
     */
    public static Sheet copySheet(Workbook workbook, int sourceSheetIndex, String targetSheetName) {
        Sheet sourceSheet = workbook.getSheetAt(sourceSheetIndex);
        Sheet targetSheet = workbook.createSheet(targetSheetName);

        // 复制所有行和单元格
        copyRows(workbook, sourceSheet, targetSheet);

        // 复制合并区域
        copyMergedRegions(sourceSheet, targetSheet);

        // 复制列宽
        copyColumnWidths(sourceSheet, targetSheet);

        // 复制工作表属性
        copySheetProperties(sourceSheet, targetSheet);

        return targetSheet;
    }

    private static void copyRows(Workbook workbook, Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i <= sourceSheet.getLastRowNum(); i++) {
            Row sourceRow = sourceSheet.getRow(i);
            Row targetRow = targetSheet.createRow(i);

            if (sourceRow != null) {
                targetRow.setHeight(sourceRow.getHeight());

                for (int j = 0; j < sourceRow.getLastCellNum(); j++) {
                    Cell sourceCell = sourceRow.getCell(j);

                    if (sourceCell != null) {
                        Cell targetCell = targetRow.createCell(j);
                        copyCellValue(sourceCell, targetCell);
                        copyCellStyle(workbook, sourceCell, targetCell);
                    }
                }
            }
        }
    }

    private static void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case ERROR:
                targetCell.setCellErrorValue(sourceCell.getErrorCellValue());
                break;
            case BLANK:
            case _NONE:
            default:
                // 不做任何处理
                break;
        }
    }

    private static void copyCellStyle(Workbook workbook, Cell sourceCell, Cell targetCell) {
        CellStyle newCellStyle = workbook.createCellStyle();
        newCellStyle.cloneStyleFrom(sourceCell.getCellStyle());
        targetCell.setCellStyle(newCellStyle);

        // 复制字体
        Font sourceFont = workbook.getFontAt(sourceCell.getCellStyle().getFontIndex());
        Font targetFont = workbook.createFont();
        targetFont.setBold(sourceFont.getBold());
        targetFont.setItalic(sourceFont.getItalic());
        targetFont.setFontName(sourceFont.getFontName());
        targetFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
        targetFont.setColor(sourceFont.getColor());
        newCellStyle.setFont(targetFont);
    }

    private static void copyMergedRegions(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            targetSheet.addMergedRegion(new CellRangeAddress(
                    mergedRegion.getFirstRow(),
                    mergedRegion.getLastRow(),
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            ));
        }
    }

    private static void copyColumnWidths(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i <= getMaxColumnUsed(sourceSheet); i++) {
            targetSheet.setColumnWidth(i, sourceSheet.getColumnWidth(i));
        }
    }

    private static void copySheetProperties(Sheet sourceSheet, Sheet targetSheet) {
        targetSheet.setDefaultRowHeight(sourceSheet.getDefaultRowHeight());

        // 可以根据需要复制更多的工作表属性
        // 如页面设置、打印设置等
    }

    private static int getMaxColumnUsed(Sheet sheet) {
        int maxColumn = 0;
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null && row.getLastCellNum() > maxColumn) {
                maxColumn = row.getLastCellNum();
            }
        }
        return maxColumn;
    }
}
