package cn.sdata.om.al.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.config.CustomCellWriteWeightConfig;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.NORMAL_FONT_SIZE;

public class ExcelUtil {

    public static CellStyle getCellStyle(XSSFWorkbook wb, boolean ifHead) {
        Objects.requireNonNull(wb, "工作簿不得为空");

        // 创建单元格样式
        CellStyle cellStyle = wb.createCellStyle();

        // 创建字体
        Font font = wb.createFont();
        font.setFontName("宋体"); // 设置字体名称
        font.setFontHeightInPoints((short) 10); // 设置字体大小

        // 设置自动换行
        cellStyle.setWrapText(true);

        // 设置细边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);

        // 设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);


        if (ifHead) {
            font.setBold(true);
            // 设置背景色
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        cellStyle.setFont(font);
        return cellStyle;
    }

    public static int getWidth(String str){
        if (str == null) {
            return 0;
        }
        return (str.getBytes().length + 5) * 256;
    }

    public static void setWidthList(XSSFSheet sheet, int[] widthList, int index, String value){
        int groupIdWidth = widthList[index];
        int width = getWidth(value);
        if (width > groupIdWidth) {
            sheet.setColumnWidth(index, width);
            widthList[index] = width;
        }
    }

    public static <T> void exportExcel(HttpServletResponse response, List<?> list, String fileName, Class<?> entityClass, boolean flag) {
        ServletOutputStream outputStream = null;
        Map<Integer, Integer> customColumnWidths = new HashMap<>();
        if (flag){
            customColumnWidths.put(2, 40);
        }

        try {
            byte[] byteArray;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ExcelWriterSheetBuilder sheet = EasyExcelFactory.write(out, entityClass)
                    .registerWriteHandler(new CustomCellWriteWeightConfig())
                    .registerWriteHandler(new CustomCellWriteWeightConfig().getStyleStrategy()).sheet();

            sheet.doWrite(list);
            byteArray = out.toByteArray();
            // 获取字节流
            outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, String.valueOf(StandardCharsets.UTF_8)));
            outputStream.write(byteArray);
        }catch (Exception e){
            throw new RuntimeException("导出Excel异常");
        }finally {
            try {
                if (ObjectUtil.isNotNull(outputStream)) {
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
