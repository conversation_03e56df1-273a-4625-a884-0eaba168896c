package cn.sdata.om.al.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;


import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/25 17:09
 * @Version 1.0
 */
@Slf4j
@Component
public class HsExcelCopyUtil {

    /**
     * 通过poi复制excel
     *
     * @param sourceFilePath
     * @param sourceSheetName
     * @param targetFilePath
     * @param targetSheetName
     */
    public static void copyByPoi(String sourceFilePath, String sourceSheetName,
                                 String targetFilePath, String targetSheetName) {
        try {
            // 调整压缩比阈值（低于默认值 0.01）
            ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
            // 1. 读取源工作簿和目标工作簿
            Workbook sourceWorkbook = new XSSFWorkbook(new FileInputStream(sourceFilePath));
            Workbook targetWorkbook = new XSSFWorkbook(new FileInputStream(targetFilePath));

            // 2. 获取源工作表和目标工作表
            Sheet sourceSheet = sourceWorkbook.getSheet(sourceSheetName);
            if (sourceSheet == null) {
                sourceWorkbook.close();
                targetWorkbook.close();
                throw new IllegalArgumentException("源工作表不存在: " + sourceSheetName);
            }

            // 3. 如果目标工作表已存在，则删除后重建（保持其他sheet不变）
            Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);
            if (targetSheet != null) {
                int sheetIndex = targetWorkbook.getSheetIndex(targetSheet);
                targetWorkbook.removeSheetAt(sheetIndex);
                targetWorkbook.createSheet(targetSheetName);
                targetSheet = targetWorkbook.getSheet(targetSheetName);
            } else {
                targetSheet = targetWorkbook.createSheet(targetSheetName);
            }

            // 4. 样式缓存
            Map<Short, CellStyle> styleCache = new HashMap<>();

            // 5. 复制合并区域
            copyMergedRegions(sourceSheet, targetSheet);

            // 6. 复制行和单元格（带样式缓存）
            for (int i = 0; i <= sourceSheet.getLastRowNum(); i++) {
                Row sourceRow = sourceSheet.getRow(i);
                if (sourceRow != null) {
                    Row targetRow = targetSheet.createRow(i);
                    copyRowWithStyle(sourceRow, targetRow, targetWorkbook, styleCache);
                }
            }

            // 7. 复制列宽
            copyColumnWidths(sourceSheet, targetSheet);

            // 8. 特别处理前两行的合并居中样式
            reinforceFirstTwoRowsStyle(sourceSheet, targetSheet, targetWorkbook, styleCache);

            // 9. 保存目标工作簿（覆盖原文件）
            try (FileOutputStream fos = new FileOutputStream(targetFilePath)) {
                targetWorkbook.write(fos);
            }

            // 10. 关闭工作簿
            sourceWorkbook.close();
            targetWorkbook.close();

        } catch (Exception e) {
            log.error("copyByPoi_error:{},{}", e, e.getMessage());
            throw new RuntimeException("copyByPoi复制Excel文件失败", e);
        }
    }

    // 复制合并区域
    private static void copyMergedRegions(Sheet sourceSheet, Sheet targetSheet) {
        for (CellRangeAddress mergedRegion : sourceSheet.getMergedRegions()) {
            targetSheet.addMergedRegion(new CellRangeAddress(
                    mergedRegion.getFirstRow(),
                    mergedRegion.getLastRow(),
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            ));
        }
    }

    // 复制行内容及样式（带缓存）
    private static void copyRowWithStyle(Row sourceRow,
                                         Row targetRow,
                                         Workbook targetWorkbook,
                                         Map<Short, CellStyle> styleCache) {
        targetRow.setHeight(sourceRow.getHeight());

        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell targetCell = targetRow.createCell(i);
                copyCellWithStyle(sourceCell, targetCell, targetWorkbook, styleCache);
            }
        }
    }

    // 复制单元格内容及样式（带缓存）
    private static void copyCellWithStyle(Cell sourceCell,
                                          Cell targetCell,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        // 复制单元格值
        CellType cellType = sourceCell.getCellType();
        switch (cellType) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                targetCell.setBlank();
        }

        CellStyle sourceStyle = sourceCell.getCellStyle();
        short sourceStyleIndex = sourceStyle.getIndex();

        CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
            CellStyle newStyle = targetWorkbook.createCellStyle();
            newStyle.cloneStyleFrom(sourceStyle);
            return newStyle;
        });

        targetCell.setCellStyle(targetStyle);
    }

    // 复制列宽
    private static void copyColumnWidths(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getRow(0).getLastCellNum(); i++) {
            targetSheet.setColumnWidth(i, sourceSheet.getColumnWidth(i));
        }
    }

    // 特别强化前两行的样式（带缓存）
    private static void reinforceFirstTwoRowsStyle(Sheet sourceSheet,
                                                   Sheet targetSheet,
                                                   Workbook targetWorkbook,
                                                   Map<Short, CellStyle> styleCache) {
        // 处理第一行
        if (sourceSheet.getRow(0) != null && targetSheet.getRow(0) != null) {
            reinforceRowStyle(sourceSheet.getRow(0), targetSheet.getRow(0), targetWorkbook, styleCache);
        }

        // 处理第二行
        if (sourceSheet.getRow(1) != null && targetSheet.getRow(1) != null) {
            reinforceRowStyle(sourceSheet.getRow(1), targetSheet.getRow(1), targetWorkbook, styleCache);
        }
    }

    // 强化行样式（带缓存）
    private static void reinforceRowStyle(Row sourceRow,
                                          Row targetRow,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            Cell targetCell = targetRow.getCell(i);

            if (sourceCell != null && targetCell != null) {
                // 从缓存获取或创建样式
                CellStyle sourceStyle = sourceCell.getCellStyle();
                short sourceStyleIndex = sourceStyle.getIndex();

                CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
                    CellStyle newStyle = targetWorkbook.createCellStyle();
                    newStyle.cloneStyleFrom(sourceStyle);
                    return newStyle;
                });

                targetCell.setCellStyle(targetStyle);
            }
        }
    }

    /**
     * 对UL的统计工作表公式进行专门的处理
     *
     * @param sourceFilePath
     * @param sourceSheetName
     */
    public static void setFormulaTypeAndCalculate(String sourceFilePath, String sourceSheetName) {
        // 调整压缩比阈值（低于默认值 0.01）
        ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
        try (Workbook sourceWorkbook = new XSSFWorkbook(new FileInputStream(sourceFilePath))) {
            Sheet sourceSheet = sourceWorkbook.getSheet(sourceSheetName);
            if (sourceSheet == null) {
                sourceWorkbook.close();
                throw new IllegalArgumentException("Sheet not found: " + sourceSheetName);
            }

            DataFormat dataFormat = sourceWorkbook.createDataFormat();
            FormulaEvaluator evaluator = sourceWorkbook.getCreationHelper().createFormulaEvaluator();

            Cell b6Cell = sourceSheet.getRow(5).getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            evaluator.evaluateFormulaCell(b6Cell);

            Cell i6Cell = sourceSheet.getRow(5).getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            evaluator.evaluateFormulaCell(i6Cell);

            for (int rowNum = 9; rowNum <= 22; rowNum++) {
                Row row = sourceSheet.getRow(rowNum);
                if (row == null) continue;
                for (int colNum = 1; colNum < 12; colNum++) {
                    Cell cell = row.getCell(colNum, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    if (cell == null) continue;
                    // 跳过会计/数值格式
                    String formatString = dataFormat.getFormat(cell.getCellStyle().getDataFormat());
                    System.out.println("自定义格式: " + formatString);
                    if (isAccountingOrNumberFormat(formatString)) {
                        System.out.printf("Skipping [%s%d]: Accounting/Number format\n",
                                CellReference.convertNumToColString(colNum), rowNum + 1);
                        continue;
                    }
                    // 保留原值并设置为公式类型
                    CellType cellType = cell.getCellType();
                    if (cellType != CellType.FORMULA) {
                        System.out.printf("单元格 [%s] 类型: %s%n", cell.getAddress(), cellType);
                        Object originalValue = getCellValue(cell);
                        if (ObjectUtil.isNull(originalValue))
                            continue;
                        if (cellType.equals(CellType.STRING) && StringUtils.isNotBlank(String.valueOf(originalValue))) {
                            //System.out.printf("Updated [%s%d] to formula (original: %s)\n", CellReference.convertNumToColString(colNum), rowNum + 1, originalValue);
                            cell.setCellFormula(sanitizeFormula(String.valueOf(originalValue)));
                        }

                    }
                    evaluator.evaluateFormulaCell(cell);
                }
            }
            // 保存文件
            try (FileOutputStream out = new FileOutputStream(sourceFilePath)) {
                sourceWorkbook.write(out);
            }
            sourceWorkbook.close();
            log.info("setFormulaTypeAndCalculate end");

        } catch (Exception e) {
            log.error("setFormulaTypeAndCalculate_error:{},{}", e, e.getMessage());
        }
    }

    public static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case FORMULA:
                return getFormulaCellValue(cell);
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                }
                return cell.getNumericCellValue();
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case ERROR:
                return cell.getErrorCellValue();
            case BLANK:
                return "";
            default:
                return null;
        }
    }

    private static Object getFormulaCellValue(Cell cell) {
        switch (cell.getCachedFormulaResultType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                }
                return cell.getNumericCellValue();
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case ERROR:
                return cell.getErrorCellValue();
            default:
                return cell.getCellFormula(); // 返回公式本身
        }
    }

    private static boolean isAccountingOrNumberFormat(String formatString) {
        return formatString.contains("¥")
                || formatString.contains("$")
                || formatString.contains("0.00")
                || formatString.contains("#,##0");
    }

    public static String sanitizeFormula(String formula) {
        if (formula.startsWith("=+")) {
            formula = formula.substring(2);
        }
        if (formula.startsWith("=")) {
            formula = formula.substring(1);
        }
        // 确保含空格的工作表名被正确引用
        if (formula.contains(" ")) {
            formula = formula.replaceFirst("=([^']+!)", "='$1");
        }
        return formula;
    }


    public static void evaluateFormulas(String sourceFilePath, String sourceSheetName) {
        try (Workbook workbook = new XSSFWorkbook(new FileInputStream(sourceFilePath))) {
            // 1. 获取指定Sheet
            Sheet sheet = workbook.getSheet(sourceSheetName);
            if (sheet == null) {
                throw new IllegalArgumentException("Sheet '" + sourceSheetName + "' 不存在！");
            }
            DataFormat dataFormat = workbook.createDataFormat();
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            int startRow = 9; // 第10行
            int startCol = 1; // B列

            for (int rowNum = startRow; rowNum <= 22; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) continue;

                for (int colNum = startCol; colNum < 12; colNum++) {
                    Cell cell = row.getCell(colNum, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    if (cell == null) continue;

                    evaluator.evaluateFormulaCell(cell);
                }
            }

            // 3. 遍历所有行和单元格
            /*for (Row row : sheet) {
                if (row == null) continue; // 跳过空行
                for (Cell cell : row) {
                    if (cell == null) continue; // 跳过空单元格

                    try {
                        evaluator.evaluateFormulaCell(cell);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }*/

            // 6. 保存文件（覆盖原文件或另存为新文件）
            try (FileOutputStream out = new FileOutputStream(sourceFilePath)) {
                workbook.write(out);
            }
            System.out.println("公式计算完成，文件已保存！");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*public static void main(String[] args) {
        copyByPoi("C:\\Users\\<USER>\\Desktop\\2024-11-13\\证券投资基金估值表_投连LA投连成长_2024-11-13.xlsx",
                "Sheet1",
                "C:\\Users\\<USER>\\Desktop\\2024-11-13\\UL valuation_取数模板.xlsx",
                "HS-LA0001");

        setFormulaTypeAndCalculate("C:\\Users\\<USER>\\Desktop\\2024-11-13\\UL valuation_取数模板.xlsx",
                "Daily Summary");
    }*/
}
