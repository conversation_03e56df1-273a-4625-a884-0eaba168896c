package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.MD5;
import cn.sdata.om.al.entity.mail.*;
import cn.sdata.om.al.mapper.mail.MailAttachmentMapper;
import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class JavaMailUtils {

    @Value("${file.dir}")
    private String baseFilePath;

    private MailAttachmentMapper mailAttachmentMapper;

    @Autowired
    public void setMailAttachmentMapper(MailAttachmentMapper mailAttachmentMapper) {
        this.mailAttachmentMapper = mailAttachmentMapper;
    }

    /**
     * 读取邮件
     *
     * @param message 消息对象
     * @return 日志内容对象
     */
    @SneakyThrows
    public MailContent getMailContent(Message message) {
        MailContent mailContent = new MailContent();
        String contentId = IdUtil.getSnowflakeNextIdStr();
        mailContent.setId(contentId);
        int messageNumber = message.getMessageNumber();
        log.info("======正在同步第{}条======", messageNumber);
        mailContent.setMessageNumber(messageNumber);
        // 标题
        String subject = StringUtils.isNotEmpty(message.getSubject()) ? MimeUtility.decodeText(message.getSubject()) : "";
        mailContent.setSubject(subject);
        // 发件人
        Address[] from = message.getFrom();
        if (from.length > 0) {
            InternetAddress internetAddress = (InternetAddress) from[0];
            String personalStr = StringUtils.defaultIfEmpty(internetAddress.getPersonal(), StringUtils.EMPTY);
            String personal = MimeUtility.decodeText(personalStr);
            MailUser mailUser = new MailUser(personal, internetAddress.getAddress());
            mailContent.setMailFrom(mailUser);
            mailContent.setMailFromStr(JSON.toJSONString(mailUser));
        }
        // 抄送人
        Address[] recipients = message.getAllRecipients();
        if (null != recipients && recipients.length > 0) {
            List<MailUser> receiveUsers = new ArrayList<>();
            for (Address recipient : recipients) {
                InternetAddress internetAddress = (InternetAddress) recipient;
                String address = internetAddress.getAddress();
                String personalStr = StringUtils.defaultIfEmpty(internetAddress.getPersonal(), StringUtils.EMPTY);
                String personal = MimeUtility.decodeText(personalStr);
                MailUser mailUser = new MailUser(personal, address);
                receiveUsers.add(mailUser);
            }
            mailContent.setReceive(receiveUsers);
            mailContent.setReceiveStr(JSON.toJSONString(receiveUsers));
        }
        boolean containAttachment = isContainAttachment(message);
        // 邮件包含附件
        if (containAttachment) {
            List<Attachment> attachments = new ArrayList<>();
            // 处理附件
            saveAttachment(message, attachments);
            // 附件列表
            mailContent.setAttachments(attachments.stream().map(n -> {
                MailAttachment mailAttachment = new MailAttachment();
                mailAttachment.setFileName(n.getFileName());
                mailAttachment.setFilePath(n.getFilePath() + n.getFileName());
                mailAttachment.setId(IdUtil.getSnowflakeNextIdStr());
                mailAttachment.setType(1);
                mailAttachment.setMailId(contentId);
                return mailAttachment;
            }).collect(Collectors.toList()));
            List<MailAttachment> mailAttachments = mailContent.getAttachments();
            mailContent.setAttachmentStr(JSON.toJSONString(mailAttachments));
            if (CollectionUtil.isNotEmpty(mailAttachments)) {
                mailAttachmentMapper.batchSave(mailAttachments);
            }
        }
        MailTextTO mailTextTO = new MailTextTO();
        getMailTextContent(message, mailTextTO);
        mailContent.setContent(mailTextTO.getContent());
        // 发送时间
        mailContent.setSentDate(DateUtil.format(message.getReceivedDate(), "yyyy-MM-dd"));
        // 接收时间
        mailContent.setReceivedDate(DateUtil.format(message.getSentDate(), "yyyy-MM-dd"));
        mailContent.setReceivedTime(message.getReceivedDate());
        // 邮件分拣状态
        mailContent.setPickStatus(0);
        mailContent.setBoxId("1");
        return mailContent;
    }


    /**
     * 判断邮件中是否包含附件
     *
     * @param part 邮件内容
     * @return 邮件中存在附件返回true，不存在返回false
     */
    private boolean isContainAttachment(Part part) throws MessagingException, IOException {
        boolean flag = false;
        if (part.isMimeType("multipart/*")) {
            MimeMultipart multipart = (MimeMultipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                //某一个邮件体也有可能是由多个邮件体组成的复杂体
                String disposition = bodyPart.getDisposition();
                // 这个部分是一个附件或者内联显示的附件
                if (disposition != null && (disposition.equalsIgnoreCase(Part.ATTACHMENT) || disposition.equalsIgnoreCase(Part.INLINE))) {
                    flag = true;
                    break; // 发现附件后立即结束循环
                } else {
                    //可能这个部分是一个 multipart 部分，内部还有更多的子部分 递归调用检查子部分是否包含附件
                    flag = isContainAttachment(bodyPart);
                    if (flag) {
                        break; // 发现附件后立即结束循环
                    }
                }
            }
        } else if (part.isMimeType("message/rfc822")) {
            // 递归调用检查内部消息是否包含附件
            flag = isContainAttachment((Part) part.getContent());
        } else {
            // 检查单个部分的内容类型是否为 application 类型
            String contentType = part.getContentType();
            if (contentType != null && (contentType.toLowerCase().contains("application") || contentType.toLowerCase().contains("name"))) {
                flag = true;
            }
        }
        return flag;
    }


    /**
     * 获得邮件文本内容
     *
     * @param part       邮件体
     * @param mailTextTO 存储邮件文本内容的对象
     */
    private void getMailTextContent(Part part, MailTextTO mailTextTO) throws MessagingException, IOException {
        boolean isContainTextAttach = part.getContentType().contains("name");
        // 1、先进text/plain h5为false 拼上去 h5为ture 不拼内容
        // 2、进text/html content 清空 拼接字符串 ifH5改成ture
        if (part.isMimeType("text/html") && !isContainTextAttach) {
            mailTextTO.setContent("");
            mailTextTO.setContent(part.getContent().toString());
            mailTextTO.setIfH5(true);
        } else if (part.isMimeType("text/plain")) {
            //若邮件正文不是html格式,则需要在判断一遍是否为文本格式,否则会导致抓取的邮件内容为空
            if (!mailTextTO.ifH5) {
                mailTextTO.setContent(part.getContent().toString());
            }
        } else if (part.isMimeType("message/rfc822")) {
            getMailTextContent((Part) part.getContent(), mailTextTO);
        } else if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                getMailTextContent(bodyPart, mailTextTO);
            }
        }
    }


    /**
     * 处理邮件中的附件
     *
     * @param part 邮件中多个组合体中的其中一个组合体
     */
    private void saveAttachment(Part part, List<Attachment> attachments) throws Exception {
        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();    //复杂体邮件
            //复杂体邮件包含多个邮件体
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                //获得复杂体邮件中其中一个邮件体
                BodyPart bodyPart = multipart.getBodyPart(i);
                String disp = bodyPart.getDisposition();
                if (disp != null && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                    InputStream is = bodyPart.getInputStream();
                    String fileName = bodyPart.getFileName().replaceAll("[\\r\\n\\t\"]", "");
                    uploadAttachment2Local(fileName, is, attachments);
                } else if (bodyPart.isMimeType("multipart/*")) {
                    //某一个邮件体也有可能是由多个邮件体组成的复杂体 递归处理
                    saveAttachment(bodyPart, attachments);
                } else {
                    String contentType = bodyPart.getContentType();
                    if (contentType.toLowerCase().contains("name") || contentType.toLowerCase().contains("application")) {
                        String fileName = bodyPart.getFileName().replaceAll("[\\r\\n\\t\"]", "");
                        uploadAttachment2Local(fileName, bodyPart.getInputStream(), attachments);
                    }
                }
            }
        } else if (part.isMimeType("message/rfc822")) {
            saveAttachment((Part) part.getContent(), attachments);
        } else {
            // NOTICE:这种情况无法获取通过bodyPart.getFileName()获取fileName 只能从头部字段中提取了
            String fileName = "";
            String contentType = part.getContentType();
            if (contentType.toLowerCase().contains("name") || contentType.toLowerCase().contains("application")) {
                // 获取 Content-Disposition 头部字段
                String[] contentDisposition = part.getHeader("Content-Disposition");
                if (contentDisposition != null) {
                    // 从 Content-Disposition 头部字段中提取文件名
                    for (String disposition : contentDisposition) {
                        if (disposition.startsWith("attachment") || disposition.startsWith("inline")) {
                            // 如果包含附件或内联，则提取文件名信息
                            int index = disposition.indexOf("filename=");
                            if (index != -1) {
                                fileName = disposition.substring(index + 9).replaceAll("[\"'\\s]", "");
                                break;
                            }
                        }
                    }
                }
                fileName = fileName.replaceAll("[\\r\\n\\t\"]", "");
                uploadAttachment2Local(fileName, part.getInputStream(), attachments);
            }
        }
    }

    /**
     * 保存附件文件到本地
     *
     * @param fileName    文件名
     * @param is          流
     * @param attachments 附件列表
     */
    private void uploadAttachment2Local(String fileName, InputStream is, List<Attachment> attachments) throws Exception {
        String filePath = null;

        // 改进的解码逻辑，处理所有可能的编码情况
        try {
            // 统一使用MimeUtility进行解码，它能处理各种RFC 2047格式的编码
            fileName = MimeUtility.decodeText(fileName.replace("\"", ""));
        } catch (Exception e) {
            // 如果标准解码失败，尝试使用自定义解码方法
            if (fileName.contains("?")) {
                fileName = getPdfName(fileName);
            }
            log.info("附件名称解码异常: {}", e.getMessage());
        }
        if (null != is) {
            filePath = baseFilePath + File.separator + DateUtil.today() + File.separator + IdUtil.simpleUUID() + File.separator;
            // 附件上传到指定路径
            FileUtil.mkdir(filePath);
            File file = FileUtil.file(filePath + fileName);
            FileUtil.writeBytes(is.readAllBytes(), file);
        }
        Attachment attachment = new Attachment();
        attachment.setId(MD5.create().digestHex(fileName));
        attachment.setFileName(fileName);
        attachment.setFilePath(filePath);
        attachments.add(attachment);
    }


    /**
     * 对采用Base64编码的结果进行解码
     *
     * @param fileName 编码后的名称(字符串中自有部分是Base64编码后的结果)
     */
    public String getPdfName(String fileName) {
        if (!fileName.contains("=?")) {
            return fileName;
        }
        StringBuilder decodedFileName = new StringBuilder();
        int lastIndex = 0;
        // 改进的正则表达式，同时处理Base64编码(B)和引用可打印编码(Q)
        Pattern pattern = Pattern.compile("=\\?([A-Za-z0-9-]+)\\?([BQ])\\?([^?]+)\\?=");
        Matcher matcher = pattern.matcher(fileName);
        while (matcher.find()) {
            // 添加解码前的部分
            decodedFileName.append(fileName, lastIndex, matcher.start());
            try {
                String charset = matcher.group(1); // 获取字符集
                String encodingType = matcher.group(2); // 获取编码类型 B或Q
                String encodedText = matcher.group(3); // 获取编码的文本
                
                String decodedText;
                if ("B".equalsIgnoreCase(encodingType)) {
                    // 解码 Base64 编码
                    byte[] decodedBytes = Base64.getDecoder().decode(encodedText);
                    decodedText = new String(decodedBytes, charset);
                } else {
                    // 解码 Quoted-Printable 编码 (Q编码)
                    decodedText = decodeQuotedPrintable(encodedText, charset);
                }
                decodedFileName.append(decodedText);
            } catch (Exception e) {
                log.error("解码文件名失败: {}", e.getMessage());
                // 如果解码失败，保留原始编码的文本
                decodedFileName.append(fileName, matcher.start(), matcher.end());
            }
            // 更新 lastIndex
            lastIndex = matcher.end();
        }
        // 添加解码后的最后部分（如果存在）
        decodedFileName.append(fileName.substring(lastIndex));
        // 移除可能存在的换行符
        // 移除可能存在的换行符和制表符
        return decodedFileName.toString();
    }

    /**
     * 解码Quoted-Printable (Q编码)格式的文本
     * 
     * @param text Q编码的文本
     * @param charset 字符集
     * @return 解码后的文本
     */
    private String decodeQuotedPrintable(String text, String charset) throws Exception {
        // 首先替换特殊字符
        text = text.replace('_', ' ');
        
        StringBuilder result = new StringBuilder();
        int i = 0;
        while (i < text.length()) {
            char c = text.charAt(i);
            if (c == '=') {
                if (i + 2 < text.length()) {
                    try {
                        String hex = text.substring(i + 1, i + 3);
                        int value = Integer.parseInt(hex, 16);
                        result.append((char)value);
                        i += 3;
                    } catch (NumberFormatException e) {
                        result.append(c);
                        i++;
                    }
                } else {
                    result.append(c);
                    i++;
                }
            } else {
                result.append(c);
                i++;
            }
        }
        
        return new String(result.toString().getBytes("ISO-8859-1"), charset);
    }

}
