package cn.sdata.om.al.utils;

import cn.sdata.om.al.constant.JobConstant;
import cn.sdata.om.al.qrtz.constant.CronConstant;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;

import java.util.Objects;

public class JobExecutionContextUtil {

    public static String getDataDate(JobExecutionContext jobExecutionContext) {
        Objects.requireNonNull(jobExecutionContext, "jobExecutionContext不为空");
        JobDataMap mergedJobDataMap = jobExecutionContext.getMergedJobDataMap();
        return (String) mergedJobDataMap.get(JobConstant.DATA_DATE);
    }

    public static String getTradeCalendar(JobExecutionContext jobExecutionContext) {
        Objects.requireNonNull(jobExecutionContext, "jobExecutionContext不为空");
        JobDataMap mergedJobDataMap = jobExecutionContext.getMergedJobDataMap();
        return (String) mergedJobDataMap.get(CronConstant.TRADE_CALENDAR);
    }

}
