package cn.sdata.om.al.utils;

import cn.hutool.core.lang.Tuple;
import cn.sdata.om.al.config.DynamicSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.util.Hashtable;

/**
 * 安联域控登录认证，与永用户读取
 * <p>
 * 只读user: rpa.services
 * 域控：**********、**********
 * 域名：aziamc.com
 * ou：aziamc.com\aziamc
 * 密码：Aa7ujm&UJM
 * 这个是测试环境对接办公网
 */
@Slf4j
public class LdapAuthUtils {

    private static int getPort(String ldapUrl) {
        try {
            // 解析 URL
            String protocol = ldapUrl.substring(0, ldapUrl.indexOf(':'));
            String hostAndPort = ldapUrl.substring(ldapUrl.indexOf("//") + 2);
            int port = -1;

            // 检查是否有显式端口
            if (hostAndPort.contains(":")) {
                port = Integer.parseInt(hostAndPort.split(":")[1]);
            } else {
                // 如果没有显式端口，使用默认端口
                port = (protocol.equals("ldaps")) ? 636 : 389;
            }
            return port;
        } catch (Exception e) {
            log.error("Invalid URL: {} ", ldapUrl, e);
        }
        return -1;
    }


    /**
     * 域控链接验证，不做数据用户同同步
     *
     * @param username 域控账户
     * @param password 密码
     * @param ldapUrl  服务器地址
     * @return bool
     */
    public static Pair<Boolean, Integer> authenticate(String username, String password, String ldapUrl) {
        if (getPort(ldapUrl) == 636) {
            return sslLdap(username, password, ldapUrl);
        }
        if (getPort(ldapUrl) == 389) {
            return normalLdap(username, password, ldapUrl);
        }

        return Pair.of(false, 500);
    }


    private static Pair<Boolean, Integer> normalLdap(String username, String password, String ldapUrl) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl); // LDAP 服务器地址
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username); // LDAP 用户的 DN"cn="+username + ",ou=develop,dc=ADTEST,dc=cn"
        env.put(Context.SECURITY_CREDENTIALS, password);
        try {
            DirContext context = new InitialDirContext(env);
            context.close();
            log.info("ldaps 验证通过 : {}", username);
            return Pair.of(true, 200);
        } catch (AuthenticationException e) {
            log.error("ldaps 389 AuthenticationException 验证失败 {},{}", username, e);
            String errorMessage = e.getExplanation();
            if (errorMessage != null && errorMessage.contains("49")) {
                if (errorMessage.contains("525")
                        || errorMessage.contains("533")
                        || errorMessage.contains("534")
                        || errorMessage.contains("701")
                        || errorMessage.contains("775")) {
                    return Pair.of(false, 404);
                } else if (errorMessage.contains("52e")
                        || errorMessage.contains("532")
                        || errorMessage.contains("773")) {
                    return Pair.of(false, 501);
                }
                return Pair.of(false, 49);
            } else if (errorMessage != null && errorMessage.contains("32")) {
                return Pair.of(false, 32);
            }
            return Pair.of(false, 500);
        } catch (NamingException e) {
            log.error("ldaps 验证失败 {}", username, e);
            return Pair.of(false, 500);
        }
    }

    private static Pair<Boolean, Integer> sslLdap(String username, String password, String ldapUrl) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, username);
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put(Context.SECURITY_PROTOCOL, "ssl");
        env.put("java.naming.ldap.factory.socket", DynamicSSLSocketFactory.class.getName());

        try {
            DirContext context = new InitialDirContext(env);
            context.close();
            log.info("ldaps 验证通过 : {}", username);
            return Pair.of(true, 200);
        } catch (AuthenticationException e) {
            log.error("ldaps 636 AuthenticationException 验证失败 {},{}", username, e);
            String errorMessage = e.getExplanation();
            if (errorMessage != null && errorMessage.contains("49")) {
                if (errorMessage.contains("525")
                        || errorMessage.contains("533")
                        || errorMessage.contains("534")
                        || errorMessage.contains("701")
                        || errorMessage.contains("775")) {
                    return Pair.of(false, 404);
                } else if (errorMessage.contains("52e")
                        || errorMessage.contains("532")
                        || errorMessage.contains("773")) {
                    return Pair.of(false, 501);
                }
                return Pair.of(false, 49);
            } else if (errorMessage != null && errorMessage.contains("32")) {
                return Pair.of(false, 32);
            }
            return Pair.of(false, 500);
        } catch (NamingException e) {
            log.error("ldaps 验证失败 {}", username, e);
            return Pair.of(false, 500);
        }
    }


}
