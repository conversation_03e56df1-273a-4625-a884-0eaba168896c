package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.mapper.*;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class LogBRUtil {

    /**
     * 差异标记 - 调用方法前记录日志
     * @param params 参数
     */
    public synchronized static void preMarkDiffLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            LogBRMarkDiffRecord logBRMarkDiffRecord = new LogBRMarkDiffRecord();
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            logBRMarkDiffRecord.setId(logId);
            logBRMarkDiffRecord.setDataDate(params.getString("dataDate"));
            logBRMarkDiffRecord.setExecuteTime(beginTime);
            logBRMarkDiffRecord.setCreateByName(username);
            logBRMarkDiffRecord.setParams(JSON.toJSONString(params));
            logBRMarkDiffRecord.setStatus(CommonStatus.EXECUTING.name());
            List<LogBRMarkDiffDesc> logBRMarkDiffDescList = generateDesc(params);
            if (CollectionUtil.isNotEmpty(logBRMarkDiffDescList)) {
                bankReconciliationLogMapper.batchSaveMarkDiffDesc(logBRMarkDiffDescList);
            }
            logBRMarkDiffRecord.setMarkDesc(logBRMarkDiffDescList);
            bankReconciliationLogMapper.saveMarkDiffLog(logBRMarkDiffRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static List<LogBRMarkDiffDesc> generateDesc(JSONObject params) {
        try {
            List<LogBRMarkDiffDesc> logBRMarkDiffDescList = new ArrayList<>();
            List<String> ids = params.getList("ids", String.class);
            String differenceReasons = params.getString("differenceReasons");
            if (CollectionUtil.isEmpty(ids)) {
                return new ArrayList<>();
            }
            List<JSONObject> jsonObjectList = SpringUtil.getBean(BankReconciliationMapper.class).getProductByIds(ids);
            if (CollectionUtil.isNotEmpty(jsonObjectList)) {
                for (JSONObject jsonObject : jsonObjectList) {
                    LogBRMarkDiffDesc logBRMarkDiffDesc = new LogBRMarkDiffDesc();
                    String productId = jsonObject.getString("productId");
                    String productName = jsonObject.getString("productName");
                    logBRMarkDiffDesc.setId(IdUtil.getSnowflakeNextIdStr());
                    logBRMarkDiffDesc.setProductId(productId);
                    logBRMarkDiffDesc.setProductName(productName);
                    logBRMarkDiffDesc.setAction("MARK");
                    logBRMarkDiffDesc.setRemark(differenceReasons);
                    logBRMarkDiffDesc.setLogId(params.getString("logId"));
                    logBRMarkDiffDesc.setSecurityCode(jsonObject.getString("sourceCode"));
                    logBRMarkDiffDesc.setSecurityName(jsonObject.getString("securityName"));
                    logBRMarkDiffDescList.add(logBRMarkDiffDesc);
                }
            }
            return logBRMarkDiffDescList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * 差异标记 - 调用方法后记录日志
     * @param params 参数
     */
    public synchronized static void postMarkDiffLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String result = params.getString("result");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            bankReconciliationLogMapper.updateMarkDiffLogById(logId, endTime, result, errorMsg, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preUploadLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            LogBRImportFileRecord logBRImportFileRecord = new LogBRImportFileRecord();
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String type = params.getString("type");
            String logId = params.getString("logId");
            logBRImportFileRecord.setId(logId);
            logBRImportFileRecord.setCreateByName(username);
            logBRImportFileRecord.setExecuteTime(beginTime);
            logBRImportFileRecord.setStatus(CommonStatus.EXECUTING.name());
            logBRImportFileRecord.setExecuteType(type);
            logBRImportFileRecord.setDataDate(params.getString("dataDate"));
            bankReconciliationLogMapper.saveUploadLog(logBRImportFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postUploadLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            String fileUrl = params.getString("fileUrl");
            String dataDate = params.getString("dataDate");
            bankReconciliationLogMapper.updateUploadLogById(logId, endTime, errorMsg, status, fileUrl, dataDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preSyncLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            LogBRSyncValuationRecord logBRSyncValuationRecord = new LogBRSyncValuationRecord();
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            logBRSyncValuationRecord.setId(logId);
            logBRSyncValuationRecord.setStatus(CommonStatus.EXECUTING.name());
            logBRSyncValuationRecord.setBeginTime(beginTime);
            logBRSyncValuationRecord.setCreateByName(username);
            logBRSyncValuationRecord.setParams(params.getString("params"));
            logBRSyncValuationRecord.setDataDate(params.getString("dataDate"));
            bankReconciliationLogMapper.saveSyncLog(logBRSyncValuationRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postSyncLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            bankReconciliationLogMapper.updateSyncLogById(logId, endTime, errorMsg, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void deleteSyncLog(String logId) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            bankReconciliationLogMapper.deleteSyncLog(logId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preExportLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            LogBRExportFileRecord logBRExportFileRecord = new LogBRExportFileRecord();
            logBRExportFileRecord.setExecuteTime(beginTime);
            logBRExportFileRecord.setStatus(CommonStatus.EXECUTING.name());
            logBRExportFileRecord.setId(logId);
            logBRExportFileRecord.setCreateByName(username);
            logBRExportFileRecord.setDataDate(params.getString("dataDate"));
            bankReconciliationLogMapper.saveExportLog(logBRExportFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postExportLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            String fileUrl = params.getString("fileUrl");
            bankReconciliationLogMapper.updateExportLogById(logId, endTime, errorMsg, status, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preRpaLog(JSONObject params) {
        try {
            BankReconciliationLogMapper bankReconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String logId = params.getString("logId");
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogBRRPARecord brrpaRecord = new LogBRRPARecord();
                String taskId = baseCronLog.getTaskId();
                brrpaRecord.setId(logId);
                brrpaRecord.setTaskId(taskId);
                brrpaRecord.setTaskName(cron != null ? cron.getJobName() : "");
                brrpaRecord.setRpaLogId(rpaLogId);
                brrpaRecord.setBeginTime(DateUtil.now());
                brrpaRecord.setRpaParam(params.getString("rpaParam"));
                brrpaRecord.setDataDate(params.getString("dataDate"));
                brrpaRecord.setCreateByName(params.getString("username"));
                brrpaRecord.setType(params.getString("type"));
                brrpaRecord.setStatus(CommonStatus.EXECUTING.name());
                brrpaRecord.setParams(params.getString("params"));
                bankReconciliationLogMapper.saveRpaLog(brrpaRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postRpaLog(JSONObject params) {
        try {
            BankReconciliationLogMapper reconciliationLogMapper = SpringUtil.getBean(BankReconciliationLogMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String fileType = params.getString("fileType");
            LogBRRPARecord logBRRPARecord = reconciliationLogMapper.getBRRpaLogByRpaLogId(rpaLogId);
            if (logBRRPARecord != null) {
                BaseCronLog baseCronLog = baseCronLogMapper.selectById(logBRRPARecord.getRpaLogId());
                if (baseCronLog != null) {
                    logBRRPARecord.setEndTime(params.getString("endTime"));
                    String exception = params.getString("exception");
                    if (StringUtils.isEmpty(exception)) {
                        exception = baseCronLog.getTaskInfo();
                    }
                    logBRRPARecord.setErrorMsg(exception);
                    logBRRPARecord.setFileType(fileType);
                    logBRRPARecord.setStatus(baseCronLog.getStatus().name());
                    logBRRPARecord.setEndTime(baseCronLog.getEndDateTime());
                    List<RemoteFileInfo> files = params.getList("files", RemoteFileInfo.class);
                    if (CollectionUtil.isNotEmpty(files)) {
                        List<File> localFiles = OmFileUtil.transformToFiles(files);
                        if (CollectionUtil.isNotEmpty(localFiles)) {
                            String brRpaFilePath = SpringUtil.getProperty("file.br-rpa-file-path");
                            File dest = new File(brRpaFilePath + File.separator + fileType + "_银行对账_" + DateUtil.current() + ".zip");
                            OmFileUtil.zipFiles(localFiles, dest);
                            if (dest.exists()) {
                                logBRRPARecord.setFileUrl(dest.getAbsolutePath());
                            }
                        }
                    }
                    reconciliationLogMapper.updateRpaLogById(logBRRPARecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
