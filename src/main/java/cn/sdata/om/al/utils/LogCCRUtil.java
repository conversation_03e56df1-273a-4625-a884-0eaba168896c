package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.LogCCRExportRecord;
import cn.sdata.om.al.entity.LogCCRRPARecord;
import cn.sdata.om.al.entity.LogCCRSendMailRecord;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.CashClearReportLogMapper;
import cn.sdata.om.al.mapper.CronMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.List;

public class LogCCRUtil {

    public static synchronized void preRpaLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String logId = params.getString("logId");
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogCCRRPARecord logCCRRPARecord = new LogCCRRPARecord();
                String taskId = baseCronLog.getTaskId();
                logCCRRPARecord.setId(logId);
                logCCRRPARecord.setTaskId(taskId);
                logCCRRPARecord.setTaskName(cron != null ? cron.getJobName() : "");
                logCCRRPARecord.setRpaLogId(rpaLogId);
                logCCRRPARecord.setBeginTime(DateUtil.now());
                logCCRRPARecord.setRpaParam(params.getString("rpaParam"));
                logCCRRPARecord.setCreateByName(params.getString("username"));
                logCCRRPARecord.setType(params.getString("type"));
                logCCRRPARecord.setDataDate(params.getString("dataDate"));
                logCCRRPARecord.setStatus(CommonStatus.EXECUTING.name());
                logCCRRPARecord.setParams(params.getString("params"));
                cashClearReportLogMapper.saveRpaLog(logCCRRPARecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postRpaLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            LogCCRRPARecord logCCRRPARecord = cashClearReportLogMapper.getCCRRpaLogByRpaLogId(rpaLogId);
            if (logCCRRPARecord != null) {
                BaseCronLog baseCronLog = baseCronLogMapper.selectById(logCCRRPARecord.getRpaLogId());
                if (baseCronLog != null) {
                    logCCRRPARecord.setEndTime(params.getString("endTime"));
                    String exception = params.getString("exception");
                    if (StringUtils.isEmpty(exception)) {
                        exception = baseCronLog.getTaskInfo();
                    }
                    logCCRRPARecord.setErrorMsg(exception);
                    logCCRRPARecord.setStatus(baseCronLog.getStatus().name());
                    logCCRRPARecord.setEndTime(baseCronLog.getEndDateTime());
                    List<RemoteFileInfo> files = params.getList("files", RemoteFileInfo.class);
                    if (CollectionUtil.isNotEmpty(files)) {
                        List<File> localFiles = OmFileUtil.transformToFiles(files);
                        if (CollectionUtil.isNotEmpty(localFiles)) {
                            String ccrRpaFilePath = SpringUtil.getProperty("file.ccr-rpa-file-path");
                            File dest = new File(ccrRpaFilePath + File.separator + "资金清算报表_" + DateUtil.current() + ".zip");
                            OmFileUtil.zipFiles(localFiles, dest);
                            if (dest.exists()) {
                                logCCRRPARecord.setFileUrl(dest.getAbsolutePath());
                            }
                        }
                    }
                    cashClearReportLogMapper.updateRpaLogById(logCCRRPARecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void preExportLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            LogCCRExportRecord logCCRExportRecord = new LogCCRExportRecord();
            logCCRExportRecord.setBeginTime(beginTime);
            logCCRExportRecord.setDataDate(params.getString("dataDate"));
            logCCRExportRecord.setId(logId);
            logCCRExportRecord.setStatus(CommonStatus.EXECUTING.name());
            logCCRExportRecord.setCreateByName(username);
            logCCRExportRecord.setParams(params.getString("params"));
            cashClearReportLogMapper.saveExportLog(logCCRExportRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postExportLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            String fileUrl = params.getString("fileUrl");
            cashClearReportLogMapper.updateExportLogById(logId, endTime, errorMsg, status, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void preSendMailLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogCCRSendMailRecord logCCRSendMailRecord = new LogCCRSendMailRecord();
                logCCRSendMailRecord.setBeginTime(params.getString("beginTime"));
                logCCRSendMailRecord.setId(params.getString("logId"));
                logCCRSendMailRecord.setStatus(params.getString("status"));
                logCCRSendMailRecord.setCreateByName(params.getString("username"));
                logCCRSendMailRecord.setDataDate(params.getString("dataDate"));
                logCCRSendMailRecord.setParams(params.getString("params"));
                logCCRSendMailRecord.setTaskId(baseCronLog.getTaskId());
                logCCRSendMailRecord.setType(params.getString("type"));
                logCCRSendMailRecord.setTaskName(cron != null ? cron.getJobName() : "");
                logCCRSendMailRecord.setRpaLogId(rpaLogId);
                logCCRSendMailRecord.setMailIdStr(params.getString("mailId"));
                logCCRSendMailRecord.setSendStatus(params.getString("sendStatus"));
                logCCRSendMailRecord.setEndTime(params.getString("endTime"));
                cashClearReportLogMapper.saveSendMailLog(logCCRSendMailRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postSendMailLog(JSONObject params) {
        try {
            CashClearReportLogMapper cashClearReportLogMapper = SpringUtil.getBean(CashClearReportLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            List<String> mailIds = params.getList("mailIds", String.class);
            LogCCRSendMailRecord logCCRSendMailRecord = cashClearReportLogMapper.getCCRSendMailLogByLogId(rpaLogId);
            if (logCCRSendMailRecord != null) {
                BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
                BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
                if (baseCronLog != null) {
                    String endDateTime = baseCronLog.getEndDateTime();
                    if (CollectionUtil.isNotEmpty(mailIds)) {
                        logCCRSendMailRecord.setMailIdStr(StringUtils.join(mailIds, ","));
                        logCCRSendMailRecord.setEndTime(endDateTime);
                        logCCRSendMailRecord.setSendStatus(params.getString("sendStatus"));
                        logCCRSendMailRecord.setStatus(params.getString("status"));
                        logCCRSendMailRecord.setErrorMsg(params.getString("exception"));
                        cashClearReportLogMapper.updateSendMailLogById(logCCRSendMailRecord);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
