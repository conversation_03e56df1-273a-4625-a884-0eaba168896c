package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.CronMapper;
import cn.sdata.om.al.mapper.LogFYBMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.List;

public class LogFYBUtils {

    public static void preUpdatePayStatusLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBUpdatePayStatusRecord logFYBUpdatePayStatusRecord = new LogFYBUpdatePayStatusRecord();
            logFYBUpdatePayStatusRecord.setId(params.getString("logId"));
            logFYBUpdatePayStatusRecord.setBeginTime(params.getString("beginTime"));
            logFYBUpdatePayStatusRecord.setPreUpdateStatus(params.getString("preUpdateStatus"));
            logFYBUpdatePayStatusRecord.setProductId(params.getString("productId"));
            logFYBUpdatePayStatusRecord.setParams(params.getString("params"));
            logFYBUpdatePayStatusRecord.setDataDate(params.getString("dataDate"));
            logFYBUpdatePayStatusRecord.setCreateByName(params.getString("username"));
            logFYBMapper.saveUpdatePayStatusLog(logFYBUpdatePayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postUpdatePayStatusLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String endTime = params.getString("endTime");
            String logId = params.getString("logId");
            String postUpdateStatus = params.getString("postUpdateStatus");
            logFYBMapper.modifyUpdatePayStatusLog(logId, endTime, postUpdateStatus);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preDownloadFile(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBDownloadFileRecord fybDownloadFileRecord = new LogFYBDownloadFileRecord();
            fybDownloadFileRecord.setId(params.getString("logId"));
            fybDownloadFileRecord.setBeginTime(params.getString("beginTime"));
            fybDownloadFileRecord.setCreateByName(params.getString("username"));
            fybDownloadFileRecord.setParams(params.getString("params"));
            fybDownloadFileRecord.setStatus(params.getString("status"));
            logFYBMapper.saveDownloadFileLog(fybDownloadFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postDownloadFile(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String logId = params.getString("logId");
            String fileUrl = params.getString("fileUrl");
            String status = params.getString("status");
            String errorMsg = params.getString("errorMsg");
            String endTime = params.getString("endTime");
            logFYBMapper.updateDownloadFileLog(logId, fileUrl, status, errorMsg, endTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preGenerateO32File(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBGenerateFileRecord logFYBGenerateFileRecord = new LogFYBGenerateFileRecord();
            logFYBGenerateFileRecord.setId(params.getString("logId"));
            logFYBGenerateFileRecord.setBeginTime(params.getString("beginTime"));
            logFYBGenerateFileRecord.setCreateByName(params.getString("username"));
            logFYBGenerateFileRecord.setParams(params.getString("params"));
            logFYBGenerateFileRecord.setStatus(params.getString("status"));
            logFYBMapper.saveGenerateO32File(logFYBGenerateFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postGenerateO32File(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String logId = params.getString("logId");
            String fileUrl = params.getString("fileUrl");
            String status = params.getString("status");
            String errorMsg = params.getString("errorMsg");
            String endTime = params.getString("endTime");
            List<String> productIds = params.getList("productIds", String.class);
            String productIdsStr = JSON.toJSONString(productIds);
            logFYBMapper.updateGenerateO32File(logId, fileUrl, status, errorMsg, endTime, productIdsStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preSendMailLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogFYBSendMailRecord logFYBSendMailRecord = new LogFYBSendMailRecord();
                logFYBSendMailRecord.setBeginTime(params.getString("beginTime"));
                logFYBSendMailRecord.setId(params.getString("logId"));
                logFYBSendMailRecord.setStatus(params.getString("status"));
                logFYBSendMailRecord.setCreateByName(params.getString("username"));
                logFYBSendMailRecord.setParams(params.getString("params"));
                logFYBSendMailRecord.setTaskId(baseCronLog.getTaskId());
                logFYBSendMailRecord.setType(params.getString("type"));
                logFYBSendMailRecord.setTaskName(cron != null ? cron.getJobName() : "");
                logFYBSendMailRecord.setRpaLogId(rpaLogId);
                logFYBSendMailRecord.setMailIdStr(params.getString("mailId"));
                logFYBSendMailRecord.setSendStatus(params.getString("sendStatus"));
                logFYBSendMailRecord.setEndTime(params.getString("endTime"));
                logFYBMapper.saveSendMailLog(logFYBSendMailRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preO32ConfirmLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBO32ConfirmRecord logFYBO32ConfirmRecord = new LogFYBO32ConfirmRecord();
            logFYBO32ConfirmRecord.setId(params.getString("logId"));
            logFYBO32ConfirmRecord.setBeginTime(params.getString("beginTime"));
            logFYBO32ConfirmRecord.setCreateByName(params.getString("username"));
            logFYBO32ConfirmRecord.setParams(params.getString("params"));
            logFYBO32ConfirmRecord.setStatus(params.getString("status"));
            logFYBO32ConfirmRecord.setProductIdsStr(JSON.toJSONString(params.getList("productIds", String.class)));
            logFYBMapper.saveO32ConfirmLog(logFYBO32ConfirmRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postO32ConfirmLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String endTime = params.getString("endTime");
            String status = params.getString("status");
            String logId = params.getString("logId");
            logFYBMapper.updateO32ConfirmLog(logId, endTime, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preRpaLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String logId = params.getString("logId");
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogFYBRPARecord logFYBRPARecord = new LogFYBRPARecord();
                String taskId = baseCronLog.getTaskId();
                logFYBRPARecord.setId(logId);
                logFYBRPARecord.setTaskId(taskId);
                logFYBRPARecord.setTaskName(cron != null ? cron.getJobName() : "");
                logFYBRPARecord.setRpaLogId(rpaLogId);
                logFYBRPARecord.setBeginTime(DateUtil.now());
                logFYBRPARecord.setRpaParam(params.getString("rpaParam"));
                logFYBRPARecord.setDataDate(params.getString("dataDate"));
                logFYBRPARecord.setCreateByName(params.getString("username"));
                logFYBRPARecord.setType(params.getString("type"));
                logFYBRPARecord.setStatus(CommonStatus.EXECUTING.name());
                logFYBRPARecord.setParams(params.getString("params"));
                logFYBMapper.saveRpaLog(logFYBRPARecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postRpaLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String fileType = params.getString("fileType");
            LogFYBRPARecord logFYBRPARecord = logFYBMapper.getFYBRpaLogByRpaLogId(rpaLogId);
            if (logFYBRPARecord != null) {
                BaseCronLog baseCronLog = baseCronLogMapper.selectById(logFYBRPARecord.getRpaLogId());
                if (baseCronLog != null) {
                    logFYBRPARecord.setEndTime(params.getString("endTime"));
                    String exception = params.getString("exception");
                    if (StringUtils.isEmpty(exception)) {
                        exception = baseCronLog.getTaskInfo();
                    }
                    logFYBRPARecord.setErrorMsg(exception);
                    logFYBRPARecord.setFileType(fileType);
                    logFYBRPARecord.setStatus(baseCronLog.getStatus().name());
                    logFYBRPARecord.setEndTime(baseCronLog.getEndDateTime());
                    List<RemoteFileInfo> files = params.getList("files", RemoteFileInfo.class);
                    if (CollectionUtil.isNotEmpty(files)) {
                        List<File> localFiles = OmFileUtil.transformToFiles(files);
                        if (CollectionUtil.isNotEmpty(localFiles)) {
                            String fybRpaFilePath = SpringUtil.getProperty("file.fyb-rpa-file-path");
                            File dest = new File(fybRpaFilePath + File.separator + fileType + "_银行间费用_" + DateUtil.current() + ".zip");
                            OmFileUtil.zipFiles(localFiles, dest);
                            if (dest.exists()) {
                                logFYBRPARecord.setFileUrl(dest.getAbsolutePath());
                            }
                        }
                    }
                    logFYBMapper.updateRpaLogById(logFYBRPARecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preSyncPayStatusLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBSyncPayStatusRecord logFYBSyncPayStatusRecord = new LogFYBSyncPayStatusRecord();
            logFYBSyncPayStatusRecord.setId(params.getString("logId"));
            logFYBSyncPayStatusRecord.setBeginTime(params.getString("beginTime"));
            logFYBSyncPayStatusRecord.setCreateByName(params.getString("username"));
            logFYBSyncPayStatusRecord.setParams(params.getString("params"));
            logFYBSyncPayStatusRecord.setProductInfosStr(params.getString("productInfos"));
            logFYBSyncPayStatusRecord.setStatus(params.getString("status"));
            logFYBMapper.saveSyncPayStatusLog(logFYBSyncPayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postSyncPayStatusLog(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String productInfos = params.getString("productInfos");
            String status = params.getString("status");
            logFYBMapper.updateSyncPayStatusLog(logId, endTime, productInfos, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preImportO32Log(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            LogFYBImportO32Record logFYBImportO32Record = new LogFYBImportO32Record();
            logFYBImportO32Record.setId(params.getString("logId"));
            logFYBImportO32Record.setBeginTime(params.getString("beginTime"));
            logFYBImportO32Record.setCreateByName(params.getString("username"));
            logFYBImportO32Record.setParams(params.getString("params"));
            logFYBImportO32Record.setStatus(params.getString("status"));
            logFYBMapper.saveImportO32Log(logFYBImportO32Record);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postImportO32Log(JSONObject params) {
        try {
            LogFYBMapper logFYBMapper = SpringUtil.getBean(LogFYBMapper.class);
            String endTime = params.getString("endTime");
            String status = params.getString("status");
            String logId = params.getString("logId");
            String o32Result = params.getString("o32Result");
            String fileUrl = params.getString("fileUrl");
            logFYBMapper.updateImportO32Log(logId, endTime, status, o32Result, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
