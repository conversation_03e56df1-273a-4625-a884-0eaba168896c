package cn.sdata.om.al.utils;

import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.CronMapper;
import cn.sdata.om.al.mapper.LogFYIMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;

public class LogFYIUtils {

    public static void preDownloadFile(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYIDownloadFileRecord fybDownloadFileRecord = new LogFYIDownloadFileRecord();
            fybDownloadFileRecord.setId(params.getString("logId"));
            fybDownloadFileRecord.setBeginTime(params.getString("beginTime"));
            fybDownloadFileRecord.setCreateByName(params.getString("username"));
            fybDownloadFileRecord.setParams(params.getString("params"));
            fybDownloadFileRecord.setStatus(params.getString("status"));
            logFYIMapper.saveDownloadFileLog(fybDownloadFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postDownloadFile(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String logId = params.getString("logId");
            String fileUrl = params.getString("fileUrl");
            String status = params.getString("status");
            String errorMsg = params.getString("errorMsg");
            String endTime = params.getString("endTime");
            logFYIMapper.updateDownloadFileLog(logId, fileUrl, status, errorMsg, endTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preGenerateO32File(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYIGenerateFileRecord logFYIGenerateFileRecord = new LogFYIGenerateFileRecord();
            logFYIGenerateFileRecord.setId(params.getString("logId"));
            logFYIGenerateFileRecord.setBeginTime(params.getString("beginTime"));
            logFYIGenerateFileRecord.setCreateByName(params.getString("username"));
            logFYIGenerateFileRecord.setParams(params.getString("params"));
            logFYIGenerateFileRecord.setStatus(params.getString("status"));
            logFYIMapper.saveGenerateO32File(logFYIGenerateFileRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postGenerateO32File(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String logId = params.getString("logId");
            String fileUrl = params.getString("fileUrl");
            String status = params.getString("status");
            String errorMsg = params.getString("errorMsg");
            String endTime = params.getString("endTime");
            List<String> productIds = params.getList("productIds", String.class);
            String productIdsStr = JSON.toJSONString(productIds);
            logFYIMapper.updateGenerateO32File(logId, fileUrl, status, errorMsg, endTime, productIdsStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preSendMailLog(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogFYISendMailRecord logFYISendMailRecord = new LogFYISendMailRecord();
                logFYISendMailRecord.setBeginTime(params.getString("beginTime"));
                logFYISendMailRecord.setId(params.getString("logId"));
                logFYISendMailRecord.setStatus(params.getString("status"));
                logFYISendMailRecord.setCreateByName(params.getString("username"));
                logFYISendMailRecord.setParams(params.getString("params"));
                logFYISendMailRecord.setTaskId(baseCronLog.getTaskId());
                logFYISendMailRecord.setType(params.getString("type"));
                logFYISendMailRecord.setTaskName(cron != null ? cron.getJobName() : "");
                logFYISendMailRecord.setRpaLogId(rpaLogId);
                logFYISendMailRecord.setMailIdStr(params.getString("mailId"));
                logFYISendMailRecord.setSendStatus(params.getString("sendStatus"));
                logFYISendMailRecord.setEndTime(params.getString("endTime"));
                logFYIMapper.saveSendMailLog(logFYISendMailRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preO32ConfirmLog(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYIO32ConfirmRecord logFYIO32ConfirmRecord = new LogFYIO32ConfirmRecord();
            logFYIO32ConfirmRecord.setId(params.getString("logId"));
            logFYIO32ConfirmRecord.setBeginTime(params.getString("beginTime"));
            logFYIO32ConfirmRecord.setCreateByName(params.getString("username"));
            logFYIO32ConfirmRecord.setParams(params.getString("params"));
            logFYIO32ConfirmRecord.setStatus(params.getString("status"));
            logFYIO32ConfirmRecord.setProductIdsStr(JSON.toJSONString(params.getList("productIds", String.class)));
            logFYIMapper.saveO32ConfirmLog(logFYIO32ConfirmRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postO32ConfirmLog(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String endTime = params.getString("endTime");
            String status = params.getString("status");
            String logId = params.getString("logId");
            logFYIMapper.updateO32ConfirmLog(logId, endTime, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preRpaLog(JSONObject params) {

    }

    public static void postRpaLog(JSONObject params) {

    }

    public static void preSyncPayStatusLog(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYISyncPayStatusRecord logFYISyncPayStatusRecord = new LogFYISyncPayStatusRecord();
            logFYISyncPayStatusRecord.setId(params.getString("logId"));
            logFYISyncPayStatusRecord.setBeginTime(params.getString("beginTime"));
            logFYISyncPayStatusRecord.setCreateByName(params.getString("username"));
            logFYISyncPayStatusRecord.setParams(params.getString("params"));
            logFYISyncPayStatusRecord.setProductInfosStr(params.getString("productInfos"));
            logFYISyncPayStatusRecord.setStatus(params.getString("status"));
            logFYIMapper.saveSyncPayStatusLog(logFYISyncPayStatusRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postSyncPayStatusLog(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String productInfos = params.getString("productInfos");
            String status = params.getString("status");
            logFYIMapper.updateSyncPayStatusLog(logId, endTime, productInfos, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preImportO32Log(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYIImportO32Record logFYIImportO32Record = new LogFYIImportO32Record();
            logFYIImportO32Record.setId(params.getString("logId"));
            logFYIImportO32Record.setBeginTime(params.getString("beginTime"));
            logFYIImportO32Record.setCreateByName(params.getString("username"));
            logFYIImportO32Record.setParams(params.getString("params"));
            logFYIImportO32Record.setStatus(params.getString("status"));
            logFYIMapper.saveImportO32Log(logFYIImportO32Record);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postImportO32Log(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String endTime = params.getString("endTime");
            String status = params.getString("status");
            String logId = params.getString("logId");
            String o32Result = params.getString("o32Result");
            String fileUrl = params.getString("fileUrl");
            logFYIMapper.updateImportO32Log(logId, endTime, status, o32Result, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void preUploadFile(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            LogFYIUploadRecord logFYIUploadRecord = new LogFYIUploadRecord();
            logFYIUploadRecord.setId(params.getString("logId"));
            logFYIUploadRecord.setCreateByName(params.getString("username"));
            logFYIUploadRecord.setExecuteTime(params.getString("beginTime"));
            logFYIUploadRecord.setFileUrl(params.getString("fileUrl"));
            logFYIUploadRecord.setStatus(CommonStatus.EXECUTING.name());
            logFYIUploadRecord.setDataDate(params.getString("dataDate"));
            logFYIUploadRecord.setExecuteType(params.getString("executeType"));
            logFYIMapper.saveUploadFile(logFYIUploadRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void postUploadFile(JSONObject params) {
        try {
            LogFYIMapper logFYIMapper = SpringUtil.getBean(LogFYIMapper.class);
            String logId = params.getString("logId");
            String status = params.getString("status");
            String endTime = params.getString("endTime");
            logFYIMapper.updateUploadFile(logId, status, endTime);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
