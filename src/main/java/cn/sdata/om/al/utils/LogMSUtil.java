package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.*;
import cn.sdata.om.al.enums.CommonStatus;
import cn.sdata.om.al.mapper.BaseCronLogMapper;
import cn.sdata.om.al.mapper.CronMapper;
import cn.sdata.om.al.mapper.MonthlySettlementLogMapper;
import cn.sdata.om.al.qrtz.entity.BaseCronLog;
import cn.sdata.om.al.qrtz.entity.Cron;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class LogMSUtil {

    public static synchronized void preExportLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper monthlySettlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            LogMSExportRecord logMSExportRecord = new LogMSExportRecord();
            logMSExportRecord.setBeginTime(beginTime);
            logMSExportRecord.setId(logId);
            logMSExportRecord.setStatus(CommonStatus.EXECUTING.name());
            logMSExportRecord.setDataDate(params.getString("dataDate"));
            logMSExportRecord.setCreateByName(username);
            logMSExportRecord.setParams(params.getString("params"));
            monthlySettlementLogMapper.saveExportLog(logMSExportRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public static synchronized void postExportLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper monthlySettlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            String fileUrl = params.getString("fileUrl");
            monthlySettlementLogMapper.updateExportLogById(logId, endTime, errorMsg, status, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static synchronized void preFileOptLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper monthlySettlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String username = params.getString("username");
            String beginTime = params.getString("beginTime");
            String logId = params.getString("logId");
            LogMSFileOptRecord logMSFileOptRecord = new LogMSFileOptRecord();
            logMSFileOptRecord.setBeginTime(beginTime);
            logMSFileOptRecord.setId(logId);
            logMSFileOptRecord.setStatus(CommonStatus.EXECUTING.name());
            logMSFileOptRecord.setCreateByName(username);
            logMSFileOptRecord.setParams(params.getString("params"));
            logMSFileOptRecord.setType(params.getString("type"));
            logMSFileOptRecord.setDataDate(params.getString("dataDate"));
            monthlySettlementLogMapper.saveFileOptLog(logMSFileOptRecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postFileOptLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper monthlySettlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String logId = params.getString("logId");
            String endTime = params.getString("endTime");
            String errorMsg = params.getString("exception");
            String status = params.getString("status");
            String fileUrl = params.getString("fileUrl");
            monthlySettlementLogMapper.updateFileOptLogById(logId, endTime, errorMsg, status, fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void preRpaLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper monthlySettlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            LogMSRPARecord logMSRPARecord = new LogMSRPARecord();
            logMSRPARecord.setBeginTime(params.getString("beginTime"));
            logMSRPARecord.setId(params.getString("logId"));
            logMSRPARecord.setStatus(CommonStatus.EXECUTING.name());
            logMSRPARecord.setCreateByName(params.getString("username"));
            logMSRPARecord.setParams(params.getString("params"));
            logMSRPARecord.setType(params.getString("type"));
            logMSRPARecord.setDataDate(params.getString("dataDate"));
            monthlySettlementLogMapper.saveRpaLog(logMSRPARecord);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postRpaLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper settlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            String logId = params.getString("logId");
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogMSRPARecord logMSRPARecord = settlementLogMapper.getById(logId);
                if (logMSRPARecord != null) {
                    logMSRPARecord.setTaskId(baseCronLog.getTaskId());
                    logMSRPARecord.setTaskName(cron != null ? cron.getJobName() : "");
                    logMSRPARecord.setFileUrl(params.getString("fileUrl"));
                    logMSRPARecord.setEndTime(DateUtil.now());
                    logMSRPARecord.setRpaLogId(rpaLogId);
                    logMSRPARecord.setErrorMsg(params.getString("exception"));
                    logMSRPARecord.setStatus(baseCronLog.getStatus().name());
                    logMSRPARecord.setResult(params.getString("result"));
                    settlementLogMapper.updateRpaLog(logMSRPARecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void preSendMailLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper settlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
            BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
            if (baseCronLog != null) {
                Cron cron = SpringUtil.getBean(CronMapper.class).selectById(baseCronLog.getTaskId());
                LogCCRSendMailRecord logCCRSendMailRecord = new LogCCRSendMailRecord();
                logCCRSendMailRecord.setBeginTime(params.getString("beginTime"));
                logCCRSendMailRecord.setId(params.getString("logId"));
                logCCRSendMailRecord.setStatus(params.getString("status"));
                logCCRSendMailRecord.setSendStatus(params.getString("sendStatus"));
                logCCRSendMailRecord.setCreateByName(params.getString("username"));
                logCCRSendMailRecord.setParams(params.getString("params"));
                logCCRSendMailRecord.setEndTime(params.getString("endTime"));
                logCCRSendMailRecord.setTaskId(baseCronLog.getTaskId());
                logCCRSendMailRecord.setDataDate(params.getString("dataDate"));
                logCCRSendMailRecord.setMailIdStr(params.getString("mailId"));
                logCCRSendMailRecord.setType(params.getString("type"));
                logCCRSendMailRecord.setTaskName(cron != null ? cron.getJobName() : "");
                logCCRSendMailRecord.setRpaLogId(rpaLogId);
                settlementLogMapper.saveSendMailLog(logCCRSendMailRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static synchronized void postSendMailLog(JSONObject params) {
        try {
            MonthlySettlementLogMapper settlementLogMapper = SpringUtil.getBean(MonthlySettlementLogMapper.class);
            String rpaLogId = params.getString("rpaLogId");
            List<String> mailIds = params.getList("mailIds", String.class);
            LogMSSendMailRecord logMSSendMailRecord = settlementLogMapper.getMSSendMailLogByLogId(rpaLogId);
            if (logMSSendMailRecord != null) {
                BaseCronLogMapper baseCronLogMapper = SpringUtil.getBean(BaseCronLogMapper.class);
                BaseCronLog baseCronLog = baseCronLogMapper.selectById(rpaLogId);
                if (baseCronLog != null) {
                    String endDateTime = baseCronLog.getEndDateTime();
                    if (CollectionUtil.isNotEmpty(mailIds)) {
                        logMSSendMailRecord.setMailIdStr(StringUtils.join(mailIds, ","));
                        logMSSendMailRecord.setEndTime(endDateTime);
                        logMSSendMailRecord.setSendStatus(params.getString("sendStatus"));
                        logMSSendMailRecord.setStatus(params.getString("status"));
                        logMSSendMailRecord.setErrorMsg(params.getString("exception"));
                        settlementLogMapper.updateSendMailLogById(logMSSendMailRecord);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
