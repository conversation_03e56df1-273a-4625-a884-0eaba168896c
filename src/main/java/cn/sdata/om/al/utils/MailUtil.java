package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.config.MailProperties;
import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.mail.MailAttachment;
import cn.sdata.om.al.exception.BusinessException;
import cn.sdata.om.al.ftp.FtpService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * 邮件相关功能工具
 */
@Slf4j
@Component
@AllArgsConstructor
public class MailUtil {

    private final FtpService ftpService;

    private final MailProperties mailProperties;

    /**
     * 发送电子邮件(带附件)
     *
     * @param receivers 邮件接收者集合
     * @param filePaths 附件路径集合（必须为全路径）
     * @param title     邮件的标题
     * @param content   邮件的内容
     */
    public void sendEmailFile(List<String> receivers, List<String> carbonCopyList, List<String> filePaths, String title, String content) throws Exception {
        // 初始化
        Properties props = getProperties();
        Session emailSession = Session.getInstance(props);
        emailSession.setDebug(mailProperties.isDebug());
        MimeMessage message = new MimeMessage(emailSession);
        // 发件人的邮箱
        InternetAddress from = new InternetAddress(mailProperties.getSender(), BaseConstant.SENDER, mailProperties.getDefaultEncoding());
        message.setFrom(from);
        // 设置收件人
        message.setRecipients(Message.RecipientType.TO, receivers.stream().map(receiver -> {
            try {
                return new InternetAddress(receiver);
            } catch (AddressException e) {
                log.error(e.getMessage(), e);
            }
            return null;
        }).toArray(InternetAddress[]::new));
        // 设置抄送人
        if (CollUtil.isNotEmpty(carbonCopyList)) {
            message.setRecipients(Message.RecipientType.CC, carbonCopyList.stream().map(carbonCopy -> {
                try {
                    return new InternetAddress(carbonCopy);
                } catch (AddressException e) {
                    log.error(e.getMessage(), e);
                }
                return null;
            }).toArray(InternetAddress[]::new));
        }
        message.setSubject(title, mailProperties.getDefaultEncoding());
        message.setSentDate(new Date());
        // 新建一个MimeMultipart对象用来存放BodyPart对象(事实上可以存放多个)
        Multipart multipart = new MimeMultipart();
        // 给消息对象设置内容，新建一个存放信件内容的BodyPart对象
        BodyPart mdp = new MimeBodyPart();
        // 给BodyPart对象设置内容和格式/编码方式防止邮件出现乱码
        mdp.setContent(content, "text/html;charset=" + mailProperties.getDefaultEncoding());
        // 装填附件
        if (CollUtil.isNotEmpty(filePaths)) {
            log.info("携带的附件的数量为{}", filePaths.size());
            // 建立ftp连接
            FTPClient ftpClient = ftpService.connectFtpServer();
            if (null == ftpClient) {
                return;
            }
            // 从ftp下载文件 放入附件
            for (String filePath : filePaths) {
                int lastSeparatorIndex = filePath.lastIndexOf("/");
                if (lastSeparatorIndex < 0) {
                    continue;
                }
                String fileName = filePath.substring(lastSeparatorIndex + 1);
                filePath = filePath.substring(0, lastSeparatorIndex);
                InputStream inputStream = ftpService.getInputStream(ftpClient, filePath, fileName);
                if (null != inputStream) {
                    BodyPart attachmentBodyPart = new MimeBodyPart();
                    ByteArrayDataSource dataSource = new ByteArrayDataSource(inputStream, "application/octet-stream");
                    attachmentBodyPart.setDataHandler(new DataHandler(dataSource));
                    // MimeUtility.encodeWord可以避免文件名乱码
                    attachmentBodyPart.setFileName(MimeUtility.encodeWord(fileName, mailProperties.getDefaultEncoding(), "B"));
                    multipart.addBodyPart(attachmentBodyPart);
                    inputStream.close();
                    // 发送完成符号，否则无法继续获取
                    ftpClient.completePendingCommand();
                }
            }
            // 关闭ftp连接
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.logout();
                    ftpClient.disconnect();
                }
            } catch (IOException e) {
                log.error("ftp关闭连接失败");
            }
        }
        // 将BodyPart加入到MimeMultipart对象中(可以加入多个BodyPart)
        multipart.addBodyPart(mdp);
        // 把mm作为消息对象的内容
        message.setContent(multipart);
        message.saveChanges();
        Transport transport = emailSession.getTransport("smtp");
        // 设置发邮件的网关，发信的帐户和密码
        log.info("准备发送邮件,ip为:{},port为{},name为{}", mailProperties.getHost(), mailProperties.getPort(), mailProperties.getName());
        transport.connect(mailProperties.getHost(), Integer.parseInt(mailProperties.getPort()), mailProperties.getName(), mailProperties.getPassword());
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }


    /**
     * 发送电子邮件(带附件) 附件文件从共享文件夹下载
     *
     * @param receivers       邮件接收者集合
     * @param mailAttachments 附件文件
     * @param title           邮件的标题
     * @param content         邮件的内容
     */
    public void sendEmailFileFromShareFolder(List<String> receivers, List<String> carbonCopyList, List<MailAttachment> mailAttachments, String title, String content) throws Exception {
        if (CollectionUtil.isEmpty(receivers)) {
            BusinessException.throwException("发送邮件收件人为空");
        }
        // 初始化
        Properties props = getProperties();
        Session emailSession = Session.getInstance(props);
        emailSession.setDebug(mailProperties.isDebug());
        MimeMessage message = new MimeMessage(emailSession);
        // 发件人的邮箱
        InternetAddress from = new InternetAddress(mailProperties.getSender(), BaseConstant.SENDER, mailProperties.getDefaultEncoding());
        log.info("----发送邮件发送者地址为:{}", from.getAddress());
        message.setFrom(from);
        // 设置收件人
        message.setRecipients(Message.RecipientType.TO, receivers.stream().map(receiver -> {
            try {
                return new InternetAddress(receiver);
            } catch (AddressException e) {
                log.error(e.getMessage(), e);
            }
            return null;
        }).toArray(InternetAddress[]::new));
        Address[] allRecipients = message.getRecipients(Message.RecipientType.TO);
        log.info("----发送邮件接收者地址为:{}", CollectionUtil.toList(allRecipients));
        // 设置抄送人
        if (CollUtil.isNotEmpty(carbonCopyList)) {
            message.setRecipients(Message.RecipientType.CC, carbonCopyList.stream().filter(Objects::nonNull).filter(s -> !s.isEmpty()).map(carbonCopy -> {
                try {
                    return new InternetAddress(carbonCopy);
                } catch (AddressException e) {
                    log.error(e.getMessage(), e);
                }
                return null;
            }).toArray(InternetAddress[]::new));
            Address[] allCCTos = message.getRecipients(Message.RecipientType.CC);
            log.info("----发送邮件抄送者地址为:{}", CollectionUtil.toList(allCCTos));
        }
        message.setSubject(title, mailProperties.getDefaultEncoding());
        message.setSentDate(new Date());
        message.setHeader("Content-Type", "text/plain; charset=UTF-8");
        // 新建一个MimeMultipart对象用来存放BodyPart对象(事实上可以存放多个)
        Multipart multipart = new MimeMultipart();
        // 给消息对象设置内容，新建一个存放信件内容的BodyPart对象
        BodyPart mdp = new MimeBodyPart();
        // 给BodyPart对象设置内容和格式/编码方式防止邮件出现乱码
        mdp.setContent(content, "text/html;charset=" + mailProperties.getDefaultEncoding());
        // 装填附件
        if (CollectionUtil.isNotEmpty(mailAttachments)) {
            for (MailAttachment mailAttachment : mailAttachments) {
                InputStream inputStream = mailAttachment.getInputStream();
                if (ObjectUtil.isNotNull(inputStream)) {
                    log.info("----发送的附件文件名为:{}", mailAttachment.getFileName());
                    BodyPart attachmentBodyPart = new MimeBodyPart();
                    ByteArrayDataSource dataSource = new ByteArrayDataSource(inputStream, "application/octet-stream");
                    attachmentBodyPart.setDataHandler(new DataHandler(dataSource));
                    // MimeUtility.encodeWord可以避免文件名乱码
                    attachmentBodyPart.setFileName(MimeUtility.encodeWord(mailAttachment.getFileName(), mailProperties.getDefaultEncoding(), "B"));
                    multipart.addBodyPart(attachmentBodyPart);
                    inputStream.close();
                }
            }
        }
        // 将BodyPart加入到MimeMultipart对象中(可以加入多个BodyPart)
        multipart.addBodyPart(mdp);
        // 把mm作为消息对象的内容
        message.setContent(multipart);
        message.saveChanges();
        Transport transport = emailSession.getTransport("smtp");
        // 设置发邮件的网关，发信的帐户和密码
        log.info("准备发送邮件,ip为:{},port为{},name为{}", mailProperties.getHost(), mailProperties.getPort(), mailProperties.getName());
        transport.connect(mailProperties.getHost(), Integer.parseInt(mailProperties.getPort()), mailProperties.getName(), mailProperties.getPassword());
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }

    /**
     * 获取邮件配置信息
     *
     * @return 邮件配置信息
     * @throws GeneralSecurityException 异常
     */
    private Properties getProperties() throws GeneralSecurityException {
        Properties props = new Properties();
        props.put("mail.smtp.host", mailProperties.getHost());
        props.put("mail.smtp.port", mailProperties.getPort());
        props.put("mail.transport.protocol", "smtps");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        /*props.put("mail.smtp.ssl.ciphersuites",
                "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384," +
                        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384," +
                        "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256," +
                        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256");
        props.put("mail.smtp.connectiontimeout", "30000");
        props.put("mail.smtp.timeout", "60000");
        props.put("mail.smtp.writetimeout", "60000");*/
        return props;
    }
}
