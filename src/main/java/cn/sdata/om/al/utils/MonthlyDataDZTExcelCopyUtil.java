package cn.sdata.om.al.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.sdata.om.al.enums.MonthlyDataFileEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/25 17:09
 * @Version 1.0
 */
@Slf4j
@Component
public class MonthlyDataDZTExcelCopyUtil {

    /*public static void main(String[] args) {
        copyByPoi("C:\\Users\\<USER>\\Desktop\\test\\多账套科目发生及余额表-债权计划.xlsx",
                "Sheet1",
                "C:\\Users\\<USER>\\Desktop\\test\\增值税相关报表模板-产品汇总.xlsx",
                "多账套科目发生及余额表-债权计划");
    }*/

    /**
     * 通过poi复制excel
     *
     * @param sourceFilePath
     * @param sourceSheetName
     * @param targetFilePath
     * @param targetSheetName
     */
    public static void copyByPoi(String sourceFilePath,
                                 String sourceSheetName,
                                 String targetFilePath,
                                 String targetSheetName) {
        try {
            // 调整压缩比阈值（低于默认值 0.01）
            ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比
            // 1. 读取源工作簿和目标工作簿
            Workbook sourceWorkbook = new XSSFWorkbook(new FileInputStream(sourceFilePath));
            Workbook targetWorkbook = new XSSFWorkbook(new FileInputStream(targetFilePath));

            // 2. 获取源工作表和目标工作表
            Sheet sourceSheet = sourceWorkbook.getSheet(sourceSheetName);
            if (sourceSheet == null) {
                sourceWorkbook.close();
                targetWorkbook.close();
                throw new IllegalArgumentException("源工作表不存在: " + sourceSheetName);
            }

            // 3. 如果目标工作表已存在，则删除后重建（保持其他sheet不变）
            Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);
            if (targetSheet != null) {
                int sheetIndex = targetWorkbook.getSheetIndex(targetSheet);
                targetWorkbook.removeSheetAt(sheetIndex);
                targetWorkbook.createSheet(targetSheetName);
                targetSheet = targetWorkbook.getSheet(targetSheetName);
            } else {
                targetSheet = targetWorkbook.createSheet(targetSheetName);
            }

            // 4. 样式缓存
            Map<Short, CellStyle> styleCache = new HashMap<>();

            // 5. 复制合并区域
            copyMergedRegions(sourceSheet, targetSheet);

            // 6. 复制行和单元格（带样式缓存）
            for (int i = 0; i <= sourceSheet.getLastRowNum(); i++) {
                Row sourceRow = sourceSheet.getRow(i);
                if (sourceRow != null) {
                    Row targetRow = targetSheet.createRow(i);
                    copyRowWithStyle(sourceRow, targetRow, targetWorkbook, styleCache);
                }
            }

            // 7. 复制列宽
            copyColumnWidths(sourceSheet, targetSheet);

            // 8. 特别处理前两行的合并居中样式
            reinforceFirstTwoRowsStyle(sourceSheet, targetSheet, targetWorkbook, styleCache);

            if (MonthlyDataFileEnum.DZTKMYE_ZQJH.getFileName().contains(targetSheetName)) {
                addSignature(targetSheet, styleCache, new int[]{0, 2}); // A和C列
            } else if (MonthlyDataFileEnum.DZTKMYE_ZH.getFileName().contains(targetSheetName)) {
                addSignature(targetSheet, styleCache, new int[]{1, 4}); // B和E列
            }

            // 9. 保存目标工作簿（覆盖原文件）
            try (FileOutputStream fos = new FileOutputStream(targetFilePath)) {
                targetWorkbook.write(fos);
            }

            // 10. 关闭工作簿
            sourceWorkbook.close();
            targetWorkbook.close();

        } catch (Exception e) {
            log.error("copyByPoi_error:{},{}", e, e.getMessage());
            throw new RuntimeException("copyByPoi复制Excel文件失败", e);
        }
    }

    /**
     * 在Sheet的最后一行+3行处添加签名信息
     *
     * @param sheet         目标工作表
     * @param styleCache    样式缓存
     * @param columnIndexes 列索引数组，第一个元素是"制表："列，第二个是"复核："列
     */
    private static void addSignature(Sheet sheet, Map<Short, CellStyle> styleCache, int[] columnIndexes) {
        int lastRowNum = sheet.getLastRowNum();
        int targetRowNum = lastRowNum + 3;

        Row row = sheet.getRow(targetRowNum) != null ?
                sheet.getRow(targetRowNum) : sheet.createRow(targetRowNum);

        // 设置"制表："
        Cell cell1 = row.createCell(columnIndexes[0]);
        cell1.setCellValue("制表：");

        // 设置"复核："
        Cell cell2 = row.createCell(columnIndexes[1]);
        cell2.setCellValue("复核：");
    }

    // 复制合并区域
    private static void copyMergedRegions(Sheet sourceSheet, Sheet targetSheet) {
        for (CellRangeAddress mergedRegion : sourceSheet.getMergedRegions()) {
            targetSheet.addMergedRegion(new CellRangeAddress(
                    mergedRegion.getFirstRow(),
                    mergedRegion.getLastRow(),
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            ));
        }
    }

    // 复制行内容及样式（带缓存）
    private static void copyRowWithStyle(Row sourceRow,
                                         Row targetRow,
                                         Workbook targetWorkbook,
                                         Map<Short, CellStyle> styleCache) {
        targetRow.setHeight(sourceRow.getHeight());

        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell targetCell = targetRow.createCell(i);
                copyCellWithStyle(sourceCell, targetCell, targetWorkbook, styleCache);
            }
        }
    }

    // 复制单元格内容及样式（带缓存）
    private static void copyCellWithStyle(Cell sourceCell,
                                          Cell targetCell,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        // 复制单元格值
        CellType cellType = sourceCell.getCellType();
        switch (cellType) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                targetCell.setBlank();
        }

        CellStyle sourceStyle = sourceCell.getCellStyle();
        short sourceStyleIndex = sourceStyle.getIndex();

        CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
            CellStyle newStyle = targetWorkbook.createCellStyle();
            newStyle.cloneStyleFrom(sourceStyle);
            return newStyle;
        });

        targetCell.setCellStyle(targetStyle);
    }

    // 复制列宽
    private static void copyColumnWidths(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getRow(0).getLastCellNum(); i++) {
            targetSheet.setColumnWidth(i, sourceSheet.getColumnWidth(i));
        }
    }

    // 特别强化前两行的样式（带缓存）
    private static void reinforceFirstTwoRowsStyle(Sheet sourceSheet,
                                                   Sheet targetSheet,
                                                   Workbook targetWorkbook,
                                                   Map<Short, CellStyle> styleCache) {
        // 处理第一行
        if (sourceSheet.getRow(0) != null && targetSheet.getRow(0) != null) {
            reinforceRowStyle(sourceSheet.getRow(0), targetSheet.getRow(0), targetWorkbook, styleCache);
        }

        // 处理第二行
        if (sourceSheet.getRow(1) != null && targetSheet.getRow(1) != null) {
            reinforceRowStyle(sourceSheet.getRow(1), targetSheet.getRow(1), targetWorkbook, styleCache);
        }
    }

    // 强化行样式（带缓存）
    private static void reinforceRowStyle(Row sourceRow,
                                          Row targetRow,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            Cell targetCell = targetRow.getCell(i);

            if (sourceCell != null && targetCell != null) {
                // 从缓存获取或创建样式
                CellStyle sourceStyle = sourceCell.getCellStyle();
                short sourceStyleIndex = sourceStyle.getIndex();

                CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
                    CellStyle newStyle = targetWorkbook.createCellStyle();
                    newStyle.cloneStyleFrom(sourceStyle);
                    return newStyle;
                });

                targetCell.setCellStyle(targetStyle);
            }
        }
    }


}
