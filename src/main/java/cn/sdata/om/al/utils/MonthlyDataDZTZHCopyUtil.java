package cn.sdata.om.al.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/09 17:09
 * @Version 1.0
 */
@Slf4j
@Component
public class MonthlyDataDZTZHCopyUtil {

    /*public static void main(String[] args) {
        copyByPoi("C:\\Users\\<USER>\\Desktop\\monthlyDataTest\\多账套科目发生及余额表-组合.xlsx",
                "Sheet1",
                "C:\\Users\\<USER>\\Desktop\\monthlyDataTest\\增值税相关报表模板-产品汇总.xlsx",
                "多账套科目发生及余额表-组合");
    }*/

    /**
     * 通过poi复制excel
     *
     * @param sourceFilePath
     * @param sourceSheetName
     * @param targetFilePath
     * @param targetSheetName
     */
    public static void copyByPoi(String sourceFilePath,
                                 String sourceSheetName,
                                 String targetFilePath,
                                 String targetSheetName) {
        try {
            ZipSecureFile.setMinInflateRatio(0.001); // 允许更小的压缩比

            Map<Short, CellStyle> styleCache = new HashMap<>();

            Workbook sourceWorkbook = new XSSFWorkbook(new FileInputStream(sourceFilePath));
            Workbook targetWorkbook = new XSSFWorkbook(new FileInputStream(targetFilePath));

            Sheet sourceSheet = sourceWorkbook.getSheet(sourceSheetName);
            Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);
            if (sourceSheet == null) {
                sourceWorkbook.close();
                targetWorkbook.close();
                throw new IllegalArgumentException("源工作表不存在: " + sourceSheetName);
            }
            String originalPrintArea = targetWorkbook.getPrintArea(targetSheet.getWorkbook().getSheetIndex(targetSheet));

            int sourceStartRow = 0;
            int sourceRowCount = sourceSheet.getLastRowNum() - sourceStartRow;

            int targetInsertRow = 0;
            if (sourceRowCount > 0) {
                targetSheet.shiftRows(targetInsertRow, targetSheet.getLastRowNum(), sourceRowCount, true, false);
            }

            // 5. 复制合并区域
            copyMergedRegions(sourceSheet, targetSheet);

            // 6. 复制行和单元格（带样式缓存）
            for (int i = 0; i < sourceSheet.getLastRowNum(); i++) {
                Row sourceRow = sourceSheet.getRow(sourceStartRow + i);
                Row targetRow = targetSheet.createRow(targetInsertRow + i);
                if (sourceRow != null) {
                    copyRowWithStyle(sourceRow, targetRow, targetWorkbook, styleCache);
                }
            }

            // 7. 复制列宽
            copyColumnWidths(sourceSheet, targetSheet);

            // 8. 特别处理前两行的合并居中样式
            reinforceFirstTwoRowsStyle(sourceSheet, targetSheet, targetWorkbook, styleCache);

            if (originalPrintArea != null && !originalPrintArea.isEmpty()) {
                // 解析原始打印区域
                String[] printAreas = originalPrintArea.split(",");
                for (int i = 0; i < printAreas.length; i++) {
                    String area = printAreas[i];
                    CellRangeAddress cra = CellRangeAddress.valueOf(area);

                    cra.setFirstRow(0);
                    cra.setLastRow(cra.getLastRow() + sourceRowCount);

                    printAreas[i] = cra.formatAsString();
                }

                // 设置新的打印区域
                targetWorkbook.setPrintArea(
                        targetSheet.getWorkbook().getSheetIndex(targetSheet),
                        String.join(",", printAreas)
                );
            } else {
                // 如果没有设置打印区域，创建一个包含所有数据的打印区域
                int lastRow = targetSheet.getLastRowNum();
                int lastCol = 0;
                for (Row row : targetSheet) {
                    if (row.getLastCellNum() > lastCol) {
                        lastCol = row.getLastCellNum();
                    }
                }

                String newPrintArea = CellRangeAddress.valueOf(
                        "A1:" + CellReference.convertNumToColString(lastCol - 1) + (lastRow + 1)
                ).formatAsString();

                targetWorkbook.setPrintArea(
                        targetSheet.getWorkbook().getSheetIndex(targetSheet),
                        newPrintArea
                );
            }
            // 9. 保存目标工作簿（覆盖原文件）
            try (FileOutputStream fos = new FileOutputStream(targetFilePath)) {
                targetWorkbook.write(fos);
            }

            // 10. 关闭工作簿
            sourceWorkbook.close();
            targetWorkbook.close();

        } catch (Exception e) {
            log.error("MonthlyDataDZTZHCopyUtil_copyByPoi_error:{},{}", e, e.getMessage());
            throw new RuntimeException("MonthlyDataDZTZHCopyUtil_copyByPoi复制Excel文件失败", e);
        }
    }


    // 复制合并区域
    private static void copyMergedRegions(Sheet sourceSheet, Sheet targetSheet) {
        for (CellRangeAddress mergedRegion : sourceSheet.getMergedRegions()) {
            targetSheet.addMergedRegion(new CellRangeAddress(
                    mergedRegion.getFirstRow(),
                    mergedRegion.getLastRow(),
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            ));
        }
    }

    // 复制行内容及样式（带缓存）
    private static void copyRowWithStyle(Row sourceRow,
                                         Row targetRow,
                                         Workbook targetWorkbook,
                                         Map<Short, CellStyle> styleCache) {
        targetRow.setHeight(sourceRow.getHeight());

        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell targetCell = targetRow.createCell(i);
                copyCellWithStyle(sourceCell, targetCell, targetWorkbook, styleCache);
            }
        }
    }

    // 复制单元格内容及样式（带缓存）
    private static void copyCellWithStyle(Cell sourceCell,
                                          Cell targetCell,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        // 复制单元格值
        CellType cellType = sourceCell.getCellType();
        switch (cellType) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                targetCell.setBlank();
        }

        CellStyle sourceStyle = sourceCell.getCellStyle();
        short sourceStyleIndex = sourceStyle.getIndex();

        CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
            CellStyle newStyle = targetWorkbook.createCellStyle();
            newStyle.cloneStyleFrom(sourceStyle);
            return newStyle;
        });

        targetCell.setCellStyle(targetStyle);
    }

    // 复制列宽
    private static void copyColumnWidths(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getRow(0).getLastCellNum(); i++) {
            targetSheet.setColumnWidth(i, sourceSheet.getColumnWidth(i));
        }
    }

    // 特别强化前两行的样式（带缓存）
    private static void reinforceFirstTwoRowsStyle(Sheet sourceSheet,
                                                   Sheet targetSheet,
                                                   Workbook targetWorkbook,
                                                   Map<Short, CellStyle> styleCache) {
        // 处理第一行
        if (sourceSheet.getRow(0) != null && targetSheet.getRow(0) != null) {
            reinforceRowStyle(sourceSheet.getRow(0), targetSheet.getRow(0), targetWorkbook, styleCache);
        }

        // 处理第二行
        if (sourceSheet.getRow(1) != null && targetSheet.getRow(1) != null) {
            reinforceRowStyle(sourceSheet.getRow(1), targetSheet.getRow(1), targetWorkbook, styleCache);
        }
    }

    // 强化行样式（带缓存）
    private static void reinforceRowStyle(Row sourceRow,
                                          Row targetRow,
                                          Workbook targetWorkbook,
                                          Map<Short, CellStyle> styleCache) {
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            Cell targetCell = targetRow.getCell(i);

            if (sourceCell != null && targetCell != null) {
                // 从缓存获取或创建样式
                CellStyle sourceStyle = sourceCell.getCellStyle();
                short sourceStyleIndex = sourceStyle.getIndex();

                CellStyle targetStyle = styleCache.computeIfAbsent(sourceStyleIndex, k -> {
                    CellStyle newStyle = targetWorkbook.createCellStyle();
                    newStyle.cloneStyleFrom(sourceStyle);
                    return newStyle;
                });

                targetCell.setCellStyle(targetStyle);
            }
        }
    }


}
