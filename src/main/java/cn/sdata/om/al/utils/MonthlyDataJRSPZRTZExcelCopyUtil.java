package cn.sdata.om.al.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/9 10:24
 * @Version 1.0
 */
@Slf4j
@Component
public class MonthlyDataJRSPZRTZExcelCopyUtil {

    /*public static void main(String[] args) {
        try {
            copyJRZRTZZHData(
                    "C:\\Users\\<USER>\\Desktop\\monthlyDataTest\\金融商品转让台账表-组合-20241231.xlsx",
                    "C:\\Users\\<USER>\\Desktop\\monthlyDataTest\\增值税相关报表模板-产品汇总.xlsx",
                    "金融商品转让台账表-组合");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/

    /**
     * 复制 金融商品转让台账表-组合->汇总表
     *
     * @param sourcePath
     * @param targetPath
     * @throws Exception
     */
    public static void copyJRZRTZZHData(String sourcePath, String targetPath, String targetSheetName) throws Exception {
        ZipSecureFile.setMinInflateRatio(0.001);
        String[] targetHeaders = {"账套", "产品代码", "日期", "当日税基发生额", "当日税基余额", "当日抵扣后税基余额", "计税标志"};
        try {
            Workbook sourceWorkbook = WorkbookFactory.create(new FileInputStream(sourcePath));
            Workbook targetWorkbook = WorkbookFactory.create(new FileInputStream(targetPath));

            Sheet sourceSheet = sourceWorkbook.getSheet("Sheet1");
            Sheet targetSheet = targetWorkbook.getSheet(targetSheetName);

            CellStyle dateCellStyle = targetWorkbook.createCellStyle();
            dateCellStyle.setDataFormat(targetWorkbook.getCreationHelper().createDataFormat().getFormat("yyyy/m/d"));

            int sourceStartRow = 3; // 第4行（0-based）
            int sourceRowCount = sourceSheet.getLastRowNum() - sourceStartRow ;

            String originalPrintArea = targetWorkbook.getPrintArea(targetSheet.getWorkbook().getSheetIndex(targetSheet));

            int targetInsertRow = 3; // 第4行（0-based）
            if (sourceRowCount > 0) {
                targetSheet.shiftRows(targetInsertRow, targetSheet.getLastRowNum(), sourceRowCount, true, false);
            }

            Row sourceHeaderRow = sourceSheet.getRow(2); // 第三行是表头
            Map<String, Integer> headerMap = new HashMap<>();
            Map<Integer, Integer> columnMapping = new HashMap<>();

            for (Cell cell : sourceHeaderRow) {
                String header = cell.getStringCellValue().trim().toLowerCase();
                headerMap.put(header, cell.getColumnIndex());
            }

            for (int i = 0; i < targetHeaders.length; i++) {
                Integer sourceColIndex = headerMap.get(targetHeaders[i].toLowerCase());
                if (sourceColIndex == null) {
                    throw new Exception("未找到标题列: " + targetHeaders[i]);
                }
                columnMapping.put(sourceColIndex, i); // 目标列索引从0(A)到6(G)
            }

            for (int i = 0; i < sourceRowCount; i++) {
                Row sourceRow = sourceSheet.getRow(sourceStartRow + i);
                Row targetRow = targetSheet.createRow(targetInsertRow + i);
                if (sourceRow != null) {
                    for (Map.Entry<Integer, Integer> entry : columnMapping.entrySet()) {
                        Cell sourceCell = sourceRow.getCell(entry.getKey());
                        if (sourceCell != null) {
                            Cell targetCell = targetRow.createCell(entry.getValue());
                            if (entry.getValue() == 2) {
                                //日期列
                                targetCell.setCellStyle(dateCellStyle);
                                // 处理日期值
                                if (sourceCell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(sourceCell)) {
                                    targetCell.setCellValue(sourceCell.getDateCellValue());
                                } else if (sourceCell.getCellType() == CellType.STRING) {
                                    // 尝试解析字符串为日期
                                    try {
                                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/M/d");
                                        Date date = sdf.parse(sourceCell.getStringCellValue());
                                        targetCell.setCellValue(date);
                                    } catch (Exception e) {
                                        // 如果不是日期格式，直接复制字符串值
                                        targetCell.setCellValue(sourceCell.getStringCellValue());
                                    }
                                } else {
                                    // 其他类型直接复制值
                                    copyCellValue(sourceCell, targetCell);
                                }
                            } else {
                                copyCellValue(sourceCell, targetCell);
                            }
                        }
                    }
                }
            }

            // 7. 调整打印区域
            if (originalPrintArea != null && !originalPrintArea.isEmpty()) {
                // 解析原始打印区域
                String[] printAreas = originalPrintArea.split(",");
                for (int i = 0; i < printAreas.length; i++) {
                    String area = printAreas[i];
                    CellRangeAddress cra = CellRangeAddress.valueOf(area);

                    // 如果打印区域在插入行之后，需要下移
                    if (cra.getFirstRow() >= targetInsertRow) {
                        cra.setFirstRow(cra.getFirstRow() + sourceRowCount);
                        cra.setLastRow(cra.getLastRow() + sourceRowCount);
                    }
                    // 如果打印区域包含插入行，需要扩展
                    else if (cra.getFirstRow() < targetInsertRow && cra.getLastRow() >= targetInsertRow) {
                        cra.setLastRow(cra.getLastRow() + sourceRowCount);
                    }

                    printAreas[i] = cra.formatAsString();
                }

                // 设置新的打印区域
                targetWorkbook.setPrintArea(
                        targetSheet.getWorkbook().getSheetIndex(targetSheet),
                        String.join(",", printAreas)
                );
            } else {
                // 如果没有设置打印区域，创建一个包含所有数据的打印区域
                int lastRow = targetSheet.getLastRowNum();
                int lastCol = 0;
                for (Row row : targetSheet) {
                    if (row.getLastCellNum() > lastCol) {
                        lastCol = row.getLastCellNum();
                    }
                }

                String newPrintArea = CellRangeAddress.valueOf(
                        "A1:" + CellReference.convertNumToColString(lastCol - 1) + (lastRow + 1)
                ).formatAsString();

                targetWorkbook.setPrintArea(
                        targetSheet.getWorkbook().getSheetIndex(targetSheet),
                        newPrintArea
                );
            }

            try (FileOutputStream fos = new FileOutputStream(targetPath)) {
                targetWorkbook.write(fos);
            }
            sourceWorkbook.close();
            targetWorkbook.close();
        } catch (Exception e) {
            log.error("MonthlyDataZZSTZHandler_copyJRZRTZZHData_error:{},{}", e, e.getMessage());
            throw e;
        }
    }

    private static void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(sourceCell)) {
                    targetCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case BLANK:
                targetCell.setBlank();
                break;
            default:
                targetCell.setCellValue("");
        }
    }
}
