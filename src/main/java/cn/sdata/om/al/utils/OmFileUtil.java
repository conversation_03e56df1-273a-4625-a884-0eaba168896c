package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.sdata.om.al.entity.MonthlySettlementList;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.apache.commons.compress.archivers.sevenz.SevenZOutputFile;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class OmFileUtil extends cn.hutool.core.io.FileUtil {

    private static final int BUFFER_SIZE = 2 * 1024;

    /**
     * 生成压缩文件
     *
     * @param srcFiles 多个文件
     * @param zipFile  压缩文件
     */
    public static void zipFiles(List<File> srcFiles, File zipFile) {
        // 判断压缩后的文件存在不，不存在则创建
        if (!zipFile.exists()) {
            try {
                zipFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 创建 FileOutputStream 对象
        FileOutputStream fileOutputStream;
        // 创建 ZipOutputStream
        ZipOutputStream zipOutputStream;
        try {
            // 实例化 FileOutputStream 对象
            fileOutputStream = new FileOutputStream(zipFile);
            // 实例化 ZipOutputStream 对象
            zipOutputStream = new ZipOutputStream(fileOutputStream);
            // 创建 ZipEntry 对象
            ZipEntry zipEntry;
            // 遍历源文件数组
            for (File srcFile : srcFiles) {
                // 将源文件数组中的当前文件读入 FileInputStream 流中
                try (FileInputStream fileInputStream = new FileInputStream(srcFile)) {
                    // 实例化 ZipEntry 对象，源文件数组中的当前文件
                    zipEntry = new ZipEntry(srcFile.getName());
                    zipOutputStream.putNextEntry(zipEntry);
                    // 该变量记录每次真正读的字节个数
                    int len;
                    // 定义每次读取的字节数组
                    byte[] buffer = new byte[1024];
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        zipOutputStream.write(buffer, 0, len);
                    }
                }
            }
            zipOutputStream.closeEntry();
            zipOutputStream.close();
            fileOutputStream.close();
            log.info("压缩成功");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 按照给定文件或者文件夹进行压缩
     *
     * @param srcDir           文件或文件夹路径
     * @param out              输出流
     * @param KeepDirStructure 是否保留原目录结构进行压缩
     * @throws RuntimeException 异常
     */
    public static void toZip(String srcDir, OutputStream out, boolean KeepDirStructure) throws RuntimeException {
        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            File sourceFile = newFile(srcDir);
            compress(sourceFile, zos, sourceFile.getName(), KeepDirStructure);
            long end = System.currentTimeMillis();
            log.info("压缩完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 将多个文件压缩到一个压缩包内
     *
     * @param srcFiles 文件对象列表
     * @param out      输出流
     * @throws RuntimeException 异常信息
     */
    public static void toZip(List<File> srcFiles, OutputStream out) throws RuntimeException {
        long start = System.currentTimeMillis();
        ZipOutputStream zos = null;
        try {
            zos = new ZipOutputStream(out);
            for (File srcFile : srcFiles) {
                byte[] buf = new byte[BUFFER_SIZE];
                zos.putNextEntry(new ZipEntry(srcFile.getName()));
                int len;
                FileInputStream in = new FileInputStream(srcFile);
                while ((len = in.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
                zos.closeEntry();
                in.close();
            }
            long end = System.currentTimeMillis();
            log.info("压缩完成，耗时：{} ms", end - start);
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 压缩方法
     *
     * @param sourceFile       文件或目录
     * @param zos              输出流
     * @param name             名称
     * @param KeepDirStructure 是否保留原目录
     * @throws Exception 异常
     */
    private static void compress(File sourceFile, ZipOutputStream zos, String name, boolean KeepDirStructure) throws Exception {
        byte[] buf = new byte[BUFFER_SIZE];
        if (sourceFile.isFile()) {
            zos.putNextEntry(new ZipEntry(name));
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                if (KeepDirStructure) {
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    zos.closeEntry();
                }
            } else {
                for (File file : listFiles) {
                    if (KeepDirStructure) {
                        compress(file, zos, name + "/" + file.getName(), KeepDirStructure);
                    } else {
                        compress(file, zos, file.getName(), KeepDirStructure);
                    }
                }
            }
        }
    }

    /**
     * 批量压缩
     *
     * @param src              原地址
     * @param newSrc           新地址
     * @param zipName          zip名称
     * @param KeepDirStructure 是否保留原目录
     * @throws Exception 异常
     */
    public static void batchCompress(String src, String newSrc, String zipName, boolean KeepDirStructure) throws Exception {
        FileOutputStream fos3 = new FileOutputStream(newFile(newSrc));
        File file = newFile(src);
        ZipOutputStream zos = new ZipOutputStream(fos3);
        compress(file, zos, zipName, KeepDirStructure);
        zos.close();
    }

    /**
     * 压缩多个文件夹到ZIP文件
     *
     * @param srcDirs 源文件夹路径数组
     * @param zipFile 目标ZIP文件
     */
    public static void zipDirectories(String[] srcDirs, File zipFile) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile), StandardCharsets.UTF_8)) {
            zos.setLevel(Deflater.BEST_COMPRESSION); // 设置最高压缩级别[7](@ref)
            for (String srcDir : srcDirs) {
                File dir = new File(srcDir);
                if (!dir.exists()) continue;
                // 保留每个文件夹的根目录名称[1](@ref)
                compressDirectory(dir, dir.getName(), zos);
            }
        }
    }

    /**
     * 递归压缩目录
     *
     * @param dir        当前目录
     * @param parentPath ZIP中的父路径
     */
    private static void compressDirectory(File dir, String parentPath, ZipOutputStream zos) throws IOException {
        for (File file : dir.listFiles()) {
            String entryName = parentPath + "/" + file.getName();
            if (file.isDirectory()) {
                // 创建目录条目（需添加斜杠）[1](@ref)
                zos.putNextEntry(new ZipEntry(entryName + "/"));
                zos.closeEntry();
                compressDirectory(file, entryName, zos); // 递归处理子目录[9](@ref)
            } else {
                try (FileInputStream fis = new FileInputStream(file)) {
                    ZipEntry entry = new ZipEntry(entryName);
                    zos.putNextEntry(entry);
                    byte[] buffer = new byte[BUFFER_SIZE];
                    int len;
                    while ((len = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }
                    zos.closeEntry();
                }
            }
        }
    }

    /**
     * 7z文件压缩
     *
     * @param inputFile  待压缩文件夹/文件名
     * @param outputFile 生成的压缩包名字
     */

    public static void zip7z(String inputFile, String outputFile, List<String> needZipFiles) throws Exception {
        File input = new File(inputFile);
        if (!input.exists()) {
            throw new Exception(input.getPath() + "待压缩文件不存在");
        }
        SevenZOutputFile out = new SevenZOutputFile(new File(outputFile));
        compress(out, input, null, needZipFiles);
        out.close();
    }

    /**
     * @param name 压缩文件名，可以写为null保持默认
     */
    //递归压缩
    private static void compress(SevenZOutputFile out, File input, String name, List<String> needZipFiles) throws IOException {
        if (name == null) {
            name = input.getName();
        }
        SevenZArchiveEntry entry;
        //如果路径为目录（文件夹）
        if (input.isDirectory()) {
            //取出文件夹中的文件（或子文件夹）
            File[] flist = input.listFiles();
            assert flist != null;
            if (flist.length == 0)//如果文件夹为空，则只需在目的地.7z文件中写入一个目录进入
            {
                entry = out.createArchiveEntry(input, name + "/");
                out.putArchiveEntry(entry);
            } else//如果文件夹不为空，则递归调用compress，文件夹中的每一个文件（或文件夹）进行压缩
            {
                for (File file : flist) {
                    if (file.getName().matches("\\d{1,3}\\..*")) {
                        if (CollectionUtil.isNotEmpty(needZipFiles)) {
                            for (String needZipFile : needZipFiles) {
                                if (needZipFile.equals(file.getName())) {
                                    log.info("需要压缩到7z的目录是:{}", needZipFile);
                                    compress(out, file, name + "/" + file.getName(), needZipFiles);
                                }
                            }
                        }
                    } else {
                        compress(out, file, name + "/" + file.getName(), needZipFiles);
                    }
                }
            }
        } else//如果不是目录（文件夹），即为文件，则先写入目录进入点，之后将文件写入7z文件中
        {
            FileInputStream fos = new FileInputStream(input);
            BufferedInputStream bis = new BufferedInputStream(fos);
            entry = out.createArchiveEntry(input, name);
            out.putArchiveEntry(entry);
            int len;
            //将源文件写入到7z文件中
            byte[] buf = new byte[1024];
            while ((len = bis.read(buf)) != -1) {
                out.write(buf, 0, len);
            }
            bis.close();
            fos.close();
            out.closeArchiveEntry();
        }
    }

    /**
     * 7z解压缩
     *
     * @param z7zFilePath 7z文件的全路径
     * @return 压缩包中所有的文件
     */
    public static Map<String, String> unZip7z(String z7zFilePath) {
        String un7zFilePath = "";        //压缩之后的绝对路径
        SevenZFile zIn = null;
        try {
            File file = new File(z7zFilePath);
            un7zFilePath = file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf(".7z"));
            zIn = new SevenZFile(file);
            SevenZArchiveEntry entry = null;
            File newFile = null;
            while ((entry = zIn.getNextEntry()) != null) {
                //不是文件夹就进行解压
                if (!entry.isDirectory()) {
                    newFile = new File(un7zFilePath, entry.getName());
                    if (!newFile.exists()) {
                        new File(newFile.getParent()).mkdirs();   //创建此文件的上层目录
                    }
                    OutputStream out = new FileOutputStream(newFile);
                    BufferedOutputStream bos = new BufferedOutputStream(out);
                    int len = -1;
                    byte[] buf = new byte[(int) entry.getSize()];
                    while ((len = zIn.read(buf)) != -1) {
                        bos.write(buf, 0, len);
                    }
                    bos.flush();
                    bos.close();
                    out.close();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (zIn != null)
                    zIn.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return getFileNameList(un7zFilePath, "");
    }

    /**
     * 获取压缩包中的全部文件
     *
     * @param path 文件夹路径
     * @return 压缩包中所有的文件
     * @childName 每一个文件的每一层的路径==D==区分层数
     */
    private static Map<String, String> getFileNameList(String path, String childName) {
        Map<String, String> files = new HashMap<>();
        File file = new File(path); // 需要获取的文件的路径
        String[] fileNameLists = file.list(); // 存储文件名的String数组
        File[] filePathLists = file.listFiles(); // 存储文件路径的String数组
        for (int i = 0; i < Objects.requireNonNull(filePathLists).length; i++) {
            if (filePathLists[i].isFile()) {
                assert fileNameLists != null;
                files.put(fileNameLists[i] + "==D==" + childName, path + File.separator + filePathLists[i].getName());
            } else {
                files.putAll(getFileNameList(path + File.separator + filePathLists[i].getName(), childName + "&" + filePathLists[i].getName()));
            }
        }
        return files;
    }


    /**
     * 获取zip压缩文件
     *
     * @param monthlySettlementLists 文件信息
     * @param zipFile                压缩文件
     */
    public static void getZipFile(List<MonthlySettlementList> monthlySettlementLists, File zipFile) {
        try {
            LinkedHashMap<Integer, List<MonthlySettlementList>> collect =
                    monthlySettlementLists.stream().collect(Collectors.groupingBy(MonthlySettlementList::getFolderOrder, LinkedHashMap::new, Collectors.toList()));
            List<File> srcFiles = new ArrayList<>();
            for (Map.Entry<Integer, List<MonthlySettlementList>> next : collect.entrySet()) {
                List<MonthlySettlementList> value = next.getValue();
                if (CollectionUtil.isNotEmpty(value)) {
                    Optional<MonthlySettlementList> first = value.stream().filter(n -> StringUtils.isNotBlank(n.getFilePath())).findFirst();
                    if (first.isPresent()) {
                        MonthlySettlementList settlementList = first.get();
                        String filePath = settlementList.getFilePath();
                        File file1 = new File(filePath);
                        File parentFile = file1.getParentFile();
                        srcFiles.add(parentFile);
                        log.info("需要压缩的文件夹为:{}", parentFile.getName());
                    }
                }
            }
            if (CollectionUtil.isEmpty(srcFiles)) {
                BusinessException.throwException("没有找到可压缩的文件");
            }
            List<String> needZipFiles = srcFiles.stream().map(File::getName).collect(Collectors.toList());
            File file = srcFiles.get(0);
            zip7z(file.getParentFile().getAbsolutePath(), zipFile.getAbsolutePath(), needZipFiles);
        } catch (Exception e) {
            BusinessException.throwException(e.getMessage());
        }
    }


    public static MultipartFile fileToMultipartFile(File file) throws Exception {
        DiskFileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("file", Files.probeContentType(file.toPath()), true, file.getName());
        try (FileInputStream fis = new FileInputStream(file);
             OutputStream os = item.getOutputStream()) {
            IOUtils.copy(fis, os);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new CommonsMultipartFile(item);
    }

    public static List<File> transformToFiles(List<RemoteFileInfo> remoteFileInfos) {
        SMBManager smbManager = SpringUtil.getBean(SMBManager.class);
        List<File> res = new ArrayList<>();
        for (RemoteFileInfo remoteFileInfo : remoteFileInfos) {
            String fileName = remoteFileInfo.getFileName();
            byte[] bytes = smbManager.downloadFile(remoteFileInfo.getRelativePath() + File.separator + remoteFileInfo.getFileName());
            try {
                File file = FileUtil.createTempFile(FileUtil.getPrefix(fileName), "." + FileUtil.extName(fileName), true);
                // file = FileUtil.rename(file, FileUtil.getPrefix(fileName) + "." + FileUtil.extName(file), true);
                com.google.common.io.Files.write(bytes, file);
                res.add(file);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return res;
    }
}
