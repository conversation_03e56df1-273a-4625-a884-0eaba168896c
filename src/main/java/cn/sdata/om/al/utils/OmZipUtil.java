package cn.sdata.om.al.utils;


import cn.hutool.core.io.FileUtil;
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class OmZipUtil {

    /**
     * 解压7z文件
     *
     * @param file 7z文件
     * @return 压缩文件中的文件列表
     */
    public static List<File> unzip7z(File file) {
        List<File> fileList = new ArrayList<>();
        try (SevenZFile sevenZFile = new SevenZFile(file)) {
            SevenZArchiveEntry entry;
            while ((entry = sevenZFile.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    String name = entry.getName();
                    File tempFile = FileUtil.createTempFile(FileUtil.getPrefix(name), "." + FileUtil.extName(name), true);
                    InputStream inputStream = sevenZFile.getInputStream(entry);
                    FileUtil.writeFromStream(inputStream, tempFile);
                    inputStream.close();
                    fileList.add(tempFile);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileList;
    }
}
