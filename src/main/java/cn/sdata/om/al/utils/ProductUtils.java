package cn.sdata.om.al.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.sdata.om.al.entity.CommonEntity;
import cn.sdata.om.al.service.mail.MailCommonInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ProductUtils {

    private MailCommonInfoService mailCommonInfoService;

    @Autowired
    public void setMailCommonInfoService(MailCommonInfoService mailCommonInfoService) {
        this.mailCommonInfoService = mailCommonInfoService;
    }

    public List<String> getProductNamesByIds(List<String> ids) {
        List<String> productNames = new ArrayList<>();
        Map<String, String> collect = new HashMap<>();
        List<CommonEntity> list = mailCommonInfoService.list();
        if (CollectionUtil.isNotEmpty(list)) {
            collect = list.stream().collect(Collectors.toMap(CommonEntity::getId, CommonEntity::getName));
        }
        for (String id : ids) {
            String name = collect.get(id);
            if (StringUtils.isNotBlank(name)) {
                productNames.add(name);
            }
        }
        return productNames;
    }
}
