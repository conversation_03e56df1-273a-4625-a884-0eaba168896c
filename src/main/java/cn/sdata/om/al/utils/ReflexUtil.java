package cn.sdata.om.al.utils;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.lang.reflect.Method;
import java.util.*;

public class ReflexUtil<T> {

    public String doInvoke(Object obj, String methodName) throws Exception {
        Class<?> clazz = obj.getClass();
        Method method = clazz.getMethod(methodName);
        return (String) method.invoke(obj);
    }

    @SuppressWarnings("unchecked")
    public List<Date> invokeTradeDayQuery(Object obj, String methodName, String arg) throws Exception {
        Class<?> clazz = obj.getClass();
        Method method = clazz.getMethod(methodName, String.class);
        return (List<Date>) method.invoke(obj, arg);
    }

    @SuppressWarnings("unchecked")
    public Set<Date> invokeTradeDayQuery(Object obj, String methodName, List<Class<?>> paramTypes, List<Object> paramList) throws Exception {
        Class<?> clazz = obj.getClass();
        Class<?>[] array = paramTypes.toArray(new Class<?>[0]);
        Method method = clazz.getMethod(methodName, array);
        Object[] params = paramList.toArray(new Object[0]);
        return (Set<Date>) method.invoke(obj, params);
    }

    public void doSave(Object service, List<?> result, String methodName) {
        if (service == null){
            throw new RuntimeException("入参不得为空");
        }
        if (methodName == null) {
            methodName = "saveOrUpdateBatch";
        }
        try {
            Class<?> clazz = service.getClass();
            Method saveOrUpdateBatch = clazz.getMethod(methodName, Collection.class);
            saveOrUpdateBatch.invoke(service, result);
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }

    public void doRemove(Object service, Map<String, Object> param) {
        if (service == null){
            throw new RuntimeException("入参不得为空");
        }
        try {
            Class<?> clazz = service.getClass();
            Method removeByMap = clazz.getMethod("removeByMap", Map.class);
            removeByMap.invoke(service, param);
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }

    @SuppressWarnings("unchecked")
    public Page<T> doQueryWithPage(Object service, IPage<T> param, QueryWrapper<T> queryWrapper) {
        if (service == null){
            throw new RuntimeException("service不得为空");
        }
        try {
            Class<?> clazz = service.getClass();
            Method page = clazz.getMethod("page", IPage.class, Wrapper.class);
            return (Page<T>) page.invoke(service, param, queryWrapper);
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }

    @SuppressWarnings("unchecked")
    public List<T> doQuery(Object service, QueryWrapper<T> queryWrapper) {
        if (service == null){
            throw new RuntimeException("service不得为空");
        }
        try {
            Class<?> clazz = service.getClass();
            Method method = clazz.getMethod("list", Wrapper.class);
            return (List<T>) method.invoke(service, queryWrapper);
        } catch (Exception e) {
            throw new RuntimeException(e.getCause());
        }
    }
}
