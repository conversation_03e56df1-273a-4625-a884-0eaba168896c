package cn.sdata.om.al.utils;

import cn.hutool.core.io.FileUtil;
import cn.sdata.om.al.config.SMBConfig;
import cn.sdata.om.al.entity.FileInfo;
import cn.sdata.om.al.entity.RemoteFileInfo;
import cn.sdata.om.al.entity.SMBClientInfo;
import cn.sdata.om.al.entity.mail.ShareDownloadFile;
import cn.sdata.om.al.exception.SMBException;
import com.hierynomus.msdtyp.AccessMask;
import com.hierynomus.msdtyp.FileTime;
import com.hierynomus.msfscc.fileinformation.FileIdBothDirectoryInformation;
import com.hierynomus.mssmb2.SMB2CreateDisposition;
import com.hierynomus.mssmb2.SMB2ShareAccess;
import com.hierynomus.smbj.share.DiskShare;
import com.hierynomus.smbj.share.File;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.sdata.om.al.utils.StringUtil.concatSeparator;

@Slf4j
@Component
@AllArgsConstructor
public class SMBManager {


    private final SMBClientInfo smbClientInfo;

    public List<String> listDir(String path) {
        if (path == null) {
            path = "\\";
        }
        List<String> list = new ArrayList<>();
        smbClientInfo.reconnect();
        DiskShare share = smbClientInfo.getShare();
        String sharePath = share.getSmbPath().toUncPath();
        Set<String> skipFiles = Set.of(".", "..", ".DS_Store");
        for (FileIdBothDirectoryInformation fileIdBothDirectoryInformation : share.list(path)) {
            String fileName = fileIdBothDirectoryInformation.getFileName();
            String fullPath = concatSeparator(concatSeparator(sharePath, path), fileName);
            if (!skipFiles.contains(fileName)) {
                list.add(fullPath);
            }
        }
        return list;
    }

    public List<RemoteFileInfo> listFileInfo(String path) {
        if (path == null) {
            path = "\\";
        }
        smbClientInfo.reconnect();
        DiskShare share = smbClientInfo.getShare();
        String sharePath = share.getSmbPath().toUncPath();
        Set<String> skipFiles = Set.of(".", "..", ".DS_Store");
        List<RemoteFileInfo> list = new ArrayList<>();
        List<FileIdBothDirectoryInformation> fileInfoList = share.list(path);
        if (fileInfoList != null) {
            for (int i = 0; i < fileInfoList.size(); i++) {
                FileIdBothDirectoryInformation fileInfo = fileInfoList.get(i);
                String fileName = fileInfo.getFileName();
                if (skipFiles.contains(fileName)) {
                    continue;
                }
                RemoteFileInfo remoteFileInfo = new RemoteFileInfo();
                remoteFileInfo.setFileName(fileName);
                remoteFileInfo.setFilePath(concatSeparator(sharePath, path));
                remoteFileInfo.setRelativePath(path);
                FileTime creationTime = fileInfo.getCreationTime();
                if (creationTime != null) {
                    remoteFileInfo.setDownloadTime(creationTime.toDate());
                    remoteFileInfo.setDealTime(creationTime.toDate());
                }
                remoteFileInfo.setFileSize(fileInfo.getEndOfFile());
                remoteFileInfo.setSerialNumber(i + 1);
                list.add(remoteFileInfo);
            }
        }
        return list;
    }

    public byte[] downloadFile(String filePath) {
        try {
            smbClientInfo.reconnect();
            DiskShare share = smbClientInfo.getShare();
            File file = share.openFile(filePath, EnumSet.of(AccessMask.GENERIC_READ), null, SMB2ShareAccess.ALL, SMB2CreateDisposition.FILE_OPEN, null);
            InputStream inputStream = file.getInputStream();
            return inputStream.readAllBytes();
        } catch (Exception e) {
            throw new SMBException("下载文件异常", e);
        } finally {
            smbClientInfo.closeAll();
        }
    }

    public void uploadFile(String path, String fileName, byte[] bytes) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new SMBException("文件名不能为空");
        }
        if (bytes == null || bytes.length == 0) {
            throw new SMBException("文件内容不能为空");
        }

        try {
            smbClientInfo.reconnect();
            SMBConfig smbConfig = smbClientInfo.getSmbConfig();
            String upload = smbConfig.getUpload();
            DiskShare share = smbClientInfo.getShare();
            String subPath = concatSeparator(upload, path);

            log.info("开始上传文件: fileName={}, path={}, subPath={}", fileName, path, subPath);

            // 确保目录存在
            createDir(subPath);

            String fullPath = concatSeparator(subPath, fileName);
            log.info("文件完整路径: {}", fullPath);

            // 验证目录是否真的存在
            if (!share.folderExists(subPath)) {
                throw new SMBException("目录创建失败或不存在: " + subPath);
            }

            File file = share.openFile(fullPath, EnumSet.of(AccessMask.GENERIC_WRITE), null, SMB2ShareAccess.ALL, SMB2CreateDisposition.FILE_OVERWRITE_IF, null);
            OutputStream outputStream = file.getOutputStream();
            outputStream.write(bytes);
            outputStream.flush();
            outputStream.close();
            file.close();

            log.info("文件上传成功: {}", fullPath);

        } catch (SMBException e) {
            log.error("SMB异常 - 上传文件失败: fileName={}, path={}, 错误信息: {}", fileName, path, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("上传文件失败: fileName={}, path={}, 错误信息: {}", fileName, path, e.getMessage(), e);
            throw new SMBException("上传文件失败: " + fileName, e);
        } finally {
            smbClientInfo.closeAll();
        }
    }

    @PreDestroy
    public void down() {
        log.info("销毁SMBClientInfo相关数据");
        smbClientInfo.closeAll();
    }

    private void createDir(String path) {
        if (path == null || path.trim().isEmpty()) {
            log.warn("创建目录失败: 路径为空");
            return;
        }

        try {
            smbClientInfo.reconnect();
            DiskShare share = smbClientInfo.getShare();

            // 处理路径分隔符，支持 / 和 \ 两种格式
            String normalizedPath = path.replace("/", "\\");
            List<String> dirs = Arrays.stream(normalizedPath.split("\\\\"))
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());

            if (dirs.isEmpty()) {
                log.warn("创建目录失败: 解析后的路径为空, 原始路径: {}", path);
                return;
            }

            dirs = buildPaths(dirs);
            log.info("准备创建目录层级: {}", dirs);

            for (String thisDir : dirs) {
                try {
                    if (!share.folderExists(thisDir)) {
                        log.info("创建目录: {}", thisDir);
                        share.mkdir(thisDir);
                        log.info("目录创建成功: {}", thisDir);
                    } else {
                        log.debug("目录已存在: {}", thisDir);
                    }
                } catch (Exception e) {
                    log.error("创建目录失败: {}, 错误信息: {}", thisDir, e.getMessage(), e);
                    throw new SMBException("创建目录失败: " + thisDir, e);
                }
            }

            log.info("目录创建完成: {}", path);

        } catch (Exception e) {
            log.error("创建目录过程中发生异常: {}, 错误信息: {}", path, e.getMessage(), e);
            throw new SMBException("创建目录失败: " + path, e);
        }
    }

    public List<String> buildPaths(List<String> input) {
        List<String> result = new ArrayList<>();
        StringBuilder currentPath = new StringBuilder();
        for (int i = 0; i < input.size(); i++) {
            if (i > 0) {
                currentPath.append("\\");
            }
            currentPath.append(input.get(i));
            result.add(currentPath.toString());
        }

        return result;
    }

    /**
     * 下载文件并构建所需要的属性
     *
     * @param filePath 文件路径
     * @return 下载的文件信息
     */
    public ShareDownloadFile downloadReturnFile(String filePath) {
        try {
            // 下载前新增重连
            smbClientInfo.reconnect();
            ShareDownloadFile shareDownloadFile = new ShareDownloadFile();
            DiskShare share = smbClientInfo.getShare();
            File file = share.openFile(filePath, EnumSet.of(AccessMask.GENERIC_READ), null, SMB2ShareAccess.ALL, SMB2CreateDisposition.FILE_OPEN, null);
            String fileName = FileUtil.getName(file.getUncPath());
            InputStream inputStream = file.getInputStream();
            shareDownloadFile.setFileName(fileName);
            // 注意：此方法会读取完流
            shareDownloadFile.setContent(inputStream.readAllBytes());
            // 根据文件内容重新生成流供下面使用 上下两行不能调换
            shareDownloadFile.setInputStream(new ByteArrayInputStream(shareDownloadFile.getContent()));
            shareDownloadFile.setFilePath("http://" + smbClientInfo.getSmbConfig().getHost() + java.io.File.separator + filePath);
            return shareDownloadFile;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            smbClientInfo.closeAll();
        }
        return null;
    }

    public List<String> listRelativeDir(String path) {
        if (path == null) {
            path = "/";
        }
        List<String> list = new ArrayList<>();
        DiskShare share = smbClientInfo.getShare();
        Set<String> skipFiles = Set.of(".", "..", ".DS_Store");
        for (FileIdBothDirectoryInformation fileIdBothDirectoryInformation : share.list(path)) {
            String fileName = fileIdBothDirectoryInformation.getFileName();
            String fullPath = concatSeparator(path, fileName);
            if (!skipFiles.contains(fileName)) {
                list.add(fullPath);
            }
        }
        return list;
    }

    public FileInfo downloadBatch(List<RemoteFileInfo> files) {
        if (files == null || files.isEmpty()) {
            throw new SMBException("下载文件为空");
        }
        FileInfo fileInfo = new FileInfo();
        if (files.size() == 1) {
            RemoteFileInfo remoteFileInfo = files.get(0);
            fileInfo.setFileName(remoteFileInfo.getFileName());
            fileInfo.setFileData(downloadFile(concatSeparator(remoteFileInfo.getRelativePath(), remoteFileInfo.getFileName())));
            return fileInfo;
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (ZipOutputStream zos = new ZipOutputStream(outputStream)) {
            for (int i = 0; i < files.size(); i++) {
                RemoteFileInfo remoteFileInfo = files.get(i);
                String relativePath = concatSeparator(remoteFileInfo.getRelativePath(), remoteFileInfo.getFileName());
                try {
                    byte[] bytes = downloadFile(relativePath);
                    // 防止重复文件名冲突，可根据需要改为保留路径结构
                    String zipEntryName = (i + 1) + "_" + remoteFileInfo.getFileName();
                    zos.putNextEntry(new ZipEntry(zipEntryName));
                    try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = bis.read(buffer)) >= 0) {
                            zos.write(buffer, 0, length);
                        }
                    }
                    zos.closeEntry();
                } catch (IOException e) {
                    log.error("打包文件失败: {}", relativePath, e);
                    // 继续打包其他文件，也可以选择记录失败文件名返回提示
                }
            }

            // 生成压缩包名称，使用第一个文件的路径信息（兼容 / 和 \）
            RemoteFileInfo first = files.get(0);
            String relativePath = first.getRelativePath().replace("\\", "/");
            String[] split = relativePath.split("/");
            String fileName = split[split.length - 1];
            fileInfo.setFileName(fileName + ".zip");
            // 写入压缩包内容
            fileInfo.setFileData(outputStream.toByteArray());
        } catch (Exception e) {
            throw new SMBException("导出zip出错", e);
        } finally {
            smbClientInfo.closeAll();
        }
        return fileInfo;
    }
}



