package cn.sdata.om.al.utils;

import cn.dev33.satoken.stp.StpUtil;
import cn.sdata.om.al.entity.User;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

import static cn.sdata.om.al.constant.BaseConstant.DEFAULT_USERNAME;

@Slf4j
public class SecureUtil {

    private SecureUtil() {
    }

    public static User currentUser() {
        return (User) StpUtil.getSession().get("user");
    }

    public static String currentUserName() {
        Object userName = DEFAULT_USERNAME;
        try {
            userName = StpUtil.getExtra("userName");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return Objects.isNull(userName) ? DEFAULT_USERNAME : userName.toString();
    }

    public static String currentUserId() {
        return StpUtil.getExtra("userId").toString();
    }

    public static boolean isAdmin(String userId) {
        if (StpUtil.getExtra("userName").toString().equals("admin")) {
            return true;
        }
        return StpUtil.getExtra("role") != null && List.of(StpUtil.getExtra("role").toString().split(",")).contains("1");
    }

}
