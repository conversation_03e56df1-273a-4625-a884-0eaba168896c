package cn.sdata.om.al.utils;

import cn.sdata.om.al.constant.BaseConstant;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;


import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class StringUtil {

    public static String concatSeparator(String first, String second) {
        if (first == null || second == null) {
            return null;
        }
        if (second.equals("\\")) {
            return first;
        }
        return first + (first.endsWith("\\") ? "" : "\\") + second;
    }

    public static String getNowDateStr(String pattern) {
        if (pattern == null) {
            pattern = BaseConstant.HORIZONTAL_DATE_FORMAT_PATTERN;
        }
        return new SimpleDateFormat(pattern).format(Calendar.getInstance().getTime());
    }

    public static List<String> splitStr(String input, String separate) {
        if (input == null) {
            return new ArrayList<>();
        }
        if (input.contains(separate)) {
            return new ArrayList<>(Arrays.asList(input.split(separate)));
        } else {
            return new ArrayList<>(List.of(input));
        }
    }

    public static String extractValuationAccountName(String fileName) {
        if (fileName == null) {
            return null;
        }
        // 使用下划线分割文件名
        String[] parts = fileName.split("_");

        // 检查是否至少包含固定前缀、账套名称的一部分和日期部分
        if (parts.length < 3) {
            throw new IllegalArgumentException("文件名格式不符合预期");
        }

        // 计算账套名称的结束位置（排除最后一个部分）
        int endIndex = parts.length - 2;

        // 从第二个部分开始，到倒数第二个部分结束
        List<String> accountParts = new ArrayList<>(Arrays.asList(parts).subList(1, endIndex + 1));

        // 合并账套名称部分
        return String.join("_", accountParts);
    }

    public static String extractTAAccountName(String fileName) {
        if (fileName == null) {
            return null;
        }
        // 使用下划线分割文件名
        String[] parts = fileName.split("_");
        // 确保分割后的数组长度足够
        if (parts.length >= 2) {
            // 提取倒数第二个元素作为账套名称
            return parts[parts.length - 2];
        }
        return null;
    }

    public static String dealMacro(String original, Map<String, String> variables) {
        if (variables == null || variables.isEmpty()) {
            return original;
        }
        StringSubstitutor substitute = new StringSubstitutor(variables, "#", "#", '$');
        return substitute.replace(original);
    }

    public static String extractBalanceAccountNumber(String fileName) {
        if (fileName == null) {
            return null;
        }
        // 使用下划线分割文件名
        String[] parts = fileName.split("_");
        // 确保分割后的数组长度足够
        if (parts.length >= 3) {
            // 提取第三个元素作为账套编号
            return parts[2];
        }
        return null;
    }

    public static String transFileToBase64(byte[] bytes) {
        Objects.requireNonNull(bytes, "文件不得为空");
        try {
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            throw new RuntimeException("文件转换失败", e);
        }
    }

    public static String getKeyStr(String input) {
        if (input == null) {
            return null;
        }
        if (input.contains("-")) {
            String[] split = input.split("-");
            return split[0];
        } else {
            return input;
        }
    }

    public static String getProductName(@NonNull List<String> nameList) {
        String nameStr = "";
        if (!nameList.isEmpty()) {
            nameStr = nameList.get(0);
            if (nameList.size() > 1) {
                nameStr = nameStr + "等";
            }
        }
        return nameStr;
    }

    public static String dealAccountName(String accountName) {
        if (accountName == null) {
            return null;
        } else {
            // 1. 替换"一"为"-"
            // 2. 去除多余的空格（包括全角空格）
            // 3. 去除首尾空格
            return accountName.replace("一", "-")
                    .replaceAll("\\s+", "")  // 去除所有空格（包括全角空格）
                    .trim();
        }
    }

    public static String delStartStr(String source) {
        if (StringUtils.isNotBlank(source)) {
            return source.replaceFirst("^[a-zA-Z]+", "");
        }
        return "";
    }
}
