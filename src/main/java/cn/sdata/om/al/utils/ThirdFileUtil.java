package cn.sdata.om.al.utils;

import cn.sdata.om.al.constant.BaseConstant;
import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.entity.ValuationTableData;
import cn.sdata.om.al.service.AccountInformationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class ThirdFileUtil {

    private final ResourceLoader resourceLoader;
    private final AccountInformationService accountInformationService;

    public byte[] getAttachment(List<ValuationTableData> netValueFromValuation, String name) {
        Resource resource = resourceLoader.getResource("classpath:third/" + name +"净值对接--模板.xlsx");
        try (InputStream inputStream = resource.getInputStream();
             ByteArrayOutputStream fos = new ByteArrayOutputStream();
             XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            int numberOfSheets = workbook.getNumberOfSheets();
            int catalogNum = numberOfSheets - 2;
            int templateNum = numberOfSheets - 1;
            int size = netValueFromValuation.size();
            createSheet(size, workbook, templateNum);
            XSSFSheet catalogSheet = workbook.getSheetAt(catalogNum);
            writeDataToCatalogSheet(netValueFromValuation, catalogSheet, workbook, templateNum);
            writeDataToTemplateSheet(netValueFromValuation, workbook, templateNum);
            workbook.write(fos);
            return fos.toByteArray();
        } catch (Exception e) {
            log.error("生成异常", e);
            throw new RuntimeException(e);
        }
    }

    private void createSheet(int size, XSSFWorkbook workbook, int templateNum){
        if (size > 1) {
            for (int i = 0; i < size - 1; i++) {
                workbook.cloneSheet(templateNum, BaseConstant.SHEET_NAME + (i + 2));
            }
        }
    }

    private void writeDataToCatalogSheet(List<ValuationTableData> netValueFromValuation, XSSFSheet catalogSheet, XSSFWorkbook workbook, int templateNum) {
        Objects.requireNonNull(netValueFromValuation, "目录表不得为空");
        Map<String, AccountInformation> accountMap = accountInformationService.list().stream().collect(Collectors.toMap(AccountInformation::getId,
                accountInformation -> accountInformation,
                (oldOne, newOne) -> newOne));
        for (int i = 0; i < netValueFromValuation.size(); i++) {
            ValuationTableData valuationTableData = netValueFromValuation.get(i);
            String productId = valuationTableData.getProductId();
            AccountInformation accountInformation = accountMap.get(productId);
            if (accountInformation != null) {
                XSSFRow row = catalogSheet.createRow(i + 1);
                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(accountInformation.getWindCode());
                XSSFCell cell = row.createCell(2);
                CreationHelper helper = workbook.getCreationHelper();
                Hyperlink hyperlink = helper.createHyperlink(HyperlinkType.DOCUMENT);
                hyperlink.setAddress("'" + BaseConstant.SHEET_NAME + (templateNum + i) + "'!A1");
                cell.setCellValue(accountInformation.getFullProductName());
                cell.setHyperlink(hyperlink);
                row.createCell(3).setCellValue(accountInformation.getEstablishmentDate());
            }
        }
    }

    private void writeDataToTemplateSheet(List<ValuationTableData> netValueFromValuation, XSSFWorkbook workbook, int templateNum) {
        Objects.requireNonNull(netValueFromValuation, "模板表不得为空");
        for (int i = 0; i < netValueFromValuation.size(); i++) {
            int sheetNum = templateNum + i;
            ValuationTableData valuationTableData = netValueFromValuation.get(i);
            XSSFSheet sheet = workbook.getSheetAt(sheetNum);
            XSSFRow row = sheet.createRow(1);
            row.createCell(0).setCellValue(valuationTableData.getProductName());
            row.createCell(1).setCellValue(valuationTableData.getValuationDate());
            row.createCell(2).setCellValue(valuationTableData.getNetValue());
            row.createCell(3).setCellValue(valuationTableData.getSumNetValue());
        }
    }

}
