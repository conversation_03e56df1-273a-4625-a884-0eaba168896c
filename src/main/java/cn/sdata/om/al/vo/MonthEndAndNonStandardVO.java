package cn.sdata.om.al.vo;

import cn.sdata.om.al.entity.PageParam;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 月末存款及非标行情检查
 */
@Data
@ExcelIgnoreUnannotated
public class MonthEndAndNonStandardVO extends PageParam {


    /**
     * 账套名称
     */
    private List<String> productNames;

    /**
     * id
     */
    private String id;

    /**
     * 数据日期
     */
    @ExcelProperty(value = "数据日期", index = 0)
    private String dataDate;

    /**
     * 账套编号
     */
    private String productId;

    /**
     * 产品代码
     */
    @ExcelProperty(value = "产品代码", index = 2)
    private String productCode;

    /**
     * 账套名称
     */
    @ExcelProperty(value = "账套名称", index = 1)
    private String productName;

    /**
     * 证券类型
     */
    @ExcelProperty(value = "证券类型", index = 3)
    private String securityType;

    /**
     * 证券类型
     */
    private List<String> securityTypes;

    /**
     * 证券名称
     */
    @ExcelProperty(value = "证券名称", index = 4)
    private String securityName;

    /**
     * 证券代码
     */
    @ExcelProperty(value = "证券代码", index = 5)
    private String securityCode;

    /**
     * 证券代码
     */
    private List<String> securityCodes;

    /**
     * 证券成本
     */
    @ExcelProperty(value = "证券成本", index =6)
    private String securityCost;

    /**
     * 证券市值
     */
    @ExcelProperty(value = "证券市值", index = 7)
    private String securityMarketValue;

    /**
     * 估值与本金的比例
     */
    @ExcelProperty(value = "估值与本金的比例", index = 8)
    private String roi;

    /**
     * 比例异常提示
     */
//    @ExcelProperty(value = "比例异常提示", index = 9)
    private Integer proportionPrompt;

    /**
     * 比例异常提示
     */
    @ExcelProperty(value = "比例异常提示", index = 9)
    private String proportionPromptFlag;

    /**
     * 本月末估值价格
     */
    @ExcelProperty(value = "本月末估值价格", index = 10)
    private String monthEndValuationPrice;

    /**
     * 上月末估值价格
     */
    @ExcelProperty(value = "上月末估值价格", index = 11)
    private String valuationPriceEndLastMonth;

    /**
     * 价格波动率
     */
    @ExcelProperty(value = "价格波动率", index = 12)
    private String priceVolatility;

    /**
     * 波动率提示
     */
//    @ExcelProperty(value = "波动率提示", index = 13)
    private Integer volatilityAlert;

    /**
     * 波动率提示
     */
    @ExcelProperty(value = "波动率提示", index = 13)
    private String volatilityAlertFlag;

    /**
     * 付息日
     */
    @ExcelProperty(value = "付息日", index = 14)
    private String interestPaymentDate;

    /**
     * 行权日
     */
    @ExcelProperty(value = "行权日", index = 15)
    private String exerciseDate;

    /**
     * 到期日
     */
    @ExcelProperty(value = "到期日", index = 16)
    private String dueDate;

    private String flag;
}