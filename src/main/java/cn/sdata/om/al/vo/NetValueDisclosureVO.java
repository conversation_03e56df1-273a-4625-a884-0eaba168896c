package cn.sdata.om.al.vo;

import lombok.Data;

@Data
public class NetValueDisclosureVO {

    private String id;
    private String productId;
    private String groupName;
    private String fullProductName;
    private String productCode;
    private String custodianBank;
    private Integer isElectronicDirectConnection;
    private Integer isRevalued;
    private String assetType;
    private String valuationDate;
    private Integer valuationTableGenerated;
    private String custodyReconciliationEmailSent;
    private String reconciliationStatus;
    private Integer valuationTableConfirmed;
    private Integer netValueDisclosed;
    private String valuationTableDownloaded;
    private String valuationTableSent;
    private String investorReportSent;
    private String thirdPartySent;

}
