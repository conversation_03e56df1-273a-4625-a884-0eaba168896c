package cn.sdata.om.al.vo;

import cn.sdata.om.al.entity.PageParam;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 多账套净值信息
 */
@Data
public class NetValueOfAccountSetVO  extends PageParam {
    /**
     * id
     */
    private String id;

    /**
     * 数据日期
     */
    private String dataDate;

    /**
     * 对比日
     */
    private String comparisonDay;

    /**
     * 开始数据日期
     */
    private String startDate;

    /**
     * 结束数据日期
     */
    private String endDate;

    /**
     * 估值时间  T1 T0
     */
    private String valuationTime;

    /**
     * 账套名称
     */
    private List<String> productNames;

    /**
     * 账套组id
     */
    private List<String> productGroups;

    /**
     * 产品代码
     */
    private List<String> productCodes;

    /**
     * 日期类型  1交易日  2自然日
     */
    private String dateType;

    /**
     * 账套编号
     */
    private String productId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 结构化名称
     */
    private String structuredName;

    /**
     * 产品总份额
     */
    private String enJjzfe;

    /**
     * 产品总净值
     */
    private String enJjzjz;

    /**
     * 今日单位净值
     */
    private String enJjdwjz;

    /**
     * 累计单位净值
     */
    private String enLjjz;

    /**
     * 万份单位基金收益
     */
    private String enDwjjsy;

    /**
     * 7日年化收益率(%)
     */
    private String enFdsy;

    /**
     * 单位净值波动-单日差额
     */
    private String drce;

    /**
     * 单位净值波动-单日涨跌幅（%）
     */
    private String drzdf;

    /**
     * 当年收益率（年化）-单位净值（%）
     */
    private String dwjzYear;

    /**
     * 当年收益率（年化）-累计单位净值（%）
     */
    private String ljdwjzYear;

    /**
     * 区间段收益率（年化）-单位净值（%）
     */
    private String dwjzInterval;

    /**
     * 区间段收益率（年化）-累计单位净值（%）
     */
    private String ljdwjzInterval;
}