package cn.sdata.om.al.vo;

import cn.sdata.om.al.enums.OperationStatus;
import lombok.Data;

import java.util.Date;

/**
 * 开基确认单日志展示VO
 */
@Data
public class OpenFundConfirmationLogVO {

    /**
     * 上传确认单日志
     */
    @Data
    public static class UploadLogVO {
        /**
         * 文件名称（可下载）
         */
        private String fileName;

        /**
         * 文件ID（用于下载）
         */
        private String fileId;

        /**
         * 确认单ID（用于下载）
         */
        private String confirmationId;

        /**
         * 上传时间
         */
        private Date uploadTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作状态
         */
        private OperationStatus operationStatus;
    }

    /**
     * 下载确认单日志
     */
    @Data
    public static class DownloadLogVO {
        /**
         * 时间
         */
        private Date downloadTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作状态
         */
        private OperationStatus operationStatus;

        /**
         * 文件名称（可下载）
         */
        private String fileName;

        /**
         * 文件ID（用于下载）
         */
        private String fileId;

        /**
         * 确认单ID（用于下载）
         */
        private String confirmationId;
    }

    /**
     * 发送邮件日志
     */
    @Data
    public static class MailLogVO {
        /**
         * 邮件发送任务
         */
        private String taskName;

        /**
         * 发送方式
         */
        private String sendMethod;

        /**
         * 状态
         */
        private String mailStatus;

        /**
         * 邮件发送时间
         */
        private Date sendTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 查看（邮件日志ID）
         */
        private String mailLogId;

        /**
         * 确认单ID（用于下载）
         */
        private String confirmationId;
    }

    /**
     * OCR确认日志
     */
    @Data
    public static class OcrLogVO {
        /**
         * 确认时间
         */
        private Date confirmTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 账套名称
         */
        private String accountSetName;

        /**
         * 交易渠道
         */
        private String transactionChannel;

        /**
         * 状态
         */
        private String confirmStatus;

        /**
         * 确认单ID（用于下载）
         */
        private String confirmationId;
    }

    /**
     * 删除记录日志
     */
    @Data
    public static class DeleteLogVO {
        /**
         * 时间
         */
        private Date deleteTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 账套名称
         */
        private String accountSetName;

        /**
         * 交易渠道
         */
        private String transactionChannel;

        /**
         * 操作
         */
        private String deleteStatus;

        /**
         * 确认单ID（用于下载）
         */
        private String confirmationId;
    }
}
