package cn.sdata.om.al.vo;

import cn.sdata.om.al.enums.MailRuleMatchStatus;
import cn.sdata.om.al.enums.MailStatus;
import cn.sdata.om.al.enums.OcrConfirmationStatus;
import lombok.Data;

import java.util.Date;

@Data
public class OpenFundConfirmationVO {
    /**
     * 主键，确认单ID
     */
    private String id;

    /**
     * 确认日期
     */
    private String dataDate;

    /**
     * 交易渠道
     */
    private String transactionChannel;

    /**
     * 账套名称
     */
    private String productName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 邮件ID
     */
    private String emailId;

    /**
     * 邮件规则匹配状态，0表示未匹配，1表示已匹配
     */
    private MailRuleMatchStatus emailRuleMatchStatus;

    /**
     * 邮件接收时间
     */
    private Date emailReceivedTime;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * OCR确认状态，0表示未确认，1表示已确认，2表示已修改
     */
    private OcrConfirmationStatus ocrConfirmationStatus;

    /**
     * 邮件发送状态，0表示未发送，1表示已发送
     */
    private MailStatus emailSentStatus;

    /**
     * 文件路径
     */
    private String filePath;
}
