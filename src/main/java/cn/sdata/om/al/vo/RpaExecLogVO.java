package cn.sdata.om.al.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RpaExecLogVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * rpa业务类型：{@link cn.sdata.om.al.enums.RpaExecLogBizTypeEnum}
     */
    private String rpaBizType;

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 执行ID
     */
    private String execId;

    /**
     * 执行时间
     */
    private String execTime;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 执行状态（-1：正在执行；0：手动停止；1：执行成功；2：执行超时；3：节点异常）
     */
    private String execState;

    /**
     * 执行结果
     */
    private String execResult;

    /**
     * O32节点状态（执行中/成功/失败）
     */
    private String o32NodeState;

    /**
     * 导入结果
     */
    private String importResult;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 执行人
     */
    private String executor;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 完成时间
     */
    private String finishTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 修改时间
     */
    private String updatedTime;

    /**
     * 账户类型(1:一般户; 2、买方托管户; 3、卖方托管户)
     */
    private Integer accountType;

    /**
     * rpa业务场景：{@link cn.sdata.om.al.enums.RpaExecLogBizSceneEnum}
     */
    private String rpaBizScene;

    /**
     * 截图
     */
    private String errorImage;
}
