-- 创建组合净值波动范围规则表
CREATE TABLE IF NOT EXISTS `audit_portfolio_fluctuation_range` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `rule_key` varchar(50) NOT NULL COMMENT '规则标识，同一规则下的记录标识相同',
  `product_id` varchar(50) DEFAULT NULL COMMENT '产品/账套ID',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品/账套名称',
  `lower_compare_type` int DEFAULT NULL COMMENT '下限比较类型：0=大于(>)、1=大于等于(>=)',
  `upper_compare_type` int DEFAULT NULL COMMENT '上限比较类型：2=小于(<)、3=小于等于(<=)',
  `lower_limit` decimal(20,8) DEFAULT NULL COMMENT '下限值',
  `upper_limit` decimal(20,8) DEFAULT NULL COMMENT '上限值',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_key` (`rule_key`) COMMENT '规则标识索引',
  KEY `idx_product_id` (`product_id`) COMMENT '产品ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组合净值波动范围规则'; 