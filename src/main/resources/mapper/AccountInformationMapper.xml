<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.AccountInformationMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.AccountInformation">
    <!--@mbg.generated-->
    <!--@Table account_information-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="full_product_name" jdbcType="VARCHAR" property="fullProductName" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="valuation_time" jdbcType="VARCHAR" property="valuationTime" />
    <result column="is_structured" jdbcType="TINYINT" property="isStructured" />
    <result column="is_electronic_direct_connection" jdbcType="TINYINT" property="isElectronicDirectConnection" />
    <result column="custodian_bank" jdbcType="VARCHAR" property="custodianBank" />
    <result column="secondary_valuation_security_type" jdbcType="VARCHAR" property="secondaryValuationSecurityType" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_number_detail" jdbcType="VARCHAR" property="accountNumberDetail" />
    <result column="opening_bank" jdbcType="VARCHAR" property="openingBank" />
    <result column="large_payment_number" jdbcType="VARCHAR" property="largePaymentNumber" />
    <result column="is_auto_deduction" jdbcType="TINYINT" property="isAutoDeduction" />
    <result column="central_debt_account_number" jdbcType="VARCHAR" property="centralDebtAccountNumber" />
    <result column="foreign_exchange_center_member_code" jdbcType="VARCHAR" property="foreignExchangeCenterMemberCode" />
    <result column="clearing_house_holder_account" jdbcType="VARCHAR" property="clearingHouseHolderAccount" />
    <result column="holder_account_number" jdbcType="VARCHAR" property="holderAccountNumber" />
    <result column="holder_account_full_name" jdbcType="VARCHAR" property="holderAccountFullName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, full_product_name, product_code, valuation_time, is_structured, is_electronic_direct_connection, 
    custodian_bank, secondary_valuation_security_type, account_name, account_number_detail, 
    opening_bank, large_payment_number, is_auto_deduction, central_debt_account_number, 
    foreign_exchange_center_member_code, clearing_house_holder_account, holder_account_number, 
    holder_account_full_name
  </sql>
</mapper>