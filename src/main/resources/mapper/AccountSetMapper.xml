<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.AccountSetMapper">
    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, full_product_name as name
        from account_information
    </select>

    <select id="productCodeList" resultType="java.lang.String">
        select distinct product_code
        from account_information
    </select>

    <select id="accountSetCodeList" resultType="java.lang.String">
        select id
        from account_information
    </select>

    <select id="accountSetNameList" resultType="java.lang.String">
        select full_product_name
        from account_information
    </select>

    <select id="queryAccountSetByIds" resultType="cn.sdata.om.al.entity.mail.MailCommonInfo">
        select t1.id,
               full_product_name,
               product_code,
               valuation_time,
               product_category,
               is_structured                   as structuredIs,
               is_electronic_direct_connection as electronicDirectConnectionIs,
               custodian_bank,
               secondary_valuation_security_type,
               account_name,
               account_number_detail,
               opening_bank,
               large_payment_number,
               is_auto_deduction               as autoDeductionIs,
               central_debt_account_number,
               foreign_exchange_center_member_code,
               clearing_house_holder_account,
               holder_account_number,
               holder_account_full_name,
               net_asset_value_warning_threshold,
               product_status,
               wind_code,
               establishment_date,
               (select id
                from account_set_group
                where find_in_set(t1.id, account_code) > 0
                limit 1)                       as accountSetGroupId
        from account_information t1 where t1.id in
        <foreach collection="list" open="(" separator="," close=")" item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getIdZZByAccount" resultType="java.lang.String">
        select id
        from account_information
        where central_debt_account_number = #{accCode,jdbcType=VARCHAR}
    </select>

    <select id="getIdSQByAccount" resultType="java.lang.String">
        select id
        from account_information
        where clearing_house_holder_account = #{accCode,jdbcType=VARCHAR}
    </select>

    <select id="queryAll" resultType="cn.sdata.om.al.entity.mail.MailCommonInfo">
        select id,
               full_product_name,
               product_code,
               valuation_time,
               custodian_bank,
               secondary_valuation_security_type,
               account_name,
               account_number_detail,
               opening_bank,
               large_payment_number,
               central_debt_account_number,
               foreign_exchange_center_member_code,
               clearing_house_holder_account,
               holder_account_number,
               holder_account_full_name,
               net_asset_value_warning_threshold,
               product_status,
               wind_code,
               establishment_date,
               product_category
        from account_information
    </select>
</mapper>
