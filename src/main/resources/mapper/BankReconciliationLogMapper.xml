<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.BankReconciliationLogMapper">
    <select id="pageLogImport" resultType="cn.sdata.om.al.entity.LogBRImportFileRecord">
        select id,
               execute_time,
               create_by_name,
               execute_type,
               status,
               file_url,
               data_date,
               params,
               result,
               error_msg
        from log_br_import_file_record
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="pageLogDiff" resultType="cn.sdata.om.al.entity.LogBRMarkDiffRecord">
        select id, execute_time, create_by_name, data_date, params, result, error_msg
        from log_br_mark_diff_record
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getDiffDesc" resultType="cn.sdata.om.al.entity.LogBRMarkDiffDesc">
        select id, product_id, product_name, action, remark, security_name, security_code
        from log_br_mark_diff_desc
        where mark_log_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="pageLogExport" resultType="cn.sdata.om.al.entity.LogBRExportFileRecord">
        select id,
               execute_time,
               create_by_name,
               status,
               file_url,
               result,
               error_msg,
               params
        from log_br_export_file_record
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="pageLogSync" resultType="cn.sdata.om.al.entity.LogBRSyncValuationRecord">
        select id,
               begin_time,
               end_time,
               status,
               create_by_name,
               data_date,
               params,
               error_msg,
               result
        from log_br_sync_valuation_record
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="batchSaveMarkDiffDesc">
        insert into log_br_mark_diff_desc(id, product_id, product_name, action, remark, mark_log_id, security_code,
                                          security_name)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.productId}, #{e.productName,jdbcType=VARCHAR}, #{e.action,jdbcType=VARCHAR},
             #{e.remark,jdbcType=VARCHAR},
             #{e.logId,jdbcType=VARCHAR}, #{e.securityCode,jdbcType=VARCHAR}, #{e.securityName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="saveMarkDiffLog">
        insert into log_br_mark_diff_record(id, execute_time, create_by_name, status, data_date)
        values (#{id,jdbcType=VARCHAR}, #{executeTime,jdbcType=VARCHAR},
                #{createByName,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateMarkDiffLogById">
        update log_br_mark_diff_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            result    = #{result,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveUploadLog">
        insert into log_br_import_file_record(id, execute_time, create_by_name, execute_type, status, data_date)
        values (#{id,jdbcType=VARCHAR}, #{executeTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{executeType,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateUploadLogById">
        update log_br_import_file_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            data_date = #{dataDate,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveSyncLog">
        insert into log_br_sync_valuation_record(id, begin_time, end_time, status, create_by_name, params, data_date)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{createByName,jdbcType=VARCHAR}, #{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateSyncLogById">
        update log_br_sync_valuation_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteSyncLog">
        delete
        from log_br_sync_valuation_record
        where id = #{logId,jdbcType=VARCHAR}
    </delete>

    <update id="updateExportLogById">
        update log_br_export_file_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveExportLog">
        insert into log_br_export_file_record(id, execute_time, create_by_name, status, params, data_date)
        values (#{id,jdbcType=VARCHAR}, #{executeTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <select id="getLogBRRPARecordList" resultType="cn.sdata.om.al.entity.LogBRRPARecord">
        select id,
               execute_time beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param,
               file_type
        from log_br_rpa_record
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getBRRpaLogByRpaLogId" resultType="cn.sdata.om.al.entity.LogBRRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param
        from log_br_rpa_record
        where rpa_log_id = #{rpaLogId,jdbcType=VARCHAR}
    </select>

    <insert id="saveRpaLog">
        insert into log_br_rpa_record(id, execute_time, create_by_name, params, status, task_id, task_name, rpa_log_id,
                                      data_date, rpa_param, type)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR},
                #{taskName,jdbcType=VARCHAR},
                #{rpaLogId,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{rpaParam,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR})
    </insert>

    <update id="updateRpaLogById">
        update log_br_rpa_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            file_type = #{fileType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getBRRpaLogById" resultType="cn.sdata.om.al.entity.LogBRRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param
        from log_br_rpa_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
</mapper>
