<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.BankReconciliationMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.BankReconciliation">
        select t1.id,
               product_id           as accountSetCode,
               account_set_group_id,
               data_date,
               settlement_location,
               security_code,
               security_name,
               valuation_position_quantity,
               settlement_company_position_quantity,
               difference,
               difference_reasons,
               t1.update_by,
               t1.update_time,
               t3.name              as updateByName,
               t2.full_product_name as accountSetName,
               t2.product_code,
               t1.source_code,
               t1.product_category,
               t1.valuation_time
        from bank_reconciliation_info t1
                 left join account_information t2 on t1.product_id = t2.id
                 left join user t3 on t1.update_by = t3.id
        <where>
            <if test="accountSetNames != null and accountSetNames.size() != 0">
                and t2.full_product_name in
                <foreach collection="accountSetNames" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="securityNames != null and securityNames.size() != 0">
                and security_name in
                <foreach collection="securityNames" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="securityCodes != null and securityCodes.size() != 0">
                and security_code in
                <foreach collection="securityCodes" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="settlementLocations != null and settlementLocations.size() != 0">
                and settlement_location in
                <foreach collection="settlementLocations" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="accountSetIds != null and accountSetIds.size() != 0">
                and product_id in
                <foreach collection="accountSetIds" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="sourceCodes != null and sourceCodes.size() != 0">
                and source_code in
                <foreach collection="sourceCodes" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="accountSetGroupIds != null and accountSetGroupIds.size() != 0">
                and (select count(id)
                     from account_set_group where find_in_set(t1.product_id, account_code) > 0
                                              and id in
                <foreach collection="accountSetGroupIds" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>) > 0
            </if>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= str_to_date(#{beginDataDate,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= str_to_date(#{endDataDate,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="securityCode != null and securityCode != ''">
                and security_code like concat('%', #{securityCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productCode != null and productCode != ''">
                and product_code like concat('%', #{productCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="securityName != null and securityName != ''">
                and security_name like concat('%', #{securityName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="accountSetName != null and accountSetName != ''">
                and t2.full_product_name like concat('%', #{accountSetName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="settlementLocation != null and settlementLocation != ''">
                <choose>
                    <when test="settlementLocation == 1">
                        and settlement_location = '上清'
                    </when>
                    <otherwise>
                        and settlement_location = '中债'
                    </otherwise>
                </choose>
            </if>
            <if test="differenceStatus != null and differenceStatus != ''">
                <choose>
                    <when test="differenceStatus == 1">
                        and ifnull(valuation_position_quantity, '0') -
                            ifnull(settlement_company_position_quantity, '0') != 0
                    </when>
                    <otherwise>
                        and ifnull(valuation_position_quantity, '0') -
                            ifnull(settlement_company_position_quantity, '0') = 0
                    </otherwise>
                </choose>
            </if>
            <if test="accountSetCode != null and accountSetCode != ''">
                and product_id like concat('%', #{accountSetCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="productCodes != null and productCodes.size() != 0">
                and t2.product_code in
                <foreach collection="productCodes" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCategory != null">
                and t1.product_category = #{productCategory,jdbcType=INTEGER}
            </if>
            <if test="valuationTime != null and valuationTime != ''">
                and t1.valuation_time = #{valuationTime,jdbcType=VARCHAR}
            </if>
            and INSTR(t2.full_product_name, 'I9') &lt;= 0
            and (cast(ifnull(valuation_position_quantity, 0) as signed) != 0
                or cast(ifnull(settlement_company_position_quantity, 0) as signed) != 0)
        </where>
        order by data_date desc, account_set_group_id, product_id, source_code, security_name
    </select>

    <select id="settlementLocationList" resultType="java.lang.String">
        select distinct settlement_location
        from bank_reconciliation_info
    </select>

    <select id="securityNameList" resultType="java.lang.String">
        select distinct security_name
        from bank_reconciliation_info
    </select>

    <select id="securitiesCodeList" resultType="java.lang.String">
        select distinct security_code
        from bank_reconciliation_info
    </select>

    <update id="markDifference">
        update bank_reconciliation_info
        set difference_reasons = #{differenceReasons,jdbcType=VARCHAR},
            update_by          = #{userId,jdbcType=VARCHAR},
            update_time        = now()
        where id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="queryValuationTableByZQDM" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from hsfa.TACCOUNTZQJC where VC_ZQDM = #{code,jdbcType=VARCHAR}
                                 and d_date = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY-MM-DD')
                                 and l_sclb = '3'
        <if test="accountSetId != null and accountSetId != ''">
            and L_ZTBH = #{accountSetId,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="saveBatch">
        insert into bank_reconciliation_info(id, product_id, account_set_group_id, data_date, settlement_location,
                                             security_code, security_name, valuation_position_quantity,
                                             settlement_company_position_quantity, difference, difference_reasons,
                                             account_number, source_code, product_category, valuation_time)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.accountSetCode,jdbcType=VARCHAR}, #{e.accountSetGroupId,jdbcType=VARCHAR},
             #{e.dataDate,jdbcType=VARCHAR},
             #{e.settlementLocation,jdbcType=VARCHAR}, #{e.securityCode,jdbcType=VARCHAR},
             #{e.securityName,jdbcType=VARCHAR},
             #{e.valuationPositionQuantity,jdbcType=VARCHAR}, #{e.settlementCompanyPositionQuantity,jdbcType=VARCHAR},
             #{e.difference,jdbcType=VARCHAR},
             #{e.differenceReasons,jdbcType=VARCHAR}, #{e.accountNumber,jdbcType=VARCHAR},
             #{e.sourceCode,jdbcType=VARCHAR}, #{e.productCategory,jdbcType=INTEGER},
             #{e.valuationTime,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="queryValuationTableByZQDMs" resultType="com.alibaba.fastjson2.JSONObject">
        select L_ZTBH, L_ZQCC, TO_CHAR(D_DATE, 'YYYY-MM-DD') as D_DATE, VC_ZQDM
        from hsfa.TACCOUNTZQJC
        where VC_ZQDM = #{param.securityCode}
          and d_date = TO_DATE(#{param.dataDate}, 'YYYY-MM-DD')
          and l_sclb = '3'
    </select>

    <update id="syncBankReconciliation">
        update bank_reconciliation_info
        set valuation_position_quantity = #{q.valuationPositionQuantity, jdbcType=VARCHAR},
            difference                  = ifnull(#{q.valuationPositionQuantity,jdbcType=VARCHAR} -
                                                 ifnull(settlement_company_position_quantity, '0.0'), '0.0'),
            difference                  = if(difference = '0', '0.0', difference)
        where id = #{q.id, jdbcType=VARCHAR}
    </update>

    <select id="checkData" resultType="int">
        select count(id)
        from bank_reconciliation_info
        where data_date = #{date,jdbcType=VARCHAR}
          and security_name = #{securityName,jdbcType=VARCHAR}
          and product_id = #{accountSetId,jdbcType=VARCHAR}
    </select>

    <select id="checkCode" resultType="java.lang.String">
        select VC_ZQDM
        from hsfa.TZQXX
        where VC_TRADEZQDM = #{code,jdbcType=VARCHAR}
    </select>

    <select id="selectBankReconciliationInfoByDate" resultType="cn.sdata.om.al.entity.BankReconciliation">
        select id,
               product_id as accountSetCode,
               account_set_group_id,
               data_date,
               settlement_location,
               security_code,
               security_name,
               valuation_position_quantity,
               settlement_company_position_quantity,
               difference,
               difference_reasons,
               update_by,
               update_time,
               create_by,
               create_time,
               deleted,
               account_number,
               source_code
        from bank_reconciliation_info
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryValuationTableByConditions" resultType="com.alibaba.fastjson2.JSONObject">
        select L_ZTBH, L_ZQCC, TO_CHAR(D_DATE, 'YYYY-MM-DD') as D_DATE, VC_ZQDM
        from hsfa.TACCOUNTZQJC
        where VC_ZQDM = #{securityCode,jdbcType=VARCHAR}
          and d_date = TO_DATE(#{dataDate,jdbcType=VARCHAR}, 'YYYY-MM-DD')
          and L_ZTBH = #{accountSetCode,jdbcType=VARCHAR}
          and l_sclb = '3'
    </select>

    <select id="sourceCodeList" resultType="java.lang.String">
        select distinct source_code
        from bank_reconciliation_info
    </select>

    <insert id="saveBankReconciliationFile">
        insert into bank_reconciliation_file(id, file_name, file_path, type, data_date)
        values (#{id,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR},
                #{dataDate,jdbcType=VARCHAR})
    </insert>

    <select id="getBankReconciliationFiles" resultType="cn.sdata.om.al.entity.BankReconciliationFile">
        select id, file_name, file_path, type, data_date
        from bank_reconciliation_file
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryValuationTableByDate" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT *
        FROM (select L_ZTBH, VC_ZQDM, SUM(L_ZQCC) as L_ZQCC, TO_CHAR(D_DATE, 'YYYY-MM-DD') as D_DATE, L_ZQNM
              FROM hsfa.TACCOUNTZQJC
              WHERE D_DATE = TO_DATE(#{testDate,jdbcType=VARCHAR}, 'YYYY-MM-DD')
              GROUP BY L_ZQNM, VC_ZQDM, L_ZTBH, D_DATE) t1
                 INNER JOIN
             (select vc_zqjc, vc_zqdm, L_ZQNM, vc_tradezqdm from hsfa.tzqxx where l_sclb = 3 and l_zqlb = 2) t2
             ON t1.L_ZQNM = t2.L_ZQNM
    </select>

    <select id="selectValuationSettlementLocation" resultType="com.alibaba.fastjson2.JSONObject">
        select vc_zqjc, vc_zqdm, decode(l_jsjg, 1, '中债', 2, '上清') vc_location, vc_tradezqdm
        from hsfa.tzqxx
        where l_sclb = 3
          and l_zqlb = 2
          and vc_zqdm in
        <foreach collection="list" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectAlreadyExistInfo" resultType="cn.sdata.om.al.entity.BankReconciliation">
        select id,
               product_id as accountSetCode,
               data_date,
               settlement_location,
               security_code,
               security_name
        from bank_reconciliation_info
        where data_date = #{date,jdbcType=VARCHAR}
    </select>

    <update id="syncExistBankReconciliation">
        update bank_reconciliation_info
        set settlement_company_position_quantity = #{settlementCompanyPositionQuantity,jdbcType=VARCHAR},
            difference                           = ifnull(ifnull(valuation_position_quantity, '0.0') -
                                                          ifnull(settlement_company_position_quantity, '0.0'), '0.0'),
            difference                           = if(difference = '0', '0.0', difference)
        where settlement_location = #{settlementLocation,jdbcType=VARCHAR}
          and security_name = #{securityName,jdbcType=VARCHAR}
          and data_date = #{dataDate,jdbcType=VARCHAR}
          and product_id = #{accountSetCode,jdbcType=VARCHAR}
    </update>

    <select id="selectValuationSettlementLocationBySourceCode" resultType="com.alibaba.fastjson2.JSONObject">
        select vc_zqjc, vc_zqdm, decode(l_jsjg, 1, '中债', 2, '上清') vc_location, vc_tradezqdm
        from hsfa.tzqxx
        where l_sclb = 3
          and l_zqlb = 2
          and vc_tradezqdm in
        <foreach collection="list" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <delete id="deleteByDate">
        delete
        from bank_reconciliation_info where settlement_location = #{location,jdbcType=VARCHAR}
                                        and data_date in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateData">
        update bank_reconciliation_info
        set valuation_position_quantity = #{valuationPositionQuantity,jdbcType=VARCHAR},
            difference                  = #{difference,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateSettlementQuantity">
        update bank_reconciliation_info
        set settlement_company_position_quantity = #{settlementCompanyPositionQuantity,jdbcType=VARCHAR}
        where data_date = #{dataDate,jdbcType=VARCHAR}
          and security_name = #{securityName,jdbcType=VARCHAR}
          and product_id = #{accountSetCode,jdbcType=VARCHAR}
          and settlement_location = #{settlementLocation,jdbcType=VARCHAR}
    </update>

    <select id="queryValuationTableByDateScope" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT *
        FROM (select L_ZTBH, VC_ZQDM, SUM(L_ZQCC) as L_ZQCC, TO_CHAR(D_DATE, 'YYYY-MM-DD') as D_DATE, L_ZQNM
              FROM hsfa.TACCOUNTZQJC
              WHERE (D_DATE &gt;= TO_DATE(#{beginDate,jdbcType=VARCHAR}, 'YYYY-MM-DD') and
                     D_DATE &lt;= TO_DATE(#{endDate,jdbcType=VARCHAR}, 'YYYY-MM-DD'))
              GROUP BY L_ZQNM, VC_ZQDM, L_ZTBH, D_DATE) t1
                 INNER JOIN
             (select vc_zqjc, vc_zqdm, L_ZQNM, vc_tradezqdm, decode(l_jsjg, 1, '中债', 2, '上清') vc_location
              from hsfa.tzqxx
              where l_sclb = 3
                and l_zqlb = 2) t2
             ON t1.L_ZQNM = t2.L_ZQNM
    </select>

    <select id="selectSyncTimeByUserId" resultType="com.alibaba.fastjson2.JSONObject">
        select max(sync_time) as bankReconciliation, max(settlement_sync_time) as settlementCompany
        from bank_reconciliation_sync_time
    </select>

    <update id="updateSyncTime">
        update bank_reconciliation_sync_time
        set sync_time = #{syncTime,jdbcType=VARCHAR}
        where data_date = #{endDate,jdbcType=VARCHAR}
    </update>

    <insert id="insertSyncTime">
        insert into bank_reconciliation_sync_time(data_date, sync_time, settlement_sync_time)
        values (#{endDate,jdbcType=VARCHAR}, #{syncTime,jdbcType=VARCHAR}, '')
    </insert>

    <update id="updateSettlementSyncTime">
        update bank_reconciliation_sync_time
        set settlement_sync_time = #{syncTime,jdbcType=VARCHAR}
        where data_date = #{endDate,jdbcType=VARCHAR}
    </update>

    <insert id="insertSettlementSyncTime">
        insert into bank_reconciliation_sync_time(data_date, sync_time, settlement_sync_time)
        values (#{endDate,jdbcType=VARCHAR}, '', #{syncTime,jdbcType=VARCHAR})
    </insert>

    <update id="updateAllSyncTime">
        update bank_reconciliation_sync_time
        set sync_time            = #{syncTime,jdbcType=VARCHAR},
            settlement_sync_time = #{syncTime,jdbcType=VARCHAR}
        where data_date = #{endDate,jdbcType=VARCHAR}
    </update>

    <insert id="insertAllSyncTime">
        insert into bank_reconciliation_sync_time(data_date, sync_time, settlement_sync_time)
        values (#{endDate,jdbcType=VARCHAR}, #{syncTime,jdbcType=VARCHAR}, #{syncTime,jdbcType=VARCHAR})
    </insert>

    <update id="updateQuantityToZero">
        update bank_reconciliation_info
        set valuation_position_quantity = '0'
        where data_date = #{dataDate,jdbcType=VARCHAR}
          and security_name = #{securityName,jdbcType=VARCHAR}
          and product_id = #{accountSetCode,jdbcType=VARCHAR}
    </update>

    <select id="selectAlreadyExistInfoByLocation" resultType="cn.sdata.om.al.entity.BankReconciliation">
        select id,
               product_id as accountSetCode,
               data_date,
               settlement_location,
               security_code,
               security_name
        from bank_reconciliation_info
        where data_date = #{date,jdbcType=VARCHAR}
          and settlement_location = #{location,jdbcType=VARCHAR}
    </select>

    <select id="selectAlreadyExistInfoByLocationAndAccountNumber" resultType="cn.sdata.om.al.entity.BankReconciliation">
        select id,
               product_id as accountSetCode,
               data_date,
               settlement_location,
               security_code,
               security_name
        from bank_reconciliation_info
        <where>
            and account_number = #{accountNumber,jdbcType=VARCHAR}
            and data_date = #{date,jdbcType=VARCHAR}
            and settlement_location = #{location,jdbcType=VARCHAR}
        </where>
    </select>

    <select id="queryValuationTableByConditionsAndSourceCode" resultType="com.alibaba.fastjson2.JSONObject">
        select t1.L_ZTBH, t1.L_ZQCC, TO_CHAR(t1.D_DATE, 'YYYY-MM-DD') as D_DATE, t1.VC_ZQDM
        from hsfa.TACCOUNTZQJC t1
                 INNER JOIN hsfa.tzqxx t2 ON t1.L_ZQNM = t2.L_ZQNM
        where t2.vc_tradezqdm = #{sourceCode,jdbcType=VARCHAR}
          and t1.d_date = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY-MM-DD')
          and t1.L_ZTBH = #{accountSetCode,jdbcType=VARCHAR}
          and t1.l_sclb = '3'
    </select>

    <select id="selectSyncTimeByEndDate" resultType="com.alibaba.fastjson2.JSONObject">
        select sync_time as bankReconciliation, settlement_sync_time as settlementCompany
        from bank_reconciliation_sync_time where data_date = #{endDate,jdbcType=VARCHAR}
    </select>

    <select id="selectAllSyncTime" resultType="com.alibaba.fastjson2.JSONObject">
        select data_date, sync_time, settlement_sync_time
        from bank_reconciliation_sync_time
        order by data_date
    </select>

    <select id="getProductByIds" resultType="com.alibaba.fastjson2.JSONObject">
        select product_id                                                                as productId,
               (select full_product_name from account_information where id = product_id) as productName,
               source_code                                                               as sourceCode,
               security_name                                                             as securityName
        from bank_reconciliation_info where id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectDataDateByIds" resultType="com.alibaba.fastjson2.JSONObject">
        select id, data_date as dataDate
        from bank_reconciliation_info where id in
        <foreach collection="list" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
