<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.BaseCronLogMapper">
    <resultMap id="BaseResultMap" type="cn.sdata.om.al.qrtz.entity.BaseCronLog">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="TASK_ID" jdbcType="VARCHAR" property="taskId"/>
        <result column="START_DATE_TIME" jdbcType="VARCHAR" property="startDateTime"/>
        <result column="END_DATE_TIME" jdbcType="VARCHAR" property="endDateTime"/>
        <result column="DATA_DATE" jdbcType="VARCHAR" property="dataDate"/>
        <result column="EXECUTOR" jdbcType="VARCHAR" property="executor"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="TASK_INFO" jdbcType="CLOB" property="taskInfo"/>
        <result column="EXEC_ID" jdbcType="VARCHAR" property="execId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        TASK_ID,
        START_DATE_TIME,
        END_DATE_TIME,
        DATA_DATE,
        EXECUTOR,
        "STATUS",
        TASK_INFO,
        EXEC_ID
    </sql>
    <update id="liivSendMailUpdateStatus">
        update base_cron_log
        set status        = #{status},
            end_date_time = #{endDateTime},
            data_date= #{dataDate}
        where id = #{id}
    </update>

    <select id="queryGroupByTask" resultMap="BaseResultMap">
        SELECT ID,
               TASK_ID,
               START_DATE_TIME,
               END_DATE_TIME,
               DATA_DATE,
               EXECUTOR,
               STATUS,
               TASK_INFO
        FROM (SELECT ID,
                     TASK_ID,
                     START_DATE_TIME,
                     END_DATE_TIME,
                     DATA_DATE,
                     EXECUTOR,
                     STATUS,
                     TASK_INFO,
                     ROW_NUMBER() OVER (PARTITION BY TASK_ID ORDER BY START_DATE_TIME DESC) AS row_num
              FROM base_cron_log) t
        WHERE t.row_num = 1
    </select>

    <select id="getLastExecute" resultType="java.lang.String">
        select max(id)
        from base_cron_log
        where task_id = #{taskId}
    </select>

    <select id="getLatestLog" resultType="cn.sdata.om.al.qrtz.entity.BaseCronLog">
        SELECT ID,
               TASK_ID,
               START_DATE_TIME,
               END_DATE_TIME,
               DATA_DATE,
               EXECUTOR,
               STATUS,
               TASK_INFO,
               RPA_STATUS,
               EXEC_ID
        FROM (SELECT *,
                     ROW_NUMBER() OVER (PARTITION BY TASK_ID ORDER BY START_DATE_TIME DESC) AS row_num
              FROM base_cron_log
              where task_id = #{taskId}
                and data_date = #{dataDate}) t
        WHERE t.row_num = 1
    </select>
</mapper>
