<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.CashClearReportLogMapper">

    <select id="getLogCCRRPARecordList" resultType="cn.sdata.om.al.entity.LogCCRRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               status,
               file_url,
               data_date,
               params,
               error_msg,
               result,
               task_id,
               task_name,
               type
        from log_ccr_rpa_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLogCCRExportRecordList" resultType="cn.sdata.om.al.entity.LogCCRExportRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               status,
               file_url,
               data_date,
               params,
               error_msg,
               result
        from log_ccr_export_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLogCCRSendMailRecordList" resultType="cn.sdata.om.al.entity.LogCCRSendMailRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               status,
               task_id,
               task_name,
               type,
               mail_id      as mailIdStr,
               data_date,
               error_msg,
               params,
               result,
               send_status
        from log_ccr_send_mail_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="saveRpaLog">
        insert into log_ccr_rpa_record(id, execute_time, create_by_name, params, status, task_id, task_name, rpa_log_id, data_date, rpa_param, type)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},
        #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},#{taskId,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR},
                #{rpaLogId,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{rpaParam,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR})
    </insert>

    <select id="getCCRRpaLogByRpaLogId" resultType="cn.sdata.om.al.entity.LogCCRRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id
        from log_ccr_rpa_record
        where rpa_log_id = #{rpaLogId,jdbcType=VARCHAR}
    </select>

    <update id="updateRpaLogById">
        update log_ccr_rpa_record set
            end_time = #{endTime,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            file_url = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveExportLog">
        insert into log_ccr_export_record(id, execute_time, create_by_name, params, status, data_date)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},
        #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateExportLogById">
        update log_ccr_export_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveSendMailLog">
        insert into log_ccr_send_mail_record(id, execute_time, create_by_name, params, status, task_id, task_name, type, rpa_log_id, data_date
        , end_time, send_status, mail_id)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},
        #{params,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{taskId,jdbcType=VARCHAR},#{taskName,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR}, #{rpaLogId,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{sendStatus,jdbcType=VARCHAR},
                #{mailIdStr,jdbcType=VARCHAR})
    </insert>

    <select id="getCCRSendMailLogByLogId" resultType="cn.sdata.om.al.entity.LogCCRSendMailRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               task_id,
               task_name,
               type,
               mail_id as mailIdStr,
               rpa_log_id
        from log_ccr_send_mail_record
        where rpa_log_id = #{rpaLogId,jdbcType=VARCHAR}
    </select>

    <update id="updateSendMailLogById">
        update log_ccr_send_mail_record set end_time = #{endTime,jdbcType=VARCHAR},
          mail_id = #{mailIdStr,jdbcType=VARCHAR},
          status = #{status,jdbcType=VARCHAR},
          error_msg = #{errorMsg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getCCRRpaLogById" resultType="cn.sdata.om.al.entity.LogCCRRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param
        from log_ccr_rpa_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
</mapper>
