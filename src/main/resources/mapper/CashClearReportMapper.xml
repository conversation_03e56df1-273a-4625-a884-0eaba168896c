<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.cashClearReport.CashClearReportMapper">

    <select id="getRemoteFilesByDateAndJobName" resultType="cn.sdata.om.al.entity.RemoteFileInfo">
        with baseinfo as
                 (select bcl.*,
                         ROW_NUMBER() OVER(PARTITION BY bcl.task_id, bcl.data_date ORDER BY bcl.end_date_time DESC) AS row_num
                  from base_cron_log bcl
                  WHERE bcl.task_id = (select bc.job_id
                                       from base_cron bc
                                       where bc.job_name = #{jobName})
                    and bcl.data_date = #{dataDateStr}
                    and bcl.rpa_status = 1
                    and bcl.status = 1
                    and bcl.end_date_time is not null)

        select rfi.*
        from remote_file_info rfi
        where rfi.log_Id in (select t.id from baseinfo t WHERE t.row_num = 1)
    </select>

</mapper>