<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.CronMapper">

    <resultMap id="result" type="cn.sdata.om.al.qrtz.entity.BaseCronLog">
        <result column="group_id" property="groupId"/>
        <result column="group_name" property="groupName"/>
        <result column="job_id" property="jobId"/>
        <result column="seq" property="seq"/>
    </resultMap>

    <select id="getRelation" resultType="cn.sdata.om.al.qrtz.entity.CronGroupRelation">
        select a.group_id, b.group_name, a.job_id, a.seq
        from base_cron_group_relation a
                 inner join base_cron_group b on a.group_id = b.group_id order by a.seq
    </select>

    <select id="listAll" resultType="cn.sdata.om.al.qrtz.entity.Cron">
        select *
        from base_cron
    </select>

    <update id="restoreForce">
        update base_cron set is_deleted = 0 where job_id = #{jobId}
    </update>
</mapper>