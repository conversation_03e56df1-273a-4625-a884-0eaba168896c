<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.CustodianBankContactsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.CustodianBankContacts">
    <!--@mbg.generated-->
    <!--@Table custodian_bank_contacts-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="custodian_bank" jdbcType="VARCHAR" property="custodianBank" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="custodian_role" jdbcType="VARCHAR" property="custodianRole" />
    <result column="recipient" jdbcType="LONGVARCHAR" property="recipient" />
    <result column="recipient_cc" jdbcType="LONGVARCHAR" property="recipientCc" />
    <result column="phone" jdbcType="LONGVARCHAR" property="phone" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, custodian_bank, product_id, custodian_role, recipient, recipient_cc, phone
  </sql>

  <select id="pageResult" resultType="cn.sdata.om.al.vo.CustodianBankContactsVO">
      select * from (SELECT
      a.id AS id,
      max(b.custodian_bank) AS custodian_bank,
      GROUP_CONCAT(DISTINCT b.product_id SEPARATOR ',') AS product_id,
      GROUP_CONCAT(DISTINCT c.full_product_name SEPARATOR ',') AS product_name,
      max(custodian_role) AS custodian_role,
      max(recipient) AS recipient,
      max(recipient_cc) AS recipient_cc,
      max(phone) AS phone
      FROM
      custodian_bank_contacts_view a
      INNER JOIN custodian_bank_contacts b ON a.sub_id = b.id
      INNER JOIN account_information c ON b.product_id = c.id
      GROUP BY
      a.id) t
      <where>
          <if test="bankNames != null and bankNames.size() != 0">
              and custodian_bank in (<foreach collection="bankNames" item="bankName" separator=",">
              #{bankName}
          </foreach>)
          </if>
          <if test="products != null and products.size() != 0">
              and
              (<foreach collection="products" item="product" separator="OR">
              FIND_IN_SET(#{product}, product_id) > 0
          </foreach>)
          </if>
          <if test="custodianRoles != null and custodianRoles.size() != 0">
              and
              (<foreach collection="custodianRoles" item="custodianRole" separator="OR">
              FIND_IN_SET(#{custodianRole}, custodian_role) > 0
          </foreach>)
          </if>
      </where>
    </select>

  <select id="listProduct" resultType="cn.sdata.om.al.entity.CommonEntity">
      select distinct cbc.product_id as id, ai.account_name as name
      from custodian_bank_contacts cbc inner join account_information ai on cbc.product_id = ai.id order by cbc.product_id
    </select>

  <select id="getRecipientInfo" resultType="cn.sdata.om.al.entity.CustodianBankContacts">
      select cbcv.id                                         as id,
             max(recipient)                                  as recipient,
             max(recipient_cc)                               as recipient_cc,
             group_concat(distinct product_id separator ',') as product_id
      from custodian_bank_contacts_view cbcv
               inner join custodian_bank_contacts cbc on cbcv.sub_id = cbc.id
      where custodian_role = #{role}
      <if test="ids != null and ids.size() != 0">
          and cbcv.id in
          <foreach open="(" collection="ids" separator="," close=")" item="id">
              #{id}
          </foreach>
      </if>
      group by cbcv.id
    </select>

  <select id="listResult" resultType="cn.sdata.om.al.vo.CustodianBankContactsVO">
      select * from (SELECT
                         a.id AS id,
                         max(b.custodian_bank) AS custodian_bank,
                         GROUP_CONCAT(DISTINCT b.product_id SEPARATOR ',') AS product_id,
                         GROUP_CONCAT(DISTINCT c.full_product_name SEPARATOR ',') AS product_name,
                         max(custodian_role) AS custodian_role,
                         max(recipient) AS recipient,
                         max(recipient_cc) AS recipient_cc,
                         max(phone) AS phone
                     FROM
                         custodian_bank_contacts_view a
                             INNER JOIN custodian_bank_contacts b ON a.sub_id = b.id
                             INNER JOIN account_information c ON b.product_id = c.id
                     GROUP BY
      a.id) t
      <if test="custodianRole != null and custodianRole != ''">
          where FIND_IN_SET(#{custodianRole}, custodian_role) > 0
      </if>
    </select>
</mapper>