<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.CustodianBankContactsViewMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.CustodianBankContactsView">
    <!--@mbg.generated-->
    <!--@Table custodian_bank_contacts_view-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <id column="sub_id" jdbcType="VARCHAR" property="subId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sub_id
  </sql>
</mapper>