<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.dividendDetail.DividendDetailMapper">

    <select id="getD2F2MList_v1" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        with baseInfo as
                 (select d.*,
                         f.id as file_id,
                         f.file_name,
                         f.file_update_time,
                         m.mail_id,
                         m.mail_send_time,
                         m.mail_send_status,
                         m.mail_type
                  from dividend_info_detail d
                   left join dividend_info_detail_file f
                        on d.id = f.detail_id
                   left join dividend_info_detail_file_mail m
                        on f.detail_id = m.detail_id
                        and f.id = m.detail_file_id
                  where d.data_date between #{startDate}  and #{endDate}
                    and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
                    <if test="productNames != null and productNames.size() != 0">
                        and d.product_name in
                            <foreach collection="productNames"  item="productName" separator="," open="(" close=")">
                                #{productName,jdbcType=VARCHAR}
                            </foreach>
                    </if>
                ),

             baseInfo1 as
                 (select b.*,
                         row_number() over(PARTITION BY b.product_id, b.data_date ORDER BY b.file_update_time DESC) AS rn
                  from baseInfo b)

        select t.id,
               t.product_id,
               t.product_code,
               t.product_name,
               t.data_date,
               t.file_id,
               t.file_name,
               t.file_update_time,
               t.mail_id,
               t.mail_send_time,
               t.mail_send_status
        from baseInfo1 t
        where t.rn = 1
    </select>

    <select id="getD2F2MList" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        with baseInfo as
             (select d.*,
                     f.id as file_id,
                     f.file_name,
                     f.file_update_time,
                     m.mail_id,
                     m.mail_send_time,
                     m.mail_send_status,
                     m.mail_type
              from dividend_info_detail d
              left join account_information ai
                on d.product_id = ai.id
              left join dividend_info_detail_file f
                on d.id = f.detail_id
              left join dividend_info_detail_file_mail m
                 on f.detail_id = m.detail_id
                and f.id = m.detail_file_id
              where d.data_date between #{startDate} and  #{endDate}
                and ai.is_currency != 1
                and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
                <if test="productNames != null and productNames.size() != 0">
                    and d.product_name in
                    <foreach collection="productNames"  item="productName" separator="," open="(" close=")">
                        #{productName,jdbcType=VARCHAR}
                    </foreach>
                </if>
             ),

         baseInfo1 as
             (select b.*,
                     row_number() over(PARTITION BY b.product_id, b.data_date ORDER BY b.file_update_time DESC) AS rn
              from baseInfo b),

         baseInfo2 as
             (select c.*,
                     row_number() over(PARTITION BY c.product_id, c.data_date ORDER BY c.file_update_time DESC, c.mail_send_time DESC, c.mail_send_status) AS rn
              from baseInfo c)

        select t1.id,
               t1.product_id,
               t1.product_code,
               t1.product_name,
               t1.data_date,
               t1.file_id,
               t1.file_name,
               t1.file_update_time,
               t2.mail_id,
               t2.mail_send_time,
               t2.mail_send_status
        from baseInfo1 t1
         left join baseInfo2 t2
           on t1.product_id = t2.product_id
            and t1.data_date = t2.data_date
        where t1.rn = 1
          and t2.rn = 1
    </select>

    <select id="getSendMails_v0" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        select f.file_update_time, m.mail_send_time, m.mail_send_status, m.mail_id
        from dividend_info_detail d
         left join dividend_info_detail_file f
           on d.id = f.detail_id
         left join dividend_info_detail_file_mail m
           on f.detail_id = m.detail_id
            and f.id = m.detail_file_id
        where d.id = #{detailId}
          and d.data_date = #{dataDate}
          and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
        order by f.file_update_time
    </select>
    <select id="getSendMails_v1" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        with baseInfo as
             (select f.file_update_time,
                     m.detail_id,
                     m.detail_file_id,
                     m.mail_send_time,
                     m.mail_send_status,
                     m.mail_id,
                     m.mail_type
              from dividend_info_detail d
               left join dividend_info_detail_file f
                on d.id = f.detail_id
               left join dividend_info_detail_file_mail m
                 on f.detail_id = m.detail_id
                and f.id = m.detail_file_id
              where d.id = #{detailId}
                and d.data_date = #{dataDate}
                and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
              order by f.file_update_time desc, m.mail_send_status),

         baseInfo1 as
             (select b.*,
                     row_number() over(PARTITION BY b.detail_id, b.detail_file_id ORDER BY b.file_update_time DESC, b.mail_send_time DESC, b.mail_send_status) AS rn
              from baseInfo b)

        select t.file_update_time, t.mail_send_time, t.mail_send_status, t.mail_id, t.mail_type
        from baseInfo1 t
        order by t.file_update_time DESC,  t.mail_send_time DESC
    </select>

    <select id="getSendMails" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        select f.file_update_time,
               m.detail_id,
               m.detail_file_id,
               m.mail_send_time,
               case
                   when m.mail_send_status is null then
                       'UNSENT'
                   else
                       m.mail_send_status
               end as mail_send_status,
               m.mail_id,
               m.mail_type
        from dividend_info_detail d
         left join dividend_info_detail_file f
           on d.id = f.detail_id
         left join dividend_info_detail_file_mail m
           on f.detail_id = m.detail_id
           and f.id = m.detail_file_id
        where d.id = #{detailId}
          and d.data_date = #{dataDate}
          and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
        order by f.file_update_time desc,m.mail_send_time desc, m.mail_send_status
    </select>
    <select id="getD2F2MListV1" resultType="cn.sdata.om.al.entity.dividendDetail.DividendDetailEntity">
        with baseInfo as
            (select d.*,
                    f.id as file_id,
                    f.file_name,
                    f.file_update_time,
                    m.mail_id,
                    m.mail_send_time,
                    m.mail_send_status,
                    m.mail_type
             from dividend_info_detail d
              left join account_information ai
                on d.product_id = ai.id
              left join dividend_info_detail_file f
                on d.id = f.detail_id
              left join dividend_info_detail_file_mail m
                on f.detail_id = m.detail_id
                and f.id = m.detail_file_id
             where d.data_date between #{startDate} and #{endDate}
                and ai.is_currency != 1
                and ((f.init_status = 1 and f.select_status = 1) OR (f.init_status = 0 and f.select_status = 0))
                <if test="productNames != null and productNames.size() != 0">
                    and d.product_name in
                    <foreach collection="productNames"  item="productName" separator="," open="(" close=")">
                        #{productName,jdbcType=VARCHAR}
                    </foreach>
                </if>
            ),

            baseInfo1 as
            (select b.*,
                    row_number() over(PARTITION BY b.product_id, b.data_date ORDER BY b.file_update_time DESC) AS rn
                from baseInfo b),

            baseInfo2 as
            (select c.*,
                    row_number() over(PARTITION BY c.product_id, c.data_date ORDER BY c.file_update_time DESC, c.mail_send_time DESC, c.mail_send_status) AS rn
                from baseInfo c
                where c.mail_type = 'custodianBank'),

            baseInfo3 as
            (select d.*,
                    row_number() over(PARTITION BY d.product_id, d.data_date ORDER BY d.file_update_time DESC, d.mail_send_time DESC, d.mail_send_status) AS rn
                from baseInfo d
                where d.mail_type = 'investor')

        select t1.id,
               t1.product_id,
               t1.product_code,
               t1.product_name,
               t1.data_date,
               t1.file_id,
               t1.file_name,
               t1.file_update_time,
               t2.mail_id          as "mailBankId",
               t2.mail_send_time   as "mailBankSendTime",
               t2.mail_send_status as "mailBankSendStatus",
               t3.mail_id          as "mailInvestorId",
               t3.mail_send_time   as "mailInvestorSendTime",
               t3.mail_send_status as "mailInvestorSendStatus"
        from (select * from baseInfo1 ib1 where ib1.rn = 1) t1
         left join (select * from baseInfo2 ib2 where ib2.rn = 1) t2
           on t1.product_id = t2.product_id
           and t1.data_date = t2.data_date
           and t1.file_id = t2.file_id
         left join (select * from baseInfo3 ib3 where ib3.rn = 1) t3
           on t1.product_id = t3.product_id
           and t1.data_date = t3.data_date
           and t1.file_id = t3.file_id
    </select>
</mapper>