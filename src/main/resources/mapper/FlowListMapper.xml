<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.FlowListMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.FlowList">
    <!--@mbg.generated-->
    <!--@Table om_flow_list-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, show_name
  </sql>
</mapper>