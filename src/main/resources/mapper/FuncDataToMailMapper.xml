<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.FuncDataToMailMapper">
    <select id="getLatestMailByDataDateAndFuncType" resultType="cn.sdata.om.al.entity.FuncDataToMailEntity">
        SELECT t.id,
               t.mail_id,
               t.mail_send_log_id,
               t.data_date,
               t.mail_status,
               t.func_type,
               t.data_type,
               t.create_time,
               t.product_id
        FROM (SELECT *,
                     ROW_NUMBER() OVER (PARTITION BY data_date, data_type ORDER BY create_time DESC) AS row_num
              FROM func_data_mail
              where data_date = #{dataDate}
                and func_type = #{funcType}) t
        WHERE t.row_num = 1
    </select>

    <select id="getCashClearReportMailList" resultType="cn.sdata.om.al.entity.FuncDataToMailEntity">
        select t.id,
               t.mail_id,
               t.mail_send_log_id,
               t.data_date,
               t.mail_status,
               t.func_type,
               t.data_type,
               t.create_time,
               t.product_id
        from func_data_mail t
        <where>
            <if test="beginDate != null and beginDate != ''">
                and data_date &gt;= #{beginDate,jdbcType=VARCHAR}
            </if>
            <if test="endDate != null and endDate != ''">
                and data_date &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
        </where>
        and func_type like concat('%', 'TA资金清算报表', '%')
        and data_type like concat('%', 'TA资金清算报表', '%')
    </select>
</mapper>
