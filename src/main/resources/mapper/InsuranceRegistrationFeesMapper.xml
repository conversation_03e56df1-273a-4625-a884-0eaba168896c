<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.InsuranceRegistrationFeesMapper">
    <insert id="batchSaveFile">
        insert into insurance_registration_fees_file(id, file_name, file_path, type)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.fileName,jdbcType=VARCHAR}, #{e.filePath,jdbcType=VARCHAR},
             #{e.type,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="batchSave">
        insert into insurance_registration_fee(id, product_name, fee_collection_agencies, begin_cost_date,
                                               end_cost_date, payment_method, payment_status, payment_date,
                                               payment_status_update_time, import_O32_status, amount, name_of_payee,
                                               beneficiary_account, bank_account, bank_account_number, remark,
                                               notice_date, update_by, update_time, file_id, product_id, o32_message,
                                               holder_account_number, large_payment_number, holder_account_name,
                                               order_number, begin_payment_period_date, end_payment_period_date,
                                               ocr_recognize_status, mail_id, mail_send_status, pay_month)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.productName,jdbcType=VARCHAR}, #{e.feeCollectionAgencies,jdbcType=VARCHAR},
             #{e.beginCostDate,jdbcType=VARCHAR}, #{e.endCostDate,jdbcType=VARCHAR},
             #{e.paymentMethod,jdbcType=VARCHAR}, #{e.paymentStatus,jdbcType=VARCHAR},
             #{e.paymentDate,jdbcType=VARCHAR}, #{e.paymentStatusUpdateTime,jdbcType=VARCHAR},
             #{e.importO32Status,jdbcType=VARCHAR}, #{e.amount,jdbcType=VARCHAR}, #{e.nameOfPayee,jdbcType=VARCHAR},
             #{e.beneficiaryAccount,jdbcType=VARCHAR}, #{e.bankAccount,jdbcType=VARCHAR},
             #{e.bankAccountNumber,jdbcType=VARCHAR},
             #{e.remark,jdbcType=VARCHAR}, #{e.noticeDate,jdbcType=VARCHAR}, #{e.updateByName,jdbcType=VARCHAR},
             #{e.updateTime,jdbcType=VARCHAR},
             #{e.fileId,jdbcType=VARCHAR}, #{e.productId,jdbcType=VARCHAR}, #{e.o32Message,jdbcType=VARCHAR},
             #{e.holderAccountNumber,jdbcType=VARCHAR},
             #{e.largePaymentNumber,jdbcType=VARCHAR}, #{e.holderAccountName,jdbcType=VARCHAR},
             #{e.orderNumber,jdbcType=VARCHAR}, #{e.beginPaymentPeriodDate,jdbcType=VARCHAR},
             #{e.endPaymentPeriodDate,jdbcType=VARCHAR}, #{e.ocrRecognizeStatus,jdbcType=VARCHAR},
             #{e.mailId,jdbcType=VARCHAR}, #{e.mailSendStatus,jdbcType=VARCHAR}, #{e.payMonth,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="page" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFees">
        select t1.id,
               t2.full_product_name                        as productName,
               fee_collection_agencies,
               concat(begin_cost_date, '-', end_cost_date) as costDate,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by                                   as updateByName,
               update_time,
               file_id,
               product_id,
               o32_message,
               t1.holder_account_number,
               t1.large_payment_number,
               holder_account_name,
               order_number,
               ocr_recognize_status,
               mail_id,
               mail_send_time,
               mail_send_status,
               import_O32_Time,
               handle_result
        from insurance_registration_fee t1
                 left join account_information t2 on t1.product_id = t2.id
        <where>
            ((
                 begin_cost_date between #{beginCostDate,jdbcType=VARCHAR} and #{endCostDate,jdbcType=VARCHAR}
                     or end_cost_date between #{beginCostDate,jdbcType=VARCHAR} and #{endCostDate,jdbcType=VARCHAR}
                 )
                or
             (
                 begin_cost_date &lt;= #{beginCostDate,jdbcType=VARCHAR} and
                 end_cost_date &gt;= #{endCostDate,jdbcType=VARCHAR}
                 ))
            <if test="importO32Status != null and importO32Status != ''">
                and import_O32_status = #{importO32Status,jdbcType=VARCHAR}
            </if>
            <if test="ocrRecognizeStatus != null and ocrRecognizeStatus != ''">
                and ocr_recognize_status = #{ocrRecognizeStatus,jdbcType=VARCHAR}
            </if>
            <if test="mailSendStatus != null and mailSendStatus != ''">
                and mail_send_status = #{mailSendStatus,jdbcType=VARCHAR}
            </if>
            <if test="productIds != null and productIds.size() != 0">
                and product_id in
                <foreach collection="productIds" item="e" separator="," open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                and payment_status = #{paymentStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by begin_cost_date desc
    </select>

    <select id="selectListByDate" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFees">
        select t1.id,
               t2.full_product_name as productName,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by            as updateByName,
               update_time,
               file_id,
               product_id,
               o32_message,
               t1.holder_account_number,
               t1.large_payment_number,
               holder_account_name,
               order_number,
               begin_payment_period_date,
               end_payment_period_date
        from insurance_registration_fee t1
                 left join account_information t2 on t1.product_id = t2.id
        where (
            begin_cost_date between #{beginDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                or end_cost_date between #{beginDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
            )
           or (
            begin_cost_date &lt;= #{beginDate,jdbcType=VARCHAR} and end_cost_date &gt;= #{endDate,jdbcType=VARCHAR}
            )
        order by begin_payment_period_date desc
    </select>

    <select id="selectByFileIds" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFeesFile">
        select id, file_name, file_path, type
        from insurance_registration_fees_file where id in
        <foreach collection="list" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByFileId" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFeesFile">
        select id, file_name, file_path, type
        from insurance_registration_fees_file
        where id = #{fileId,jdbcType=VARCHAR}
    </select>

    <update id="updateBaseInfo">
        update insurance_registration_fee
        set name_of_payee        = #{data.nameOfPayee}
          , beneficiary_account  = #{data.beneficiaryAccount}
          , bank_account         = #{data.bankAccount}
          , large_payment_number = #{data.largePaymentNumber}
          , ocr_recognize_status = 'CONFIRMED' where id in
        <foreach collection="ids" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateTableData">
        update insurance_registration_fee
        set holder_account_number     = #{data.holderAccountNumber},
            holder_account_name       = #{data.holderAccountName},
            product_id                = #{data.productId},
            begin_payment_period_date = #{data.beginPaymentPeriodDate},
            end_payment_period_date   = #{data.endPaymentPeriodDate},
            amount                    = #{data.amount},
            payment_status            = #{data.paymentStatus}
        where id = #{data.id}
    </update>

    <select id="selectList" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFees">
        select t1.id,
               t2.full_product_name as productName,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by            as updateByName,
               update_time,
               file_id,
               product_id,
               o32_message,
               t1.holder_account_number,
               t1.large_payment_number,
               holder_account_name,
               order_number,
               begin_payment_period_date,
               end_payment_period_date,
               pay_month
        from insurance_registration_fee t1
                 left join account_information t2 on t1.product_id = t2.id
        where t1.id in
        <foreach collection="list" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>


    <update id="batchUpdateImportStatus">
        update insurance_registration_fee
        set import_O32_status = #{status,jdbcType=VARCHAR},
            o32_message       = #{errMsg,jdbcType=VARCHAR}
        <if test="importTime != null and importTime != ''">
            ,
                import_O32_Time = #{importTime,jdbcType=VARCHAR}
        </if>
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateImportStatus">
        update insurance_registration_fee
        set import_O32_status = #{status,jdbcType=VARCHAR},
            o32_message       = #{msg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getFileByIds" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFeesFile">
        select id, file_name, file_path, type
        from insurance_registration_fees_file
        where id in (select file_id
                     from insurance_registration_fee t2
        where t2.id in
        <foreach collection="list" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
        )
    </select>

    <select id="selectRepeatData" resultType="int">
        select count(id)
        from insurance_registration_fee
        where name_of_payee = #{nameOfPayee,jdbcType=VARCHAR}
          and beneficiary_account = #{beneficiaryAccount,jdbcType=VARCHAR}
          and bank_account = #{bankAccount,jdbcType=VARCHAR}
          and amount = #{amount,jdbcType=VARCHAR}
          and begin_cost_date = #{beginCostDate,jdbcType=VARCHAR}
          and end_cost_date = #{endCostDate,jdbcType=VARCHAR}
          and holder_account_name = #{holderAccountName,jdbcType=VARCHAR}
          and holder_account_number = #{holderAccountNumber,jdbcType=VARCHAR}
    </select>

    <update id="updatePayStatus">
        update insurance_registration_fee
        set payment_status             = 'PAID',
            payment_status_update_time = #{statusUpdateTime,jdbcType=VARCHAR},
            payment_date               = #{tradeTime,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateSendStatus">
        update insurance_registration_fee
        set mail_send_status = #{sendStatus,jdbcType=VARCHAR},
            mail_send_time   = now() where
        id in
        <foreach collection="ids" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getAllFiles" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFeesFile">
        select id, file_name, file_path, type
        from insurance_registration_fees_file
    </select>

    <select id="selectLastQuarterData" resultType="cn.sdata.om.al.entity.InsuranceRegistrationFees">
        select id,
               product_name,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               file_id,
               product_id,
               o32_message,
               holder_account_number,
               large_payment_number,
               holder_account_name,
               order_number,
               begin_payment_period_date,
               end_payment_period_date,
               ocr_recognize_status,
               mail_id,
               mail_send_status,
               import_O32_Time,
               mail_send_time,
               pay_month
        from insurance_registration_fee
        where begin_cost_date &gt;= #{beginDate,jdbcType=VARCHAR}
          and end_cost_date &lt;= #{endDate,jdbcType=VARCHAR}
    </select>

    <update id="updateHandleResult">
        update insurance_registration_fee
        set handle_result = #{handleResult,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteById">
        delete
        from insurance_registration_fee
        where id = #{id,jdbcType=VARCHAR}
    </delete>
</mapper>
