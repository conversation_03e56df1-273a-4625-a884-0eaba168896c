<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.InterbankFeesMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.InterBankFees">
        select t1.id,
               t2.full_product_name                        as product_name,
               fee_collection_agencies,
               concat(begin_cost_date, '-', end_cost_date) as costDate,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by                                   as updateByName,
               update_time,
               file_id,
               product_id,
               o32_message,
               mail_id,
               mail_send_time,
               mail_send_status,
               import_O32_Time,
               ocr_recognize_status,
               handle_result,
               t2.payment_method_zz                        as paymentMethodZZ,
               t2.payment_method_sq                        as paymentMethodSQ,
               t2.payment_method_wh                        as paymentMethodWH
        from inter_bank_fees t1
                 left join account_information t2 on t1.product_id = t2.id
        <where>
            ((begin_cost_date between #{beginCostDate,jdbcType=VARCHAR} and #{endCostDate,jdbcType=VARCHAR}
                or end_cost_date between #{beginCostDate,jdbcType=VARCHAR} and #{endCostDate,jdbcType=VARCHAR})
                or (
                 begin_cost_date &lt;= #{beginCostDate,jdbcType=VARCHAR}
                     and end_cost_date &gt;= #{endCostDate,jdbcType=VARCHAR}
                 ))
            <if test="paymentStatus != null and paymentStatus != ''">
                and payment_status = #{paymentStatus,jdbcType=VARCHAR}
            </if>
            <if test="feeCollectionAgencies != null and feeCollectionAgencies != ''">
                and fee_collection_agencies = #{feeCollectionAgencies,jdbcType=VARCHAR}
            </if>
            <if test="importO32Status != null and importO32Status != ''">
                and import_O32_status = #{importO32Status,jdbcType=VARCHAR}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                and payment_method = #{paymentMethod,jdbcType=VARCHAR}
            </if>
            <if test="paymentDate != null and paymentDate != ''">
                and payment_date = #{paymentDate,jdbcType=VARCHAR}
            </if>
            <if test="mailSendStatus != null and mailSendStatus != ''">
                and mail_send_status = #{mailSendStatus,jdbcType=VARCHAR}
            </if>
            <if test="productIds != null and productIds.size() != 0">
                and product_id in
                <foreach collection="productIds" item="e" separator="," open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="ocrRecognizeStatus != null and ocrRecognizeStatus != ''">
                and ocr_recognize_status = #{ocrRecognizeStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by begin_cost_date desc
    </select>

    <insert id="batchSave">
        insert into inter_bank_fees(id, product_name, fee_collection_agencies, begin_cost_date, end_cost_date,
                                    payment_method, payment_status,
                                    payment_date, payment_status_update_time, import_O32_status, amount, name_of_payee,
                                    beneficiary_account,
                                    bank_account, bank_account_number, remark, notice_date, update_by, update_time,
                                    file_id, product_id, ocr_recognize_status, mail_id, mail_send_status, pay_month, code, name)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.productName,jdbcType=VARCHAR}, #{e.feeCollectionAgencies,jdbcType=VARCHAR},
             #{e.beginCostDate,jdbcType=VARCHAR}, #{e.endCostDate,jdbcType=VARCHAR},
             #{e.paymentMethod,jdbcType=VARCHAR}, #{e.paymentStatus,jdbcType=VARCHAR},
             #{e.paymentDate,jdbcType=VARCHAR}, #{e.paymentStatusUpdateTime,jdbcType=VARCHAR},
             #{e.importO32Status,jdbcType=VARCHAR}, #{e.amount,jdbcType=VARCHAR}, #{e.nameOfPayee,jdbcType=VARCHAR},
             #{e.beneficiaryAccount,jdbcType=VARCHAR}, #{e.bankAccount,jdbcType=VARCHAR},
             #{e.bankAccountNumber,jdbcType=VARCHAR},
             #{e.remark,jdbcType=VARCHAR}, #{e.noticeDate,jdbcType=VARCHAR}, #{e.updateByName,jdbcType=VARCHAR},
             #{e.updateTime,jdbcType=VARCHAR},
             #{e.fileId,jdbcType=VARCHAR}, #{e.productId,jdbcType=VARCHAR}, #{e.ocrRecognizeStatus,jdbcType=VARCHAR},
             #{e.mailId,jdbcType=VARCHAR}, #{e.mailSendStatus,jdbcType=VARCHAR}, #{e.payMonth,jdbcType=VARCHAR}, #{e.code,jdbcType=VARCHAR},
             #{e.name,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="batchSaveFile">
        insert into inter_bank_fees_file(id, file_name, file_path, type) VALUES
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.fileName,jdbcType=VARCHAR}, #{e.filePath,jdbcType=VARCHAR}, '')
        </foreach>
    </insert>

    <select id="getById" resultType="cn.sdata.om.al.entity.InterBankFees">
        select t1.id,
               product_name,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by as updateByName,
               update_time,
               file_id,
               product_id,
               o32_message
        from inter_bank_fees t1
                 left join inter_bank_fees_file t2 on t1.file_id = t2.id
        where t1.id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getFileById" resultType="cn.sdata.om.al.entity.InterBankFeesFile">
        select id, file_name, file_path, type
        from inter_bank_fees_file
        where id = #{fileId,jdbcType=VARCHAR}
    </select>

    <update id="update">
        update inter_bank_fees
        <trim prefix="set" suffixOverrides=",">
            <if test="productName != null and productName != ''">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="feeCollectionAgencies != null and feeCollectionAgencies != ''">
                fee_collection_agencies = #{feeCollectionAgencies,jdbcType=VARCHAR},
            </if>
            <if test="amount != null and amount != ''">
                amount = #{amount,jdbcType=VARCHAR},
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                name_of_payee = #{nameOfPayee,jdbcType=VARCHAR},
            </if>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                beneficiary_account = #{beneficiaryAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount != ''">
                bank_account = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankAccountNumber != null and bankAccountNumber != ''">
                bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="noticeDate != null and noticeDate != ''">
                notice_date = #{noticeDate,jdbcType=VARCHAR},
            </if>
            <if test="paymentStatus != null and paymentStatus != ''">
                payment_status = #{paymentStatus,jdbcType=VARCHAR},
            </if>
            <if test="ocrRecognizeStatus != null and ocrRecognizeStatus != ''">
                ocr_recognize_status = #{ocrRecognizeStatus,jdbcType=VARCHAR},
            </if>
            <if test="productId != null and productId != ''">
                product_id = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="paymentStatusUpdateTime != null and paymentStatusUpdateTime != ''">
                payment_status_update_time = #{paymentStatusUpdateTime,jdbcType=VARCHAR},
            </if>
            update_time = #{updateTime,jdbcType=VARCHAR},
            update_by   = #{updateByName,jdbcType=VARCHAR}
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getFileByIds" resultType="cn.sdata.om.al.entity.InterBankFeesFile">
        select id, file_name, file_path, type
        from inter_bank_fees_file
        where id in (select file_id
                     from inter_bank_fees t2
        where t2.id in
        <foreach collection="list" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
        )
    </select>

    <select id="selectList" resultType="cn.sdata.om.al.entity.InterBankFees">
        select id,
               product_name,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               update_by as updateByName,
               update_time,
               file_id,
               product_id,
               pay_month,
               o32_message
        from inter_bank_fees where id in
        <foreach collection="list" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getImportOrderNumber" resultType="java.lang.String">
        select order_num
        from fee_manager_order
        where date = #{date,jdbcType=VARCHAR}
          and type = #{type,jdbcType=VARCHAR}
    </select>

    <insert id="insertImportOrderNumber">
        insert into fee_manager_order(date, order_num, type)
        values (#{date,jdbcType=VARCHAR}, #{orderNum,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR})
    </insert>

    <update id="updateImportOrderNumber">
        update fee_manager_order
        set order_num = #{orderNum,jdbcType=VARCHAR}
        where date = #{date,jdbcType=VARCHAR}
          and type = #{type,jdbcType=VARCHAR}
    </update>

    <update id="batchUpdateImportStatus">
        update inter_bank_fees
        set import_O32_status = #{status,jdbcType=VARCHAR},
            o32_message       = #{errMsg,jdbcType=VARCHAR}
        <if test="importTime != null and importTime != ''">
            ,
                import_O32_Time = #{importTime,jdbcType=VARCHAR}
        </if>
        where id in
        <foreach collection="ids" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateImportStatus">
        update inter_bank_fees
        set import_O32_status = #{status,jdbcType=VARCHAR},
            o32_message       = #{msg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectCopPayStatus" resultType="com.alibaba.fastjson2.JSONObject">
        select t1.rival_account_no, t1.rival_account_name, t1.trade_balance, t2.fund_id, t1.curr_date, t1.TRANSFER_TIME
        from cop.cop_taccounttradeflow t1
                 inner join (select * from cop.cop_taccountinfo where fund_id is not null and fund_id != '') t2
                            on t1.bank_account_id = t2.bank_account_id
                 inner join cop.cop_tfundinfo t3 on t2.fund_id = t3.fund_id
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and t1.rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and t1.rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="amount != null and amount != ''">
                and t1.trade_balance = #{amount,jdbcType=VARCHAR}
            </if>
            <if test="beginDate != null and beginDate != '' and endDate != null and endDate != ''">
                and t1.curr_date between str_to_date(#{beginDate,jdbcType=VARCHAR}, '%Y%m%d') and str_to_date(#{endDate,jdbcType=VARCHAR}, '%Y%m%d')
            </if>
            <if test="productId != null and productId != ''">
                and t2.fund_id = #{productId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="testSelectAllCop" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from cop.cop_taccounttradeflow
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount != ''">
                and rival_bank_name = #{bankAccount,jdbcType=VARCHAR}
            </if>
        </where>
        limit 10
    </select>

    <select id="testSelectO32Date" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT *
        FROM (SELECT e.*, ROWNUM rn
              FROM (SELECT *
                    FROM trade.tsysteminfo
                    ORDER BY l_init_date DESC) e
              WHERE ROWNUM &lt;= 10)
    </select>

    <select id="selectRepeatData" resultType="int">
        select count(id)
        from inter_bank_fees
        where name_of_payee = #{nameOfPayee,jdbcType=VARCHAR}
          and beneficiary_account = #{beneficiaryAccount,jdbcType=VARCHAR}
          and bank_account = #{bankAccount,jdbcType=VARCHAR}
          and amount = #{amount,jdbcType=VARCHAR}
          and bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR}
          and begin_cost_date = #{beginCostDate,jdbcType=VARCHAR}
          and end_cost_date = #{endCostDate,jdbcType=VARCHAR}
          and name = #{name,jdbcType=VARCHAR}
          and code = #{code,jdbcType=VARCHAR}
    </select>

    <select id="testHisQueryCop" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from cop.cop_thisaccounttradeflow
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount != ''">
                and rival_bank_name = #{bankAccount,jdbcType=VARCHAR}
            </if>
        </where>
        limit 50
    </select>

    <select id="selectCopHistoryPayStatus" resultType="com.alibaba.fastjson2.JSONObject">
        select t1.rival_account_no, t1.rival_account_name, t1.trade_balance, t2.fund_id, t1.curr_date, t1.TRANSFER_TIME
        from cop.cop_thisaccounttradeflow t1
                 inner join (select * from cop.cop_taccountinfo where fund_id is not null and fund_id != '') t2
                            on t1.bank_account_id = t2.bank_account_id
                 inner join cop.cop_tfundinfo t3 on t2.fund_id = t3.fund_id
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and t1.rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and t1.rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="amount != null and amount != ''">
                and t1.trade_balance = #{amount,jdbcType=VARCHAR}
            </if>
            <if test="beginDate != null and beginDate != '' and endDate != null and endDate != ''">
                and t1.curr_date between str_to_date(#{beginDate,jdbcType=VARCHAR}, '%Y%m%d') and str_to_date(#{endDate,jdbcType=VARCHAR}, '%Y%m%d')
            </if>
            <if test="productId != null and productId != ''">
                and t2.fund_id = #{productId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updatePayStatus">
        update inter_bank_fees
        set payment_status             = 'PAID',
            payment_status_update_time = #{statusUpdateTime,jdbcType=VARCHAR},
            payment_date               = #{tradeTime,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getAllFiles" resultType="cn.sdata.om.al.entity.InterBankFeesFile">
        select id, file_name, file_path, type
        from inter_bank_fees_file
    </select>

    <update id="updateSendStatus">
        update inter_bank_fees
        set mail_send_status = #{sendStatus,jdbcType=VARCHAR},
            mail_send_time   = now() where
        id in
        <foreach collection="ids" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="testSelectAllCopV2" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from cop.cop_thisaccounttradeflow
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount != ''">
                and rival_bank_name = #{bankAccount,jdbcType=VARCHAR}
            </if>
        </where>
        limit 50
    </select>

    <select id="testQueryCopAcc" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from cop.cop_taccountinfo
        where fund_id is not null
          and fund_id != ''
        limit 50
    </select>

    <select id="testQueryCopFund" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from cop.cop_tfundinfo
        limit 50
    </select>

    <select id="testJoinQueryCop" resultType="com.alibaba.fastjson2.JSONObject">
        select t1.rival_account_no, t1.rival_account_name, t1.trade_balance, t2.fund_id, t1.curr_date
        from cop.cop_thisaccounttradeflow t1
                 inner join (select * from cop.cop_taccountinfo where fund_id is not null and fund_id != '') t2
                            on t1.bank_account_id = t2.bank_account_id
                 inner join cop.cop_tfundinfo t3 on t2.fund_id = t3.fund_id
        <where>
            <if test="beneficiaryAccount != null and beneficiaryAccount != ''">
                and t1.rival_account_no = #{beneficiaryAccount,jdbcType=VARCHAR}
            </if>
            <if test="nameOfPayee != null and nameOfPayee != ''">
                and t1.rival_account_name = #{nameOfPayee,jdbcType=VARCHAR}
            </if>
            <if test="beginDate != null and beginDate != '' and endDate != null and endDate != ''">
                and t1.curr_date between str_to_date(#{beginDate,jdbcType=VARCHAR}, '%Y%m%d') and str_to_date(#{endDate,jdbcType=VARCHAR}, '%Y%m%d')
            </if>
        </where>
        limit 50
    </select>

    <select id="selectLastQuarterData" resultType="cn.sdata.om.al.entity.InterBankFees">
        select id,
               product_name,
               fee_collection_agencies,
               begin_cost_date,
               end_cost_date,
               payment_method,
               payment_status,
               payment_date,
               payment_status_update_time,
               import_O32_status,
               amount,
               name_of_payee,
               beneficiary_account,
               bank_account,
               bank_account_number,
               remark,
               notice_date,
               file_id,
               product_id,
               o32_message,
               ocr_recognize_status,
               mail_id,
               mail_send_status,
               import_O32_Time,
               mail_send_time,
               pay_month,
               code,
               name
        from inter_bank_fees
        where begin_cost_date &gt;= #{beginDate,jdbcType=VARCHAR}
          and end_cost_date &lt;= #{endDate,jdbcType=VARCHAR}
    </select>

    <select id="selectProductCodeById" resultType="java.lang.String">
        select product_code
        from account_information
        where id = #{productId,jdbcType=VARCHAR} limit 1
    </select>

    <update id="updateHandleResult">
        update inter_bank_fees set handle_result = #{handleResult,jdbcType=VARCHAR} where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteById">
        delete from inter_bank_fees where id = #{id,jdbcType=VARCHAR}
    </delete>
</mapper>
