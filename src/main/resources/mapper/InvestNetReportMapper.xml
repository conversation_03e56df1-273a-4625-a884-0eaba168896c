<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.InvestNetReportMapper">

    <select id="getShareChangeExcelLocalPath" resultType="java.lang.String">
        select url
        from (select *,
                     row_number() over(PARTITION BY received_date ORDER BY message_number DESC) AS rn
              from om_mail_content
              where sent_date = #{bizDate}
                and box_id = (select ompb.id
                              from om_mail_pick_basket ompb
                              where ompb.name = '寿险份额变动表')) omc
         left join om_mail_attachment oma
            on omc.id = oma.mail_id
        where omc.rn = 1
          and oma.type = 1
          and oma.name like '%xlsx'
        order by oma.id desc

    </select>
    <select id="getShareChangeExcelLocalPathV1" resultType="cn.sdata.om.al.dto.InvestNetReportShareChangeDto">
        select oma.url, omc.received_time
        from (select *,
                     row_number() over(PARTITION BY received_date ORDER BY message_number DESC) AS rn
              from om_mail_content
              where sent_date = #{bizDate}
                and box_id = (select ompb.id
                              from om_mail_pick_basket ompb
                              where ompb.name = '寿险份额变动表')) omc
                 left join om_mail_attachment oma
                           on omc.id = oma.mail_id
        where omc.rn = 1
          and oma.type = 1
          and oma.name like '%xlsx'
        order by oma.id desc
    </select>
</mapper>