<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.InvestNetReportValuationDBMapper">

    <select id="netValueListByDates" resultType="cn.sdata.om.al.entity.investNetReport.ValuationNetValueEntity">
        SELECT
            l_ztbh AS "accountSetCode",
            TO_CHAR(d_ywrq, 'YYYY-MM-DD') AS "valuationDate",
            MAX(CASE WHEN vc_kmdm = '委托资产净值:' THEN en_sz END) AS "assetNetValue",
            MAX(CASE WHEN vc_kmdm = '投资单位卖出价' THEN en_sz END) AS "shareNetValue"
        FROM hsfa.vjk_wbfk_gzb
        WHERE l_ztbh IN
            <foreach collection="accountSetCodes" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
          AND d_ywrq IN
            <foreach collection="dates" item="date" open="(" separator="," close=")">
                TO_DATE(#{date}, 'YYYY-MM-DD')
            </foreach>
          AND vc_kmdm IN ('委托资产净值:', '投资单位卖出价')
        GROUP BY l_ztbh, d_ywrq
        ORDER BY l_ztbh, d_ywrq

    </select>
</mapper>