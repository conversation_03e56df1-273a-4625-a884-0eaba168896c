<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.InvestorContactsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.InvestorContacts">
    <!--@mbg.generated-->
    <!--@Table investor_contacts-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="investor" jdbcType="VARCHAR" property="investor" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="recipient" jdbcType="LONGVARCHAR" property="recipient" />
    <result column="recipient_cc" jdbcType="LONGVARCHAR" property="recipientCc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, investor, product_id, `method`, `handler`, recipient, recipient_cc
  </sql>

  <select id="pageResult" resultType="cn.sdata.om.al.vo.InvestorContactVO">
    select * from (SELECT
    a.id AS id,
    max(investor) AS investor,
    GROUP_CONCAT(DISTINCT b.product_id SEPARATOR ',') AS product_id,
    GROUP_CONCAT(DISTINCT c.full_product_name SEPARATOR ',') AS product_name,
    GROUP_CONCAT(DISTINCT `method` SEPARATOR ',') AS `method`,
    GROUP_CONCAT(DISTINCT `handler` SEPARATOR ',') AS `handler`,
    max(recipient) AS recipient,
    max(recipient_cc) AS recipient_cc,
    max(szt_path) AS szt_path
    FROM
    investor_contacts_view a
    INNER JOIN investor_contacts b ON a.sub_id = b.id
    INNER JOIN account_information c ON b.product_id = c.id
    where a.type = #{type}
    GROUP BY
    a.id) t
      <where>
        <if test="investors != null and investors.size() != 0">
          and investor in (<foreach collection="investors" item="investor" separator=",">
          #{investor}
        </foreach>)
        </if>
        <if test="products != null and products.size() != 0">
          and
          (<foreach collection="products" item="product" separator="OR">
          FIND_IN_SET(#{product}, product_id) > 0
        </foreach>)
        </if>
        <if test="methods != null and methods.size() != 0">
          and
          (<foreach collection="methods" item="method" separator="OR">
          FIND_IN_SET(#{method}, method) > 0
        </foreach>)
        </if>
      </where>
    </select>

  <select id="listProduct" resultType="cn.sdata.om.al.entity.CommonEntity">
    select distinct ic.product_id as id, ai.account_name as name
    from investor_contacts ic inner join account_information ai on ic.product_id = ai.id order by ic.product_id
  </select>

  <select id="listResult" resultType="cn.sdata.om.al.vo.InvestorContactVO">
    select *
    from (SELECT a.id                                                     AS id,
                 max(investor)                                            AS investor,
                 GROUP_CONCAT(DISTINCT b.product_id SEPARATOR ',')        AS product_id,
                 GROUP_CONCAT(DISTINCT c.full_product_name SEPARATOR ',') AS product_name,
                 GROUP_CONCAT(DISTINCT `method` SEPARATOR ',')            AS `method`,
                 GROUP_CONCAT(DISTINCT `handler` SEPARATOR ',')           AS `handler`,
                 max(recipient)                                           AS recipient,
                 max(recipient_cc)                                        AS recipient_cc
          FROM investor_contacts_view a
                 INNER JOIN investor_contacts b ON a.sub_id = b.id
                 INNER JOIN account_information c ON b.product_id = c.id
          where a.type = #{type}
          GROUP BY a.id) t
  </select>
</mapper>