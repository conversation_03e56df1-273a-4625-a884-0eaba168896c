<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.InvestorContactsViewMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.InvestorContactsView">
    <!--@mbg.generated-->
    <!--@Table investor_contacts_view-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sub_id" jdbcType="VARCHAR" property="subId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sub_id
  </sql>

  <select id="getRecipientInfo" resultType="cn.sdata.om.al.entity.InvestorContacts">
    select icv.id                                          as id,
           max(investor)                                  as investor,
           max(recipient)                                  as recipient,
           max(recipient_cc)                               as recipient_cc,
           `handler`                                   as `handler`,
           group_concat(distinct product_id separator ',') as product_id
    from investor_contacts_view icv
           inner join investor_contacts ic on icv.sub_id = ic.id
    where icv.type = #{type}
      and method = #{method}
      <if test="handler != null and handler !=''">
          and `handler` = #{handler}
      </if>
      <if test="ids != null and ids.size() != 0">
          and icv.id in <foreach open="(" collection="ids" separator="," close=")" item="id">
          #{id}
      </foreach>
      </if>
    group by icv.id,`handler`
    </select>

  <select id="getRecipientInfoByProduct" resultType="cn.sdata.om.al.entity.InvestorContacts">
      select max(recipient)                                  as recipient,
             max(investor)                                  as investor,
             max(recipient_cc)                               as recipient_cc,
             `handler`                                       as `handler`,
             product_id as product_id
      from investor_contacts_view icv
               inner join investor_contacts ic on icv.sub_id = ic.id
      where icv.type = #{type}
        and method = #{method}
        <if test="handler != null and handler !=''">
            and `handler` = #{handler}
        </if>
      <if test="productIds != null and productIds.size() != 0">
          and ic.product_id in
          <foreach open="(" collection="productIds" separator="," close=")" item="productId">
              #{productId}
          </foreach>
      </if>
      group by product_id,`handler`
    </select>
</mapper>