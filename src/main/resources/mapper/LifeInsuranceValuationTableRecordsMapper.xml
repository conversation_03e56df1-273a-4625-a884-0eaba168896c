<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.LifeInsuranceValuationTableRecordsMapper">


    <select id="getLatestList"
            resultType="cn.sdata.om.al.entity.investNetReport.LifeInsuranceValuationTableRecords">
        SELECT *
            FROM (SELECT *,
                         row_number() over(PARTITION BY account_set_code ORDER BY create_time DESC) AS rn
                  FROM life_insurance_valuation_table_records
                    <where>
                        valuation_date = #{valuationDate} and local_file_path is not null
                        <if test="accountSetCodes != null and accountSetCodes.size()>0">
                            and
                            account_set_code in
                            <foreach separator="," close=")" open="(" item="accountSetCode" collection="accountSetCodes">
                                #{accountSetCode}
                            </foreach>
                        </if>
                    </where>
                 )m
        WHERE m.rn = 1
    </select>
</mapper>