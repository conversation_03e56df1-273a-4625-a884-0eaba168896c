<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.LogCustodianRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.LogCustodianRecords">
    <!--@mbg.generated-->
    <!--@Table log_custodian_records-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="valuation_date" jdbcType="VARCHAR" property="valuationDate" />
    <result column="custodian_status" jdbcType="VARCHAR" property="custodianStatus" />
    <result column="mail_send_time" jdbcType="TIMESTAMP" property="mailSendTime" />
    <result column="custodian_operator" jdbcType="VARCHAR" property="custodianOperator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, valuation_date, custodian_status, mail_send_time, custodian_operator
  </sql>
</mapper>