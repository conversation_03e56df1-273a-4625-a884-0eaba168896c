<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.LogFYBMapper">
    <select id="getLogFYBGenerateFileRecord" resultType="cn.sdata.om.al.entity.LogFYBGenerateFileRecord">
        select id,
               file_url,
               product_ids as productIdsStr,
               begin_time,
               create_by_name,
               status,
               end_time,
               params,
               result,
               error_msg,
               data_date
        from log_fyb_generate_file_record
    </select>

    <select id="getLogFYBDownloadFileRecord" resultType="cn.sdata.om.al.entity.LogFYBDownloadFileRecord">
        select id,
               begin_time,
               create_by_name,
               status,
               file_url,
               params,
               result,
               error_msg,
               data_date,
               end_time
        from log_fyb_download_file_record
    </select>

    <select id="getLogFYBImportO32Record" resultType="cn.sdata.om.al.entity.LogFYBImportO32Record">
        select id,
               begin_time,
               end_time,
               create_by_name,
               import_result,
               file_url,
               product_id,
               product_name,
               handle_result,
               params,
               result,
               error_msg
        from log_fyb_import_o32_record
    </select>

    <select id="getLogFYBO32ConfirmRecord" resultType="cn.sdata.om.al.entity.LogFYBO32ConfirmRecord">
        select id,
               begin_time,
               end_time,
               create_by_name,
               status,
               product_ids as productIdsStr,
               params,
               error_msg,
               data_date
        from log_fyb_o32_confirm_record
    </select>

    <select id="getLogFYBSendMailRecord" resultType="cn.sdata.om.al.entity.LogFYBSendMailRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               task_id,
               task_name,
               type,
               mail_id      as mailIdStr,
               rpa_log_id,
               data_date,
               send_status
        from log_fyb_send_mail_record
    </select>

    <select id="getLogFYBSyncPayStatusRecord" resultType="cn.sdata.om.al.entity.LogFYBSyncPayStatusRecord">
        select id,
               begin_time,
               end_time,
               create_by_name,
               product_infos as productInfosStr,
               params,
               error_msg,
               data_date
        from log_fyb_sync_pay_status_record
    </select>

    <select id="getLogFYBUpdatePayStatusRecord" resultType="cn.sdata.om.al.entity.LogFYBUpdatePayStatusRecord">
        select id,
               begin_time,
               end_time,
               create_by_name,
               product_id,
               params,
               error_msg,
               data_date,
               pre_update_status,
               post_update_status
        from log_fyb_update_pay_status_record
    </select>

    <insert id="saveUpdatePayStatusLog">
        insert into log_fyb_update_pay_status_record(id, begin_time, create_by_name, product_id, params, data_date,
                                                     pre_update_status)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{preUpdateStatus,jdbcType=VARCHAR})
    </insert>

    <update id="modifyUpdatePayStatusLog">
        update log_fyb_update_pay_status_record
        set end_time           = #{endTime,jdbcType=VARCHAR},
            post_update_status = #{postUpdateStatus,jdbcType=VARCHAR}
        where id = #{logId,jdbcType=VARCHAR}
    </update>

    <insert id="saveDownloadFileLog">
        insert into log_fyb_download_file_record(id, begin_time, create_by_name, status, params, data_date)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateDownloadFileLog">
        update log_fyb_download_file_record
        set status   = #{status,jdbcType=VARCHAR},
            file_url = #{fileUrl,jdbcType=VARCHAR},
            end_time = #{endTime,jdbcType=VARCHAR},
            status   = #{status,jdbcType=VARCHAR}
        where id = #{logId,jdbcType=VARCHAR}
    </update>

    <insert id="saveGenerateO32File">
        insert into log_fyb_generate_file_record(id, begin_time, create_by_name, status, params)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR})
    </insert>

    <update id="updateGenerateO32File">
        update log_fyb_generate_file_record
        set end_time    = #{endTime,jdbcType=VARCHAR},
            status      = #{status,jdbcType=VARCHAR},
            product_ids = #{productIds,jdbcType=VARCHAR},
            error_msg   = #{errorMsg,jdbcType=VARCHAR},
            file_url    = #{fileUrl,jdbcType=VARCHAR}
        where id = #{logId,jdbcType=VARCHAR}
    </update>

    <insert id="saveSendMailLog">
        insert into log_fyb_send_mail_record(id, execute_time, create_by_name, end_time, params,
                                             status, task_id, task_name, type, mail_id, rpa_log_id, data_date,
                                             send_status)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{endTime,jdbcType=VARCHAR}, #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
                #{taskId,jdbcType=VARCHAR},
                #{taskName,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR}, #{mailIdStr,jdbcType=VARCHAR}, #{rpaLogId,jdbcType=VARCHAR},
                #{dataDate,jdbcType=VARCHAR},
                #{sendStatus,jdbcType=VARCHAR})
    </insert>

    <insert id="saveO32ConfirmLog">
        insert into log_fyb_o32_confirm_record(id, begin_time, create_by_name, status, product_ids, params, data_date)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},
        #{productIdsStr,jdbcType=VARCHAR},#{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateO32ConfirmLog">
        update log_fyb_o32_confirm_record set end_time = #{endTime,jdbcType=VARCHAR}, status = #{status,jdbcType=VARCHAR}
        where id = #{logId,jdbcType=VARCHAR}
    </update>

    <insert id="saveSyncPayStatusLog">
        insert into log_fyb_sync_pay_status_record(id, begin_time, create_by_name, product_infos, params, data_date, status)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},#{productInfosStr,jdbcType=VARCHAR},
        #{params,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
    </insert>

    <update id="updateSyncPayStatusLog">
        update log_fyb_sync_pay_status_record set end_time = #{endTime,jdbcType=VARCHAR},
        product_infos = #{productInfos,jdbcType=VARCHAR}, status = #{status,jdbcType=VARCHAR} where id = #{logId,jdbcType=VARCHAR}
    </update>

    <insert id="saveImportO32Log">
        insert into log_fyb_import_o32_record(id, begin_time, create_by_name, params, status)
        values (#{id,jdbcType=VARCHAR},#{beginTime,jdbcType=VARCHAR},#{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
    </insert>

    <update id="updateImportO32Log">
        update log_fyb_import_o32_record set end_time = #{endTime,jdbcType=VARCHAR}, status = #{status,jdbcType=VARCHAR},
        import_result = #{o32Result,jdbcType=VARCHAR}, file_url = #{fileUrl,jdbcType=VARCHAR}
        where id = #{logId,jdbcType=VARCHAR}
    </update>

    <select id="getFYBRpaLogByRpaLogId" resultType="cn.sdata.om.al.entity.LogFYBRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param
        from log_fyb_rpa_record
        where rpa_log_id = #{rpaLogId,jdbcType=VARCHAR}
    </select>

    <insert id="saveRpaLog">
        insert into log_fyb_rpa_record(id, execute_time, create_by_name, params, status, task_id, task_name, rpa_log_id,
                                       data_date, rpa_param, type)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR},
                #{taskName,jdbcType=VARCHAR},
                #{rpaLogId,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR}, #{rpaParam,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR})
    </insert>

    <update id="updateRpaLogById">
        update log_fyb_rpa_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            file_type = #{fileType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getLogFYBRPARecord" resultType="cn.sdata.om.al.entity.LogFYBRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param,
               file_type
        from log_fyb_rpa_record
    </select>

    <select id="getFYBRpaLogById" resultType="cn.sdata.om.al.entity.LogFYBRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type,
               rpa_param,
               file_type
        from log_fyb_rpa_record
        where id = #{rpaLogId,jdbcType=VARCHAR}
    </select>
</mapper>
