<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.LogMailSendRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.LogMailSendRecords">
    <!--@mbg.generated-->
    <!--@Table log_mail_send_records-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="valuation_date" jdbcType="VARCHAR" property="valuationDate" />
    <result column="send_method" jdbcType="VARCHAR" property="sendMethod" />
    <result column="send_content" jdbcType="VARCHAR" property="sendContent" />
    <result column="send_to" jdbcType="VARCHAR" property="sendTo" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="mail_id" jdbcType="VARCHAR" property="mailId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, valuation_date, send_method, send_content, send_to, send_time, `status`, 
    mail_id
  </sql>
</mapper>