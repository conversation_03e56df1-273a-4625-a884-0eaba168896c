<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.LogReconRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.LogReconRecords">
    <!--@mbg.generated-->
    <!--@Table log_recon_records-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="valuation_date" jdbcType="VARCHAR" property="valuationDate" />
    <result column="recon_date" jdbcType="TIMESTAMP" property="reconDate" />
    <result column="recon_status" jdbcType="VARCHAR" property="reconStatus" />
    <result column="recon_operator" jdbcType="VARCHAR" property="reconOperator" />
    <result column="operate_reason" jdbcType="VARCHAR" property="operateReason" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, valuation_date, recon_date, recon_status, recon_operator, operate_reason
  </sql>
</mapper>