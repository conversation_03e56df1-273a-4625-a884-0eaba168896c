<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.LogValuationConfirmRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.LogValuationConfirmRecords">
    <!--@mbg.generated-->
    <!--@Table log_valuation_confirm_records-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="valuation_date" jdbcType="VARCHAR" property="valuationDate" />
    <result column="confirm_status" jdbcType="VARCHAR" property="confirmStatus" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="confirm_operator" jdbcType="VARCHAR" property="confirmOperator" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, valuation_date, confirm_status, confirm_time, confirm_operator
  </sql>
</mapper>