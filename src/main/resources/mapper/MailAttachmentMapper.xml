<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailAttachmentMapper">
    <insert id="batchSave">
        insert into om_mail_attachment(id, url, name, mail_id, type) values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.filePath,jdbcType=VARCHAR}, #{e.fileName,jdbcType=VARCHAR},
             #{e.mailId,jdbcType=VARCHAR}, #{e.type})
        </foreach>
    </insert>

    <select id="getMailAttachments" resultType="cn.sdata.om.al.entity.mail.MailAttachment">
        select id, url as filePath, name as fileName, mail_id
        from om_mail_attachment
        where mail_id = #{mailId,jdbcType=VARCHAR}
        group by name
    </select>

    <select id="selectMailAttachmentsByFileId" resultType="cn.sdata.om.al.entity.mail.MailAttachment">
        select id, url as filePath, name as fileName, mail_id
        from om_mail_attachment
        where id = #{fileId,jdbcType=VARCHAR}
    </select>
</mapper>
