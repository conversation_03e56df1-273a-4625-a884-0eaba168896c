<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailCommonInfoMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.mail.MailCommonInfo">
        select *,
               group_concat(distinct t8.t3id) as threePartyOrganization,
               group_concat(distinct t8.t4id) as investor from (select t2.*, t3.organization_id as t3id, t4.investor_id as t4id  from (select t1.id,
                                                                                                                                              full_product_name,
                                                                                                                                              product_code,
                                                                                                                                              valuation_time,
                                                                                                                                              is_structured                   as structured_is,
                                                                                                                                              is_electronic_direct_connection as electronic_direct_connection_is,
                                                                                                                                              custodian_bank,
                                                                                                                                              secondary_valuation_security_type,
                                                                                                                                              account_name,
                                                                                                                                              account_number_detail,
                                                                                                                                              opening_bank,
                                                                                                                                              large_payment_number,
                                                                                                                                              is_auto_deduction               as auto_deduction_is,
                                                                                                                                              central_debt_account_number,
                                                                                                                                              foreign_exchange_center_member_code,
                                                                                                                                              clearing_house_holder_account,
                                                                                                                                              holder_account_number,
                                                                                                                                              holder_account_full_name,
                                                                                                                                              establishment_date,
                                                                                                                                              wind_code,
                                                                                                                                              product_status,
                                                                                                                                              product_category,
                                                                                                                                              warn_value,
                                                                                                                                              close_value,
                                                                                                                                              payment_method_sq,
                                                                                                                                              payment_method_zz,
                                                                                                                                              payment_method_wh,
                                                                                                                                              is_currency                     as currencyIs,
                                                                                                                                              net_asset_value_warning_threshold
                                                                                                                                       from account_information t1
                                                                                                                                                left join account_set_group asg on find_in_set(t1.id, asg.account_code) > 0
        <where>
            <if test="custodianBank != null and custodianBank != ''">
                and custodian_bank like concat('%'
                , #{custodianBank,jdbcType=VARCHAR}
                , '%')
            </if>
            <if test="structuredIs != null">
                and is_structured = #{structuredIs}
            </if>
            <if test="electronicDirectConnectionIs != null">
                and is_electronic_direct_connection = #{electronicDirectConnectionIs}
            </if>
            <if test="fullProductName != null and fullProductName != ''">
                and full_product_name like concat('%'
                    , #{fullProductName,jdbcType=VARCHAR}
                    , '%')
            </if>
            <if test="productIds != null and productIds.size() != 0">
                and t1.id in
                <foreach collection="productIds" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodes != null and productCodes.size() != 0">
                and t1.product_code in
                <foreach collection="productCodes" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCategory != null">
                and product_category = #{productCategory}
            </if>
            <if test="valuationTime != null and valuationTime != ''">
                and valuation_time = #{valuationTime,jdbcType=VARCHAR}
            </if>
            <if test="groupIds != null and groupIds.size() != 0">
                and asg.id in
                <foreach collection="groupIds" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productStatus != null and productStatus != ''">
                and product_status = #{productStatus,jdbcType=VARCHAR}
            </if>
            <if test="custodianBanks != null and custodianBanks.size() != 0">
                and custodian_bank in
                <foreach collection="custodianBanks" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        )
            t2
            left join product_organization_relation t3 on t2.id = t3.product_id
            left join product_investor_relation t4 on t2.id = t4.product_id
        <where>
            <if test="investorIds != null and investorIds.size() != 0">
                and t4.investor_id in
                <foreach collection="investorIds" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="threePartyOrganizationIds != null and threePartyOrganizationIds.size() != 0">
                or t3.organization_id in
                <foreach collection="threePartyOrganizationIds" separator="," open="(" close=")" item="e">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        ) t8
        group by t8.id
    </select>

    <update id="update">
        update account_information
        <trim prefix="set" suffixOverrides=",">
            full_product_name                   = #{fullProductName,jdbcType=VARCHAR},
            custodian_bank                      = #{custodianBank,jdbcType=VARCHAR},
            account_name                        = #{accountName,jdbcType=VARCHAR},
            account_number_detail               = #{accountNumberDetail,jdbcType=VARCHAR},
            central_debt_account_number         = #{centralDebtAccountNumber,jdbcType=VARCHAR},
            clearing_house_holder_account       = #{clearingHouseHolderAccount,jdbcType=VARCHAR},
            foreign_exchange_center_member_code = #{foreignExchangeCenterMemberCode,jdbcType=VARCHAR},
            holder_account_full_name            = #{holderAccountFullName,jdbcType=VARCHAR},
            is_electronic_direct_connection     = #{electronicDirectConnectionIs},
            is_structured                       = #{structuredIs},
            is_auto_deduction                   = #{autoDeductionIs},
            valuation_time                      = #{valuationTime,jdbcType=VARCHAR},
            large_payment_number                = #{largePaymentNumber,jdbcType=VARCHAR},
            net_asset_value_warning_threshold   = #{netAssetValueWarningThreshold,jdbcType=VARCHAR},
            product_code                        = #{productCode,jdbcType=VARCHAR},
            secondary_valuation_security_type   = #{secondaryValuationSecurityType,jdbcType=VARCHAR},
            opening_bank                        = #{openingBank,jdbcType=VARCHAR},
            holder_account_number               = #{holderAccountNumber,jdbcType=VARCHAR},
            establishment_date                  = #{establishmentDate,jdbcType=VARCHAR},
            product_status                      = #{productStatus,jdbcType=INTEGER},
            wind_code                           = #{windCode,jdbcType=VARCHAR},
            payment_method_zz                   = #{paymentMethodZZ,jdbcType=VARCHAR},
            payment_method_sq                   = #{paymentMethodSQ,jdbcType=VARCHAR},
            payment_method_wh                   = #{paymentMethodWH,jdbcType=VARCHAR},
            warn_value                          = #{warnValue,jdbcType=VARCHAR},
            close_value                         = #{closeValue,jdbcType=VARCHAR},
            is_currency = #{currencyIs}
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="save">
        insert into account_information(id, full_product_name, product_code, valuation_time, is_structured,
                                        is_electronic_direct_connection, custodian_bank,
                                        secondary_valuation_security_type, account_name, account_number_detail,
                                        opening_bank, large_payment_number, is_auto_deduction,
                                        central_debt_account_number, foreign_exchange_center_member_code,
                                        clearing_house_holder_account, holder_account_number, holder_account_full_name,
                                        net_asset_value_warning_threshold, wind_code, establishment_date,
                                        product_status, payment_method_zz, payment_method_sq, payment_method_wh,
                                        warn_value, close_value, product_category, is_currency)
        values (#{id,jdbcType=VARCHAR}, #{fullProductName,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR},
                #{valuationTime,jdbcType=VARCHAR},
                #{structuredIs}, #{electronicDirectConnectionIs}, #{custodianBank,jdbcType=VARCHAR},
                #{secondaryValuationSecurityType,jdbcType=VARCHAR},
                #{accountName,jdbcType=VARCHAR}, #{accountNumberDetail,jdbcType=VARCHAR},
                #{openingBank,jdbcType=VARCHAR}, #{largePaymentNumber,jdbcType=VARCHAR},
                #{autoDeductionIs}, #{centralDebtAccountNumber,jdbcType=VARCHAR},
                #{foreignExchangeCenterMemberCode,jdbcType=VARCHAR}, #{clearingHouseHolderAccount,jdbcType=VARCHAR},
                #{holderAccountNumber,jdbcType=VARCHAR}, #{holderAccountFullName,jdbcType=VARCHAR},
                #{netAssetValueWarningThreshold,jdbcType=VARCHAR}, #{windCode,jdbcType=VARCHAR},
                #{establishmentDate,jdbcType=VARCHAR},
                #{productStatus,jdbcType=VARCHAR}, #{paymentMethodZZ,jdbcType=VARCHAR},
                #{paymentMethodSQ,jdbcType=VARCHAR},
                #{paymentMethodWH,jdbcType=VARCHAR}, #{warnValue,jdbcType=VARCHAR}, #{closeValue,jdbcType=VARCHAR},
                #{productCategory,jdbcType=INTEGER}, #{currencyIs})
    </insert>

    <select id="getById" resultType="cn.sdata.om.al.entity.mail.MailCommonInfo">
        select t1.id,
               full_product_name,
               product_code,
               valuation_time,
               is_structured                             as structured_is,
               is_electronic_direct_connection           as electronic_direct_connection_is,
               custodian_bank,
               secondary_valuation_security_type,
               account_name,
               account_number_detail,
               opening_bank,
               large_payment_number,
               is_auto_deduction                         as auto_deduction_is,
               central_debt_account_number,
               foreign_exchange_center_member_code,
               clearing_house_holder_account,
               holder_account_number,
               holder_account_full_name,
               net_asset_value_warning_threshold,
               group_concat(distinct t2.organization_id) as threePartyOrganization,
               group_concat(distinct t3.investor_id)     as investor,
               establishment_date,
               wind_code,
               product_status,
               payment_method_zz,
               payment_method_sq,
               payment_method_wh,
               warn_value,
               close_value,
               product_category
        from account_information t1
                 left join product_organization_relation t2 on t1.id = t2.product_id
                 left join product_investor_relation t3 on t1.id = t3.product_id
        where t1.id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, full_product_name as name
        from account_information
    </select>

    <select id="selectContactsByTypeAndCategory" resultType="java.lang.String">
        select contacts
        from om_mail_common_info
        where dict_type = #{type,jdbcType=VARCHAR}
          and value = #{category,jdbcType=VARCHAR}
    </select>

    <update id="funInfoUpdate">
        update account_fund_information
        <trim prefix="set" suffixOverrides=",">
            <if test="accountName != null and accountName != ''">
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNumber != null and accountNumber != ''">
                account_number = #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="administrator != null and administrator != ''">
                administrator = #{administrator,jdbcType=VARCHAR},
            </if>
            <if test="emailSuffix != null and emailSuffix != ''">
                email_suffix = #{emailSuffix,jdbcType=VARCHAR},
            </if>
            <if test="productId != null and productId != ''">
                product_id = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType}
            </if>
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="fundInfoSave">
        insert into account_fund_information(id, product_id, channel_type, administrator, account_name, account_number,
                                             email_suffix)
        values (#{id,jdbcType=VARCHAR}, #{productId,jdbcType=VARCHAR}, #{channelType},
                #{administrator,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR},
                #{accountNumber,jdbcType=VARCHAR}, #{emailSuffix,jdbcType=VARCHAR})
    </insert>

    <delete id="fundInfoBatchDelete">
        delete
        from account_fund_information where id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="fundInfoPage" resultType="cn.sdata.om.al.entity.mail.AccountFundInformation">
        select t1.id,
               product_id,
               channel_type,
               administrator,
               t1.account_name,
               account_number,
               email_suffix,
               t2.full_product_name as productFullName
        from account_fund_information t1
                 left join account_information t2 on t1.product_id = t2.id
        <where>
            <if test="list != null and list.size() != 0">
                and product_id in
                <foreach collection="list" item="e" open="(" close=")" separator=",">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="admins != null and admins.size() != 0">
                and administrator in
                <foreach collection="admins" item="e" open="(" close=")" separator=",">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="type != null and type != ''">
                and channel_type = #{type,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="fundInfoGetById" resultType="cn.sdata.om.al.entity.mail.AccountFundInformation">
        select t1.id,
               product_id,
               channel_type,
               administrator,
               t1.account_name,
               account_number,
               email_suffix,
               t2.full_product_name as productFullName
        from account_fund_information t1
                 left join account_information t2 on t1.product_id = t2.id
        where t1.id = #{fundInfoId,jdbcType=VARCHAR}
    </select>

    <select id="selectPartyOrganizationByProductId" resultType="java.lang.String">
        select organization_id
        from product_organization_relation
        where product_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectInvestorByProductId" resultType="java.lang.String">
        select investor_id
        from product_investor_relation
        where product_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="accountSetListNoGroup" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, full_product_name as name
        from account_information where id not in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <delete id="deleteProductAndInvestorIdsRelation">
        delete
        from product_investor_relation
        where product_id = #{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteProductAndThreePartyOrganizationIdsRelation">
        delete
        from product_organization_relation
        where product_id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="saveProductAndInvestorIdsRelation">
        insert into product_investor_relation(id, product_id, investor_id) values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.name,jdbcType=VARCHAR}, #{e.extra,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="saveThreePartyOrganizationIdsRelation">
        insert into product_organization_relation(id, product_id, organization_id) values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.name,jdbcType=VARCHAR}, #{e.extra,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="saveBatch">
        insert into account_information_1(id, full_product_name, product_code, valuation_time, is_structured,
                                          is_electronic_direct_connection, custodian_bank,
                                          secondary_valuation_security_type, account_name, account_number_detail,
                                          opening_bank, large_payment_number, is_auto_deduction,
                                          central_debt_account_number, foreign_exchange_center_member_code,
                                          clearing_house_holder_account, holder_account_number,
                                          holder_account_full_name, net_asset_value_warning_threshold, product_category,
                                          payment_method_sq, payment_method_zz, payment_method_wh, establishment_date,
                                          product_status, is_currency)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.fullProductName,jdbcType=VARCHAR}, #{e.productCode,jdbcType=VARCHAR},
             #{e.valuationTime,jdbcType=VARCHAR},
             #{e.structuredIs}, #{e.electronicDirectConnectionIs}, #{e.custodianBank,jdbcType=VARCHAR},
             #{e.secondaryValuationSecurityType,jdbcType=VARCHAR},
             #{e.accountName,jdbcType=VARCHAR}, #{e.accountNumberDetail,jdbcType=VARCHAR},
             #{e.openingBank,jdbcType=VARCHAR}, #{e.largePaymentNumber,jdbcType=VARCHAR},
             #{e.autoDeductionIs}, #{e.centralDebtAccountNumber,jdbcType=VARCHAR},
             #{e.foreignExchangeCenterMemberCode,jdbcType=VARCHAR}, #{e.clearingHouseHolderAccount,jdbcType=VARCHAR},
             #{e.holderAccountNumber,jdbcType=VARCHAR}, #{e.holderAccountFullName,jdbcType=VARCHAR},
             #{e.netAssetValueWarningThreshold,jdbcType=VARCHAR}, #{e.productCategory,jdbcType=INTEGER},
             #{e.paymentMethodSQ,jdbcType=VARCHAR}, #{e.paymentMethodZZ,jdbcType=VARCHAR},
             #{e.paymentMethodWH,jdbcType=VARCHAR}, #{e.establishmentDate,jdbcType=VARCHAR},
             #{e.productStatus,jdbcType=INTEGER}, #{e.currencyIs})
        </foreach>
    </insert>

    <insert id="saveBatchFundInfo">
        insert into account_fund_information(id, product_id, channel_type, administrator, account_name, account_number,
                                             email_suffix)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.productId,jdbcType=VARCHAR}, #{e.channelType},
             #{e.administrator,jdbcType=VARCHAR}, #{e.accountName,jdbcType=VARCHAR},
             #{e.accountNumber,jdbcType=VARCHAR}, #{e.emailSuffix,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="fundAdminList" resultType="java.lang.String">
        select distinct administrator
        from account_fund_information
    </select>
</mapper>
