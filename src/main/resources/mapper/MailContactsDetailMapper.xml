<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.MailContactsDetailMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.MailContactsDetail">
    <!--@mbg.generated-->
    <!--@Table om_mail_contacts_detail-->
    <id column="contacts_id" jdbcType="VARCHAR" property="contactsId" />
    <id column="split_type" jdbcType="VARCHAR" property="splitType" />
    <id column="value" jdbcType="VARCHAR" property="value" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    contacts_id, split_type, `value`
  </sql>
</mapper>