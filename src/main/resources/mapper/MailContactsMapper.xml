<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailContactsMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.mail.vo.MailContactsListVo">
        select t1.id,
               t1.update_by as updateByName,
               t1.update_time,
               t4.job_name  as taskName,
               t1.without_attachment_is_send,
               t1.category,
               t1.type,
               t2.template_name,
               t1.task_id,
               t1.template_id
        from om_mail_contacts t1
                 left join base_cron t4 on t1.task_id = t4.job_id
                 left join om_mail_template t2 on t2.id = t1.template_id
        <where>
            and t1.deleted = '0'
            <if test="taskId != null and taskId != ''">
                and t1.task_id = #{taskId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select t1.id, t2.task_id as name
        from om_mail_contacts t1
                 left join om_mail_contacts_task_relation t2 on t1.id = t2.contacts_id
        where deleted = '0'
    </select>

    <delete id="delete">
        delete
        from om_mail_contacts where id in
        <foreach collection="list" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getById" resultType="cn.sdata.om.al.entity.mail.vo.MailContactsVo">
        select id,
               template_id,
               type,
               category,
               task_id,
               without_attachment_is_send,
               `handler`,
               GROUP_CONCAT(DISTINCT omcd.value SEPARATOR ',') as typeValues,
               t1.`handler` as `handler`,
               immediate_send as immediate_send
        from om_mail_contacts t1
            left join om_mail_contacts_detail omcd on t1.id = omcd.contacts_id
        where t1.id = #{id,jdbcType=VARCHAR}
          and t1.deleted = '0' group by id, template_id, type, category, task_id, without_attachment_is_send,t1.`handler`,immediate_send
    </select>

    <insert id="save">
        insert into om_mail_contacts(id, template_id, create_by, create_time, deleted, type, category, task_id,
                                     without_attachment_is_send, `handler`,immediate_send)
        values (#{id,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR},
                #{createByName,jdbcType=VARCHAR}, now(), '0',
                #{type,jdbcType=VARCHAR},
                #{category,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{withoutAttachmentIsSend}, #{handler}, #{immediateSend})
    </insert>

    <update id="update">
        update om_mail_contacts
        <trim prefix="set" suffixOverrides=",">
            <if test="templateId != null and templateId != ''">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="withoutAttachmentIsSend != null">
                without_attachment_is_send = #{withoutAttachmentIsSend},
            </if>
            <if test="handler != null">
                handler = #{handler},
            </if>
            <if test="immediateSend != null">
                immediate_send = #{immediateSend},
            </if>
            update_by   = #{updateByName,jdbcType=VARCHAR},
            update_time = now()
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteContactsAndTaskRelation">
        delete
        from om_mail_contacts_task_relation
        where contacts_id = #{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteContactsAndAccountSetRelation">
        delete
        from om_mail_contacts_account_set_relation
        where contacts_id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="saveContactsAndTaskRelation">
        insert into om_mail_contacts_task_relation(id, task_id, contacts_id) values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.taskId,jdbcType=VARCHAR}, #{e.contactsId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="saveContactsAndAccountSetRelation">
        insert into om_al.om_mail_contacts_account_set_relation(id, account_set_id, contacts_id) values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.accountSetId,jdbcType=VARCHAR}, #{e.contactsId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getAccountSetByTemplateId" resultType="string">
        select account_set_id
        from om_mail_contacts_account_set_relation
        where contacts_id in (select id from om_mail_contacts where template_id = #{templateId,jdbcType=VARCHAR})
    </select>

    <select id="accountSetList" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, name
        from om_mail_account_set
    </select>

    <select id="getAllRecipient" resultType="java.lang.String">
        select recipient
        from om_mail_contacts
    </select>

    <select id="getMailContactsType" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, name
        from om_mail_contacts_type
    </select>

    <select id="getMailContactsCategoryByType" resultType="cn.sdata.om.al.entity.CommonEntity">
        select t1.id, t1.name
        from om_mail_contacts_category t1
                 left join om_mail_contacts_type_category_relation t2 on t1.id = t2.category_id
        where t2.type_id = #{typeId,jdbcType=VARCHAR}
    </select>

    <delete id="batchDeleteContactsAndTaskRelation">
        delete
        from om_mail_contacts_task_relation where contacts_id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="batchDeleteContactsAndAccountSetRelation">
        delete
        from om_mail_contacts_account_set_relation where contacts_id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getAccountSetByIds" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, full_product_name as name
        from account_information where
        <foreach collection="list" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getMailContactsByAccountSetIdsAndTemplateId" resultType="java.lang.String">
        select contacts_id
        from om_mail_contacts_account_set_relation t1
                 inner join om_mail_contacts t2 on t1.contacts_id = t2.id where t2.template_id = #{templateId,jdbcType=VARCHAR}
                                                                            and t1.account_set_id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getAllContactsAccountId" resultType="cn.sdata.om.al.entity.CommonEntity">
        select contacts_id as id, account_set_id as name
        from om_mail_contacts_account_set_relation a
                 inner join om_mail_contacts b on a.contacts_id = b.id
                 inner join om_mail_template c on b.template_id = c.id
        where b.template_id = #{templateId};
    </select>

    <select id="getTemplateIdByName" resultType="java.lang.String">
        select template_id
        from net_value_disclosure_mail
        where name = #{name}
    </select>

    <select id="getAccountSetByContactsId" resultType="cn.sdata.om.al.entity.CommonEntity">
        select t1.account_set_id as id, t2.full_product_name as name
        from om_mail_contacts_account_set_relation t1
                 left join
             account_information t2 on t1.account_set_id = t2.id
        where t1.contacts_id = #{contactsId,jdbcType=VARCHAR}
    </select>

    <select id="getInfoByType" resultType="cn.sdata.om.al.entity.mail.vo.MailContactsVo">
        select id, template_id, type, category, task_id, without_attachment_is_send from om_mail_contacts where type = #{type}
        <if test="category != null">
            and category = #{category}
        </if>
    </select>

    <select id="getInfoByTaskId" resultType="cn.sdata.om.al.entity.mail.vo.MailContactsVo">
        select id,
               max(template_id) as template_id,
               max(type) as type,
               max(category) as category,
               max(task_id) as task_id,
               max(without_attachment_is_send) as without_attachment_is_send,
               group_concat(distinct omcd.value separator ',') as typeValues,
               max(`handler`) as `handler`,
               max(immediate_send) as immediate_send
        from om_mail_contacts omc
                 left join om_mail_contacts_detail omcd on omc.id = omcd.contacts_id and omc.type = omcd.split_type
        where task_id = #{taskId} group by omc.id
    </select>
</mapper>
