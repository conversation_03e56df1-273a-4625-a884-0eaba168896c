<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailContentMapper">
    <insert id="save">
        insert into om_mail_content(id, message_number, subject, receive, contentType, content, attachments,
                                    received_date, sent_date, box_id, pick_status, mail_from, received_time)
        values (#{id,jdbcType=VARCHAR}, #{messageNumber,jdbcType=INTEGER}, #{subject,jdbcType=VARCHAR},
                #{receiveStr,jdbcType=VARCHAR},
                #{contentType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{attachmentStr,jdbcType=VARCHAR},
                #{receivedDate,jdbcType=VARCHAR}, #{sentDate,jdbcType=VARCHAR}, #{boxId,jdbcType=VARCHAR},
                #{pickStatus}, #{mailFromStr,jdbcType=VARCHAR}, #{receivedTime,jdbcType=TIMESTAMP})
    </insert>

    <select id="getMaxNumber" resultType="int">
        select ifnull(max(message_number), 0)
        from om_mail_content
    </select>

    <select id="getById" resultType="cn.sdata.om.al.entity.mail.MailContent">
        select id,
               message_number,
               subject,
               receive     as receiveStr,
               contentType,
               content,
               attachments as attachmentStr,
               received_date,
               sent_date,
               box_id,
               pick_status,
               mail_from   as mailFromStr,
               received_time
        from om_mail_content
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="list" resultType="cn.sdata.om.al.entity.mail.vo.MailContentListVo">
        select id,
               subject       as title,
               t1.mail_from  as sender,
               sent_date     as sendDate,
               received_date as receiveDate
        from om_mail_content t1
        <where>
            <if test="title != null and title != ''">
                and subject like concat('%', #{title,jdbcType=VARCHAR}, '%')
            </if>
            <if test="content != null and content != ''">
                and content like concat('%', #{content,jdbcType=VARCHAR}, '%')
            </if>
            <if test="pickBasket != null and pickBasket != ''">
                and box_id = #{pickBasket,jdbcType=VARCHAR}
            </if>
            <if test="attachmentName != null and attachmentName != ''">
                and (select group_concat(name) from om_mail_attachment where mail_id = t1.id) like
                    concat('%', #{attachmentName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="beginReceiveDate != null and beginReceiveDate != ''">
                and received_date &gt;= #{beginReceiveDate,jdbcType=VARCHAR}
            </if>
            <if test="endReceiveDate != null and endReceiveDate != ''">
                and received_date &lt;= #{endReceiveDate,jdbcType=VARCHAR}
            </if>
            <if test="beginSendDate != null and beginSendDate != ''">
                and sent_date &gt;= #{beginSendDate,jdbcType=VARCHAR}
            </if>
            <if test="endSendDate != null and endSendDate != ''">
                and sent_date &lt;= #{endSendDate,jdbcType=VARCHAR}
            </if>
            <if test="sender != null and sender != ''">
                and mail_from like concat('%', #{sender,jdbcType=VARCHAR}, '%')
            </if>
            <if test="receiver != null and receiver != ''">
                and receive like concat('%', #{receiver,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by received_time desc
    </select>

    <update id="move">
        update om_mail_content
        set box_id = #{boxId,jdbcType=VARCHAR}
        where id = #{contentId,jdbcType=VARCHAR}
    </update>

    <update id="doPick">
        update om_mail_content
        set box_id = #{boxId,jdbcType=VARCHAR} where id in
        <foreach collection="contentIds" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectUnPickMail" resultType="cn.sdata.om.al.entity.mail.MailContent">
        select id,
               message_number,
               subject,
               receive     as receiveStr,
               contentType,
               content,
               attachments as attachmentStr,
               received_date,
               sent_date,
               box_id,
               pick_status,
               mail_from   as mailFromStr,
               received_time
        from om_mail_content
        where box_id = '1'
           or pick_status in (0, 1)
    </select>

    <select id="selectAllPickMail" resultType="cn.sdata.om.al.entity.mail.MailContent">
        select id,
               message_number,
               subject,
               receive     as receiveStr,
               contentType,
               content,
               attachments as attachmentStr,
               received_date,
               sent_date,
               box_id,
               pick_status,
               mail_from   as mailFromStr,
               received_time
        from om_mail_content
    </select>

    <select id="selectRuleNameByBoxId" resultType="java.lang.String">
        select group_concat(condition_value)
        from om_mail_pick_rule_condition
        where rule_id = (select rule_id from om_mail_pick_basket where id = #{boxId,jdbcType=VARCHAR} limit 1)
          and condition_key = 'subject'
        group by rule_id
    </select>

    <select id="listAllContent" resultType="cn.sdata.om.al.entity.mail.MailContent">
        select id,
        message_number,
        subject,
        receive as receiveStr,
        contentType,
        content,
        attachments as attachmentStr,
        received_date,
        sent_date,
        box_id,
        pick_status,
        mail_from as mailFromStr,
        received_time
        from om_mail_content
        <if test="ids != null and ids.size() != 0">
            where id in (<foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>)
        </if>
    </select>
</mapper>
