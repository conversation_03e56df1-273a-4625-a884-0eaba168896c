<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="cn.sdata.om.al.mapper.mail.MailInfoMapper">
    <select id="getAttachementsByMailId" resultType="cn.sdata.om.al.entity.mail.MailAttachment">
        select id, url as filePath, name as fileName, mail_id
        from om_mail_attachment
        where mail_id = #{id}
        group by name
    </select>

    <select id="getById" resultMap="getByIdMap">
        select t1.id,
               sender,
               recipient,
               ccTo,
               content,
               title
        from om_mail_info t1
        where t1.id = #{mailId,jdbcType=VARCHAR}
          and deleted = '0'
    </select>
    <select id="getMailInfoByMailIds" resultType="cn.sdata.om.al.entity.mail.MailInfo">
        SELECT omi.id,
               omi.send_status,
               omi.create_time,
               omi.title,
               omi.sender,
               omi.recipient,
               omi.ccTo
        FROM om_mail_info omi
        WHERE omi.id IN
        <foreach open="(" collection="list" separator="," close=")" item="mailId">
            #{mailId}
        </foreach>
    </select>

    <resultMap id="getByIdMap" type="cn.sdata.om.al.entity.mail.vo.MailDetailVo">
        <id column="id" property="mailId"/>
        <result column="ccTo" property="ccTo"/>
        <result column="sender" property="sender"/>
        <result column="recipient" property="recipient"/>
        <result column="content" property="content"/>
        <result column="title" property="title"/>
        <collection property="mailAttachments" ofType="cn.sdata.om.al.entity.mail.MailAttachment"
                    column="id" select="getAttachementsByMailId"/>
    </resultMap>

    <insert id="save">
        insert into om_mail_info(id, sender, recipient, ccTo, content, template_id, contacts_id, send_status, create_by,
                                 create_time, deleted, title)
        values (#{id,jdbcType=VARCHAR}, #{sender,jdbcType=VARCHAR}, #{recipient,jdbcType=VARCHAR},
                #{ccTo,jdbcType=VARCHAR},
                #{content,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{contactsId,jdbcType=VARCHAR},
                #{sendStatus}, 'admin', now(), '0', #{title,jdbcType=VARCHAR})
    </insert>
</mapper>
