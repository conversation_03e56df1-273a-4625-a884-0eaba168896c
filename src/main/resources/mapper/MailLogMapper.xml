<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailLogMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.mail.vo.MailLogListVo">
        select id,
               mail_id,
               title,
               send_time,
               sender,
               recipient,
               cc_to
        from om_mail_send_log
        <where>
            <if test="recipient != null and recipient != ''">
                and recipient like concat('%', #{recipient,jdbcType=VARCHAR}, '%')
            </if>
            <if test="beginSendTime != null and beginSendTime != ''">
                and send_time &gt;= str_to_date(#{beginSendTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="endSendTime != null and endSendTime != ''">
                and send_time &lt;= str_to_date(#{endSendTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="title != null and title != ''">
                and title like concat('%', #{title,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by send_time desc
    </select>

    <insert id="save">
        insert into om_mail_send_log(id, mail_id, title, send_time, sender, recipient, cc_to, send_status, task_name,
                                     task_status)
        values (#{id,jdbcType=VARCHAR}, #{mailId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
                #{sendTime,jdbcType=TIMESTAMP},
                #{sender,jdbcType=VARCHAR}, #{recipient,jdbcType=VARCHAR}, #{ccTo,jdbcType=VARCHAR}, #{sendStatus},
                #{taskName,jdbcType=VARCHAR},
                #{taskStatus})
    </insert>
</mapper>
