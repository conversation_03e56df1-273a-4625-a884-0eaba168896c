<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailMacroMapper">
    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, field_type as name
        from om_mail_macro
        <where>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <select id="all" resultType="cn.sdata.om.al.entity.CommonEntity">
        select field_type as id, value as name
        from om_mail_macro
        <where>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="page" resultType="cn.sdata.om.al.entity.mail.MailMacroMap">
        select id, module_name, field_type, value
        from om_mail_macro
        <where>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
