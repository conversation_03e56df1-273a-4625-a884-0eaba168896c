<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailPickBasketMapper">
    <select id="list" resultType="cn.sdata.om.al.entity.mail.vo.MailPickBasketVo">
        select t1.id, t1.name, t1.rule_id
        from om_mail_pick_basket t1
        order by basket_order
    </select>

    <insert id="save">
        insert into om_mail_pick_basket(id, name, rule_id, basket_order)
        values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{ruleId,jdbcType=VARCHAR}, #{basketOrderBy})
    </insert>

    <delete id="delete">
        delete
        from om_mail_pick_basket
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update">
        update om_mail_pick_basket
        <trim prefix="set" suffixOverrides=",">
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            rule_id = #{ruleId,jdbcType=VARCHAR}
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getMaxOrderBy" resultType="int">
        select max(basket_order)
        from om_mail_pick_basket
    </select>

    <select id="selectPickBasketCount" resultType="int">
        select count(id)
        from om_mail_content
        where box_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getById" resultType="cn.sdata.om.al.entity.mail.vo.MailPickBasketVo">
        select id, name, rule_id, basket_order
        from om_mail_pick_basket
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <update id="updateMailToUnPick">
        update om_mail_content
        set box_id      = '1',
            pick_status = '0'
        where box_id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateRuleIdToNull">
        update om_mail_pick_basket
        set rule_id = null
        where rule_id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="bindRule">
        update om_mail_pick_basket
        set rule_id = #{ruleId,jdbcType=VARCHAR}
        where id = #{pickBasketId,jdbcType=VARCHAR}
    </update>

    <select id="selectPickBasketCountByConditions" resultType="int">
        select count(id)
        from om_mail_content t1
        <where>
            and box_id = #{id,jdbcType=VARCHAR}
            <if test="p.title != null and p.title != ''">
                and subject like concat('%', #{p.title,jdbcType=VARCHAR}, '%')
            </if>
            <if test="p.content != null and p.content != ''">
                and content like concat('%', #{p.content,jdbcType=VARCHAR}, '%')
            </if>
            <if test="p.attachmentName != null and p.attachmentName != ''">
                and (select group_concat(name) from om_mail_attachment where mail_id = t1.id) like
                    concat('%', #{p.attachmentName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="p.beginReceiveDate != null and p.beginReceiveDate != ''">
                and received_date &gt;= #{p.beginReceiveDate,jdbcType=VARCHAR}
            </if>
            <if test="p.endReceiveDate != null and p.endReceiveDate != ''">
                and received_date &lt;= #{p.endReceiveDate,jdbcType=VARCHAR}
            </if>
            <if test="p.beginSendDate != null and p.beginSendDate != ''">
                and sent_date &gt;= #{p.beginSendDate,jdbcType=VARCHAR}
            </if>
            <if test="p.endSendDate != null and p.endSendDate != ''">
                and sent_date &lt;= #{p.endSendDate,jdbcType=VARCHAR}
            </if>
            <if test="p.sender != null and p.sender != ''">
                and mail_from like concat('%', #{p.sender,jdbcType=VARCHAR}, '%')
            </if>
            <if test="p.receiver != null and p.receiver != ''">
                and receive like concat('%', #{p.receiver,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>
</mapper>
