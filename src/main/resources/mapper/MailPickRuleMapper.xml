<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailPickRuleMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.mail.vo.MailPickRuleListVo">
        select t1.id, rule_name, t2.id as pick_basket, update_by as updateByName, update_time, t2.name as pickBasketName
        from om_mail_pick_rule t1
                 left join om_mail_pick_basket t2 on t1.id = t2.rule_id
        <where>
            <if test="ruleId != null and ruleId != ''">
                and t1.id = #{ruleId,jdbcType=VARCHAR}
            </if>
            <if test="pickBasket != null and pickBasket != ''">
                and t2.id = #{pickBasket,jdbcType=VARCHAR}
            </if>
            <if test="beginUpdateTime != null and beginUpdateTime != ''">
                and update_time &gt;= str_to_date(#{beginUpdateTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="endUpdateTime != null and endUpdateTime != ''">
                and update_time &lt;= str_to_date(#{endUpdateTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
        </where>
    </select>

    <delete id="deleteConditionsByRuleId">
        delete
        from om_mail_pick_rule_condition
        where rule_id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="update">
        update om_mail_pick_rule
        <trim prefix="set" suffixOverrides=",">
            <if test="attachmentPassword != null and attachmentPassword != ''">
                attachment_password = #{attachmentPassword,jdbcType=VARCHAR},
            </if>
            <if test="ruleName != null and ruleName != ''">
                rule_name = #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="executeType != null and executeType != ''">
                execute_type = #{executeType,jdbcType=VARCHAR},
            </if>
            <if test="ruleExpression != null and ruleExpression != ''">
                rule_expression = #{ruleExpression,jdbcType=VARCHAR},
            </if>
            update_by = #{updateBy,jdbcType=VARCHAR},
            update_time = now()
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="save">
        insert into om_mail_pick_rule(id, rule_name, execute_type, pick_basket, attachment_password, create_by,
                                      create_time, deleted, rule_expression, update_by, update_time)
        values (#{id,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, #{executeType,jdbcType=VARCHAR},
                #{pickBasket,jdbcType=VARCHAR},
                #{attachmentPassword,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR}, now(), '0', #{ruleExpression,jdbcType=VARCHAR},
                #{updateBy,jdbcType=VARCHAR}, now())
    </insert>

    <insert id="batchSaveConditions">
        insert into om_mail_pick_rule_condition(id, condition_key, condition_operate, condition_value, rule_id, type)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.conditionKey,jdbcType=VARCHAR}, #{e.conditionOperate,jdbcType=VARCHAR},
             #{e.conditionValue,jdbcType=VARCHAR}, #{e.ruleId,jdbcType=VARCHAR}, #{e.type,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getById" resultMap="getByIdMap">
        select t1.id,
               t1.rule_name,
               execute_type,
               t2.id as pick_basket,
               attachment_password,
               rule_expression
        from om_mail_pick_rule t1
                 left join om_mail_pick_basket t2 on t1.id = t2.rule_id
        where t1.id = #{id,jdbcType=VARCHAR}
    </select>

    <resultMap id="getByIdMap" type="cn.sdata.om.al.entity.mail.vo.MailPickRuleDetail">
        <id property="id" column="id"/>
        <result column="rule_name" property="ruleName"/>
        <result column="execute_type" property="executeType"/>
        <result column="pick_basket" property="pickBasket"/>
        <result column="attachment_password" property="attachmentPassword"/>
        <result column="rule_expression" property="ruleExpression"/>
        <collection property="executeConditions" ofType="cn.sdata.om.al.entity.mail.MailFilterRuleDTO" column="id"
                    select="getConditionByRuleId"/>
    </resultMap>

    <select id="getConditionByRuleId" resultType="cn.sdata.om.al.entity.mail.MailFilterRuleDTO">
        select id, condition_key as target, condition_operate as "condition", condition_value as contentListStr, type
        from om_mail_pick_rule_condition
        where rule_id = #{id}
    </select>

    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, rule_name as name
        from om_mail_pick_rule
    </select>

    <delete id="delete">
        delete
        from om_mail_pick_rule where id in
        <foreach collection="list" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="batchDeleteConditionsByRuleId">
        delete
        from om_mail_pick_rule_condition where rule_id in
        <foreach collection="list" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectAllRule" resultMap="getByIdMap">
        select id,
               rule_name,
               execute_type,
               pick_basket,
               attachment_password,
               rule_expression
        from om_mail_pick_rule
    </select>

    <select id="unbindRuleList" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, rule_name as name
        from om_mail_pick_rule
        where id not in (select rule_id from om_mail_pick_basket)
    </select>

    <update id="updateBasketRuleToNull">
        update om_mail_pick_basket
        set rule_id = null where rule_id in
        <foreach collection="list" open="(" close=")" separator="," item="e">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
