<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.mail.MailTemplateMapper">
    <delete id="batchDeleteByIds">
        delete
        from om_mail_template where id in
        <foreach collection="list" item="e" separator="," open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>


    <update id="update">
        update om_mail_template
        <trim prefix="set" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">
                template_name = #{templateName,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != ''">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != ''">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null and templateId != ''">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="mailTemplateExtStr != null and mailTemplateExtStr != ''">
                ext_info = #{mailTemplateExtStr,jdbcType=VARCHAR},
            </if>
            <if test="templateType != null and templateType != ''">
                template_type = #{templateType,jdbcType=VARCHAR},
            </if>
            update_by   = #{updateByName,jdbcType=VARCHAR},
            update_time = now()
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="save">
        insert into om_mail_template(id, template_name, title, content,
                                     create_by, create_time, deleted, template_id, ext_info, template_type, update_time)
        values (#{id,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
                #{content,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR}, now(), '0',
                #{templateId,jdbcType=VARCHAR},
                #{mailTemplateExtStr,jdbcType=VARCHAR}, #{templateType,jdbcType=VARCHAR}, now())
    </insert>


    <select id="page" resultType="cn.sdata.om.al.entity.mail.vo.MailTemplateListVo">
        select t1.id,
               t1.template_name,
               t1.update_by as updateByName,
               t1.update_time,
               t1.ext_info  as extInfoStr,
               t1.template_type
        from om_mail_template t1
        <where>
            <if test="templateId != null and templateId != ''">
                and t1.id = #{templateId,jdbcType=VARCHAR}
            </if>
            <if test="beginUpdateTime != null and beginUpdateTime != ''">
                and t1.update_time &gt;= str_to_date(#{beginUpdateTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="endUpdateTime != null and endUpdateTime != ''">
                and t1.update_time &lt;= str_to_date(#{endUpdateTime,jdbcType=VARCHAR}, '%Y-%m-%d')
            </if>
            <if test="templateType != null and templateType != ''">
                and t1.template_type = #{templateType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, template_name as name
        from om_mail_template
    </select>

    <select id="getById" resultType="cn.sdata.om.al.entity.mail.vo.MailTemplateDetailVo">
        select id,
               template_name,
               title,
               content,
               template_id,
               ext_info as mailTemplateExtStr,
               template_type
        from om_mail_template
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getAccountSetList" resultType="cn.sdata.om.al.entity.CommonEntity">
        select t1.id, t1.name
        from om_mail_account_set t1
                 left join om_al.om_mail_account_set_field_relation t2 on t1.id = t2.account_set_id
        where t2.field_type_id = #{fieldTypeId,jdbcType=VARCHAR}
    </select>

    <select id="getFieldTypeList" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, name
        from om_mail_field_type
    </select>
</mapper>
