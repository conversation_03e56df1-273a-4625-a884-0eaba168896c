<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.MarketTradeDayMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.MarketTradeDay">
            <result property="tradeDayType" column="VC_TRADEDAY_TYPE" jdbcType="VARCHAR"/>
            <result property="date" column="L_DATE" jdbcType="DECIMAL"/>
            <result property="tradeFlag" column="C_TRADE_FLAG" jdbcType="CHAR"/>
            <result property="week" column="L_WEEK" jdbcType="DECIMAL"/>
            <result property="virtualKey" column="L_VIRTUAL_KEY" jdbcType="DECIMAL"/>
            <result property="timestamp" column="VC_TIMESTAMP" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        VC_TRADEDAY_TYPE,L_DATE,C_TRADE_FLAG,
        L_WEEK,L_VIRTUAL_KEY,VC_TIMESTAMP
    </sql>

    <select id="ltTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE &lt; #{numberDate}
        <if test="tradeType != null and tradeType != ''">
            AND VC_TRADEDAY_TYPE = #{tradeType}
        </if>
        <if test="tradeFlag != null and tradeFlag != ''">
            AND C_TRADE_FLAG = #{tradeFlag}
        </if>
        ORDER BY L_DATE DESC
        LIMIT 1;
    </select>

    <select id="getAllTradeDay" resultType="java.lang.String">
        select L_DATE
        from market_trade_day
        where VC_TRADEDAY_TYPE = #{tradeDayType}
          and C_TRADE_FLAG = 1
        order by L_DATE
    </select>


    <select id="eqTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE = #{numberDate}
          AND VC_TRADEDAY_TYPE = #{tradeType}
          AND C_TRADE_FLAG = #{tradeFlag}
        LIMIT 1
    </select>

    <select id="geTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE >= #{numberDate}
          AND VC_TRADEDAY_TYPE = #{tradeType}
          AND C_TRADE_FLAG = #{tradeFlag}
        ORDER BY L_DATE
        LIMIT 1;
    </select>

    <select id="leTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE &lt;= #{numberDate} AND VC_TRADEDAY_TYPE = #{tradeType} AND C_TRADE_FLAG = #{tradeFlag}
        ORDER BY L_DATE DESC
        LIMIT 1;
    </select>

    <select id="likeTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE C_TRADE_FLAG = 1
          AND L_DATE LIKE CONCAT('%', #{date}, '%')
          AND VC_TRADEDAY_TYPE = #{tradeType}
        GROUP BY L_DATE
        ORDER BY L_DATE
    </select>

    <select id="getThisMonthSpecialTradeDay" resultType="java.lang.String">
        SELECT CONCAT(
                       SUBSTRING(L_DATE, 1, 4), '-',
                       SUBSTRING(L_DATE, 5, 2), '-',
                       SUBSTRING(L_DATE, 7, 2)
               ) AS formatted_date
        FROM market_trade_day
        WHERE L_DATE LIKE CONCAT(#{ym}, '%')
          AND C_SPECIAL = 1
        GROUP BY L_DATE
        ORDER BY L_DATE;
    </select>

    <select id="getThisMonthNoTradeDay" resultType="java.lang.String">
        select substr(L_DATE, 0, 4) || '-' || substr(L_DATE, 5, 2) || '-' ||
        substr(L_DATE, 7, 2)
        from market_trade_day
        where L_DATE like #{ym} || '%'
          and C_TRADE_FLAG != 1
          and VC_TRADEDAY_TYPE in (02, 03)
        group by L_DATE
        order by L_DATE
    </select>

    <select id="getOffsetTradeDayPositive" resultMap="BaseResultMap">
        SELECT vc_tradeday_type, l_date, c_trade_flag, l_week, l_virtual_key, vc_timestamp
        FROM market_trade_day
        WHERE VC_TRADEDAY_TYPE = #{tradeDayType}
          AND C_TRADE_FLAG = 1
          AND L_DATE &gt; #{baseDate}
        ORDER BY L_DATE
        LIMIT #{offset}, 1
    </select>

    <select id="getOffsetTradeDayNegative" resultMap="BaseResultMap">
        SELECT vc_tradeday_type, l_date, c_trade_flag, l_week, l_virtual_key, vc_timestamp
        FROM market_trade_day
        WHERE VC_TRADEDAY_TYPE = #{tradeDayType}
          AND C_TRADE_FLAG = 1
          AND L_DATE &lt; #{baseDate}
        ORDER BY L_DATE DESC
        LIMIT #{offset}, 1
    </select>

    <select id="getOffsetTradeDayZero" resultMap="BaseResultMap">
        SELECT vc_tradeday_type, l_date, c_trade_flag, l_week, l_virtual_key, vc_timestamp
        FROM market_trade_day
        WHERE VC_TRADEDAY_TYPE = #{tradeDayType}
          AND C_TRADE_FLAG = 1
          AND L_DATE = #{baseDate}
        LIMIT 1
    </select>

    <select id="getNextHolidayList" resultType="java.lang.String">
        select l_date
        from market_trade_day
        where vc_tradeday_type = #{tradeDayType}
          and l_date > #{baseDate}
          and c_trade_flag = 2
        order by l_date
        limit 12
    </select>
    <select id="gtTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE > #{numberDate}
          AND VC_TRADEDAY_TYPE = #{tradeType}
          AND C_TRADE_FLAG = #{tradeFlag}
        ORDER BY L_DATE
            LIMIT 1
    </select>

    <select id="gtNTradeDay" resultType="java.lang.Integer">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE > #{numberDate}
        AND VC_TRADEDAY_TYPE = #{tradeType}
        AND C_TRADE_FLAG = #{tradeFlag}
        ORDER BY L_DATE
        LIMIT #{n}
    </select>

    <select id="getNonTradingDays" resultType="java.lang.String">
        SELECT L_DATE
        FROM market_trade_day
        WHERE L_DATE &gt;= #{begin,jdbcType=INTEGER}
          and L_DATE &lt;= #{end,jdbcType=INTEGER}
          AND VC_TRADEDAY_TYPE = '00'
          AND C_TRADE_FLAG = '2'
        ORDER BY L_DATE
    </select>
</mapper>
