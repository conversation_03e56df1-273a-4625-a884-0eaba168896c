<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.MonthEndAndNonStandardMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.MonthEndAndNonStandard">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="dataDate" column="data_date" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="securityType" column="security_type" jdbcType="VARCHAR"/>
            <result property="securityName" column="security_name" jdbcType="VARCHAR"/>
            <result property="securityCode" column="security_code" jdbcType="VARCHAR"/>
            <result property="securityCost" column="security_cost" jdbcType="VARCHAR"/>
            <result property="securityMarketValue" column="security_market_value" jdbcType="VARCHAR"/>
            <result property="roi" column="ROI" jdbcType="VARCHAR"/>
            <result property="proportionPrompt" column="proportion_prompt" jdbcType="VARCHAR"/>
            <result property="monthEndValuationPrice" column="month_end_valuation_price" jdbcType="VARCHAR"/>
            <result property="valuationPriceEndLastMonth" column="valuation_price_end_last_month" jdbcType="VARCHAR"/>
            <result property="priceVolatility" column="price_volatility" jdbcType="VARCHAR"/>
            <result property="volatilityAlert" column="volatility_alert" jdbcType="VARCHAR"/>
            <result property="interestPaymentDate" column="interest_payment_date" jdbcType="VARCHAR"/>
            <result property="exerciseDate" column="exercise_date" jdbcType="VARCHAR"/>
            <result property="dueDate" column="due_date" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,data_date,product_id,
        product_name,security_type,security_name,
        security_code,security_cost,security_market_value,
        ROI,proportion_prompt,month_end_valuation_price,
        valuation_price_end_last_month,price_volatility,volatility_alert,
        interest_payment_date,exercise_date,due_date
    </sql>
    <select id="selectInfo" resultType="cn.sdata.om.al.vo.MonthEndAndNonStandardVO">
        select me.*,
               ai.product_code as productCode,
               ai.full_product_name as productName
        from month_end_and_non_standard me
        left join  account_information  ai on   ai.id  = me.product_id
        <where>
                 me.data_date like concat(#{vo.dataDate},'%')
            <if test="vo.productNames != null and vo.productNames.size() > 0">
                and ai.full_product_name in
                <foreach collection="vo.productNames" open="(" separator="," item="name" close=")">
                    #{name}
                </foreach>
            </if>
            <if test="vo.securityTypes != null and vo.securityTypes.size() > 0">
                and me.security_type in
                <foreach collection="vo.securityTypes" open="(" separator="," item="type" close=")">
                    #{type}
                </foreach>
            </if>
            <if test="vo.securityCodes != null and vo.securityCodes.size() > 0">
                and me.security_code in
                <foreach collection="vo.securityCodes" open="(" separator="," item="code" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        order by data_date desc
    </select>
    <select id="seleDataDate" resultType="java.lang.String">
        select data_date from month_end_and_non_standard order by data_date desc limit 1
    </select>
    <select id="getSecondDay" resultType="java.lang.String">
        select l_date
        from market_trade_day
        where vc_tradeday_type = '01'
          and l_date like concat(#{date},'%')
          and c_trade_flag = 1
        order by l_date
            limit 1,1

    </select>
    <select id="secListInfo" resultType="cn.sdata.om.al.qrtz.vo.KeyValueVO">
        select security_code as `key`,
        security_name as value
        from month_end_and_non_standard
        <where> security_name is not null
            <if test="securityTypes != null and securityTypes.size() > 0">
                and security_type in
                <foreach collection="securityTypes" open="(" separator="," item="type" close=")">
                    #{type}
                </foreach>
            </if>

        </where>
        group by security_code,security_name
    </select>

</mapper>
