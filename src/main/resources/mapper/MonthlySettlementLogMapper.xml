<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.MonthlySettlementLogMapper">
    <select id="getLogMSExportRecord" resultType="cn.sdata.om.al.entity.LogMSExportRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               data_date
        from log_ms_export_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLogMSFileOptRecord" resultType="cn.sdata.om.al.entity.LogMSFileOptRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               type,
               data_date
        from log_ms_file_opt_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLogMSRPARecord" resultType="cn.sdata.om.al.entity.LogMSRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type
        from log_ms_rpa_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getLogMSSendMailRecord" resultType="cn.sdata.om.al.entity.LogMSSendMailRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               task_id,
               task_name,
               type,
               mail_id      as mailIdStr,
               rpa_log_id,
               data_date,
               send_status
        from log_ms_send_mail_record
        <where>
            <if test="beginDataDate != null and beginDataDate != ''">
                and data_date &gt;= #{beginDataDate,jdbcType=VARCHAR}
            </if>
            <if test="endDataDate != null and endDataDate != ''">
                and data_date &lt;= #{endDataDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="saveExportLog">
        insert into log_ms_export_record(id, execute_time, create_by_name, params, status, data_date)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateExportLogById">
        update log_ms_export_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveFileOptLog">
        insert into log_ms_file_opt_record(id, execute_time, create_by_name, params, status, type, data_date)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR})
    </insert>

    <update id="updateFileOptLogById">
        update log_ms_file_opt_record
        set end_time  = #{endTime,jdbcType=VARCHAR},
            status    = #{status,jdbcType=VARCHAR},
            file_url  = #{fileUrl,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveRpaLog">
        insert into log_ms_rpa_record(id, execute_time, create_by_name, params, status, data_date, type)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{dataDate,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR})
    </insert>

    <select id="getRpaLogById" resultType="cn.sdata.om.al.entity.LogMSRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type
        from log_ms_rpa_record
        where rpa_log_id = #{logId,jdbcType=VARCHAR}
    </select>

    <update id="updateRpaLog">
        update log_ms_rpa_record
        set end_time   = #{endTime,jdbcType=VARCHAR},
            status     = #{status,jdbcType=VARCHAR},
            task_id    = #{taskId,jdbcType=VARCHAR},
            task_name  = #{taskName,jdbcType=VARCHAR},
            rpa_log_id = #{rpaLogId,jdbcType=VARCHAR},
            file_url   = #{fileUrl,jdbcType=VARCHAR},
            error_msg  = #{errorMsg,jdbcType=VARCHAR},
            result     = #{result,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="saveSendMailLog">
        insert into log_ms_send_mail_record(id, execute_time, create_by_name, params, status, task_id, task_name, type,
                                            rpa_log_id, data_date, end_time, send_status, mail_id)
        values (#{id,jdbcType=VARCHAR}, #{beginTime,jdbcType=VARCHAR}, #{createByName,jdbcType=VARCHAR},
                #{params,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR}, #{rpaLogId,jdbcType=VARCHAR},
                #{dataDate,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{sendStatus,jdbcType=VARCHAR},
                #{mailIdStr,jdbcType=VARCHAR})
    </insert>

    <select id="getMSSendMailLogByLogId" resultType="cn.sdata.om.al.entity.LogMSSendMailRecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               task_id,
               task_name,
               type,
               mail_id      as mailIdStr,
               rpa_log_id,
               data_date
        from log_ms_send_mail_record
        where rpa_log_id = #{rpaLogId,jdbcType=VARCHAR}
    </select>

    <update id="updateSendMailLogById">
        update log_ms_send_mail_record
        set mail_id   = #{mailIdStr,jdbcType=VARCHAR},
            status    =#{status,jdbcType=VARCHAR},
            error_msg = #{errorMsg,jdbcType=VARCHAR},
            result    = #{result,jdbcType=VARCHAR},
            end_time  = #{endTime,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getById" resultType="cn.sdata.om.al.entity.LogMSRPARecord">
        select id,
               execute_time as beginTime,
               create_by_name,
               end_time,
               error_msg,
               params,
               result,
               status,
               file_url,
               task_id,
               task_name,
               rpa_log_id,
               data_date,
               type
        from log_ms_rpa_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
</mapper>
