<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.MonthlySettlementMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.MonthlySettlementList">
        select id,
               folder_category,
               file_path,
               data_date,
               update_date,
               download_status,
               folder_order,
               file_name,
               folder_name,
               send_status,
               send_time,
               replace_file_name,
               type
        from monthly_settlement
        <where>
            <if test="dataDate != null and dataDate != ''">
                and data_date = #{dataDate,jdbcType=VARCHAR}
            </if>
            <if test="folderCategory != null and folderCategory != ''">
                and folder_category = #{folderCategory,jdbcType=VARCHAR}
            </if>
            <if test="downloadStatus != null and downloadStatus != ''">
                and download_status = #{downloadStatus,jdbcType=VARCHAR}
            </if>
            <if test="sendStatus != null and sendStatus != ''">
                and send_status = #{sendStatus,jdbcType=VARCHAR}
            </if>
        </where>
        order by folder_order, folder_name
    </select>

    <select id="selectMonthlySettlementByIds" resultType="cn.sdata.om.al.entity.MonthlySettlementList">
        select id,
               folder_category,
               file_name,
               file_path,
               data_date,
               update_date,
               download_status,
               folder_order,
               folder_name,
               send_status,
               send_time
        from monthly_settlement where id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
        order by folder_order, folder_name
    </select>

    <insert id="batchSave">
        insert into monthly_settlement(id, folder_category, file_name, file_path, data_date,
                                       update_date, download_status, folder_order, folder_name, download_date,
                                       send_status, send_time, type)
        values
        <foreach collection="list" separator="," item="e">
            (#{e.id,jdbcType=VARCHAR}, #{e.folderCategory,jdbcType=VARCHAR}, #{e.fileName,jdbcType=VARCHAR},
             #{e.filePath,jdbcType=VARCHAR},
             #{e.dataDate,jdbcType=VARCHAR}, #{e.updateDate,jdbcType=VARCHAR}, 0, #{e.folderOrder},
             #{e.folderName,jdbcType=VARCHAR},
             #{e.updateDate,jdbcType=VARCHAR}, #{e.sendStatus,jdbcType=VARCHAR}, #{e.sendTime,jdbcType=VARCHAR},
             #{e.type,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectAll" resultType="cn.sdata.om.al.entity.MonthlySettlementList">
        select id,
               folder_category,
               file_name,
               file_path,
               data_date,
               update_date,
               download_status,
               folder_order,
               folder_name,
               send_status,
               send_time
        from monthly_settlement
        order by folder_order
    </select>

    <delete id="deleteUploadData">
        delete
        from monthly_settlement where data_date = #{date,jdbcType=VARCHAR}
                                  and folder_name in
        <foreach collection="fNames" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByDate">
        delete
        from monthly_settlement
        where data_date = #{preMonthDate,jdbcType=VARCHAR}
    </delete>

    <select id="selectCountByDate" resultType="int">
        select count(id)
        from monthly_settlement
        where data_date = #{dataDate,jdbcType=VARCHAR}
    </select>

    <update id="updateFileInfo">
        update monthly_settlement
        set file_path         = #{path,jdbcType=VARCHAR},
            download_status   = '1',
            update_date       = date_format(now(), '%Y-%m-%d %H:%i:%s'),
            replace_file_name = #{replaceFileName,jdbcType=VARCHAR},
            type              = #{type,jdbcType=VARCHAR}
        where data_date = #{date,jdbcType=VARCHAR}
          and file_name = #{name,jdbcType=VARCHAR}
    </update>

    <update id="updateFileDownloadStatus">
        update monthly_settlement
        set download_status = #{status,jdbcType=VARCHAR} where data_date = #{date,jdbcType=VARCHAR}
                                                           and folder_order in
        <foreach collection="orders" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectFileNameByDate" resultType="java.lang.String">
        select file_name
        from monthly_settlement
        where data_date = #{date,jdbcType=VARCHAR}
          and folder_order = #{order,jdbcType=VARCHAR}
    </select>

    <select id="selectMonthlySettlementByDateAndOrders" resultType="cn.sdata.om.al.entity.MonthlySettlementList">
        select id,
               folder_category,
               file_name,
               file_path,
               data_date,
               update_date,
               download_status,
               folder_order,
               folder_name,
               send_status,
               send_time
        from monthly_settlement
        where data_date = #{date,jdbcType=VARCHAR}
          and folder_order in
        <foreach collection="orders" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
        order by folder_order
    </select>

    <update id="updateSendStatus">
        update monthly_settlement
        set send_time   = date_format(now(), '%Y-%m-%d %H:%i:%s'),
            send_status = #{status,jdbcType=VARCHAR}
        where data_date = #{date,jdbcType=VARCHAR}
          and download_status = '1'
          and folder_order in
        <foreach collection="orders" separator="," item="e" open="(" close=")">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateNoMatchFileNameStatus">
        update monthly_settlement
        set download_status = #{status,jdbcType=VARCHAR}
        where data_date = #{date,jdbcType=VARCHAR}
          and folder_order = #{order,jdbcType=VARCHAR}
          and download_status != '1'
    </update>

    <select id="countMatchFileName" resultType="int">
        select count(id)
        from monthly_settlement
        where data_date = #{date,jdbcType=VARCHAR}
          and file_name = #{fileName,jdbcType=VARCHAR}
    </select>

    <select id="selectDownloadStatus" resultType="int">
        select count(id)
        from monthly_settlement
        where data_date = #{date,jdbcType=VARCHAR}
          and folder_order = #{order,jdbcType=VARCHAR}
          and (download_status = '2' or download_status = '3')
    </select>

    <update id="updateFileDownloadStatusMatchProductId">
        update monthly_settlement
        set download_status = #{status,jdbcType=VARCHAR}
        where data_date = #{date,jdbcType=VARCHAR}
          and folder_order = #{order,jdbcType=VARCHAR}
          and file_name like concat(#{namePrefix,jdbcType=VARCHAR}, '%')
    </update>
</mapper>
