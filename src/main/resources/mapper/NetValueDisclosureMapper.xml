<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.NetValueDisclosureMapper">

  <select id="listPage" resultType="cn.sdata.om.al.vo.NetValueDisclosureVO">
      select
      a.id as id,
      a.account_id as productId,
      c.name as `groupName`,
      b.full_product_name,
      b.product_code,
      b.custodian_bank,
      b.is_electronic_direct_connection,
      a.is_revalued,
      a.asset_type,
      a.valuation_date,
      a.valuation_table_generated,
      a.custody_reconciliation_email_sent,
      a.reconciliation_status,
      a.valuation_table_confirmed,
      a.net_value_disclosed,
      a.valuation_table_downloaded,
      a.valuation_table_sent,
      a.investor_report_sent,
      a.third_party_sent
      from net_value_disclosure a
      left join account_information b on a.account_id = b.id
      left join account_set_group c on FIND_IN_SET(b.id, c.account_code) > 0
      where valuation_date between #{param.startDate} and #{param.endDate}
      <if test="param.valuationTime != null and param.valuationTime != ''">
          and b.valuation_time = #{param.valuationTime}
      </if>
      <if test="param.group != null and param.group.size > 0">
          and c.id in(<foreach collection="param.group" item="item" separator=",">
          #{item}
      </foreach> )
      </if>
      <if test="param.fullProductName != null and param.fullProductName.size > 0">
          and full_product_name in (<foreach collection="param.fullProductName" separator="," item="item">
          #{item}
      </foreach>)
    </if>
      <if test="param.productCode != null and param.productCode.size > 0">
          and product_code in (<foreach collection="param.productCode" separator="," item="item">
          #{item}
      </foreach>)
      </if>
      <if test="param.isElectronicDirectConnection != null">
          and is_electronic_direct_connection = #{param.isElectronicDirectConnection}
      </if>
      <if test="param.valuationTableGenerated != null">
          and valuation_table_generated = #{param.valuationTableGenerated}
      </if>
      <if test="param.valuationTableConfirmed != null">
          and valuation_table_confirmed = #{param.valuationTableConfirmed}
      </if>
      <if test="param.reconciliationStatus != null and param.reconciliationStatus != ''">
          and reconciliation_status = #{param.reconciliationStatus}
      </if>
      <if test="param.netValueDisclosed != null and param.netValueDisclosed != ''">
          and net_value_disclosed = #{param.netValueDisclosed}
      </if>
      <if test="param.custodyReconciliationEmailSent != null and param.custodyReconciliationEmailSent != ''">
          and custody_reconciliation_email_sent = #{param.custodyReconciliationEmailSent}
      </if>
      <if test="param.isRevalued != null">
          and is_revalued = #{param.isRevalued}
      </if>
      <if test="param.valuationTime == 'T1'">
          and b.product_category = 3
      </if>
    </select>
</mapper>