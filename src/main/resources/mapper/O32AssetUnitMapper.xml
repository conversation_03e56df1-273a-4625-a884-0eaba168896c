<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.O32AssetUnitMapper">
    <insert id="saveBatch">
        insert into o32_asset_unit(id, order_no, product_name, product_id, o32_fund_name, o32_asset_unit_name,
                                   o32_asset_unit_code)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id,jdbcType=VARCHAR}, #{e.orderNo}, #{e.productName,jdbcType=VARCHAR}, #{e.productId,jdbcType=VARCHAR},
             #{e.o32FundName,jdbcType=VARCHAR},
             #{e.o32AssetUnitName,jdbcType=VARCHAR}, #{e.o32AssetUnitCode,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="truncateTable">
        truncate table o32_asset_unit
    </update>

    <select id="page" resultType="cn.sdata.om.al.entity.O32AssetUnit">
        select id,
               order_no,
               product_name,
               product_id,
               o32_fund_name,
               o32_asset_unit_name,
               o32_asset_unit_code,
               update_by as updateByName,
               update_time
        from o32_asset_unit
        <where>
            <if test="productIds != null and productIds.size() > 0">
                and product_id in
                <foreach collection="productIds" open="(" separator="," item="e" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="assetUnitCodes != null and assetUnitCodes.size() > 0">
                and o32_asset_unit_code in
                <foreach collection="assetUnitCodes" open="(" separator="," item="e" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="assetUnitNames != null and assetUnitNames.size() > 0">
                and o32_asset_unit_name in
                <foreach collection="assetUnitNames" open="(" separator="," item="e" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getById" resultType="cn.sdata.om.al.entity.O32AssetUnit">
        select id,
               order_no,
               product_name,
               product_id,
               o32_fund_name,
               o32_asset_unit_name,
               o32_asset_unit_code,
               update_by as updateByName,
               update_time
        from o32_asset_unit
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <update id="update">
        update o32_asset_unit
        <trim prefix="set" suffixOverrides=",">
            <if test="productName != null and productName != ''">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productId != null and productId != ''">
                product_id = #{productId,jdbcType=VARCHAR},
            </if>
            <if test="o32AssetUnitName != null and o32AssetUnitName != ''">
                o32_asset_unit_name = #{o32AssetUnitName,jdbcType=VARCHAR},
            </if>
            <if test="o32AssetUnitCode != null and o32AssetUnitCode != ''">
                o32_asset_unit_code = #{o32AssetUnitCode,jdbcType=VARCHAR},
            </if>
            <if test="o32FundName != null and o32FundName != ''">
                o32_fund_name = #{o32FundName,jdbcType=VARCHAR},
            </if>
            update_time = #{updateTime,jdbcType=VARCHAR},
            update_by   = #{updateByName,jdbcType=VARCHAR}
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="insert">
        insert into o32_asset_unit(id, order_no, product_name, product_id, o32_fund_name, o32_asset_unit_name,
                                   o32_asset_unit_code, update_by, update_time)
        values (#{id,jdbcType=VARCHAR}, #{orderNo}, #{productName,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR}, #{o32FundName,jdbcType=VARCHAR},
                #{o32AssetUnitName,jdbcType=VARCHAR}, #{o32AssetUnitCode,jdbcType=VARCHAR},
                #{updateByName,jdbcType=VARCHAR},
                #{updateTime,jdbcType=VARCHAR})
    </insert>

    <select id="getMaxOrderNumber" resultType="int">
        select max(order_no)
        from o32_asset_unit
    </select>

    <select id="selectList" resultType="cn.sdata.om.al.entity.O32AssetUnit">
        select id,
               order_no,
               product_name,
               product_id,
               o32_fund_name,
               o32_asset_unit_name,
               o32_asset_unit_code,
               update_by as updateByName,
               update_time
        from o32_asset_unit
    </select>

    <select id="codeList" resultType="java.lang.String">
        select distinct o32_asset_unit_code
        from o32_asset_unit
    </select>

    <select id="nameList" resultType="java.lang.String">
        select distinct o32_asset_unit_name from o32_asset_unit
    </select>
</mapper>
