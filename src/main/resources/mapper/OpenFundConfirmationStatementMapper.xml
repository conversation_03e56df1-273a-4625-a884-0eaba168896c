<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.OpenFundConfirmationStatementMapper">

    <select id="pageQuery" resultType="cn.sdata.om.al.vo.OpenFundConfirmationVO">
        select ofcs.id,
        data_date,
        transaction_channel,
        GROUP_CONCAT(DISTINCT ai.full_product_name) AS productName,
        GROUP_CONCAT(DISTINCT ofct.business_type) AS business_type,
        email_id,
        email_rule_match_status,
        email_received_time,
        attachment_name,
        ocr_confirmation_status,
        email_sent_status,
        file_path
        from open_fund_confirmation_statement ofcs
        left join open_fund_confirmation_account ofca on ofcs.id = ofca.open_fund_id
        left join open_fund_confirmation_type ofct on ofcs.id = ofct.open_fund_id
        left join account_information ai on ofca.product_id = ai.id
        where ofcs.email_received_time
        between STR_TO_DATE(#{param.startReceiveDate}, '%Y-%m-%d %H:%i:%s')
        and STR_TO_DATE(#{param.endReceiveDate}, '%Y-%m-%d %H:%i:%s')
        <if test="param.startDataDate != null and param.startDataDate != '' and param.endDataDate != null and param.endDataDate != ''">
            and STR_TO_DATE(ofcs.data_date, '%Y-%m-%d')
            BETWEEN STR_TO_DATE(#{param.startDataDate}, '%Y-%m-%d')
            AND STR_TO_DATE(#{param.endDataDate}, '%Y-%m-%d')
        </if>
        <if test="param.productId != null and param.productId.size > 0">
            and ofca.product_id in (<foreach collection="param.productId" separator="," item="item">
            #{item}
        </foreach>)
        </if>
        <if test="param.transactionChannel != null and param.transactionChannel.size > 0">
            and ofcs.transaction_channel in (<foreach collection="param.transactionChannel" separator="," item="item">
            #{item}
        </foreach>)
        </if>
        <if test="param.businessType != null and param.businessType.size > 0">
            and ofct.business_type in (<foreach collection="param.businessType" separator="," item="item">
            #{item}
        </foreach>)
        </if>
        <if test="param.emailRuleMatchStatus != null and param.emailRuleMatchStatus.size > 0">
            and ofcs.email_rule_match_status in (<foreach collection="param.emailRuleMatchStatus" separator=","
                                                          item="item">
            #{item}
        </foreach>)
        </if>
        <if test="param.ocrConfirmationStatus != null and param.ocrConfirmationStatus.size > 0">
            and ofcs.ocr_confirmation_status in (<foreach collection="param.ocrConfirmationStatus" separator=","
                                                          item="item">
            #{item}
        </foreach>)
        </if>
        <if test="param.emailSentStatus != null and param.emailSentStatus.size > 0">
            and ofcs.email_sent_status in (<foreach collection="param.emailSentStatus" separator="," item="item">
            #{item}
        </foreach>)
        </if>
        GROUP BY
        ofcs.id,
        ofcs.data_date,
        ofcs.transaction_channel,
        ofcs.email_id,
        ofcs.email_rule_match_status,
        ofcs.email_received_time,
        ofcs.attachment_name,
        ofcs.ocr_confirmation_status,
        ofcs.email_sent_status

        ORDER BY ofcs.id
    </select>
</mapper>