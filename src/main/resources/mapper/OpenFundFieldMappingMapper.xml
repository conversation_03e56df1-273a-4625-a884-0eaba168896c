<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.OpenFundFieldMappingMapper">

    <select id="keyPageQuery" resultType="cn.sdata.om.al.entity.OpenFundFieldMapping">
        select type, method_name, group_concat(distinct value SEPARATOR ';' ) as value
        from open_fund_field_mapping where value_class = 'key'
        <if test="value != null and value != ''">
            AND value LIKE CONCAT('%', #{value}, '%')
        </if>
        group by type, method_name
    </select>

    <select id="keyGetOne" resultType="cn.sdata.om.al.entity.OpenFundFieldMapping">
        select type, method_name, group_concat(distinct value SEPARATOR ';' ) as value
        from open_fund_field_mapping where value_class = 'key' and type = #{type}
    </select>

    <select id="valuePageQuery" resultType="cn.sdata.om.al.entity.OpenFundFieldMapping">
        select type, method_name, name, group_concat(distinct value SEPARATOR ';' ) as value
        from open_fund_field_mapping where value_class = 'value'
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="value != null and value != ''">
            and value LIKE CONCAT('%', #{value}, '%')
        </if>
        group by type, name, method_name
    </select>

    <select id="valueGetOne" resultType="cn.sdata.om.al.entity.OpenFundFieldMapping">
        select type, group_concat(method_name), name, group_concat(distinct value SEPARATOR ';' ) as value
        from open_fund_field_mapping where value_class = 'value' and type = #{type} and name = #{name}
    </select>
</mapper>