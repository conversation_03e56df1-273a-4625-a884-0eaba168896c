<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.OtherContactsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.OtherContacts">
    <!--@mbg.generated-->
    <!--@Table other_contacts-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="recipient" jdbcType="LONGVARCHAR" property="recipient" />
    <result column="recipient_cc" jdbcType="LONGVARCHAR" property="recipientCc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, recipient, recipient_cc
  </sql>
</mapper>