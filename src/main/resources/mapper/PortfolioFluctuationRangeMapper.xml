<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.PortfolioFluctuationRangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.PortfolioFluctuationRange">
        <id column="id" property="id" />
        <result column="rule_key" property="ruleKey" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="lower_compare_type" property="lowerCompareType" />
        <result column="upper_compare_type" property="upperCompareType" />
        <result column="lower_limit" property="lowerLimit" />
        <result column="upper_limit" property="upperLimit" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_key, product_id, product_name, lower_compare_type, upper_compare_type, lower_limit, upper_limit, create_time, update_time
    </sql>

</mapper> 