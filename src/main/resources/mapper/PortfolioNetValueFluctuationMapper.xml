<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.audit.PortfolioNetValueFluctuationMapper">
    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.PortfolioNetValueFluctuation">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="product_code" property="productCode"/>
        <result column="product_name" property="productName"/>
        <result column="data_date" property="dataDate"/>
        <result column="valuation_time" property="valuationTime"/>
        <result column="abnormal_fluctuation" property="abnormalFluctuation"/>
        <result column="fluctuation_rate" property="fluctuationRate"/>
        <result column="retracement_flag" property="retracementFlag"/>
        <result column="retracement_reason" property="retracementReason"/>
        <result column="current_net_value" property="currentNetValue"/>
        <result column="previous_net_value" property="previousNetValue"/>
        <result column="net_value_difference" property="netValueDifference"/>
        <result column="current_ten_thousand_income" property="currentTenThousandIncome"/>
        <result column="previous_ten_thousand_income" property="previousTenThousandIncome"/>
        <result column="ten_thousand_income_difference" property="tenThousandIncomeDifference"/>
        <result column="email_sent" property="emailSent"/>
    </resultMap>
    
    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, product_id, product_code, product_name, data_date, valuation_time, abnormal_fluctuation, 
        fluctuation_rate, retracement_flag, retracement_reason, current_net_value, previous_net_value, 
        net_value_difference, current_ten_thousand_income, previous_ten_thousand_income, 
        ten_thousand_income_difference, email_sent
    </sql>
    
    <!-- 暂不实现具体SQL -->
</mapper> 