<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.ProjectGroupMapper">
    <select id="page" resultType="cn.sdata.om.al.entity.account.AccountSetGroup">
        select id,
               name,
               account_code as accountCodesStr,
               update_time,
               update_by    as updateByName,
               create_time,
               create_by    as createByName
        from account_set_group
        <where>
            <if test="list != null and list.size() != 0">
                and id in
                <foreach collection="list" separator="," item="e" open="(" close=")">
                    #{e,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <update id="update">
        update account_set_group
        <trim prefix="set" suffixOverrides=",">
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="accountCodesStr != null and accountCodesStr != ''">
                account_code = #{accountCodesStr,jdbcType=VARCHAR},
            </if>
            update_time = #{updateTime,jdbcType=VARCHAR},
            update_by = #{updateBy,jdbcType=VARCHAR}
        </trim>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="save">
        insert into account_set_group(id, name, account_code, create_time, create_by, update_by, update_time) VALUES (#{id,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},
        #{accountCodesStr,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=VARCHAR})
    </insert>

    <select id="getById" resultType="cn.sdata.om.al.entity.account.AccountSetGroup">
        select id, name, account_code as accountCodesStr
        from account_set_group
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="batchDelete">
        delete
        from account_set_group where id in
        <foreach collection="list" open="(" close=")" item="e" separator=",">
            #{e,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="list" resultType="cn.sdata.om.al.entity.CommonEntity">
        select id, name, account_code as extra
        from account_set_group
    </select>

    <select id="selectAccountCodesStrExcludeSelf" resultType="java.lang.String">
        select account_code from account_set_group where id != #{id,jdbcType=VARCHAR}
    </select>
</mapper>
