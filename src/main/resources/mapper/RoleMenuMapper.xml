<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.RoleMenuMapper">

    <insert id="insertBatch">
        insert into role_menu (id, role_id, menu_id, create_user, update_user, create_time, update_time)
            values
        <foreach collection="list" item="rm" separator=",">
            (#{rm.id,jdbcType=VARCHAR},
                #{rm.roleId,jdbcType=VARCHAR},
                #{rm.menuId,jdbcType=VARCHAR},
                #{rm.createUser,jdbcType=VARCHAR},
                #{rm.updateUser,jdbcType=VARCHAR},
                #{rm.createTime,jdbcType=TIMESTAMP},
                #{rm.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
