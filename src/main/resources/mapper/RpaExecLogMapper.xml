<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.RpaExecLogMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.RpaExecLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="rpaBizType" column="RPA_BIZ_TYPE" jdbcType="VARCHAR"/>
            <result property="flowId" column="FLOW_ID" jdbcType="VARCHAR"/>
            <result property="execId" column="EXEC_ID" jdbcType="VARCHAR"/>
            <result property="execTime" column="EXEC_TIME" jdbcType="VARCHAR"/>
            <result property="filePath" column="FILE_PATH" jdbcType="VARCHAR"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="execState" column="EXEC_STATE" jdbcType="VARCHAR"/>
            <result property="execResult" column="EXEC_RESULT" jdbcType="VARCHAR"/>
            <result property="o32NodeState" column="O32_NODE_STATE" jdbcType="VARCHAR"/>
            <result property="importResult" column="IMPORT_RESULT" jdbcType="VARCHAR"/>
            <result property="processResult" column="PROCESS_RESULT" jdbcType="VARCHAR"/>
            <result property="executor" column="EXECUTOR" jdbcType="VARCHAR"/>
            <result property="startTime" column="START_TIME" jdbcType="VARCHAR"/>
            <result property="finishTime" column="FINISH_TIME" jdbcType="VARCHAR"/>
            <result property="memo" column="MEMO" jdbcType="VARCHAR"/>
            <result property="extendInfo" column="EXTEND_INFO" jdbcType="VARCHAR"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="VARCHAR"/>
            <result property="accountType" column="ACCOUNT_TYPE" jdbcType="DECIMAL"/>
            <result property="rpaBizScene" column="RPA_BIZ_SCENE" jdbcType="VARCHAR"/>
            <result property="errorImage" column="ERROR_IMAGE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,RPA_BIZ_TYPE,FLOW_ID,
        EXEC_ID,EXEC_TIME,FILE_PATH,
        FILE_NAME,EXEC_STATE,EXEC_RESULT,
        O32_NODE_STATE,IMPORT_RESULT,PROCESS_RESULT,
        EXECUTOR,START_TIME,FINISH_TIME,
        MEMO,EXTEND_INFO,CREATED_TIME,
        UPDATED_TIME,ACCOUNT_TYPE,RPA_BIZ_SCENE,ERROR_IMAGE
    </sql>

    <select id="getErrorImageByLogId" resultType="java.lang.String">
        select error_image
        from rpa_exec_log
        where exec_id = (select exec_id from base_cron_log where id = #{logId,jdbcType=VARCHAR})
    </select>
</mapper>
