<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.ScopeOfSecuritiesTypeMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.ScopeOfSecuritiesType">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="secName" column="sec_name" jdbcType="VARCHAR"/>
            <result property="gzLowLimit" column="gz_low_limit" jdbcType="VARCHAR"/>
            <result property="gzUpLimit" column="gz_up_limit" jdbcType="VARCHAR"/>
            <result property="jgLowLimit" column="jg_low_limit" jdbcType="VARCHAR"/>
            <result property="jgUpLimit" column="jg_up_limit" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sec_name,gz_low_limit,
        gz_up_limit,jg_low_limit,jg_up_limit
    </sql>
</mapper>
