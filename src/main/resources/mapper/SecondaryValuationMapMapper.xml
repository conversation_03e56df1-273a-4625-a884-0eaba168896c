<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.SecondaryValuationMapMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.SecondaryValuationMap">
    <!--@mbg.generated-->
    <!--@Table secondary_valuation_map-->
    <id column="subject_code" jdbcType="VARCHAR" property="subjectCode" />
    <id column="valuation_time" jdbcType="VARCHAR" property="valuationTime" />
    <result column="secondary_name" jdbcType="VARCHAR" property="secondaryName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    subject_code, valuation_time, secondary_name
  </sql>
</mapper>
