<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.StockFundExRightsCheckSyncTimeMapper">

    <!-- 根据数据日期查询同步时间 -->
    <select id="selectByDataDate" resultType="cn.sdata.om.al.audit.entity.StockFundExRightsCheckSyncTime">
        SELECT
            id,
            data_date,
            sync_time,
            create_time,
            update_time
        FROM audit_stock_fund_ex_rights_check_sync_time
        <if test="dataDate != null and dataDate != ''">
            WHERE data_date = #{dataDate}
        </if>
        order by sync_time desc limit 1
    </select>

    <!-- 插入或更新同步时间记录 -->
    <insert id="insertOrUpdateSyncTime">
        INSERT INTO audit_stock_fund_ex_rights_check_sync_time (
            id, data_date, sync_time, create_time, update_time
        ) VALUES (
            REPLACE(UUID(), '-', ''), #{dataDate}, #{syncTime}, NOW(), NOW()
        )
        ON DUPLICATE KEY UPDATE
            sync_time = #{syncTime},
            update_time = NOW()
    </insert>

</mapper>
