<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.TADBMapper">

    <select id="queryMarketPrice" resultType="cn.sdata.om.al.entity.MarketPriceData">
        SELECT
            a.prd_code as marketCode,
            a.tot_vol as shares,
            b.PRD_NAME as planName,
            c.ACC_NAME as accountName
        FROM
            FUND60QUERY.tbfundshare a
                INNER JOIN FUND60QUERY.tbfundproduct b ON
                a.PRD_CODE = b.PRD_CODE
                INNER JOIN FUND60QUERY.tbtatransaccount c ON
                a.ASSET_ACC = c.ASSET_ACC
        <where>
            <if test="productCodes != null and productCodes.size() != 0">
                a.PRD_CODE in (<foreach collection="productCodes" item="productCode" separator=",">
                #{productCode}
            </foreach>)
            </if>
        </where>
        order by b.PRD_NAME, c.ACC_NAME

    </select>
</mapper>