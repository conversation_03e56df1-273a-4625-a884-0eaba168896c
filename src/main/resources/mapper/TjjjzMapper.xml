<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.audit.mapper.TjjjzMapper">

    <resultMap id="BaseResultMap" type="cn.sdata.om.al.audit.entity.Tjjjz">
            <id property="lZtbh" column="L_ZTBH" jdbcType="INTEGER"/>
            <id property="dRq" column="D_RQ" jdbcType="DATE"/>
            <id property="vcJjdm" column="VC_JJDM" jdbcType="VARCHAR"/>
            <result property="vcJjmc" column="VC_JJMC" jdbcType="VARCHAR"/>
            <result property="enJjzjz" column="EN_JJZJZ" jdbcType="DECIMAL"/>
            <result property="enJjzfe" column="EN_JJZFE" jdbcType="DECIMAL"/>
            <result property="enJjdwjz" column="EN_JJDWJZ" jdbcType="DECIMAL"/>
            <result property="enJjsy" column="EN_JJSY" jdbcType="DECIMAL"/>
            <result property="enDwjjsy" column="EN_DWJJSY" jdbcType="DECIMAL"/>
            <result property="enNsyl" column="EN_NSYL" jdbcType="DECIMAL"/>
            <result property="enXsfwf" column="EN_XSFWF" jdbcType="DECIMAL"/>
            <result property="enLjjz" column="EN_LJJZ" jdbcType="DECIMAL"/>
            <result property="enGpzjz" column="EN_GPZJZ" jdbcType="DECIMAL"/>
            <result property="lIsgzr" column="L_ISGZR" jdbcType="CHAR"/>
            <result property="enTzgwf" column="EN_TZGWF" jdbcType="DECIMAL"/>
            <result property="enKfpsy" column="EN_KFPSY" jdbcType="DECIMAL"/>
            <result property="enFdsy" column="EN_FDSY" jdbcType="DECIMAL"/>
            <result property="en30rnhsyl" column="EN_30RNHSYL" jdbcType="DECIMAL"/>
            <result property="enJznzzl" column="EN_JZNZZL" jdbcType="DECIMAL"/>
            <result property="lSfqr" column="L_SFQR" jdbcType="TINYINT"/>
            <result property="enGpcw" column="EN_GPCW" jdbcType="DECIMAL"/>
            <result property="enJzljzzl" column="EN_JZLJZZL" jdbcType="DECIMAL"/>
            <result property="enYhck" column="EN_YHCK" jdbcType="DECIMAL"/>
            <result property="enJsf" column="EN_JSF" jdbcType="DECIMAL"/>
            <result property="enJss" column="EN_JSS" jdbcType="DECIMAL"/>
            <result property="enGlf" column="EN_GLF" jdbcType="DECIMAL"/>
            <result property="enMrjg" column="EN_MRJG" jdbcType="DECIMAL"/>
            <result property="enMcjg" column="EN_MCJG" jdbcType="DECIMAL"/>
            <result property="enFdfy" column="EN_FDFY" jdbcType="DECIMAL"/>
            <result property="enTgf" column="EN_TGF" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        L_ZTBH,D_RQ,VC_JJDM,
        VC_JJMC,EN_JJZJZ,EN_JJZFE,
        EN_JJDWJZ,EN_JJSY,EN_DWJJSY,
        EN_NSYL,EN_XSFWF,EN_LJJZ,
        EN_GPZJZ,L_ISGZR,EN_TZGWF,
        EN_KFPSY,EN_FDSY,EN_30RNHSYL,
        EN_JZNZZL,L_SFQR,EN_GPCW,
        EN_JZLJZZL,EN_YHCK,EN_JSF,
        EN_JSS,EN_GLF,EN_MRJG,
        EN_MCJG,EN_FDFY,EN_TGF
    </sql>
    <select id="syncTjjjzInfo" resultType="cn.sdata.om.al.audit.entity.Tjjjz">
        select <include refid="Base_Column_List"></include> from TJJJZ
        <where>
            D_RQ &gt;=  TO_DATE(#{startDate}, 'YYYY-MM-DD')
            and D_RQ &lt;= TO_DATE(#{today}, 'YYYY-MM-DD')
        </where>
    </select>
</mapper>
