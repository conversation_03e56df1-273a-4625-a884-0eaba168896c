<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.UserMapper">
    <insert id="recordLoginFailed">
        INSERT INTO login_failed_record (id, account, msg, expire_time, count)
        VALUES (#{id,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{msg,jdbcType=VARCHAR},
                #{expireTime,jdbcType=TIMESTAMP}, 1)
        ON DUPLICATE KEY UPDATE count       = count + 1,
                                expire_time = #{expireTime,jdbcType=TIMESTAMP},
                                msg = #{msg,jdbcType=VARCHAR}
    </insert>

    <select id="getLoginFailedByAccount" resultType="com.alibaba.fastjson2.JSONObject">
        select id, account, msg, expire_time, count
        from login_failed_record
        where account = #{account,jdbcType=VARCHAR}
        limit 1
    </select>
    <select id="getUser2menuList" resultType="cn.sdata.om.al.entity.Menu">
        WITH RECURSIVE menu_tree AS
           (SELECT mm.*, mm.parent_id_1 AS parent_path
            FROM (select distinct m.*,
                                  case
                                      when m.parent_id = '' then
                                          '-1'
                                      else
                                          m.parent_id
                                      end as parent_id_1
                  from user u, user_role ur, role_menu rm, menu m
                  where u.id = ur.user_id
                    and ur.role_id = rm.role_id
                    and rm.menu_id = m.id
                    and u.id = #{userId}) mm
            WHERE mm.parent_id_1 = '-1'

            UNION ALL

            SELECT mmm.*, CONCAT(mt.parent_path, '>', mmm.parent_id_1) AS parent_path -- 拼接 parent_id
            FROM (select distinct m.*,
                                  case
                                      when m.parent_id = '' then
                                          '-1'
                                      else
                                          m.parent_id
                                      end as parent_id_1
                  from user u, user_role ur, role_menu rm, menu m
                  where u.id = ur.user_id
                    and ur.role_id = rm.role_id
                    and rm.menu_id = m.id
                    and u.id = #{userId}) mmm
                INNER JOIN menu_tree mt
                    ON mmm.parent_id_1 = mt.id
               )
        -- 最终结果
        SELECT tm.id,
               tm.name,
               tm.`type`,
               tm.parent_id,
               tm.icon,
               tm.`path`,
               tm.component,
               tm.perms,
               tm.visible,
               tm.is_frame,
               tm.redirect_path,
               tm.is_cache,
               tm.order_num,
               tm.remark,
               tm.create_user,
               tm.create_time,
               tm.update_user,
               tm.update_time
        FROM menu_tree tm
        where LOCATE('-1', tm.parent_path) > 0

    </select>

    <update id="resetLoginFailedCount">
        update login_failed_record set count = 0, expire_time = null where account = #{account,jdbcType=VARCHAR}
    </update>
</mapper>
