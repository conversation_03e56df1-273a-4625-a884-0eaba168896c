<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sdata.om.al.mapper.UserRoleMapper">

    <insert id="insertBatch">
        insert into user_role (id, user_id, role_id, create_user, update_user, create_time, update_time)
            values
        <foreach collection="list" item="ur" separator=",">
            (#{ur.id,jdbcType=VARCHAR},
                #{ur.userId,jdbcType=VARCHAR},
                #{ur.roleId,jdbcType=VARCHAR},
                #{ur.createUser,jdbcType=VARCHAR},
                #{ur.updateUser,jdbcType=VARCHAR},
                #{ur.createTime,jdbcType=TIMESTAMP},
                #{ur.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <select id="user2ApiCount" resultType="java.lang.Integer">
        SELECT COUNT(distinct ma.menu_id)
        FROM user u, user_role ur, role_menu rm, menu_api ma
        WHERE u.id = ur.user_id
          AND ur.role_id = rm.role_id
          AND rm.menu_id = ma.menu_id
          AND u.id = #{userId}
          AND ma.api_url = #{url}
    </select>
</mapper>
