<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sdata.om.al.mapper.ValuationTableRecordsMapper">
  <resultMap id="BaseResultMap" type="cn.sdata.om.al.entity.ValuationTableRecords">
    <!--@mbg.generated-->
    <!--@Table valuation_table_records-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="result_time" jdbcType="TIMESTAMP" property="resultTime" />
    <result column="download_time" jdbcType="TIMESTAMP" property="downloadTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="valuation_table_path" jdbcType="VARCHAR" property="valuationTablePath" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="valuation_date" jdbcType="VARCHAR" property="valuationDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, result_time, download_time, `operator`, `status`, valuation_table_path,product_id,valuation_date
  </sql>
</mapper>