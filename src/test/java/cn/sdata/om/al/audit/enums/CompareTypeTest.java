package cn.sdata.om.al.audit.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * CompareType枚举测试类
 */
public class CompareTypeTest {
    
    @Test
    public void testCompareTypeValues() {
        // 测试所有枚举值
        assertEquals(4, CompareType.values().length);
        assertEquals(0, CompareType.GREATER_THAN.ordinal());
        assertEquals(">", CompareType.GREATER_THAN.getSymbol());
        assertEquals("大于", CompareType.GREATER_THAN.getDescription());
        assertEquals(1, CompareType.GREATER_EQUAL.ordinal());
        assertEquals(">=", CompareType.GREATER_EQUAL.getSymbol());
        assertEquals("大于等于", CompareType.GREATER_EQUAL.getDescription());
        assertEquals(2, CompareType.LESS_THAN.ordinal());
        assertEquals("<", CompareType.LESS_THAN.getSymbol());
        assertEquals("小于", CompareType.LESS_THAN.getDescription());
        assertEquals(3, CompareType.LESS_EQUAL.ordinal());
        assertEquals("<=", CompareType.LESS_EQUAL.getSymbol());
        assertEquals("小于等于", CompareType.LESS_EQUAL.getDescription());
    }
    
    @Test
    public void testFromSymbol() {
        // 测试根据符号获取枚举
        assertEquals(CompareType.GREATER_THAN, CompareType.fromSymbol(">"));
        assertEquals(CompareType.GREATER_EQUAL, CompareType.fromSymbol(">="));
        assertEquals(CompareType.LESS_THAN, CompareType.fromSymbol("<"));
        assertEquals(CompareType.LESS_EQUAL, CompareType.fromSymbol("<="));
        
        // 测试无效符号
        assertThrows(IllegalArgumentException.class, () -> CompareType.fromSymbol("=="));
    }
    
    @Test
    public void testFromDescription() {
        // 测试根据描述获取枚举
        assertEquals(CompareType.GREATER_THAN, CompareType.fromDescription("大于"));
        assertEquals(CompareType.GREATER_EQUAL, CompareType.fromDescription("大于等于"));
        assertEquals(CompareType.LESS_THAN, CompareType.fromDescription("小于"));
        assertEquals(CompareType.LESS_EQUAL, CompareType.fromDescription("小于等于"));
        
        // 测试无效描述
        assertThrows(IllegalArgumentException.class, () -> CompareType.fromDescription("等于"));
    }
} 