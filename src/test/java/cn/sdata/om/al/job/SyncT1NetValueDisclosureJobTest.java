package cn.sdata.om.al.job;

import cn.sdata.om.al.entity.AccountInformation;
import cn.sdata.om.al.enums.PaymentMethod;
import cn.sdata.om.al.enums.ValuationTime;
import cn.sdata.om.al.service.AccountInformationService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


@SpringBootTest
class SyncT1NetValueDisclosureJobTest {

    private SyncT1NetValueDisclosureJob syncT1NetValueDisclosureJob;
    private AccountInformationService accountInformationService;

    @Autowired
    public void setSyncT1NetValueDisclosureJob(SyncT1NetValueDisclosureJob syncT1NetValueDisclosureJob) {
        this.syncT1NetValueDisclosureJob = syncT1NetValueDisclosureJob;
    }
    @Autowired
    public void setAccountInformationService(AccountInformationService accountInformationService) {
        this.accountInformationService = accountInformationService;
    }

    @Test
    void dealT1Batch() {
//        List<String> strings = syncT1NetValueDisclosureJob.dealT1Batch();
//
//        System.out.println(strings);
    }

    @Test
    void addAccountInformation() {
        try (XSSFWorkbook workbook = new XSSFWorkbook("/Users/<USER>/Downloads/1.账套信息表-20250422new.xlsx")) {
            XSSFSheet sheet = workbook.getSheetAt(0);
            List<AccountInformation> accountInformationList = new ArrayList<>();
            for (int i = 3; i < 273; i++) {
                AccountInformation accountInformation = new AccountInformation();
                XSSFRow row = sheet.getRow(i);
                accountInformation.setFullProductName(row.getCell(2).getStringCellValue());
                accountInformation.setId(String.valueOf((int) row.getCell(3).getNumericCellValue()));
                XSSFCell cell4 = row.getCell(4);
                CellType cell4Type = cell4.getCellType();
                switch (cell4Type){
                    case NUMERIC:
                        accountInformation.setProductCode(String.valueOf((int) cell4.getNumericCellValue()));
                        break;
                    case STRING:
                        accountInformation.setProductCode(cell4.getStringCellValue());
                        break;
                }
//                accountInformation.setProductCode(row.getCell(4).getStringCellValue());
                String productCategory = row.getCell(5).getStringCellValue();
                switch (productCategory) {
                    case "组合产品":
                        accountInformation.setProductCategory(3);
                        break;
                    case "委受托":
                        accountInformation.setProductCategory(1);
                        break;
                    case "债权投资计划":
                        accountInformation.setProductCategory(2);
                        break;
                }
                String valuationTime = row.getCell(6).getStringCellValue();
                switch (valuationTime) {
                    case "T1估值":
                        accountInformation.setValuationTime(ValuationTime.T1.name());
                        break;
                    case "T0估值":
                        accountInformation.setValuationTime(ValuationTime.T0.name());
                        break;
                }
                String isStructured = row.getCell(7).getStringCellValue();
                switch (isStructured) {
                    case "是":
                        accountInformation.setIsStructured(1);
                        break;
                    case "否":
                        accountInformation.setIsStructured(0);
                        break;
                }
                String isElectronicDirectConnection = row.getCell(8).getStringCellValue();
                switch (isElectronicDirectConnection) {
                    case "是":
                        accountInformation.setIsElectronicDirectConnection(1);
                        break;
                    case "否":
                        accountInformation.setIsElectronicDirectConnection(0);
                        break;
                }
                accountInformation.setCustodianBank(row.getCell(14).getStringCellValue());
                accountInformation.setSecondaryValuationSecurityType(row.getCell(15).getStringCellValue());
                accountInformation.setAccountName(row.getCell(16).getStringCellValue());
                XSSFCell cell17 = row.getCell(17);
                CellType cellType = cell17.getCellType();
                switch (cellType){
                    case NUMERIC:
                        accountInformation.setAccountNumberDetail(String.valueOf((int) cell17.getNumericCellValue()));
                        break;
                    case STRING:
                        accountInformation.setAccountNumberDetail(row.getCell(17).getStringCellValue());
                        break;
                }
                accountInformation.setOpeningBank(row.getCell(18).getStringCellValue());
                accountInformation.setLargePaymentNumber(row.getCell(19).getStringCellValue());
                String paymentMethodZZ = row.getCell(20).getStringCellValue();
                switch(paymentMethodZZ){
                    case "自动扣收":
                        accountInformation.setPaymentMethodZZ(PaymentMethod.AUTO_PAYMENT.name());
                        break;
                    case "手工支付":
                        accountInformation.setPaymentMethodZZ(PaymentMethod.MANUAL_PAYMENT.name());
                        break;
                }
                accountInformation.setCentralDebtAccountNumber(row.getCell(21).getStringCellValue());
                String paymentMethodWH = row.getCell(22).getStringCellValue();
                switch(paymentMethodWH){
                    case "自动扣收":
                        accountInformation.setPaymentMethodWH(PaymentMethod.AUTO_PAYMENT.name());
                        break;
                    case "手工支付":
                        accountInformation.setPaymentMethodWH(PaymentMethod.MANUAL_PAYMENT.name());
                        break;
                }
//                accountInformation.setForeignExchangeCenterMemberCode(row.getCell(23).getStringCellValue());
                XSSFCell cell23 = row.getCell(23);
                CellType cell23Type = cell23.getCellType();
                switch (cell23Type){
                    case NUMERIC:
                        accountInformation.setForeignExchangeCenterMemberCode(String.valueOf((int) cell23.getNumericCellValue()));
                        break;
                    case STRING:
                        accountInformation.setForeignExchangeCenterMemberCode(cell23.getStringCellValue());
                        break;
                }
                String paymentMethodSQ = row.getCell(24).getStringCellValue();
                switch(paymentMethodSQ){
                    case "自动扣收":
                        accountInformation.setPaymentMethodSQ(PaymentMethod.AUTO_PAYMENT.name());
                        break;
                    case "手工支付":
                        accountInformation.setPaymentMethodSQ(PaymentMethod.MANUAL_PAYMENT.name());
                        break;
                }
//                accountInformation.setClearingHouseHolderAccount(row.getCell(25).getStringCellValue());
                XSSFCell cell25 = row.getCell(25);
                CellType cell25Type = cell25.getCellType();
                switch (cell25Type){
                    case NUMERIC:
                        accountInformation.setClearingHouseHolderAccount(String.valueOf((int) cell25.getNumericCellValue()));
                        break;
                    case STRING:
                        accountInformation.setClearingHouseHolderAccount(cell25.getStringCellValue());
                        break;
                }
                XSSFCell cell26 = row.getCell(26);
                CellType cell26Type = cell26.getCellType();
                switch (cell26Type){
                    case NUMERIC:
                        accountInformation.setHolderAccountNumber(String.valueOf((int) cell26.getNumericCellValue()));
                        break;
                    case STRING:
                        accountInformation.setHolderAccountNumber(cell26.getStringCellValue());
                        break;
                }
//                accountInformation.setHolderAccountFullName(row.getCell(26).getStringCellValue());
                accountInformation.setHolderAccountFullName(row.getCell(27).getStringCellValue());
                accountInformationList.add(accountInformation);
            }
//            System.out.println(accountInformationList);
            accountInformationService.saveOrUpdateBatch(accountInformationList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}