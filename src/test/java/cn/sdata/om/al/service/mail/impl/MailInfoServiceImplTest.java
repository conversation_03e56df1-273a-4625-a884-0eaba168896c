package cn.sdata.om.al.service.mail.impl;

import cn.sdata.om.al.entity.SendMailInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MailInfoServiceImpl单元测试
 * 使用Spring Boot测试框架
 */
@SpringBootTest
public class MailInfoServiceImplTest {

    @Autowired
    private MailInfoServiceImpl mailInfoService;

    /**
     * 测试正常情况下发送多封邮件
     * 验证所有邮件都被成功处理
     */
    @Test
    public void testExecuteSendMailInfoSuccess() throws InterruptedException {
        // 并发线程数
        int threadCount = 3;
        // 每个线程发送的邮件数
        int mailsPerThread = 2;
        
        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // 创建并提交多个并发任务
        for (int t = 0; t < threadCount; t++) {
            final int threadId = t;
            executorService.submit(() -> {
                try {
                    // 准备该线程的测试数据
                    List<SendMailInfo> sendMailInfos = new ArrayList<>();
                    for (int i = 0; i < mailsPerThread; i++) {
                        SendMailInfo mailInfo = new SendMailInfo();
                        mailInfo.setSubject("测试邮件 线程" + threadId + " 邮件" + i);
                        mailInfo.setRecipient(List.of("<EMAIL>"));
                        mailInfo.setCc(List.of("<EMAIL>"));
                        mailInfo.setContent("邮件内容 线程" + threadId + " 邮件" + i);
                        sendMailInfos.add(mailInfo);
                    }
                    
                    // 调用邮件发送方法
                    mailInfoService.doSendMailInfo(sendMailInfos);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成，设置超时时间为30秒
        boolean completed = latch.await(1000, TimeUnit.SECONDS);
        executorService.shutdown();
        
        if (!completed) {
            org.junit.jupiter.api.Assertions.fail("并发测试超时，可能存在死锁或性能问题");
        }
        
        // 验证所有线程成功完成 - 如果执行到这里，说明没有抛出异常
        assertTrue(true, "所有并发邮件发送任务成功完成");
    }

    /**
     * 测试多线程环境下邮件发送限速功能
     * 验证在并发情况下限速机制是否正常工作
     */
    @Test
    public void testRateLimitedMailSendingWithMultipleThreads() throws InterruptedException {
        // 并发线程数
        int threadCount = 3;
        // 每个线程发送的邮件数
        int mailsPerThread = 2;
        // 总邮件数
        int totalMailCount = threadCount * mailsPerThread;

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch startLatch = new CountDownLatch(1); // 用于同步所有线程同时开始
        CountDownLatch completionLatch = new CountDownLatch(threadCount);

        // 记录所有邮件的状态
        List<SendMailInfo> allResults = new ArrayList<>();

        // 记录开始时间
        long[] startTime = new long[1]; // 使用数组以便在lambda中修改
        byte[] b1;
        byte[] b2;
        try (FileInputStream file1 = new FileInputStream("/Users/<USER>/Downloads/000621人寿万能+2.pdf");
             FileInputStream file2 = new FileInputStream("/Users/<USER>/Downloads/000621人寿万能+2.pdf")) {
            b1 = file1.readAllBytes();
            b2 = file2.readAllBytes();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 创建并提交多个并发任务
        for (int t = 0; t < threadCount; t++) {
            final int threadId = t;
            executorService.submit(() -> {
                try {
                    // 准备该线程的测试数据
                    List<SendMailInfo> sendMailInfos = new ArrayList<>();
                    for (int i = 0; i < mailsPerThread; i++) {
                        SendMailInfo mailInfo = new SendMailInfo();
                        mailInfo.setSubject("限速测试邮件 线程" + threadId + " 邮件" + i);
                        mailInfo.setRecipient(List.of("<EMAIL>"));
                        mailInfo.setCc(List.of("<EMAIL>"));
                        mailInfo.setContent("这是一封测试限速功能的邮件 线程" + threadId + " 邮件" + i);
                        Map<String, byte[]> attachments = new HashMap<>();
                        attachments.put("附件1.pdf", b1);
                        attachments.put("附件2.pdf", b2);
                        mailInfo.setAttachment(attachments);
                        mailInfo.setCompressAttachmentIs(1);
                        sendMailInfos.add(mailInfo);
                    }

                    // 等待所有线程准备就绪后同时开始
                    startLatch.await();

                    // 调用邮件发送方法
                    List<SendMailInfo> results = mailInfoService.doSendMailInfo(sendMailInfos);

                    // 收集结果
                    synchronized (allResults) {
                        allResults.addAll(results);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        // 所有线程准备就绪，记录开始时间并同时开始执行
        startTime[0] = System.currentTimeMillis();
        startLatch.countDown();

        // 等待所有线程完成，设置超时时间
        boolean completed = completionLatch.await(60, TimeUnit.SECONDS);
        executorService.shutdown();

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime[0];

        // 验证结果
        if (!completed) {
            fail("并发测试超时，可能存在死锁或性能问题");
        }

        // 1. 所有邮件都应该被处理
        assertEquals(totalMailCount, allResults.size(), "应处理所有邮件");

        // 2. 所有邮件都应该有状态
        for (SendMailInfo mailInfo : allResults) {
            assertNotNull(mailInfo.getStatus(), "邮件状态不应为空");
        }

        // 3. 由于限速，总执行时间应该超过一定阈值
        // 假设RateLimiter配置为每5秒1个请求，则15个邮件至少需要75秒
        // 这里使用一个保守的估计值，根据实际限速配置调整
        long expectedMinDuration = 5000; // 毫秒，根据实际限速配置调整
        assertTrue(duration >= expectedMinDuration,
                "执行时间(" + duration + "ms)应该不少于" + expectedMinDuration + "ms，表明限速生效");

        System.out.println("并发发送" + totalMailCount + "封邮件总耗时: " + duration + "ms");
    }
}
